# Initialization Job to:
# - create and migrate Postgres tables
# - create database for Metabase
# - create extension btree_gist (for subql database)

resource "kubernetes_job" "init" {
  wait_for_completion = true
  timeouts {
    create = "10m"
    update = "10m"
  }

  metadata {
    name      = "init"
    namespace = var.namespace
  }

  spec {
    # Avoid retrying failed runs in case manual intervention is needed
    backoff_limit              = 0
    completions                = 1
    ttl_seconds_after_finished = 600

    template {
      metadata {}
      spec {
        container {
          name              = "init"
          image             = var.python_runner_image
          image_pull_policy = "IfNotPresent"
          args              = ["python", "-m", "wallet.init"]

          dynamic "env_from" {
            for_each = var.config_maps
            iterator = config_map
            content {
              config_map_ref {
                name = config_map.value
              }
            }
          }
          dynamic "env_from" {
            for_each = var.secrets
            iterator = secret
            content {
              secret_ref {
                name = secret.value
              }
            }
          }

          resources {
            requests = {
              cpu    = "100m"
              memory = "256Mi"
            }
          }

          dynamic "volume_mount" {
            for_each = var.host_volumes
            content {
              name       = volume_mount.value.name
              mount_path = volume_mount.value.mount_path
            }
          }
        }

        dynamic "volume" {
          for_each = var.host_volumes
          content {
            name = volume.value.name
            host_path {
              path = volume.value.host_path
            }
          }
        }
      }
    }
  }
}
