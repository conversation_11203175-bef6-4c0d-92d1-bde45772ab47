import { Toast, useToast } from "@/components/Toast";
import { WalletSearch } from "@/components/autocomplete/WalletSearch";
import { useAppDispatch } from "@/hooks/store";
import {
  api,
  useIndexerWalletSyncMutation,
  useLazyIndexerJobQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Wallet, WalletSyncResult } from "@/types/schemas";
import { Sync } from "@mui/icons-material";
import { CircularProgress, IconButton } from "@mui/material";
import { useEffect, useRef, useState } from "react";

const WALLET_TO_SYNC_ID_KEY = "admin.walletSync.walletId";

export function WalletSync() {
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null);
  const dispatch = useAppDispatch();
  const [isLoading, setLoading] = useState(false);

  const [toastProps, toast] = useToast();

  const [startSync] = useIndexerWalletSyncMutation();
  const [fetchJob] = useLazyIndexerJobQuery();

  const abortControllerRef = useRef<AbortController>();

  // Abort any pending request on unmount
  useEffect(() => {
    return () => abortControllerRef.current?.abort();
  }, []);

  const startSyncAndPoll = async (walletId: number) => {
    abortControllerRef.current?.abort();
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    setLoading(true);
    try {
      const { id: jobId } = await startSync(walletId).unwrap();
      if (signal.aborted) return;

      while (true) {
        await new Promise((resolve) => setTimeout(resolve, 5000));
        if (signal.aborted) return;

        const job = await fetchJob(jobId).unwrap();
        if (signal.aborted) return;

        if (job.status === "failed") {
          toast({ severity: "error", message: job.error ?? "Unexpected failure" });
          break;
        }

        if (job.status === "completed") {
          if (job.result && "results" in job.result) {
            const [severity, message] = processResponse(job.result.results as JobResults);
            toast({ severity, message });
          } else {
            toast({ severity: "error", message: "Unexpected result" });
          }
          break;
        }
      }
    } catch (error: unknown) {
      if (signal.aborted) return;
      toast({
        severity: "error",
        message: `Unexpected error: ${getErrorDetail(error)}`,
      });
    } finally {
      dispatch(api.util.invalidateTags(["TransactionUnclassified"]));
      if (!signal.aborted) {
        setLoading(false);
      }
    }
  };

  return (
    <>
      <WalletSearch
        additionalWallet={{ id: -1, address: "∗", name: "All wallets", networks: [] }}
        disabled={isLoading}
        isAdmin
        label="Wallet to sync"
        network={null}
        onInitialLoad={(wallets) => {
          if (selectedWallet != null) return;
          const initialWalletId = getInitialWalletId();
          if (initialWalletId == null) return;
          const wallet = wallets.find((w) => w.id === initialWalletId);
          if (wallet) setSelectedWallet(wallet);
        }}
        selectedWallet={selectedWallet}
        setSelectedWallet={(wallet) => {
          setSelectedWallet(wallet);
          if (wallet) {
            localStorage.setItem(WALLET_TO_SYNC_ID_KEY, wallet.id.toString());
          }
        }}
      />
      <IconButton
        aria-label="Sync wallet"
        title="Sync wallet"
        disabled={selectedWallet == null || isLoading}
        onClick={() => {
          if (selectedWallet == null) return;
          startSyncAndPoll(selectedWallet.id);
        }}
        sx={{ width: 40 }}
      >
        {isLoading ? <CircularProgress color="inherit" size={20} /> : <Sync />}
      </IconButton>
      <Toast {...toastProps} />
    </>
  );
}

type JobResults = [walletId: number, results: WalletSyncResult[]][];

function processResponse(data: JobResults): ["warning" | "success", string] {
  let successCount = 0;
  let failCount = 0;
  const networksWithFailure = new Set<string>();

  for (const [, results] of data) {
    for (const result of results) {
      if (result.errors.length > 0) {
        failCount++;
        networksWithFailure.add(result.networkName);
      } else {
        successCount++;
      }
    }
  }

  const failedMessageSuffix =
    failCount > 0
      ? `, failed for ${failCount} addresses (${[...networksWithFailure].join(", ")})`
      : "";

  if (successCount === 0) {
    return ["warning", `No addresses were synced${failedMessageSuffix}`];
  }
  return [
    failCount > 0 ? "warning" : "success",
    `Fully synced ${successCount} addresses${failedMessageSuffix}`,
  ];
}

function getInitialWalletId() {
  const savedId = localStorage.getItem(WALLET_TO_SYNC_ID_KEY);
  if (!savedId) return null;
  const parsedId = Number.parseInt(savedId);
  if (!Number.isInteger(parsedId)) return null;
  return parsedId;
}
