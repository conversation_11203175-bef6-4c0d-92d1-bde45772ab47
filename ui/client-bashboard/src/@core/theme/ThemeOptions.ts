// ** MUI Theme Provider
import { ThemeOptions } from "@mui/material";
import { deepmerge } from "@mui/utils";

// ** User Theme Options
import UserThemeOptions from "@/layouts/UserThemeOptions";

// ** Type Import
import { Settings } from "@/@core/context/settingsContext";

// ** Theme Override Imports
import breakpoints from "@/@core/theme/breakpoints";
import palette from "@/@core/theme/palette";
import shadows from "@/@core/theme/shadows";
import spacing from "@/@core/theme/spacing";

const themeOptions = (settings: Settings): ThemeOptions => {
  // ** Vars
  const { skin, mode, direction, themeColor } = settings;

  // ** Create New object before removing user component overrides and typography objects from userThemeOptions
  const userThemeConfig = Object.assign({}, UserThemeOptions());

  const userFontFamily =
    userThemeConfig.typography && "fontFamily" in userThemeConfig.typography
      ? userThemeConfig.typography.fontFamily
      : undefined;

  // ** Remove component overrides and typography objects from userThemeOptions
  delete userThemeConfig.components;
  delete userThemeConfig.typography;

  const mergedThemeConfig = deepmerge(
    {
      direction,
      palette: palette(mode, skin, themeColor),
      typography: {
        fontFamily:
          userFontFamily ||
          [
            "Inter",
            "sans-serif",
            "-apple-system",
            "BlinkMacSystemFont",
            '"Segoe UI"',
            "Roboto",
            '"Helvetica Neue"',
            "Arial",
            "sans-serif",
            '"Apple Color Emoji"',
            '"Segoe UI Emoji"',
            '"Segoe UI Symbol"',
          ].join(","),
      },
      shadows: shadows(mode),
      ...spacing,
      breakpoints: breakpoints(),
      shape: {
        borderRadius: 6,
      },
      mixins: {
        toolbar: {
          minHeight: 64,
        },
      },
    },
    userThemeConfig,
  );

  return deepmerge(mergedThemeConfig, {
    palette: {
      primary: {
        ...mergedThemeConfig.palette[themeColor],
      },
    },
  });
};

export default themeOptions;
