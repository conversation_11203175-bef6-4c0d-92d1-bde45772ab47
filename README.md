# Git hooks setup

This repository uses git hooks with [pre-commit](https://pre-commit.com) to make it easy to
developers to follow the same code style and to avoid common mistakes. To install pre-commit,
run one of the following commands:

```bash
pipx install pre-commit
# or
conda install pre-commit
# or
brew install pre-commit
```

Then, to install the git hooks, run the following command on the root of the repository:

```bash
pre-commit install
```

Now every change you attempt to commit will be checked by the hooks. The commit will be aborted
if any hook modifies your code or finds an issue.

For more information, check the [pre-commit documentation](https://pre-commit.com).

# Release workflow

We're using continuous deployment to have code on `main` branch automatically deployed to
production environments. The deployment is done by GitHub Actions and Terraform Cloud.

The workflow is generally as follows:

1. Create a new branch from main.
2. Make changes and create a pull request.
3. Once the pull request is approved, merge it to main.
4. The changes will be automatically deployed once it reaches the main branch. To achieve it,
   the following jobs will happen in parallel:
   - Docker images are built and pushed to the registry (see .github/workflows/docker.yml).
   - [Terraform Cloud](https://app.terraform.io/app/stable-tech/workspaces) will apply the
     changes to the infrastructure (including updating Kubernetes resources).
   - The UIs are built and deployed (see lootloot-ci.yml and client-bashboard-ci.yml workflows
     in .github/workflows/).

> ℹ️ When, for example, changing an environment variable or secret in Terraform Cloud or
> changing a permission on Google Cloud, you can use the Terraform Cloud UI to trigger a manual
> run. Manual runs will use the latest code from the main branch.

## Stage deployments

We have stage environments for testing changes before deploying to production. The stage
environments are tracking the `stage` branch. So, to deploy to a stage environment, you should
push your changes to the `stage` branch. To do this correctly, better simulating merging to
main, please use the `deploy.py` script in the root of the repository:

```bash
./deploy.py --env stage
```

After pushing to `stage`, the changes will be automatically deployed to the environments as
needed, only if there are modified files relevant to each component.

Please be mindful that someone else might be using the stage environment at the same time as
you. So please check the
[commit history of the stage branch](https://github.com/Stable-Tech/temet/commits/stage/), look
for recent commits that might indicate someone else is using it.

# Pydantic2ts

UI Typescript type files are generated from the Python pydantic schema files using
`pydantic2ts`.

A python wrapper script can be invoked at `common/pydantic2ts.py`. The following command can
also be used to run it locally inside of the API container with all dependencies setup:

```
kubectl exec  `kubectl get pods --no-headers -o custom-columns=":metadata.name" |grep api` -- bash -c "cd .. ; python common/pydantic2ts.py"
```

# Developer Experience

## Auto imports on VSCode

If you use VSCode, make sure to configure the preferred path style for auto imports to be
`non-relative`, as we have rules in place to enforce canonical import paths using `@/*` aliases.

This is configured by default in the `.vscode/settings.json` file in the repository. If you have
a different setup, like opening the workspace in a different directory other than the root, you
might need to configure it manually.

You could do it through the UI, open your workspace or user settings, search for "Import Module
Specifier" and set it to `non-relative` for **TypeScript** and **JavaScript**.

Or, alternatively, you could manually edit your `.vscode/settings.json` file adding the
following properties:

```json
{
  "javascript.preferences.importModuleSpecifier": "non-relative",
  "typescript.preferences.importModuleSpecifier": "non-relative"
}
```
