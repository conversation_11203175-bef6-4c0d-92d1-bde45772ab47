/* global document, fetch, FormData, console */

import {
  Ban,
  BanknoteArrowDown,
  ChartLine,
  Check,
  ChevronDown,
  createIcons,
  Fingerprint,
  Info,
  Lock,
  Menu,
  Monitor,
  RefreshCw,
  Scale,
  Shield,
  Users,
  Wallet,
} from "lucide";

createIcons({
  nameAttr: "data-lucide",
  icons: {
    Wallet,
    RefreshCw,
    Scale,
    ChartLine,
    BanknoteArrowDown,
    Fingerprint,
    ChevronDown,
    Info,
    Menu,
    Users,
    Check,
  },
});

createIcons({
  nameAttr: "data-lucide-thick",
  icons: { Shield, Ban, Monitor, Lock },
  attrs: {
    "stroke-width": 2.5,
  },
});

function initializeContactForm() {
  // Handle form submission
  const contactForm = document.querySelector("#contact-form");
  if (!contactForm) return;

  contactForm.addEventListener("submit", async function (event) {
    event.preventDefault();

    const existingError = contactForm.querySelector("#get-started-error");
    if (existingError) {
      existingError.remove();
    }

    const submitButton = contactForm.querySelector("button[type='submit']");
    const originalButtonText = submitButton.textContent;
    submitButton.textContent = "Sending...";
    submitButton.disabled = true;

    const formData = new FormData(contactForm);
    const email = formData.get("investorEmail");

    const baseUrl = import.meta.env.DEV
      ? "http://localhost:8080"
      : "https://api.production.stabletech.capital";

    try {
      const response = await fetch(`${baseUrl}/public/get-started`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        // Toggle visibility with CSS variables
        document.documentElement.style.setProperty("--form-display", "none");
        document.documentElement.style.setProperty("--success-message-display", "block");
      } else {
        throw new Error("Server error");
      }
    } catch (error) {
      console.error("Error submitting form:", error);

      // Show error message
      const errorMsg = document.createElement("p");
      errorMsg.id = "get-started-error";
      errorMsg.className = "text-red-600 mt-4";
      errorMsg.textContent = "There was an error submitting your request. Please try again.";

      submitButton.parentNode.append(errorMsg);
    } finally {
      submitButton.textContent = originalButtonText;
      submitButton.disabled = false;
    }
  });

  // Handle "Submit Another Email" button click
  const submitAnotherButton = document.querySelector("#submit-another-button");
  submitAnotherButton.addEventListener("click", function () {
    // Reset the form field
    contactForm.reset();

    // Show form, hide success message (default state)
    document.documentElement.style.removeProperty("--form-display");
    document.documentElement.style.removeProperty("--success-message-display");
  });
}

function initializeMobileMenu() {
  const mobileMenuButton = document.querySelector("#mobile-menu-button");
  const mobileDropdown = document.querySelector("#mobile-dropdown");
  if (!mobileMenuButton || !mobileDropdown) return;

  /**
   * Toggles the visibility of the dropdown menu.
   * @param {boolean} show - If true, displays the dropdown; if false, hides it.
   */
  function toggleMobileDropdown(show) {
    if (show) {
      mobileDropdown.classList.remove("hidden");
      mobileDropdown.classList.add("flex");
      mobileMenuButton.setAttribute("aria-expanded", "true");
    } else {
      mobileDropdown.classList.add("hidden");
      mobileDropdown.classList.remove("flex");
      mobileMenuButton.setAttribute("aria-expanded", "false");
    }
  }

  mobileMenuButton.addEventListener("click", function (event) {
    event.stopPropagation(); // Prevent the document click handler from firing
    const expanded = mobileMenuButton.getAttribute("aria-expanded") === "true";
    toggleMobileDropdown(!expanded);
  });

  // Close dropdown when clicking outside
  document.addEventListener("click", function (event) {
    if (!mobileMenuButton.contains(event.target) && !mobileDropdown.contains(event.target)) {
      toggleMobileDropdown(false);
    }
  });
}

document.addEventListener("DOMContentLoaded", function () {
  initializeContactForm();
  initializeMobileMenu();
});
