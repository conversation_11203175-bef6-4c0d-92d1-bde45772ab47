import { ApiError } from "@/lib/api";

const PROPS_TO_CHECK_FOR_MESSAGE = ["message", "detail", "error"] as const;

export function getErrorMessage(error: unknown): string {
  if (error == null) {
    return "Undefined error";
  }
  if (error instanceof ApiError) {
    if (error.body?.detail) {
      if (typeof error.body.detail === "string") {
        return error.body.detail;
      }
      return error.body.detail
        .map((e) => `${e.loc.slice(1).map(snakeToTitle).join(" » ")}: ${e.msg}`)
        .join("\u2002•\u2002"); // U+2002 En Space
    }
    return error.message;
  }
  if (typeof error === "object") {
    for (const prop of PROPS_TO_CHECK_FOR_MESSAGE) {
      const value = (error as Record<string, unknown>)[prop];
      if (typeof value === "string" && value.length > 0) {
        return value;
      }
    }
  } else if (typeof error === "string" && error.length > 0) {
    return error;
  }
  return "Unknown error";
}

/** Convert snake_case to Title Case */
function snakeToTitle(snake: string): string {
  return snake.replaceAll("_", " ").replaceAll(/\b\w/gu, (c) => c.toUpperCase());
}
