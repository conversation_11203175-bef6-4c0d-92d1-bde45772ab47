from decimal import Decimal
from typing import Sequence

from passlib.context import <PERSON>pt<PERSON>ontext
from sqlalchemy import func, select

from stabletech import schemas
from stabletech.auth.models import AuthToken, Role, User
from stabletech.utils.models import BaseManager

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthTokenManager(BaseManager[AuthToken]):
    model = AuthToken

    def add(self, user_id):
        auth_token = AuthToken(user_id=user_id)
        auth_token.set_token()
        self.db.add(auth_token)
        self.db.commit()
        self.db.refresh(auth_token)
        return auth_token

    def get_or_create(self, user_id) -> AuthToken:
        auth_token = self.db.query(self.model).filter_by(user_id=user_id).first()
        return auth_token if auth_token else self.add(user_id)

    def get_by_token(self, token) -> AuthToken | None:
        return self.db.query(AuthToken).filter_by(token=token).first()


class UserManager(BaseManager[User]):
    model = User

    def add(self, email) -> User:
        existing_user = self.db.query(User).filter_by(email=email).all()
        if existing_user:
            # Re-enable if was previously added
            existing_user = existing_user[0]
            existing_user.is_active = True
            self.db.commit()
            return existing_user

        user = User(email=email)
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    def add_with_password(self, schema: schemas.LoginPassword) -> User:
        user = self.add(schema.email)
        user.password_hash = pwd_context.hash(schema.password)
        self.db.commit()
        return user

    def get_active_by_email(self, email: str) -> User | None:
        user = self.db.query(User).filter_by(email=email, is_active=True).first()
        return user

    def get_active_users(self, include_admins: bool = False) -> Sequence[User]:
        query = select(User).where(User.is_active == True).order_by(User.email)

        if not include_admins:
            query = query.where(User.role != Role.admin.value)

        return self.db.scalars(query).all()

    def get_active_user_by_id(self, user_id: int) -> User:
        return self.db.query(User).filter_by(id=user_id, is_active=True).one()

    def get_user_share_by_id(self, user_id: int) -> Decimal:
        return self.db.scalars(
            select(User.usd_invested / select(func.sum(User.usd_invested))).where(
                User.id == user_id
            )
        ).one()

    def delete(self, user_id) -> None:
        user = self.get_by_id(user_id)
        user.is_active = False
        self.db.commit()

    def update(self, user_dict: dict) -> User:
        user = self.get_by_id(user_dict["id"])
        for key, value in user_dict.items():
            setattr(user, key, value)
        self.db.commit()
        self.db.refresh(user)
        return user
