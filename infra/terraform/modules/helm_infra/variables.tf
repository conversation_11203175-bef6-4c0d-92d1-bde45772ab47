locals {
  postgresql_values = {
    global : {
      postgresql : {
        auth : {
          database : "temet"
          postgresPassword : "password"
          secretKeys : {
            adminPasswordKey : ""
          }
        },
      }
    },
    primary : {
      resourcesPreset : "2xlarge",
      persistence : {
        existingClaim : "${var.namespace}-psql-data-pvc"
      },
      initContainers : [
        {
          name : "init-chmod-data",
          image : "busybox",
          command : ["sh", "-c", "mkdir -p /bitnami/postgresql/data && chown -R 1001:1001 /bitnami/postgresql"],
          volumeMounts : [
            {
              name : "data",
              mountPath : "/bitnami/postgresql"
            }
          ],
          securityContext : {
            runAsUser : 0
          }
        }
      ]
    },
  }

  subql_postgresql_values = {
    global : {
      postgresql : {
        auth : {
          database : "subql"
          postgresPassword : "password"
          secretKeys : {
            adminPasswordKey : ""
          }
        },
      }
    },
    primary : {
      initdb : {
        scripts : {
          "init_db.sql" : "CREATE EXTENSION IF NOT EXISTS btree_gist;"
        },
      },
      persistence : {
        existingClaim : "${var.namespace}-subql-psql-data-pvc"
      },
      initContainers : [

        {
          name : "init-chmod-data",
          image : "busybox",
          command : ["sh", "-c", "mkdir -p /bitnami/postgresql/data && chown -R 1001:1001 /bitnami/postgresql"],
          volumeMounts : [
            {
              name : "data",
              mountPath : "/bitnami/postgresql"
            }
          ],
          securityContext : {
            runAsUser : 0
          }
        }
      ]
    },
  }
}

variable "enable_postgresql" {
  default = false
  type    = bool
}

variable "enable_subql_postgresql" {
  default = false
  type    = bool
}

variable "namespace" {
  description = "Kubernetes namespace to use"
}
