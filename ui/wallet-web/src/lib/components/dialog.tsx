"use client";

import * as RadixDialog from "@radix-ui/react-dialog";
import { cva, cx, type VariantProps } from "class-variance-authority";
import { X } from "lucide-react";

export * from "@radix-ui/react-dialog";

const dialogContent = cva(
  "m-1 min-w-32 rounded-sm border bg-background p-3 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:slide-out-to-bottom-[21%] data-[state=closed]:zoom-out-95 data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:slide-in-from-bottom-[21%] data-[state=open]:zoom-in-95",
  {
    variants: {
      width: {
        sm: "w-[calc(100%-1rem)] max-w-sm",
        md: "w-[calc(100%-1rem)] max-w-md",
        lg: "w-[calc(100%-1rem)] max-w-lg",
      },
    },
    defaultVariants: {
      width: "md",
    },
  },
);

interface DialogContentProps
  extends RadixDialog.DialogContentProps,
    VariantProps<typeof dialogContent> {
  title: string;
}

export function DialogContent(props: DialogContentProps) {
  const { title, children, className, ...rest } = props;
  return (
    <RadixDialog.Portal>
      <RadixDialog.Overlay className="fixed inset-0 grid place-items-center overflow-y-auto bg-background/70 pb-8 backdrop-blur-xs data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:animate-in data-[state=open]:fade-in-0">
        <RadixDialog.Content {...rest} className={cx(dialogContent(), className)}>
          <div className="mb-5 flex">
            <RadixDialog.Title className="flex-1 text-lg font-medium">
              {title}
            </RadixDialog.Title>
            <RadixDialog.Close
              className="-mt-1 -mr-1 self-start rounded-full p-1 opacity-60 hover:bg-muted hover:opacity-100 focus-visible:bg-muted focus-visible:opacity-100 focus-visible:ring-1 focus-visible:outline-hidden"
              aria-label="Close"
            >
              <X size={20} />
            </RadixDialog.Close>
          </div>
          {children}
        </RadixDialog.Content>
      </RadixDialog.Overlay>
    </RadixDialog.Portal>
  );
}

export { DialogContent as Content };
