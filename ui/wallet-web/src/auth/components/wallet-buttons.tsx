import { LOCAL_KEY_LIST_KEY } from "@/auth/local-keys";
import { AlertError } from "@/lib/components/alert";
import { But<PERSON> } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { Label } from "@/lib/components/form";
import { getErrorMessage } from "@/lib/error";
import {
  MnemonicStrength,
  SeedAccount,
  deriveMultiChainWallets,
  generateMnemonic,
  importWalletFromMnemonic,
  isValidMnemonic,
} from "@/lib/mnemonic";
import { MnemonicDisplay } from "@/wallet/mnemonic-display";
import { MnemonicValidation } from "@/wallet/mnemonic-validation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Import, Plus } from "lucide-react";
import { useState } from "react";

interface WalletButtonsProps {
  onSelectAccount: (accountId: string) => void;
}

interface WalletParams {
  readonly mnemonic: string;
  readonly password: Crypto<PERSON><PERSON>;
}

export function WalletButtons({ onSelectAccount }: WalletButtonsProps) {
  const [createOpen, setCreateOpen] = useState(false);
  const [importMnemonicOpen, setImportMnemonicOpen] = useState(false);
  const [generatedMnemonic, setGeneratedMnemonic] = useState<string>();
  const [showMnemonicValidation, setShowMnemonicValidation] = useState(false);
  const [mnemonicStrength, setMnemonicStrength] = useState<MnemonicStrength>(256);

  const queryClient = useQueryClient();

  const importMnemonicMutation = useMutation({
    mutationFn: async ({ mnemonic, password }: WalletParams) => {
      // This now returns keyId, solanaKeyId, and ethereumKeyId
      const wallet = await importWalletFromMnemonic(mnemonic, password);
      return wallet;
    },
    onSuccess(wallet: SeedAccount) {
      // Set the selected account to the Solana derived key by default
      onSelectAccount(`local/${wallet.derivedKeyIds.values().next().value}`);
      // Ensure the modal is closed
      setImportMnemonicOpen(false);
    },
    onSettled() {
      void queryClient.invalidateQueries({ queryKey: LOCAL_KEY_LIST_KEY });
    },
  });

  const createWalletMutation = useMutation({
    mutationFn: async ({ mnemonic, password }: WalletParams) => {
      if (!generatedMnemonic) throw new Error("No mnemonic generated");

      // Always create multi-chain wallet by default
      const { keyId, derivedKeyIds } = await deriveMultiChainWallets(mnemonic, password);
      return { keyId, derivedKeyIds };
    },
    onSuccess() {
      setCreateOpen(false);
      setGeneratedMnemonic(undefined);
    },
    onSettled() {
      void queryClient.invalidateQueries({ queryKey: LOCAL_KEY_LIST_KEY });
    },
  });

  const handleGenerateMnemonic = () => {
    const mnemonic = generateMnemonic(mnemonicStrength);
    setGeneratedMnemonic(mnemonic);
    setShowMnemonicValidation(false);
  };

  return (
    <div className="mt-2 flex flex-col gap-4">
      <div className="grid grid-cols-2 gap-4">
        <Dialog.Root
          open={createOpen}
          onOpenChange={(open) => {
            setCreateOpen(open);
            if (!open) {
              setGeneratedMnemonic(undefined);
              setShowMnemonicValidation(false);
            }
          }}
        >
          <Dialog.Trigger asChild>
            <Button variant="outline" className="flex items-center justify-center gap-2">
              <Plus />
              Create Wallet
            </Button>
          </Dialog.Trigger>
          <Dialog.Content title="Create new wallet" aria-describedby="">
            {generatedMnemonic ? (
              <div id="create-wallet-form" className="grid gap-4">
                {!showMnemonicValidation ? (
                  <>
                    <div className="grid gap-2">
                      <Label>Recovery Phrase</Label>
                      <MnemonicDisplay words={generatedMnemonic.split(" ")} />
                      <div className="flex flex-col gap-2 text-sm text-gray-500">
                        <p className="flex items-center gap-2">
                          <span className="text-amber-500">⚠️</span>
                          Write down these words in order and keep them safe. They can be used
                          to recover your wallet.
                        </p>
                        <p className="flex items-center gap-2">
                          <span className="text-amber-500">⚠️</span>
                          Do not take screenshots or copy these words digitally.
                        </p>
                        <p className="flex items-center gap-2">
                          <span className="text-amber-500">⚠️</span>
                          Anyone with access to these words can access your wallet.
                        </p>
                      </div>
                      {createWalletMutation.isError && (
                        <AlertError>{getErrorMessage(createWalletMutation.error)}</AlertError>
                      )}
                      <Button
                        onClick={() => {
                          setShowMnemonicValidation(true);
                        }}
                        className="mt-2"
                      >
                        Create Wallet
                      </Button>
                    </div>
                  </>
                ) : (
                  <MnemonicValidation
                    expectedWords={generatedMnemonic.split(" ")}
                    onSuccess={(mnemonic, password) => {
                      // Start the async process
                      createWalletMutation.mutate({ mnemonic, password });
                      setShowMnemonicValidation(false);
                      // Return null to indicate no error
                      return null;
                    }}
                    onCancel={() => {
                      setShowMnemonicValidation(false);
                      setGeneratedMnemonic(undefined);
                      setCreateOpen(false);
                    }}
                  />
                )}
              </div>
            ) : (
              <div className="grid gap-4">
                <p>
                  This will generate a new wallet with a recovery phrase. Make sure to save the
                  recovery phrase in a safe place.
                </p>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label>Recovery Phrase Length</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant={mnemonicStrength === 128 ? "primary" : "outline"}
                        onClick={() => {
                          setMnemonicStrength(128);
                        }}
                      >
                        12 Words
                      </Button>
                      <Button
                        variant={mnemonicStrength === 256 ? "primary" : "outline"}
                        onClick={() => {
                          setMnemonicStrength(256);
                        }}
                      >
                        24 Words
                      </Button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500">
                    This will create both Solana and Ethereum wallets from the same recovery
                    phrase.
                  </p>
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      onClick={() => {
                        handleGenerateMnemonic();
                      }}
                    >
                      Generate
                    </Button>
                    <Dialog.Close asChild>
                      <Button variant="secondary">Cancel</Button>
                    </Dialog.Close>
                  </div>
                </div>
              </div>
            )}
          </Dialog.Content>
        </Dialog.Root>

        <Dialog.Root
          open={importMnemonicOpen}
          onOpenChange={(open) => {
            setImportMnemonicOpen(open);
          }}
        >
          <Dialog.Trigger asChild>
            <Button variant="outline" className="flex items-center justify-center gap-2">
              <Import />
              Import Recovery Phrase
            </Button>
          </Dialog.Trigger>
          <Dialog.Content title="Import recovery phrase" aria-describedby="">
            <MnemonicValidation
              expectedWords={[]} // Empty since we don't know the words yet
              onSuccess={(mnemonic, password) => {
                if (!isValidMnemonic(mnemonic)) {
                  // Instead of throwing an error, set the error state in the MnemonicValidation component
                  return "Invalid recovery phrase. Please check your words and try again.";
                }

                // Start the async process
                // Force close the dialog immediately
                setImportMnemonicOpen(false);
                // Then process the mutation
                importMnemonicMutation.mutate({
                  mnemonic,
                  password,
                });

                // Return null to indicate no error
                return null;
              }}
              onCancel={() => {
                setImportMnemonicOpen(false);
              }}
              importMode={true}
            />
          </Dialog.Content>
        </Dialog.Root>
      </div>
    </div>
  );
}
