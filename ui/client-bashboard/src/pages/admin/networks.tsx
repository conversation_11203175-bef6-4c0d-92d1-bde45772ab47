import Spinner from "@/@core/components/spinner";
import { DataGridTable, TableColumns } from "@/components/DataGridTable";
import { SnackSuccess } from "@/components/SnackSuccess";
import { CoinSearch } from "@/components/autocomplete/CoinSearch";
import { CancelButton } from "@/components/button/CancelButton";
import {
  useIndexerNetworkAddMutation,
  useIndexerNetworkUpdateMutation,
  useIndexerNetworksQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AddNetwork, Coin, NetworkWithNativeCoin } from "@/types/schemas";
import { Alert, Button, Card, CardContent, Slide, TextField, Typography } from "@mui/material";
import CardHeader from "@mui/material/CardHeader";
import Grid from "@mui/material/Grid";
import Stack from "@mui/material/Stack";
import { useState } from "react";

type EmptyAddNetwork = Omit<AddNetwork, "nativeCoinId"> & {
  nativeCoin: Coin | null;
};

export const Networks = () => {
  const emptyAddNetwork = { explorerUrl: "", name: "", nativeCoin: null };
  const tableColumns: TableColumns<NetworkWithNativeCoin>[] = [
    { field: "id", header: "ID" },
    { field: "name", header: "Name" },
    {
      field: "isUsableByClients",
      header: "Usable By Clients - can only be enabled",
      editable: true,
      type: "boolean",
    },
    { field: "explorerUrl", header: "Explorer URL", editable: true },
    {
      field: "nativeCoin",
      header: "Native Coin",
      editable: true,
      renderCell: ({ row: { nativeCoin } }) => nativeCoin?.name || "",
    },
  ];

  const [addNetwork] = useIndexerNetworkAddMutation();
  const [updateNetwork] = useIndexerNetworkUpdateMutation();
  const { data: networks } = useIndexerNetworksQuery();

  const [networkToAdd, setNetworkToAdd] = useState<EmptyAddNetwork | null>(null);

  const [errorAlert, setErrorAlert] = useState<string | null>(null);
  const [showSuccessSnack, setShowSuccessSnack] = useState(false);

  const dataAdded = () => {
    setNetworkToAdd(null);
    setShowSuccessSnack(true);
  };

  if (!networks) {
    return <Spinner />;
  }

  return (
    <Grid container spacing={6}>
      <Grid item xs>
        <Card>
          <CardContent>
            <SnackSuccess
              showSuccessSnack={showSuccessSnack}
              setShowSuccessSnack={setShowSuccessSnack}
            />

            <Grid container spacing={6}>
              <Grid item xs={4}>
                <Typography variant={"h4"}> Networks </Typography>
              </Grid>

              <Grid item xs>
                <Stack direction={"row"} justifyContent={"end"} spacing={4}>
                  <Button
                    onClick={() => setNetworkToAdd(networkToAdd ? null : emptyAddNetwork)}
                  >
                    + Network
                  </Button>
                </Stack>
              </Grid>

              <Grid item xs={12}>
                <DataGridTable
                  hideIdColumn
                  rows={networks}
                  sortByField={"name"}
                  tableColumns={tableColumns}
                  processRowUpdate={(newRow: NetworkWithNativeCoin) =>
                    updateNetwork(newRow).unwrap().then()
                  }
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={5} hidden={networkToAdd === null}>
        <Grid container spacing={6}>
          <Slide direction={"left"} in={networkToAdd != null} mountOnEnter unmountOnExit>
            <Grid item xs>
              <Card>
                <CardContent>
                  <CardHeader title={"Add a Network"} />

                  {networkToAdd && (
                    <Stack spacing={6}>
                      {errorAlert && <Alert severity={"error"}>{errorAlert}</Alert>}

                      <TextField
                        label={"Name"}
                        onChange={(event) => {
                          setNetworkToAdd({ ...networkToAdd, name: event.target.value });
                        }}
                        value={networkToAdd.name}
                      />

                      <TextField
                        label={"Explorer URL"}
                        onChange={(event) =>
                          setNetworkToAdd({ ...networkToAdd, explorerUrl: event.target.value })
                        }
                        value={networkToAdd.explorerUrl}
                      />

                      <CoinSearch
                        selectedCoin={networkToAdd.nativeCoin}
                        setSelectedCoin={(coin) =>
                          setNetworkToAdd({ ...networkToAdd, nativeCoin: coin })
                        }
                      />

                      <Stack direction={"row"} spacing={4}>
                        <Button
                          disabled={
                            !networkToAdd.explorerUrl ||
                            !networkToAdd.name ||
                            !networkToAdd.nativeCoin
                          }
                          color={"success"}
                          fullWidth={true}
                          variant={"outlined"}
                          onClick={() => {
                            setErrorAlert(null);
                            if (networkToAdd.nativeCoin) {
                              addNetwork({
                                ...networkToAdd,
                                nativeCoinId: networkToAdd.nativeCoin.id,
                              })
                                .unwrap()
                                .then(dataAdded)
                                .catch((error: unknown) => {
                                  setErrorAlert(getErrorDetail(error));
                                });
                            }
                          }}
                        >
                          Add
                        </Button>
                        <CancelButton onClick={() => setNetworkToAdd(null)} />
                      </Stack>
                    </Stack>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Slide>
        </Grid>
      </Grid>
    </Grid>
  );
};

Networks.acl = {
  action: "manage",
  subject: "Network",
};

export default Networks;
