name: Client Bashboard CI
on:
  push:
    branches: [main, stage]
    paths: ["ui/client-bashboard/**", ".github/workflows/client-bashboard-ci.yml"]
  pull_request:
    paths: ["ui/client-bashboard/**", ".github/workflows/client-bashboard-ci.yml"]
permissions:
  contents: read
  pull-requests: read
jobs:
  checks:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ui/client-bashboard
    outputs:
      envs-to-deploy: ${{ steps.envs-to-deploy.outputs.env_names }}
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 10
      - uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: pnpm
          cache-dependency-path: ui/client-bashboard/pnpm-lock.yaml
      - run: pnpm install --frozen-lockfile --ignore-scripts --prefer-offline
      - run: ./node_modules/.bin/tsc --build
      - run: ./node_modules/.bin/eslint --max-warnings 0 .
      - name: Define environments to deploy
        id: envs-to-deploy
        if: github.event_name == 'push'
        run: |
          if [[ $GITHUB_REF == "refs/heads/main" ]]; then
            env_names='["production", "apollo", "diwan", "lp", "sahaba"]'
          elif [[ $GITHUB_REF == "refs/heads/stage" ]]; then
            env_names='["stage"]'
          else
            echo "Error: unexpected ref $GITHUB_REF"
            exit 1
          fi
          echo "env_names=$env_names" >> $GITHUB_OUTPUT

  deploy:
    needs: checks
    strategy:
      fail-fast: false
      matrix:
        env-name: ${{ fromJson(needs.checks.outputs.envs-to-deploy) }}
    concurrency:
      group: client-bashboard-deploy-${{ matrix.env-name }}
      cancel-in-progress: false
    environment: stabletech-${{ matrix.env-name }}
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    permissions:
      contents: read
      id-token: write
    defaults:
      run:
        working-directory: ui/client-bashboard
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 10
      - uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: pnpm
          cache-dependency-path: ui/client-bashboard/pnpm-lock.yaml
      - run: pnpm install --frozen-lockfile --prefer-offline

      - uses: actions/cache@v4
        with:
          path: ui/client-bashboard/.next/cache
          key: "${{ runner.os }}-nextjs-${{ hashFiles('ui/client-bashboard/pnpm-lock.yaml') }}-${{ matrix.env-name }}-${{ github.sha }}"
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('ui/client-bashboard/pnpm-lock.yaml') }}-${{ matrix.env-name }}-
            ${{ runner.os }}-nextjs-${{ hashFiles('ui/client-bashboard/pnpm-lock.yaml') }}-

      - name: Setup environment variables for building the app
        # .env.local has more precedence than .env
        # see https://nextjs.org/docs/pages/building-your-application/configuring/environment-variables#environment-variable-load-order
        run: cp config/${{ matrix.env-name }}.env .env.local

      - name: Build main app
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ENVIRONMENT: ${{ matrix.env-name }}
          SENTRY_RELEASE: client-bashboard@${{ github.sha }}
        run: pnpm build && pnpm sitemap

      - uses: google-github-actions/auth@v2
        id: auth
        with:
          access_token_lifetime: 1200s
          project_id: temet-359508
          workload_identity_provider: projects/639860733581/locations/global/workloadIdentityPools/github-actions/providers/temet-gh-actions

      - uses: google-github-actions/setup-gcloud@v2
        with:
          version: ">= 416.0.0"

      - name: Upload main app static files
        run: >-
          gcloud storage rsync
          --checksums-only
          --cache-control="public, max-age=31536000, immutable"
          --exclude='.*\.js\.map$'
          -r out/_next/static/
          "gs://${{ vars.WEB_BUCKET }}/_next/static/"

      - name: Upload main app non-static files
        run: >-
          gcloud storage rsync
          --checksums-only
          --cache-control="public, max-age=300"
          --exclude='_next/static/'
          --exclude='.*\.js\.map$'
          -r out/
          "gs://${{ vars.WEB_BUCKET }}/"

      - name: Build landing page
        if: matrix.env-name == 'production'
        run: pnpm landing-page:build

      - name: Upload landing page static files
        if: matrix.env-name == 'production'
        run: >-
          gcloud storage rsync
          --checksums-only
          --cache-control="public, max-age=31536000, immutable"
          --exclude='.*\.js\.map$'
          -r landing-page/dist/assets/
          "gs://${{ vars.WEB_BUCKET }}/assets/"

      - name: Upload landing page non-static files
        if: matrix.env-name == 'production'
        run: >-
          gcloud storage rsync
          --checksums-only
          --cache-control="public, max-age=300"
          --exclude='assets/'
          --exclude='.*\.js\.map$'
          -r landing-page/dist/
          "gs://${{ vars.WEB_BUCKET }}/"
