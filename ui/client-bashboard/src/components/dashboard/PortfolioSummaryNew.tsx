import { Card } from "@/components/ui/card";
import { currencyFormatter } from "@/helpers/formatters";

interface PortfolioSummaryProps {
  portfolioDollars: number;
  interestEarnedDollars: number;
  showInterest: boolean;
}

export function PortfolioSummaryNew({
  portfolioDollars,
  interestEarnedDollars,
  showInterest,
}: PortfolioSummaryProps) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
      <Card>
        <h2 className="mb-2 text-sm font-medium text-gray-600">Portfolio Value</h2>
        <p className="text-3xl font-bold text-primary">
          ${currencyFormatter.format(portfolioDollars)}
        </p>
      </Card>

      {showInterest && (
        <Card>
          <h2 className="mb-2 text-sm font-medium text-gray-600">Unpaid Interest</h2>
          <p className="text-3xl font-bold text-green-600">
            ${currencyFormatter.format(interestEarnedDollars)}
          </p>
        </Card>
      )}
    </div>
  );
}
