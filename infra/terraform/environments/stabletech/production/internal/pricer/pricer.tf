resource "random_password" "redis" {
  length = 32
}

resource "helm_release" "redis" {
  name       = "pricer-redis"
  namespace  = var.namespace
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "redis"
  version    = "18.6.4"
  values     = [file("${path.module}/redis-values.yaml")]
  set_sensitive {
    name  = "auth.password"
    value = random_password.redis.result
  }
}

module "redis_secret" {
  source = "../../../../../modules/immutable_secret"

  prefix    = "pricer-redis"
  namespace = var.namespace
  data = {
    REDIS_HOST     = "${helm_release.redis.name}-master"
    REDIS_PASSWORD = random_password.redis.result
  }
}

module "secret" {
  source = "../../../../../modules/immutable_secret"

  prefix    = "pricer"
  namespace = var.namespace
  data = {
    COINGECKO_API_KEY   = var.secrets.coingecko_api_key
    POSTGRESQL_HOST     = var.secrets.postgresql_host
    POSTGRESQL_USER     = var.secrets.postgresql_user
    POSTGRESQL_PASSWORD = var.secrets.postgresql_password
    POSTGRESQL_DB       = "pricer"
  }
}

module "config_map" {
  source = "../../../../../modules/immutable_config_map"

  prefix    = "pricer"
  namespace = var.namespace
  data = {
    ENV_NAME   = "production"
    SENTRY_DSN = "https://<EMAIL>/4504199259422720"
    LOG_LEVEL  = "INFO"
  }
}

resource "kubernetes_service" "api" {
  metadata {
    name      = "pricer"
    namespace = var.namespace
    annotations = {
      "cloud.google.com/neg" = jsonencode({ "ingress" = true })
    }
  }
  spec {
    selector = {
      app = "pricer"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
  lifecycle {
    ignore_changes = [metadata[0].annotations["cloud.google.com/neg-status"]]
  }
}

resource "kubernetes_job" "init" {
  wait_for_completion = true
  timeouts {
    create = "10m"
    update = "10m"
  }

  metadata {
    name      = "pricer-init"
    namespace = var.namespace
  }

  spec {
    # Avoid retrying failed runs in case manual intervention is needed
    backoff_limit              = 0
    completions                = 1
    ttl_seconds_after_finished = 600

    template {
      metadata {}
      spec {
        container {
          name              = "init"
          image             = var.python_runner_image
          image_pull_policy = "IfNotPresent"
          args              = ["python", "-m", "pricer.init"]

          env_from {
            config_map_ref {
              name = module.config_map.name
            }
          }
          env_from {
            secret_ref {
              name = module.secret.name
            }
          }

          resources {
            requests = {
              cpu    = "100m"
              memory = "256Mi"
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_deployment" "api" {
  depends_on = [kubernetes_job.init]

  metadata {
    name      = "pricer"
    namespace = var.namespace
    labels = {
      app = "pricer"
    }
  }

  spec {
    replicas = var.replicas
    selector {
      match_labels = {
        app = "pricer"
      }
    }
    template {
      metadata {
        labels = {
          app = "pricer"
        }
      }
      spec {
        container {
          name              = "pricer"
          image             = var.python_runner_image
          image_pull_policy = "IfNotPresent"
          args = [
            "uvicorn",
            "pricer.api:app",
            "--forwarded-allow-ips=*",
            "--host=0.0.0.0",
            "--port=80",
          ]

          port {
            container_port = 80
          }

          env_from {
            config_map_ref {
              name = module.config_map.name
            }
          }
          env_from {
            secret_ref {
              name = module.redis_secret.name
            }
          }
          env_from {
            secret_ref {
              name = module.secret.name
            }
          }

          resources {
            requests = {
              cpu    = "100m"
              memory = "128Mi"
            }
          }

          readiness_probe {
            http_get {
              path = "/"
              port = 80
            }
            initial_delay_seconds = 5
            period_seconds        = 5
          }

          liveness_probe {
            http_get {
              path = "/"
              port = 80
            }
            initial_delay_seconds = 30
            period_seconds        = 5
          }
        }
      }
    }
  }
}
