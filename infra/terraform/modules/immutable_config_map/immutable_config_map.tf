# Create a config map that is immutable.
# This is similar to how kustomize configMapGenerator does it. For changes in
# the data, there will be a different suffix on the config map name, thus
# triggering a rolling update for deployments depending on it.

variable "prefix" {
  description = "Prefix for the config map name"
  type        = string
  nullable    = false
}

variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "data" {
  description = "Data to store in the config map"
  type        = map(string)
  nullable    = false
}

resource "kubernetes_config_map" "this" {
  metadata {
    name      = "${var.prefix}-${substr(sha1(jsonencode(var.data)), 0, 9)}"
    namespace = var.namespace
  }
  immutable = true
  data      = var.data
}

output "data" {
  description = "Data stored in the config map"
  value       = var.data
  sensitive   = true
}

output "name" {
  description = "Name of the config map"
  value       = kubernetes_config_map.this.metadata[0].name
}
