// ** React Imports
import { ReactNode, useEffect, useState } from "react";

// ** Next Import
import { useRouter } from "next/router";

interface Props {
  children: ReactNode;
  spinner?: React.JSX.Element | undefined;
}

const WindowWrapper = ({ children, spinner }: Props) => {
  // ** State
  const [windowReadyFlag, setWindowReadyFlag] = useState<boolean>(false);

  const router = useRouter();

  useEffect(() => {
    if (typeof window !== "undefined") {
      setWindowReadyFlag(true);
    }
  }, [router.route]);

  return windowReadyFlag ? <>{children}</> : spinner;
};

export default WindowWrapper;
