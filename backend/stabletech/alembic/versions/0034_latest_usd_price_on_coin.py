"""Latest USD price on Coin

Revision ID: 0034
Revises: 0033
Create Date: 2023-09-12 16:55:57.836265

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0034"
down_revision = "0033"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "coin",
        sa.Column(
            "latest_usd_price",
            sa.Numeric(precision=32, scale=18),
            server_default="0",
            nullable=False,
        ),
    )
    op.add_column(
        "coin", sa.Column("latest_usd_price_updated_at", sa.DateTime(), nullable=True)
    )
    # ### end Alembic commands ###

    # Set latest_usd_price and latest_usd_price_updated_at with latest_price_id
    op.execute(
        """
        UPDATE coin SET (latest_usd_price, latest_usd_price_updated_at) = (
            SELECT dollar_price, last_updated_at FROM price
            WHERE price.id = coin.latest_price_id
        ) WHERE latest_price_id IS NOT NULL
        """
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("coin", "latest_usd_price_updated_at")
    op.drop_column("coin", "latest_usd_price")
    # ### end Alembic commands ###
