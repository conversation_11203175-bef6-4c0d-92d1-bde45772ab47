variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  default     = "temet"
  nullable    = false
}

variable "DOCKER_HOST" {
  description = "<PERSON>ku<PERSON>'s docker host passed in using TF_VAR_DOCKER_HOST env var"
  type        = string
  nullable    = false
}

variable "host_path" {
  description = "Host path to mount into containers, for development only"
  type        = string
  default     = "/work/temet/"
  nullable    = false
}

variable "auth_allowed_issuers" {
  description = "Allowed issuers of tokens for authentication. Mapping of issuer to client_id."
  type        = map(string)
  default = {
    "https://accounts.google.com" = "************-4rebtccromkrgj5k4fd3cla6l9inuons.apps.googleusercontent.com"
  }
  nullable = false
}

variable "secret_box_hexed_key" {
  description = "Secret Box encrypt/decrypt key"
  type        = string
  nullable    = false
  sensitive   = true
  default     = "f8f890c806b9c8f8139789a43035a9a01f9193aa3dc99c06d313a808b90ce225"
}

variable "helius_api_key" {
  description = "Helius API key"
  type        = string
  nullable    = false
  sensitive   = true
  default     = "ecbf7832-326d-453e-8b39-da5923ec3d78"
}
