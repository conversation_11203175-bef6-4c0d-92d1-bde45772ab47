"""Liquidity Pool Pool Position

Revision ID: 0003
Revises: 0002
Create Date: 2022-11-10 23:39:27.747206

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0003"
down_revision = "0002"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "pool_position",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("is_active", sa.<PERSON>(), nullable=True),
        sa.Column("dex_name", sa.String(), nullable=False),
        sa.Column("first_coin_id", sa.Integer(), nullable=False),
        sa.Column(
            "first_coin_amount_added", sa.Numeric(precision=32, scale=18), nullable=False
        ),
        sa.Column(
            "first_coin_amount_removed", sa.Numeric(precision=32, scale=18), nullable=False
        ),
        sa.Column("k_ratio", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("second_coin_id", sa.Integer(), nullable=False),
        sa.Column(
            "second_coin_amount_added", sa.Numeric(precision=32, scale=18), nullable=False
        ),
        sa.Column(
            "second_coin_amount_removed", sa.Numeric(precision=32, scale=18), nullable=False
        ),
        sa.ForeignKeyConstraint(["first_coin_id"], ["coin.id"]),
        sa.ForeignKeyConstraint(["second_coin_id"], ["coin.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_pool_position_first_coin_id"), "pool_position", ["first_coin_id"], unique=False
    )
    op.create_index(op.f("ix_pool_position_id"), "pool_position", ["id"], unique=False)
    op.create_index(
        op.f("ix_pool_position_is_active"), "pool_position", ["is_active"], unique=False
    )
    op.create_index(
        op.f("ix_pool_position_second_coin_id"),
        "pool_position",
        ["second_coin_id"],
        unique=False,
    )
    op.create_index(op.f("ix_position_is_active"), "position", ["is_active"], unique=False)
    op.create_index(op.f("ix_user_is_active"), "user", ["is_active"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_user_is_active"), table_name="user")
    op.drop_index(op.f("ix_position_is_active"), table_name="position")
    op.drop_index(op.f("ix_pool_position_second_coin_id"), table_name="pool_position")
    op.drop_index(op.f("ix_pool_position_is_active"), table_name="pool_position")
    op.drop_index(op.f("ix_pool_position_id"), table_name="pool_position")
    op.drop_index(op.f("ix_pool_position_first_coin_id"), table_name="pool_position")
    op.drop_table("pool_position")
    # ### end Alembic commands ###
