import { RootState } from "@/store";
import { User } from "@/types/schemas";
import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

interface AuthState {
  authToken: string | null;
  logOut: boolean;
  user: User | null;
}

export const authSlice = createSlice({
  name: "auth",
  initialState: { authToken: null, user: null, logOut: false } as AuthState,
  reducers: {
    setAuthToken: (state, { payload }: PayloadAction<string | null>) => {
      state.authToken = payload;
      if (!payload) {
        window.localStorage.removeItem("authToken");
      } else {
        window.localStorage.setItem("authToken", payload);
      }
    },
    setLogOut: (state, { payload }: PayloadAction<boolean>) => {
      state.logOut = payload;
    },
    setUser: (state, { payload }: PayloadAction<User | null>) => {
      state.user = payload;
    },
  },
});

export const { setAuthToken, setLogOut, setUser } = authSlice.actions;

export default authSlice.reducer;

export const selectAuthToken = (state: RootState) => state.auth.authToken;
export const selectLogOut = (state: RootState) => state.auth.logOut;
export const selectUser = (state: RootState) => state.auth.user;
