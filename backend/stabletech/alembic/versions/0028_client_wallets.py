"""Client Wallets

Revision ID: 0028
Revises: 0027
Create Date: 2023-07-28 07:19:58.224441

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0028"
down_revision = "0027"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("clienttransaction", "coin_id", existing_type=sa.INTEGER(), nullable=False)
    op.drop_column("clienttransaction", "transacted_at")
    op.add_column(
        "coin",
        sa.Column("is_usable_by_clients", sa.<PERSON>(), server_default="false", nullable=True),
    )
    op.create_index(
        op.f("ix_coin_is_usable_by_clients"), "coin", ["is_usable_by_clients"], unique=False
    )
    op.add_column(
        "network",
        sa.Column("is_usable_by_clients", sa.<PERSON>(), server_default="false", nullable=True),
    )
    op.create_index(
        op.f("ix_network_is_usable_by_clients"),
        "network",
        ["is_usable_by_clients"],
        unique=False,
    )
    op.add_column("wallet", sa.Column("user_id", sa.Integer(), nullable=True))
    op.drop_index("ix_wallet_name", table_name="wallet")
    op.create_index(op.f("ix_wallet_name"), "wallet", ["name"], unique=False)
    op.create_index(op.f("ix_wallet_user_id"), "wallet", ["user_id"], unique=False)
    op.create_unique_constraint(None, "wallet", ["name", "user_id"])
    op.create_unique_constraint(None, "wallet", ["address", "user_id"])
    op.create_foreign_key(None, "wallet", "user", ["user_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_wallet_user_id"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_name"), table_name="wallet")
    op.create_index("ix_wallet_name", "wallet", ["name"], unique=False)
    op.drop_column("wallet", "user_id")
    op.drop_index(op.f("ix_network_is_usable_by_clients"), table_name="network")
    op.drop_column("network", "is_usable_by_clients")
    op.drop_index(op.f("ix_coin_is_usable_by_clients"), table_name="coin")
    op.drop_column("coin", "is_usable_by_clients")
    op.add_column(
        "clienttransaction",
        sa.Column("transacted_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    )
    op.alter_column("clienttransaction", "coin_id", existing_type=sa.INTEGER(), nullable=True)
    # ### end Alembic commands ###
