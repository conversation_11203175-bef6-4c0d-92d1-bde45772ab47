import { BigNumber } from "ethers";

export interface MockBlock {
  hash: string;
  number: number;
  timestamp: number;
}

export interface MockTransaction {
  hash: string;
  from: string;
  to: string | null;
  value: number;
  blockTimestamp: number;
}

export interface MockTransferLog {
  blockNumber: number;
  blockHash: string;
  transactionHash: string;
  transactionIndex: number;
  logIndex: number;
  address: string;
  topics: string[];
  data: string;
  removed: boolean;
  args: [string, string, BigNumber] & {
    from: string;
    to: string;
    value: BigNumber;
  };
  block: MockBlock;
  transaction: MockTransaction;
}

export interface MockApproveTransaction {
  blockNumber: number;
  blockHash: string;
  hash: string;
  from: string;
  to: string | null;
  value: number;
  args?: [string, BigNumber];
  blockTimestamp: number;
}
