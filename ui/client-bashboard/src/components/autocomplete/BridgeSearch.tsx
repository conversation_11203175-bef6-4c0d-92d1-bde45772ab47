import { useIndexerBridgesQuery } from "@/store/services/api";
import { Bridge } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { Dispatch } from "react";

interface BridgeSearchProps {
  disabled?: boolean;
  hidden?: boolean;
  label?: string;
  selectedBridge: Bridge | null;
  setSelectedBridge: Dispatch<Bridge | null>;
}

export const BridgeSearch = (props: BridgeSearchProps) => {
  const selectedBridge = props.selectedBridge;
  const setSelectedBridge = props.setSelectedBridge;
  const { data: bridgesData } = useIndexerBridgesQuery();

  return (
    <Autocomplete
      fullWidth
      hidden={props.hidden}
      disabled={props.disabled}
      getOptionLabel={(option) => `${option.name}`}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      options={bridgesData || []}
      onChange={(event: unknown, newValue: Bridge | null) => {
        setSelectedBridge(newValue);
      }}
      renderInput={(params) => <TextField {...params} label={props.label || "Bridge"} />}
      value={selectedBridge}
    />
  );
};
