name: Virtuoso # https://virtuoso.qa
on:
  workflow_dispatch:
    inputs:
      goal:
        description: ID of goal to execute
        required: true
        default: "133300"
permissions: {}
jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: execute goal
        shell: bash
        run: |
          # Based on: https://docs.virtuoso.qa/guide/advanced-topics/virtuoso-api.html#example-script
          set -eou pipefail

          ENV="api"
          UI="app"
          MAX_RETRY_TIME=300
          RETRY_DELAY_TIME=10
          TOKEN="${{ secrets.VIRTUOSO_TOKEN }}"
          GOAL_ID="${{ inputs.goal }}"

          if [ -z $TOKEN ] || [ "$TOKEN" == "null" ]; then
            echo "Invalid token."
            exit 1
          fi

          if [ -z $GOAL_ID ] || [ "$GOAL_ID" == "null" ]; then
            echo "Invalid goal id: $GOAL_ID"
            exit 1
          fi

          # Launch execution
          echo "Going to execute goal $GOAL_ID"
          JOB_ID=$(curl -s --header "Authorization: Bearer $TOKEN" -X POST "https://$ENV.virtuoso.qa/api/goals/$GOAL_ID/execute?envelope=false" | jq -r .id)

          if [ -z $JOB_ID ] || [ "$JOB_ID" == "null" ]; then
            echo "Failed to execute job."
            exit 1
          fi
          echo "Launched execution job $JOB_ID"

          # wait for job to complete
          echo "--------"
          set +e
          RUNNING=true
          OUTCOME=""
          while $RUNNING; do
            ERROR=true
            RETRY_TIME=0;
            # As we poll for the status of the job, we need to ensure that a single API failure would not lead to failure of this entire script
            while $ERROR; do
              JOB=$(curl -s --fail --header "Authorization: Bearer $TOKEN" "https://$ENV.virtuoso.qa/api/executions/$JOB_ID/status?envelope=false")
              if [ "$JOB" == "" ]; then
                  if [ $MAX_RETRY_TIME -gt $RETRY_TIME ]; then
                    echo "Request failed. Retrying..."
                    RETRY_TIME=$(($RETRY_TIME + $RETRY_DELAY_TIME))
                    ERROR=true
                    sleep $RETRY_DELAY_TIME
                  else
                    echo "Failed to get job status... Try to re-run the job again."
                    exit 2
                  fi
              else
                ERROR=false
              fi
            done

            JOB_STATUS=$(echo $JOB | jq -r .status)
            OUTCOME=$(echo $JOB | jq -r .outcome)

            echo "Job execution status: $JOB_STATUS, outcome: $OUTCOME"

            if [ "$JOB_STATUS" == "FINISHED" ] || [ "$JOB_STATUS" == "CANCELED" ] || [ "$JOB_STATUS" == "FAILED" ]; then
              RUNNING=false
            else
              sleep 2
            fi
          done

          echo "--------"
          echo "Executed job $JOB_ID with outcome: $OUTCOME"

          set -e

          echo "Execution link: https://$UI.virtuoso.qa/#/project/execution/$JOB_ID"

          # Different exit code for when job did not fail/error but status was not finished (cancelled/failed)
          if [ "$JOB_STATUS" != "FINISHED" ]; then
            exit 3
          fi

          # terminate unsuccessfully if job did not pass
          if [ "$OUTCOME" == "FAIL" ] || [ "$OUTCOME" == "ERROR" ]; then
            exit 2
          fi

          echo "Done!"
