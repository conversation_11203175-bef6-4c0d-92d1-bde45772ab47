// ** React Import
import { ReactNode, useRef, useState } from "react";

// ** MUI Import
import Box, { BoxProps } from "@mui/material/Box";
import List from "@mui/material/List";
import { styled, useTheme } from "@mui/material/styles";

// ** Third Party Components
import PerfectScrollbar from "react-perfect-scrollbar";

// ** Type Import
import { Settings } from "@/@core/context/settingsContext";
import { LayoutProps, VerticalNavItemsType } from "@/@core/layouts/types";

import themeConfig from "@/configs/themeConfig";

// ** Component Imports
import Drawer from "@/@core/layouts/components/vertical/navigation/Drawer";
import VerticalNavHeader from "@/@core/layouts/components/vertical/navigation/VerticalNavHeader";
import VerticalNavItems from "@/@core/layouts/components/vertical/navigation/VerticalNavItems";

// ** Util Import
import { hexToRGBA } from "@/@core/utils/hex-to-rgba";

interface Props {
  hidden?: boolean;
  navWidth: number;
  navHover: boolean;
  settings: Settings;
  children?: ReactNode;
  navVisible: boolean;
  collapsedNavWidth: number;
  navigationBorderWidth: number;
  toggleNavVisibility: () => void;
  setNavHover: (values: boolean) => void;
  setNavVisible: (value: boolean) => void;
  verticalNavItems?: VerticalNavItemsType;
  saveSettings: (values: Settings) => void;
  verticalNavMenuContent?: (props?: LayoutProps) => ReactNode;
  afterVerticalNavMenuContent?: (props?: LayoutProps) => ReactNode;
  beforeVerticalNavMenuContent?: (props?: LayoutProps) => ReactNode;
}

const StyledBoxForShadow = styled(Box)<BoxProps>(({ theme }) => ({
  top: 60,
  left: -8,
  zIndex: 2,
  display: "none",
  position: "absolute",
  pointerEvents: "none",
  width: "calc(100% + 15px)",
  height: theme.mixins.toolbar.minHeight,
  "&.d-block": {
    display: "block",
  },
}));

const Navigation = (props: Props) => {
  // ** Props
  const {
    navHover,
    settings,
    afterVerticalNavMenuContent,
    beforeVerticalNavMenuContent,
    verticalNavMenuContent: userVerticalNavMenuContent,
  } = props;

  // ** States
  const [groupActive, setGroupActive] = useState<string[]>([]);
  const [currentActiveGroup, setCurrentActiveGroup] = useState<string[]>([]);

  // ** Ref
  const shadowRef = useRef(null);

  // ** Hooks
  const theme = useTheme();

  // ** Var
  const { skin, navCollapsed } = settings;
  const { afterVerticalNavMenuContentPosition, beforeVerticalNavMenuContentPosition } =
    themeConfig;

  const shadowBgColor = () => {
    if (skin === "semi-dark" && theme.palette.mode === "light") {
      return `linear-gradient(${theme.palette.customColors.darkBg} 5%,${hexToRGBA(
        theme.palette.customColors.darkBg,
        0.85,
      )} 30%,${hexToRGBA(theme.palette.customColors.darkBg, 0.5)} 65%,${hexToRGBA(
        theme.palette.customColors.darkBg,
        0.3,
      )} 75%,transparent)`;
    } else if (skin === "semi-dark" && theme.palette.mode === "dark") {
      return `linear-gradient(${theme.palette.customColors.lightBg} 5%,${hexToRGBA(
        theme.palette.customColors.lightBg,
        0.85,
      )} 30%,${hexToRGBA(theme.palette.customColors.lightBg, 0.5)} 65%,${hexToRGBA(
        theme.palette.customColors.lightBg,
        0.3,
      )} 75%,transparent)`;
    } else {
      return `linear-gradient(${theme.palette.background.default} 5%,${hexToRGBA(
        theme.palette.background.default,
        0.85,
      )} 30%,${hexToRGBA(theme.palette.background.default, 0.5)} 65%,${hexToRGBA(
        theme.palette.background.default,
        0.3,
      )} 75%,transparent)`;
    }
  };

  return (
    <Drawer {...props}>
      <VerticalNavHeader {...props} />
      {beforeVerticalNavMenuContent && beforeVerticalNavMenuContentPosition === "fixed"
        ? beforeVerticalNavMenuContent(props)
        : null}
      {(beforeVerticalNavMenuContentPosition === "static" || !beforeVerticalNavMenuContent) && (
        <StyledBoxForShadow ref={shadowRef} sx={{ background: shadowBgColor() }} />
      )}
      <Box sx={{ position: "relative", overflow: "hidden" }}>
        <PerfectScrollbar
        /*className={(ref: any) => handleInfiniteScroll(ref)}
          {...(hidden
            ? {
                onScroll: (container: any) => scrollMenu(container),
                sx: { height: '100%', overflowY: 'auto', overflowX: 'hidden' }
              }
            : {
                options: { wheelPropagation: false },
                onScrollY: (container: any) => scrollMenu(container)
              })} */
        >
          {beforeVerticalNavMenuContent && beforeVerticalNavMenuContentPosition === "static"
            ? beforeVerticalNavMenuContent(props)
            : null}
          {userVerticalNavMenuContent ? (
            userVerticalNavMenuContent(props)
          ) : (
            <List
              className="nav-items"
              sx={{
                pt: 0,
                transition: "padding .25s ease",
                "& > :first-of-type": { mt: "0" },
                pr: !navCollapsed || (navCollapsed && navHover) ? 4.5 : 1.25,
              }}
            >
              <VerticalNavItems
                groupActive={groupActive}
                setGroupActive={setGroupActive}
                currentActiveGroup={currentActiveGroup}
                setCurrentActiveGroup={setCurrentActiveGroup}
                {...props}
              />
            </List>
          )}
          {afterVerticalNavMenuContent && afterVerticalNavMenuContentPosition === "static"
            ? afterVerticalNavMenuContent(props)
            : null}
        </PerfectScrollbar>
      </Box>
      {afterVerticalNavMenuContent && afterVerticalNavMenuContentPosition === "fixed"
        ? afterVerticalNavMenuContent(props)
        : null}
    </Drawer>
  );
};

export default Navigation;
