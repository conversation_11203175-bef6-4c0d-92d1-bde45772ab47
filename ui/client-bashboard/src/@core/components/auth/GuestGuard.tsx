// ** React Imports
import React, { ReactElement, useEffect } from "react";

// ** Next Imports
import { useRouter } from "next/router";

// ** Hooks Import
import { useAuth } from "@/hooks/useAuth";

interface GuestGuardProps {
  children: React.JSX.Element;
  fallback: ReactElement | null;
}

const GuestGuard = (props: GuestGuardProps) => {
  const { children, fallback } = props;
  const auth = useAuth();
  const router = useRouter();
  const { replace: replaceRoute, pathname, isReady } = router;

  useEffect(() => {
    if (!isReady || auth.loading || auth.user == null) {
      return;
    }

    if (pathname === "/signin" || pathname === "/login") {
      replaceRoute((router.query.returnUrl as string) || "/dashboard");
    }
  }, [auth.loading, auth.user, pathname, replaceRoute, isReady, router.query.returnUrl]);

  if (auth.loading || auth.user != null) {
    return fallback;
  }

  return children;
};

export default GuestGuard;
