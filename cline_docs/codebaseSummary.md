# Codebase Summary

## Comprehensive Documentation

For detailed documentation of core systems, refer to:

- [blockchainIntegration.md](./blockchainIntegration.md) - Multi-chain transaction tracking and
  blockchain service integrations
- [investmentPrograms.md](./investmentPrograms.md) - Earn Program and LP Fund implementation
  details
- [pricingSystem.md](./pricingSystem.md) - Price tracking, caching, and refresh mechanisms
- [transactionSystem.md](./transactionSystem.md) - Transaction classification and accounting
  system

## Key Components and Their Interactions

### Backend Services

#### Temet Service

- Core Components

  - Authentication
  - Base models and schemas
  - Initialization and configuration
  - Utility functions

- Heist Module

  - API endpoints
  - CRUD operations
  - Data schemas
  - Type definitions

- Admin Module

  - Administrative API endpoints
  - Management operations

- Public Module

  - Public-facing API endpoints
  - Data access interfaces

- Cubist Integration

  - External service integration
  - Data processing

- NFT Management

  - NFT-related operations
  - Token handling

- Wallet Integration
  - Wallet management
  - Transaction handling

#### StableTech Service

- Accounting Module

  - Financial operations
  - Transaction tracking (see [transactionSystem.md](./transactionSystem.md))
  - BitcoinTax integration

- Client Management

  - Client data handling
  - Liabilities reminder system
  - API endpoints

- Market Module

  - Price tracking (see [pricingSystem.md](./pricingSystem.md))
  - Cost basis calculations
  - Loan health monitoring
  - Price alerts

- Indexer Module
  - Multi-chain indexing (see [blockchainIntegration.md](./blockchainIntegration.md))
    - Block fills processing
    - Covalent integration
    - Helius integration
    - Mintscan integration
  - Cointracking integration
  - Job processing system

### Frontend Applications

#### Client Dashboard

- Next.js application
- TypeScript implementation
- Environment configuration
- Public assets

#### Web Application

- Next.js framework
- TypeScript codebase
- Configuration management
- Asset handling

## Data Flow

### API Layer

- REST endpoints
- Data validation
- Error handling
- Response formatting

### Database Layer

- PostgreSQL implementation
- Alembic migrations
- Model definitions
- CRUD operations

## External Dependencies

### Blockchain Services

- Helius integration
- Covalent integration
- Mintscan integration
- See [blockchainIntegration.md](./blockchainIntegration.md) for details

### Price Services

- CoinGecko integration
- Price refresher system
- Cache management
- See [pricingSystem.md](./pricingSystem.md) for details

## Directory Structure

### Backend

```
/backend
├── common/
│   ├── conf.py
│   ├── exceptions.py
│   ├── models.py
│   ├── psql.py
│   └── pricer/
├── pricer/
│   ├── api.py
│   ├── auth.py
│   ├── cache.py
│   ├── coingecko_client.py
│   └── price_refresher.py
├── scripts/
│   ├── diff_db_schema.py
│   ├── dump_objects.py
│   ├── generate_pool_transactions.py
│   └── load_objects.py
├── stabletech/
│   ├── accounting/
│   ├── auth/
│   ├── client/
│   ├── email/
│   ├── indexer/
│   ├── market/
│   └── utils/
└── lootloot/
    ├── admin/
    ├── auth/
    ├── cubist/
    ├── heist/
    ├── nfts/
    ├── public/
    └── wallets/
```

### Frontend

```
/ui
├── client-dashboard/
│   ├── config/
│   ├── public/
│   ├── scripts/
│   ├── src/
│   └── styles/
└── lootloot/
    ├── app/
    ├── config/
    ├── lib/
    ├── scripts/
    └── types/
```

### Infrastructure

```
/infra
├── docker/
│   ├── python-runner.dockerfile
│   └── python-runner-dev.entrypoint.sh
└── terraform/
    ├── environments/
    └── modules/
```

## Recent Changes

- Added comprehensive system documentation
  - Blockchain integration architecture
  - Investment programs implementation
  - Pricing system architecture
  - Transaction and accounting system
- Initial documentation setup
- Project structure documentation
- Heist module architecture documentation
  - Added claim processing flow
  - Documented statistics tracking
  - Detailed raffle system implementation

## Development Tools

- Pre-commit hooks
- ESLint configuration
- Prettier setup
- Git workflow

## Testing Strategy

- Python unittest framework
- Integration testing
- API endpoint testing
- Frontend component testing
