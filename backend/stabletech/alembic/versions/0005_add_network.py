"""Add Network

Revision ID: 0005
Revises: 0004
Create Date: 2022-12-14 06:32:38.402420

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy import Column, Foreign<PERSON>ey, Integer, String, Table
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.orm.session import Session
from sqlalchemy.schema import UniqueConstraint
from sqlalchemy.sql import table


# Static copy of model objects limited to fields needed for data migration below
class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)

network_wallet_association_table = Table(
    "network_wallet_association",
    Base.metadata,
    Column("network_id", ForeignKey("network.id"), primary_key=True),
    Column("wallet_id", ForeignKey("wallet.id"), primary_key=True),
    UniqueConstraint("network_id", "wallet_id"),
)


class Network(Base):
    id = Column(Integer, primary_key=True)
    name: str = Column(String, nullable=False, index=True)
    wallets = relationship(
        "Wallet", secondary=network_wallet_association_table, back_populates="networks"
    )
    __tablename__ = "network"


class Wallet(Base):
    id = Column(Integer, primary_key=True)
    old_network: str = Column(String, nullable=False)
    networks = relationship(
        Network, secondary=network_wallet_association_table, back_populates="wallets"
    )
    __tablename__ = "wallet"


class Pool(Base):
    id = Column(Integer, primary_key=True)
    old_network: str = Column(String, nullable=False)
    network_id: int = Column(Integer, ForeignKey(Network.id), nullable=False, index=True)
    network = relationship(Network, foreign_keys=[network_id])
    __tablename__ = "pool"


# revision identifiers, used by Alembic.
revision = "0005"
down_revision = "0004"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "network",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_network_name"), "network", ["name"], unique=False)
    op.create_table(
        "network_wallet_association",
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.Column("wallet_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"]),
        sa.ForeignKeyConstraint(["wallet_id"], ["wallet.id"]),
        sa.PrimaryKeyConstraint("network_id", "wallet_id"),
        sa.UniqueConstraint("network_id", "wallet_id"),
    )
    op.alter_column("pool", "network", new_column_name="old_network")
    op.add_column("pool", sa.Column("network_id", sa.Integer()))
    op.create_index(op.f("ix_pool_network_id"), "pool", ["network_id"], unique=False)
    op.create_foreign_key(None, "pool", "network", ["network_id"], ["id"])
    op.alter_column("wallet", "network", new_column_name="old_network")
    op.drop_constraint("wallet_address_network_key", "wallet", type_="unique")
    op.create_unique_constraint(None, "network_wallet_association", ["network_id", "wallet_id"])
    op.alter_column("pool", "old_network", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column("wallet", "old_network", existing_type=sa.VARCHAR(), nullable=True)

    # Add Networks
    network_table = table("network", sa.Column("name", sa.String()))
    op.bulk_insert(
        network_table,
        [
            {"name": "Avalanche"},
            {"name": "BSC"},
            {"name": "Bitcoin"},
            {"name": "Cosmos"},
            {"name": "Elrond"},
            {"name": "EOS"},
            {"name": "Ethereum"},
            {"name": "Fantom"},
            {"name": "Flow"},
            {"name": "Harmony"},
            {"name": "Matic"},
            {"name": "Moonbeam"},
            {"name": "Solana"},
            {"name": "Tezos"},
        ],
    )

    # Populate Pool's and Wallet's Network
    session = Session(bind=op.get_bind())
    pools = session.query(Pool).all()
    for pool in pools:
        pool.network = session.query(Network).filter_by(name=pool.old_network).one()
    wallets = session.query(Wallet).all()
    for wallet in wallets:
        wallet.networks.append(session.query(Network).filter_by(name=wallet.old_network).one())
    session.commit()

    op.alter_column("pool", "network_id", nullable=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("pool", "old_network", new_column_name="network", nullable=False)
    op.alter_column("wallet", "old_network", new_column_name="network", nullable=False)
    op.create_unique_constraint("wallet_address_network_key", "wallet", ["address", "network"])
    op.drop_index(op.f("ix_pool_network_id"), table_name="pool")
    op.drop_column("pool", "network_id")
    op.drop_table("network_wallet_association")
    op.drop_index(op.f("ix_network_name"), table_name="network")
    op.drop_table("network")
    # ### end Alembic commands ###
