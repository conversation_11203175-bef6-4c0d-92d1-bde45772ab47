#!/usr/bin/env bash
#
# ARG_OPTIONAL_SINGLE([build],[],[env name to build before the deploy])
# ARG_OPTIONAL_SINGLE([invalidate],[],[url map to invalidate the CDN cache (optional)])
# ARG_POSITIONAL_SINGLE([bucket],[bucket to deploy],[])
# ARG_HELP([Deploy script.])
# ARGBASH_GO()
# needed because of Argbash --> m4_ignore([
### START OF CODE GENERATED BY Argbash v2.9.0 one line above ###
# Argbash is a bash code generator used to get arguments parsing right.
# Argbash is FREE SOFTWARE, see https://argbash.dev for more info
# Generated online by https://argbash.dev/generate


die()
{
	local _ret="${2:-1}"
	test "${_PRINT_HELP:-no}" = yes && print_help >&2
	echo "$1" >&2
	exit "${_ret}"
}


begins_with_short_option()
{
	local first_option all_short_options='h'
	first_option="${1:0:1}"
	test "$all_short_options" = "${all_short_options/$first_option/}" && return 1 || return 0
}

# THE DEFAULTS INITIALIZATION - POSITIONALS
_positionals=()
# THE DEFAULTS INITIALIZATION - OPTIONALS
_arg_build=
_arg_invalidate=


print_help()
{
	printf '%s\n' "Deploy script."
	printf 'Usage: %s [--build <arg>] [--invalidate <arg>] [-h|--help] <bucket>\n' "$0"
	printf '\t%s\n' "<bucket>: bucket to deploy"
	printf '\t%s\n' "--build: env name to build before the deploy (no default)"
	printf '\t%s\n' "--invalidate: url map to invalidate the CDN cache (optional) (no default)"
	printf '\t%s\n' "-h, --help: Prints help"
}


parse_commandline()
{
	_positionals_count=0
	while test $# -gt 0
	do
		_key="$1"
		case "$_key" in
			--build)
				test $# -lt 2 && die "Missing value for the optional argument '$_key'." 1
				_arg_build="$2"
				shift
				;;
			--build=*)
				_arg_build="${_key##--build=}"
				;;
			--invalidate)
				test $# -lt 2 && die "Missing value for the optional argument '$_key'." 1
				_arg_invalidate="$2"
				shift
				;;
			--invalidate=*)
				_arg_invalidate="${_key##--invalidate=}"
				;;
			-h|--help)
				print_help
				exit 0
				;;
			-h*)
				print_help
				exit 0
				;;
			*)
				_last_positional="$1"
				_positionals+=("$_last_positional")
				_positionals_count=$((_positionals_count + 1))
				;;
		esac
		shift
	done
}


handle_passed_args_count()
{
	local _required_args_string="'bucket'"
	test "${_positionals_count}" -ge 1 || _PRINT_HELP=yes die "FATAL ERROR: Not enough positional arguments - we require exactly 1 (namely: $_required_args_string), but got only ${_positionals_count}." 1
	test "${_positionals_count}" -le 1 || _PRINT_HELP=yes die "FATAL ERROR: There were spurious positional arguments --- we expect exactly 1 (namely: $_required_args_string), but got ${_positionals_count} (the last one was: '${_last_positional}')." 1
}


assign_positional_args()
{
	local _positional_name _shift_for=$1
	_positional_names="_arg_bucket "

	shift "$_shift_for"
	for _positional_name in ${_positional_names}
	do
		test $# -gt 0 || break
		eval "$_positional_name=\${1}" || die "Error during argument parsing, possibly an Argbash bug." 1
		shift
	done
}

parse_commandline "$@"
handle_passed_args_count
assign_positional_args 1 "${_positionals[@]}"

# OTHER STUFF GENERATED BY Argbash

### END OF CODE GENERATED BY Argbash (sortof) ### ])
# [ <-- needed because of Argbash


set -euo pipefail

if [ ! -z "$_arg_build" ]; then
  set -x
  pnpm install --frozen-lockfile
	pnpm tsc --build
	pnpm lint
	cp config/$_arg_build.env .env.production.local
	set +e
  pnpm build && pnpm sitemap
	build_exit_code=$?
	set -e
	rm .env.production.local
  set +x
	if [ $build_exit_code -ne 0 ] ; then
		exit $build_exit_code
	fi
fi

set -x
gcloud storage rsync --quiet \
  --cache-control="public, max-age=31536000, immutable" \
	--exclude='.*\.js\.map$' \
	-r out/_next/static/ "gs://$_arg_bucket/_next/static/"
gcloud storage rsync --quiet \
  --cache-control="public, max-age=300" \
	--exclude='_next/static/' \
	--exclude='.*\.js\.map$' \
	-r out/ "gs://$_arg_bucket/"
set +x

if [ ! -z "$_arg_invalidate" ]; then
  set -x
  gcloud compute url-maps invalidate-cdn-cache "$_arg_invalidate" --async --path '/*'
  set +x
fi

# ] <-- needed because of Argbash
