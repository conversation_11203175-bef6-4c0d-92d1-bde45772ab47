import { PasswordContext, useClearPassword } from "@/auth/password";
import { Button } from "@/lib/components/button";
import { cx } from "class-variance-authority";
import { Lock, LockOpen } from "lucide-react";
import { use } from "react";

export function PasswordMonitor({ className }: { className?: string | undefined }) {
  const passwordCtx = use(PasswordContext);
  const clearPassword = useClearPassword();

  if (!passwordCtx.registered) {
    return null;
  }

  let content;
  if (passwordCtx.password != null && passwordCtx.until) {
    // unlocked
    const date = new Date(passwordCtx.until);
    content = (
      <>
        <LockOpen className="flex-none" size={16} />
        <span className="flex-auto">
          Unlocked until {date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
        </span>
        <Button
          className="flex-none"
          variant="outline"
          size="icon"
          onClick={clearPassword}
          title="Lock wallet now"
        >
          <span className="sr-only">Lock wallet now</span>
          <Lock size={18} />
        </Button>
      </>
    );
  } else {
    // locked
    content = (
      <>
        <Lock size={16} />
        <span>Wallet locked</span>
      </>
    );
  }

  return <div className={cx("flex h-9 items-center gap-1 text-xs", className)}>{content}</div>;
}
