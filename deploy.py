#!/usr/bin/env python3

"""Deploy the current branch to the specified environment.

The deploys are only going to be made if relevant files have been modified. E.g.
if files inside ui/lootloot/ have been modified, the deployment of the lootloot will
be made, otherwise, it won't trigger a deploy of lootloot.

This will hard reset the environment branch to origin/main and then squash-merge
the current branch into the env branch. This is to ensure that the changes are the
most similar to the changes that are going to be deployed to the production
environment when merging a pull request into main.

Currently only support `stage` environment, that represents any stage environment
(i.e. temet-stage and stabletech-stage). Production environments are tracking the
main branch, and pushing to the main branch should be done by pull requests on
GitHub.

Example usage:
    ./deploy.py --env stage
"""

import argparse
import subprocess
import sys

arg_parser = argparse.ArgumentParser()
arg_parser.add_argument("--env", choices=["stage"], required=True)
args = arg_parser.parse_args()

branch: str = args.env

result = subprocess.run(["git", "status", "--porcelain"], stdout=subprocess.PIPE)
if result.stdout:
    print("There are uncommitted changes. Please commit or stash them.")
    sys.exit(1)

# Get current branch
result = subprocess.run(
    ["git", "branch", "--show-current"], check=True, stdout=subprocess.PIPE, text=True
)
current_branch = result.stdout.strip()

print(f"Going to deploy {current_branch} to {args.env} environments.")

subprocess.run(["git", "fetch", "origin", "main", "stage"], check=True)
subprocess.run(["git", "checkout", "-B", branch, "--track", f"origin/{branch}"], check=True)
subprocess.run(["git", "reset", "--hard", "origin/main"], check=True)
subprocess.run(["git", "merge", "--squash", current_branch], check=True)
subprocess.run(
    ["git", "commit", "-nm", f"Squash-merge {current_branch} into {branch}"], check=True
)
subprocess.run(["git", "push", "-f", "origin", branch], check=True)

print("---")
print(f"Updated {branch} with the changes from {current_branch}.")
print("Pages to check the deployment status:")
print(f"- https://github.com/Stable-Tech/temet/actions?query=branch%3A{branch}")
print("- https://app.terraform.io/app/stable-tech/workspaces")
print("---")

subprocess.run(["git", "checkout", current_branch], check=True)
