"""Indexer

Revision ID: 0014
Revises: 0013
Create Date: 2023-01-30 12:31:39.450978

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0014"
down_revision = "0013"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "bridge",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "nft",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("attributes", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("image_url", sa.String(), nullable=True),
        sa.Column("uid", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "account",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("address", sa.String(), nullable=False),
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.Column("wallet_id", sa.Integer(), nullable=True),
        sa.Column("api_name", sa.String(), nullable=True),
        sa.Column("ticker_symbol", sa.String(), nullable=True),
        sa.Column("bridge_id", sa.Integer(), nullable=True),
        sa.Column("coin_id", sa.Integer(), nullable=True),
        sa.Column("exchange_id", sa.Integer(), nullable=True),
        sa.Column("farm_id", sa.Integer(), nullable=True),
        sa.Column("nft_id", sa.Integer(), nullable=True),
        sa.Column("pool_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(["bridge_id"], ["bridge.id"]),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"]),
        sa.ForeignKeyConstraint(["exchange_id"], ["exchange.id"]),
        sa.ForeignKeyConstraint(["farm_id"], ["farm.id"]),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"]),
        sa.ForeignKeyConstraint(["nft_id"], ["nft.id"]),
        sa.ForeignKeyConstraint(["pool_id"], ["pool.id"]),
        sa.ForeignKeyConstraint(["wallet_id"], ["wallet.id"]),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("address", "network_id"),
    )
    op.create_index(op.f("ix_account_address"), "account", ["address"], unique=False)
    op.create_index(op.f("ix_account_api_name"), "account", ["api_name"], unique=False)
    op.create_index(op.f("ix_account_coin_id"), "account", ["coin_id"], unique=False)
    op.create_index(op.f("ix_account_network_id"), "account", ["network_id"], unique=False)
    op.create_index(op.f("ix_account_nft_id"), "account", ["nft_id"], unique=False)
    op.create_index(op.f("ix_account_pool_id"), "account", ["pool_id"], unique=False)
    op.create_index(
        op.f("ix_account_ticker_symbol"), "account", ["ticker_symbol"], unique=False
    )
    op.create_index(op.f("ix_account_wallet_id"), "account", ["wallet_id"], unique=False)
    op.create_table(
        "transaction",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("action", sa.String(), nullable=True),
        sa.Column("confirmed_at", sa.DateTime(), nullable=False),
        sa.Column("json_data", sa.String(), nullable=False),
        sa.Column("fees_paid", sa.Numeric(precision=32, scale=18), nullable=True),
        sa.Column("approval_asset_account_id", sa.Integer(), nullable=True),
        sa.Column(
            "native_coin_dollar_price", sa.Numeric(precision=32, scale=18), nullable=True
        ),
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.Column("signer_account_id", sa.Integer(), nullable=False),
        sa.Column("tx_hash", sa.String(), nullable=True),
        sa.Column("wallet_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["approval_asset_account_id"], ["account.id"]),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"]),
        sa.ForeignKeyConstraint(["signer_account_id"], ["account.id"]),
        sa.ForeignKeyConstraint(["wallet_id"], ["wallet.id"]),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("tx_hash"),
    )
    op.create_index(
        op.f("ix_transaction_approval_asset_account_id"),
        "transaction",
        ["approval_asset_account_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_transaction_network_id"), "transaction", ["network_id"], unique=False
    )
    op.create_index(
        op.f("ix_transaction_signer_account_id"),
        "transaction",
        ["signer_account_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_transaction_wallet_id"), "transaction", ["wallet_id"], unique=False
    )
    op.create_table(
        "transfer",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("asset_account_id", sa.Integer(), nullable=False),
        sa.Column("amount", sa.Numeric(precision=48, scale=18), nullable=False),
        sa.Column("nft_id", sa.Numeric(precision=32, scale=0), nullable=True),
        sa.Column("from_account_id", sa.Integer(), nullable=False),
        sa.Column("to_account_id", sa.Integer(), nullable=False),
        sa.Column("transaction_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["asset_account_id"], ["account.id"]),
        sa.ForeignKeyConstraint(["from_account_id"], ["account.id"]),
        sa.ForeignKeyConstraint(["to_account_id"], ["account.id"]),
        sa.ForeignKeyConstraint(["transaction_id"], ["transaction.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_transfer_asset_account_id"), "transfer", ["asset_account_id"], unique=False
    )
    op.create_index(
        op.f("ix_transfer_from_account_id"), "transfer", ["from_account_id"], unique=False
    )
    op.create_index(
        op.f("ix_transfer_to_account_id"), "transfer", ["to_account_id"], unique=False
    )
    op.create_index(
        op.f("ix_transfer_transaction_id"), "transfer", ["transaction_id"], unique=False
    )
    op.add_column("network", sa.Column("covalent_chain_id", sa.Integer(), nullable=True))
    op.add_column("network", sa.Column("native_coin_id", sa.Integer(), nullable=True))
    op.drop_index("ix_network_name", table_name="network")
    op.create_index(op.f("ix_network_name"), "network", ["name"], unique=True)
    op.create_index(
        op.f("ix_network_native_coin_id"), "network", ["native_coin_id"], unique=False
    )
    op.create_foreign_key(None, "network", "coin", ["native_coin_id"], ["id"])
    op.drop_column("network", "bitquery_is_enabled")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "network",
        sa.Column(
            "bitquery_is_enabled",
            sa.BOOLEAN(),
            server_default=sa.text("false"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.drop_index(op.f("ix_network_native_coin_id"), table_name="network")
    op.drop_index(op.f("ix_network_name"), table_name="network")
    op.create_index("ix_network_name", "network", ["name"], unique=False)
    op.drop_column("network", "native_coin_id")
    op.drop_column("network", "covalent_chain_id")
    op.drop_index(op.f("ix_transfer_transaction_id"), table_name="transfer")
    op.drop_index(op.f("ix_transfer_to_account_id"), table_name="transfer")
    op.drop_index(op.f("ix_transfer_from_account_id"), table_name="transfer")
    op.drop_index(op.f("ix_transfer_asset_account_id"), table_name="transfer")
    op.drop_table("transfer")
    op.drop_index(op.f("ix_transaction_wallet_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_signer_account_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_network_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_approval_asset_account_id"), table_name="transaction")
    op.drop_table("transaction")
    op.drop_index(op.f("ix_account_wallet_id"), table_name="account")
    op.drop_index(op.f("ix_account_ticker_symbol"), table_name="account")
    op.drop_index(op.f("ix_account_pool_id"), table_name="account")
    op.drop_index(op.f("ix_account_nft_id"), table_name="account")
    op.drop_index(op.f("ix_account_network_id"), table_name="account")
    op.drop_index(op.f("ix_account_coin_id"), table_name="account")
    op.drop_index(op.f("ix_account_api_name"), table_name="account")
    op.drop_index(op.f("ix_account_address"), table_name="account")
    op.drop_table("account")
    op.drop_table("nft")
    op.drop_table("bridge")
    # ### end Alembic commands ###
