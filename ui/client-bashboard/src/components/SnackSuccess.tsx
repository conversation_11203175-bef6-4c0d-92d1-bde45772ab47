import { Alert, Snackbar } from "@mui/material";
import { Dispatch } from "react";

interface SnackSuccessProps {
  label?: string;
  showSuccessSnack: boolean;
  setShowSuccessSnack: Dispatch<boolean>;
}

export const SnackSuccess = (props: SnackSuccessProps) => {
  return (
    <Snackbar
      open={props.showSuccessSnack}
      autoHideDuration={3000}
      transitionDuration={1000}
      anchorOrigin={{ horizontal: "center", vertical: "top" }}
      onClose={() => {
        props.setShowSuccessSnack(false);
      }}
    >
      <Alert severity={"success"} sx={{ width: "100%" }}>
        {props.label || "Added Successfully"}
      </Alert>
    </Snackbar>
  );
};
