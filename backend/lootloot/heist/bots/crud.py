from typing import NamedTuple, Sequence

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, delete, func, insert, select, update

import lootloot.schemas
from lootloot.auth.models import User
from lootloot.base import BaseManager
from lootloot.heist.bots import schemas
from lootloot.heist.bots.models import (
    FungibleInventory,
    FungibleTransfer,
    NonFungibleInventory,
    NonFungibleTransfer,
    Raffle,
    RaffleEntry,
)
from lootloot.models import UserWallet
from lootloot.wallets.crud import UserWalletManager


class InventoryException(Exception):
    pass


class FungibleInventoryManager(BaseManager):
    def add_entry(self, entry: schemas.FungibleEntry, wallet_address: str):
        """Add/subtract the fungible amount to the inventory and log the transfer."""
        amount = entry.amount
        asset_type = entry.fungibleAssetType
        if amount == 0:
            return

        if entry.is_deposit:
            self.db.execute(
                insert(FungibleTransfer),
                {
                    "amount": amount,
                    "asset_metadata": entry.fungibleAsset.model_dump(mode="json"),
                    "asset_type": asset_type,
                    "wallet_address": wallet_address,
                },
            )
        else:
            if amount > 0:
                raise InventoryException("Amount must be negative when withdrawing")
            # Mark the transfer as completed
            wallet = UserWalletManager(self.db).get_by_address("solana", wallet_address)
            if wallet is None:
                raise InventoryException("Wallet has no User.")
            result = self.db.execute(
                update(FungibleTransfer)
                .where(
                    FungibleTransfer.asset_type == asset_type,
                    FungibleTransfer.withdrawal_user_id == wallet.user_id,
                    FungibleTransfer.wallet_address == None,
                )
                .values(wallet_address=wallet_address)
            )
            if result.rowcount == 0:
                raise InventoryException("No row updated when withdrawing fungible")

        asset_inventory = self.db.scalar(
            select(FungibleInventory).where(FungibleInventory.asset_type == asset_type)
        )
        if not entry.is_deposit and (
            not asset_inventory or asset_inventory and abs(amount) > asset_inventory.amount
        ):
            raise InventoryException("Trying to withdraw more than is available")
        if asset_inventory:
            asset_inventory.amount += amount
        else:
            # Create asset's inventory if it doesn't exist yet
            self.db.execute(
                insert(FungibleInventory), {"amount": amount, "asset_type": asset_type}
            )

    def get_pending_transfers_for_user(self, user_id: int) -> Sequence[FungibleTransfer]:
        return self.db.scalars(
            select(FungibleTransfer).where(
                FungibleTransfer.wallet_address == None,
                FungibleTransfer.withdrawal_user_id == user_id,
            )
        ).all()


class NonFungibleInventoryManager(BaseManager):
    def add_entry(self, entry: schemas.NonFungibleEntry, wallet_address: str, is_deposit: bool):
        """Add/remove a non-fungible from the Bot's inventory and log the transfer."""
        asset = entry.asset
        asset_type = asset.asset_type
        game_id = asset.id
        if is_deposit:
            self.db.execute(
                insert(NonFungibleTransfer),
                {
                    "asset_name": entry.asset_name,
                    "asset_type": asset_type,
                    "asset_metadata": asset.metadata.model_dump(mode="json"),
                    "game_id": game_id,
                    "is_deposit": is_deposit,
                    "rarity": asset.rarity,
                    "wallet_address": wallet_address,
                },
            )
            self.db.execute(
                insert(NonFungibleInventory),
                {"asset_type": asset_type, "game_id": game_id},
            )
        else:
            wallet = UserWalletManager(self.db).get_by_address("solana", wallet_address)
            if wallet is None:
                raise InventoryException("Wallet has no User")
            # Mark transfer as completed
            result = self.db.execute(
                update(NonFungibleTransfer)
                .where(
                    NonFungibleTransfer.asset_type == asset_type,
                    NonFungibleTransfer.game_id == game_id,
                    NonFungibleTransfer.withdrawal_user_id == wallet.user_id,
                    NonFungibleTransfer.wallet_address == None,
                )
                .values(wallet_address=wallet_address)
            )
            if result.rowcount == 0:
                raise InventoryException("No row updated when withdrawing non-fungible")

    def get_all(self) -> Sequence[NonFungibleInventory]:
        return self.db.scalars(select(NonFungibleInventory)).all()

    def create_withdrawal(
        self, non_fungible: NonFungibleInventory, user_id: int, raffle_id: int | None = None
    ):
        """Deletes the item from the inventory and creates a pending transfer for the User."""
        deposit_transfer = self.db.scalars(
            select(NonFungibleTransfer).where(
                NonFungibleTransfer.asset_type == non_fungible.asset_type,
                NonFungibleTransfer.game_id == non_fungible.game_id,
                NonFungibleTransfer.is_deposit == True,
            )
        ).first()
        if not deposit_transfer:
            raise InventoryException("No deposit transfer found")

        # Delete from inventory and create withdrawal transfer
        result = self.db.execute(
            delete(NonFungibleInventory).where(
                NonFungibleInventory.asset_type == non_fungible.asset_type,
                NonFungibleInventory.game_id == non_fungible.game_id,
            )
        )
        if result.rowcount == 0:
            raise InventoryException("Non-fungible inventory not found")
        self.db.execute(
            insert(NonFungibleTransfer),
            {
                "asset_name": deposit_transfer.asset_name,
                "asset_type": non_fungible.asset_type,
                "asset_metadata": deposit_transfer.asset_metadata,
                "game_id": non_fungible.game_id,
                "is_deposit": False,
                "prize_for_raffle_id": raffle_id,
                "rarity": deposit_transfer.rarity,
                "wallet_address": None,
                "withdrawal_user_id": user_id,
            },
        )


class RaffleUserDepositCountRow(NamedTuple):
    rank: int
    display_name: str | None
    count: int


class NonFungibleTransferManager(BaseManager):
    def get_deposit_count_leaderboard(self) -> list[RaffleUserDepositCountRow]:
        order_by = func.count(NonFungibleTransfer.id).desc()
        result = self.db.execute(
            select(
                func.rank().over(order_by=order_by).label("rank"),
                User.display_name,
                func.count(NonFungibleTransfer.id),
            )
            .join(UserWallet, NonFungibleTransfer.wallet_address == UserWallet.address)
            .join(User)
            .where(NonFungibleTransfer.is_deposit == True)
            .group_by(User)
            .order_by(order_by)
            .limit(100)
        ).all()
        return [RaffleUserDepositCountRow(*row) for row in result]

    def get_pending_for_user(self, user_id: int, select_arg) -> ScalarResult:
        return self.db.scalars(
            select(select_arg).where(
                NonFungibleTransfer.wallet_address == None,
                NonFungibleTransfer.withdrawal_user_id == user_id,
            )
        )

    def get_pending_transfers_count_for_user(self, user_id: int) -> int:
        return self.get_pending_for_user(user_id, func.count()).one()

    def get_pending_transfers_for_user(self, user_id: int) -> Sequence[NonFungibleTransfer]:
        return self.get_pending_for_user(user_id, NonFungibleTransfer).all()

    def get_withdrawals_count(self):
        return self.db.scalar(
            select(func.count(NonFungibleTransfer.id)).where(
                NonFungibleTransfer.is_deposit == False,
            )
        )


class RaffleManager(BaseManager):
    def create(self, raffle: lootloot.schemas.AddRaffle) -> Raffle:
        return self.db.scalars(insert(Raffle).returning(Raffle), raffle.model_dump()).one()

    def get_by_secret_word(self, secret_word: str) -> Raffle | None:
        return self.db.scalars(
            select(Raffle).where(Raffle.secret_word == secret_word)
        ).one_or_none()


class RaffleEntryManager(BaseManager):
    def create(self, user_id: int, raffle_id: int) -> RaffleEntry:
        return self.db.scalars(
            insert(RaffleEntry).returning(RaffleEntry),
            {"raffle_id": raffle_id, "user_id": user_id},
        ).one()

    def get_entries_count_for_raffle(self, raffle_id: int) -> int:
        return self.db.scalar(
            select(func.count(RaffleEntry.id)).where(RaffleEntry.raffle_id == raffle_id)
        )

    def get_entries_for_raffle(self, raffle_id: int) -> Sequence[RaffleEntry]:
        return self.db.scalars(
            select(RaffleEntry).where(RaffleEntry.raffle_id == raffle_id)
        ).all()
