// ** MUI Imports
import Box from "@mui/material/Box";

// ** Type Import
import { HorizontalNavItemsType } from "@/@core/layouts/types";

// ** Config Import
import themeConfig from "@/configs/themeConfig";

// ** Utils
// import { hexToRGBA } from "@/@core/utils/hex-to-rgba"

// ** Menu Components
import { Settings } from "@/@core/context/settingsContext";
import HorizontalNavItems from "@/@core/layouts/components/horizontal/navigation/HorizontalNavItems";

// ** Types
interface Props {
  hasParent?: boolean;
  horizontalNavItems?: HorizontalNavItemsType;
  settings: Settings;
}

const Navigation = (props: Props) => {
  return (
    <Box
      className="menu-content"
      sx={{
        width: "100%",
        display: "flex",
        flexWrap: "wrap",
        alignItems: "center",
        "& > *": {
          "&:not(:last-child)": { mr: 2 },
          ...(themeConfig.menuTextTruncate && { maxWidth: 220 }),
        },
      }}
    >
      <HorizontalNavItems {...props} />
    </Box>
  );
};

export default Navigation;
