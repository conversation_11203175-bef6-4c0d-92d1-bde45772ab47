import type { WalletKitTypes } from "@reown/walletkit";
import { cx } from "class-variance-authority";
import { CircleDashed, Link } from "lucide-react";
import { useState } from "react";

const VARIANTS_MAP = {
  default: {
    className: "size-6",
    size: 24,
    Fallback: CircleDashed,
  },
  proposal: {
    className: "size-16",
    size: 64,
    Fallback: Link,
  },
} as const;

export function MetadataIcon(props: {
  metadata: WalletKitTypes.Metadata;
  variant?: "default" | "proposal" | undefined;
}) {
  const { metadata, variant = "default" } = props;
  const [failedToLoad, setFailedToLoad] = useState(false);

  const iconUrl = failedToLoad ? null : getIconUrl(metadata);

  const { className, size, Fallback } = VARIANTS_MAP[variant];

  return iconUrl ? (
    <img
      alt=""
      className={cx("object-contain", className)}
      src={iconUrl}
      width={size}
      height={size}
      referrerPolicy="no-referrer"
      onError={() => {
        setFailedToLoad(true);
      }}
    />
  ) : (
    <Fallback size={size} />
  );
}

function getIconUrl(metadata: WalletKitTypes.Metadata): string | null {
  const url = metadata.icons[0];
  return url ? new URL(url, metadata.url).href : null;
}
