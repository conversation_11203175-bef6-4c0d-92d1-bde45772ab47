import eslint from "@eslint/js";
import pluginQuery from "@tanstack/eslint-plugin-query";
import jsxA11y from "eslint-plugin-jsx-a11y";
import reactPlugin from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import eslintPluginUnicorn from "eslint-plugin-unicorn";
import globals from "globals";
import tseslint from "typescript-eslint";
import eslintBaseRulesConfig from "./config/eslint-base-rules-config.js";

export default tseslint.config(
  { ignores: ["dist/"] },
  eslintBaseRulesConfig,
  eslint.configs.recommended,
  tseslint.configs.strictTypeChecked,
  tseslint.configs.stylisticTypeChecked,
  {
    rules: {
      "@typescript-eslint/promise-function-async": "warn",
      // Be more lenient on template expressions
      "@typescript-eslint/restrict-template-expressions": [
        "error",
        {
          allow: [{ from: "lib", name: ["<PERSON><PERSON><PERSON>", "URL", "URLSearchParams"] }],
          allowAny: true,
          allowBoolean: true,
          allowNever: true,
          allowNullish: true,
          allowNumber: true,
          allowRegExp: true,
        },
      ],
    },
  },
  {
    files: ["src/**"],
    rules: {
      "@typescript-eslint/no-restricted-imports": [
        "warn",
        {
          patterns: [
            {
              group: ["./*", "../*"],
              message: "Use '@/*' for first-party modules",
            },
          ],
        },
      ],
    },
  },
  {
    plugins: { "react-hooks": reactHooks },
    rules: reactHooks.configs.recommended.rules,
  },
  reactPlugin.configs.flat.recommended,
  reactPlugin.configs.flat["jsx-runtime"],
  {
    rules: {
      "react/no-unknown-property": "off",
      "react/prop-types": "off",
    },
  },
  jsxA11y.flatConfigs.recommended,
  pluginQuery.configs["flat/recommended"],
  {
    ...eslintPluginUnicorn.configs.recommended,
    rules: {
      // Make all unicorn recommended rules that are "error" into "warn"
      ...Object.fromEntries(
        Object.entries(eslintPluginUnicorn.configs.recommended.rules).map(([id, state]) => [
          id,
          state === "error" ? "warn" : state,
        ]),
      ),
      // Disable rules that are undesirable
      "unicorn/explicit-length-check": "off",
      "unicorn/no-array-callback-reference": "off",
      "unicorn/no-negated-condition": "off",
      "unicorn/no-nested-ternary": "off",
      "unicorn/no-new-array": "off",
      "unicorn/no-null": "off",
      "unicorn/number-literal-case": "off",
      "unicorn/prefer-global-this": "off",
      "unicorn/prevent-abbreviations": "off",
      "unicorn/switch-case-braces": "off",
    },
  },
  {
    files: ["src/**"],
    rules: {
      // We must have the dependency with the same name in package.json
      "unicorn/prefer-node-protocol": "off",
    },
  },
  {
    languageOptions: {
      ecmaVersion: 2024,
      globals: globals.browser,
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
    settings: {
      react: { version: "detect" },
      "jsx-a11y": {
        attributes: {
          for: ["htmlFor", "for"],
        },
        components: {
          Button: "button",
          Input: "input",
        },
      },
    },
  },
  {
    ...tseslint.configs.disableTypeChecked,
    files: ["**/*.js"],
  },
);
