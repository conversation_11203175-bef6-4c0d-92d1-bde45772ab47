{"name": "wallet-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --strictPort --port 3002", "build": "vite build", "lint": "tsc -b && eslint .", "preview": "vite preview"}, "dependencies": {"@cubist-labs/cubesigner-sdk": "0.4.143-0", "@cubist-labs/cubesigner-sdk-browser-storage": "0.4.143-0", "@fontsource-variable/inter": "^5.2.5", "@noble/ciphers": "^1.3.0", "@noble/ed25519": "^2.2.3", "@noble/secp256k1": "^2.2.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@reown/walletkit": "^1.2.4", "@scure/bip32": "^1.7.0", "@scure/bip39": "^1.6.0", "@sentry/react": "^9.19.0", "@simplewebauthn/browser": "^13.1.0", "@solana/web3.js": "^1.98.2", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@walletconnect/core": "^2.20.2", "@walletconnect/types": "^2.20.2", "@walletconnect/utils": "^2.20.2", "bs58": "^6.0.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "ethereum-cryptography": "^3.2.0", "idb-keyval": "^6.2.2", "jsqr": "^1.4.0", "lucide-react": "^0.510.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-router": "^7.6.0", "tw-animate-css": "^1.2.9", "valibot": "^1.1.0", "viem": "^2.29.2"}, "devDependencies": {"@eslint/js": "^9.26.0", "@sentry/vite-plugin": "^3.4.0", "@tailwindcss/vite": "^4.1.6", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.26.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.1.0", "tailwindcss": "^4.1.6", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"], "ignoredBuiltDependencies": ["@tailwindcss/oxide"]}}