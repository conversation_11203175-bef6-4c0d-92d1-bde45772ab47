# Pricing System Architecture

## Overview

StableTech's pricing system provides real-time and historical price data for digital assets
through integration with CoinGecko's API. The system implements a sophisticated caching strategy
and refresh mechanism to ensure data reliability while managing API rate limits.

## Data Sources

### Primary Source

- CoinGecko API
  - Simple price endpoint for current prices
  - Historical data endpoint for past prices

### Price Aliasing

- Support for coin aliases
- Multiple coins can be tied to a single price reference
- Enables unified pricing for equivalent assets

## Caching Architecture

### Price Cache

- TTL (Time To Live): 5 minutes
- Fresh State Duration: 3 minutes 40 seconds
- Stale State Duration: 1 minute 20 seconds
- Cache States:
  - FRESH: Within fresh duration
  - STALE: Between fresh and TTL
  - MISSING: No cached data

### Coins List Cache

- TTL: 1 hour
- Fresh Duration: 45 minutes
- Stale Duration: 15 minutes

### Historical Data Cache

- TTL: 24 hours
- Considered fresh until expiration

### Requested Prices

- TTL: 20 minutes
- Used for tracking frequently requested prices

## Refresh Mechanism

### Auto-Refresh System

- Refresh Interval: 60 seconds
- Batch Processing:
  - Batch Size: 100 coins per request
  - Automatic batching for large requests

### Lock Management

- Lock Duration: 60 seconds
- Lock Renewal: Every 20 seconds
- Distributed locking for coordinated refreshes

### Error Handling

- Retry Mechanism:
  - Exponential backoff with jitter
  - Maximum retry duration: 120 seconds
- Handled Exceptions:
  - HTTP errors
  - Redis connection issues
  - Lock acquisition failures
  - Timeout errors

## Performance Considerations

### Optimization Strategies

- Batch processing to minimize API calls
- Distributed locking to prevent duplicate refreshes
- Staggered cache expiration
- Random sleep intervals to prevent thundering herd

### Rate Limiting

- Batch size limits
- Controlled refresh intervals
- API call optimization

## Technical Implementation

### Core Components

- PriceRefresher: Manages price updates
- Cache: Handles data caching
- CoingeckoClient: API integration

### Data Flow

1. Price request received
2. Cache check
   - Return fresh data if available
   - Use stale data while refreshing
   - Fetch new data if missing
3. Background refresh for stale/missing data
4. Cache update with new data

## Future Considerations

### Potential Enhancements

- Additional price data sources
- Advanced caching strategies
- Performance monitoring
- Rate limit optimization
- Historical data analytics

### Integration Points

- Market analysis tools
- Portfolio valuation
- Risk assessment systems
- Trading systems

## Technical Debt

### Documentation Needs

- API integration details
- Cache configuration rationale
- Performance benchmarks
- Error handling scenarios

### System Improvements

- Monitoring implementation
- Performance optimization
- Error recovery enhancement
- Cache efficiency analysis
