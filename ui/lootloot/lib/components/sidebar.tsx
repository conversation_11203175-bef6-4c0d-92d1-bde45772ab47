"use client";
import { useAuth, useLogOut } from "@/lib/auth-ctx";
import { button } from "@/lib/components/button";
import { ScrollArea } from "@/lib/components/scroll-area";
import { ModeToggle } from "@/lib/components/theme";
import { User } from "@/types/schemas";
import * as Dialog from "@radix-ui/react-dialog";
import { cx } from "class-variance-authority";
import {
  BarChartBig,
  CircleUser,
  Grab,
  LayoutGrid,
  LogIn,
  LogOut,
  Menu,
  Shield,
  Shirt,
  Trophy,
  Wallet,
  X,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useCallback, useState } from "react";

export function WithSidebar({ children }: { children: React.ReactNode }) {
  const auth = useAuth();
  const logOut = useLogOut();
  const router = useRouter();

  const [modalOpen, setModalOpen] = useState(false);
  const handleCloseModal = useCallback(() => {
    setModalOpen(false);
  }, []);

  // Hide private elements from sidebar if not logged in.
  // To prevent flickering for logged-in users, we only assume logged out state after initialized.
  const isLoggedIn = auth.user != null || !auth.initialized;

  const heistsHref = isLoggedIn ? "/heists" : "/heists?view=all";
  const claimsHistoryHref = isLoggedIn
    ? "/heists/claims-history"
    : "/heists/claims-history?view=all";

  const navItems = (
    <ul className="flex flex-col gap-2 py-4">
      <Item href={heistsHref} Icon={BarChartBig} onClick={handleCloseModal}>
        Overview
      </Item>
      <Item href={claimsHistoryHref} Icon={Trophy} onClick={handleCloseModal}>
        Claims
      </Item>
      {isLoggedIn && (
        <>
          <Item href="/nfts" Icon={LayoutGrid} onClick={handleCloseModal}>
            NFTs
          </Item>
          <Item href="/profile" Icon={CircleUser} onClick={handleCloseModal}>
            Profile
          </Item>
          <Item href="/wallets" Icon={Wallet} onClick={handleCloseModal}>
            Wallets
          </Item>
        </>
      )}
      <Item href="/holders" Icon={Grab} onClick={handleCloseModal}>
        Holders
      </Item>
      <Item href="/wardrobe" Icon={Shirt} onClick={handleCloseModal}>
        Wardrobe
      </Item>
      {auth.user?.is_admin && (
        <Item href="/admin" Icon={Shield} onClick={handleCloseModal}>
          Admin
        </Item>
      )}
    </ul>
  );

  const signInButtonClass = button({
    className: "gap-2 self-start justify-self-end",
    variant: "ghost",
    size: "sm",
  });
  const userDetail = (
    <div className="grid grid-cols-[1fr_auto]">
      <div className="flex max-w-full flex-col gap-1 overflow-hidden">
        <div className="flex flex-initial items-center gap-1 px-3">
          <CircleUser size={16} className="shrink-0" />
          <span className="truncate text-xs" title="Signed in user">
            {getIdentifier(auth.user) ||
              (auth.initialized ? (
                "Anonymous"
              ) : (
                <span className="inline-block w-24 animate-pulse rounded-md bg-foreground/10">
                  &nbsp;
                </span>
              ))}
          </span>
        </div>
        {isLoggedIn ? (
          <button
            type="button"
            className={signInButtonClass}
            onClick={() => {
              logOut();
              handleCloseModal();
            }}
            onMouseEnter={() => {
              router.prefetch("/auth");
            }}
          >
            <span>Sign out</span>
            <LogOut size={16} />
          </button>
        ) : (
          <Link href="/auth" className={signInButtonClass} onClick={handleCloseModal}>
            <span>Sign In</span>
            <LogIn size={16} />
          </Link>
        )}
      </div>
      <ModeToggle />
    </div>
  );

  const menuModal = (
    <Dialog.Root open={modalOpen} onOpenChange={setModalOpen}>
      <Dialog.Trigger className={ITEM_CLASS}>
        <Menu aria-label="Menu" />
      </Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-background/70 backdrop-blur-xs data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:animate-in data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed right-0 bottom-0 flex min-h-[50%] w-60 flex-col rounded-tl-md border-t border-l bg-background px-3 py-6 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:slide-out-to-bottom-4 data-[state=closed]:slide-out-to-right data-[state=closed]:zoom-out-95 data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:slide-in-from-bottom-4 data-[state=open]:slide-in-from-right data-[state=open]:zoom-in-95">
          <Dialog.Title className="sr-only">Menu</Dialog.Title>
          <Dialog.Close
            className={cx(button({ variant: "ghost", size: "icon" }), "absolute top-2 right-3")}
            aria-label="Close"
          >
            <X />
          </Dialog.Close>
          {navItems}
          <div className="mt-auto flex flex-col gap-1 px-1.5 pt-4">{userDetail}</div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );

  return (
    <div className="flex min-h-dvh">
      <nav className="sticky top-0 left-0 hidden h-dvh w-60 shrink-0 flex-col border-r lg:flex">
        <ScrollArea className="w-full">
          <div className="flex h-full flex-col px-3">{navItems}</div>
        </ScrollArea>
        <div className="mt-auto flex flex-col gap-1 border-t px-4 py-4">{userDetail}</div>
      </nav>
      <div className="flex w-full flex-col">
        <div className="w-full flex-1 overflow-x-auto">{children}</div>
        <nav className="sticky bottom-0 left-0 h-12 w-full shrink-0 bg-background lg:hidden">
          <ul className="flex h-full w-full items-center justify-around border-t">
            <ItemBottom href={heistsHref} Icon={BarChartBig}>
              Overview
            </ItemBottom>
            <ItemBottom href={claimsHistoryHref} Icon={Trophy}>
              Claims
            </ItemBottom>
            {isLoggedIn && (
              <>
                <ItemBottom href="/nfts" Icon={LayoutGrid}>
                  NFTs
                </ItemBottom>
              </>
            )}
            <ItemBottom href="/holders" Icon={Grab}>
              Holders
            </ItemBottom>
            {auth.user?.is_admin && (
              <ItemBottom href="/admin" Icon={Shield}>
                Admin
              </ItemBottom>
            )}
            <li>{menuModal}</li>
          </ul>
        </nav>
      </div>
    </div>
  );
}

const ITEM_CLASS = cx(button({ variant: "ghost" }), "w-full justify-start!");
const ITEM_ACTIVE_CLASS = cx(ITEM_CLASS, "bg-primary/20");

function Item({
  children,
  href,
  Icon,
  onClick,
}: {
  children: React.ReactNode;
  href: string;
  Icon: React.ComponentType;
  onClick: () => void;
}) {
  const pathname = usePathname();
  const isActive = isCurrentPath(pathname, href);
  return (
    <li>
      <Link href={href} onClick={onClick} className={isActive ? ITEM_ACTIVE_CLASS : ITEM_CLASS}>
        <Icon />
        <span className="ms-3">{children}</span>
      </Link>
    </li>
  );
}

/**
 * Pattern to match the end of path that is a trailing slash and/or a search query.
 */
const ENDING_SLASH_OR_SEARCH = /\/$|\/?\?[^/]*$/u;

function isCurrentPath(current: string, href: string) {
  return (
    current.replace(ENDING_SLASH_OR_SEARCH, "") === href.replace(ENDING_SLASH_OR_SEARCH, "")
  );
}

function ItemBottom({
  children,
  href,
  Icon,
}: {
  children: React.ReactNode;
  href: string;
  Icon: React.ComponentType;
}) {
  const pathname = usePathname();
  const isActive = isCurrentPath(pathname, href);
  return (
    <li>
      <Link href={href} className={isActive ? ITEM_ACTIVE_CLASS : ITEM_CLASS}>
        <Icon />
        <span className="sr-only">{children}</span>
        <span aria-hidden className="ms-3 hidden sm:inline">
          {children}
        </span>
      </Link>
    </li>
  );
}

function getIdentifier(user: User | null | undefined): string {
  if (!user) return "";
  // Cannot use nullish coalescing, as we don't want to prematurely return an empty string
  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  return user.email || user.display_name || user.matrica_username || "Anonymous";
}
