"use client";

import { AddRaffleButton } from "@/app/raffle/add-raffle";
import { EndRaffleButton } from "@/app/raffle/end-raffle";
import { useAuth } from "@/lib/auth-ctx";
import { AlertError, AlertSuccess } from "@/lib/components/alert";
import { Button, button } from "@/lib/components/button";
import { renderTable } from "@/lib/components/data-table";
import { InputClearable } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { useGetRaffle, useHeistAddRaffleEntry } from "@/lib/heist";
import { RaffleUserDepositCount } from "@/types/schemas";
import { ColumnDef, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import React, { useState } from "react";

export default function Raffles() {
  const auth = useAuth();
  const [secretWord, setSecretWord] = useState("");

  const addRaffleEntry = useHeistAddRaffleEntry();
  const raffleResp = useGetRaffle();

  const handleClick = () => {
    addRaffleEntry.mutate(secretWord);
  };

  return (
    <main className="grid gap-6 px-2 py-8 sm:justify-center">
      <h1 className="mb-4 grid text-2xl sm:justify-center">Lossless Raffle</h1>
      {raffleResp.data && raffleResp.data.pending_prizes_count !== 0 && (
        <div className="mb-4 max-w-lg text-center">
          {
            <AlertSuccess>
              {`You have ${raffleResp.data.pending_prizes_count}
              prize${raffleResp.data.pending_prizes_count > 1 ? "s" : ""} waiting!
              Claim it in-game from "LootLoot Raffles"`}
            </AlertSuccess>
          }
        </div>
      )}
      {auth.user?.is_admin && (
        <div className="flex sm:justify-center">
          <AddRaffleButton
            className={button({ variant: "ghost", className: "mb-4 px-2.5! font-normal!" })}
          />
          <EndRaffleButton
            className={button({ variant: "ghost", className: "mb-4 px-2.5! font-normal!" })}
          />
        </div>
      )}
      {addRaffleEntry.error && (
        <div className="mb-4 max-w-lg">
          {<AlertError>{getErrorMessage(addRaffleEntry.error)}</AlertError>}
        </div>
      )}
      {addRaffleEntry.isSuccess && (
        <div className="mb-4 max-w-lg">
          {<AlertSuccess>{"You've been entered into the raffle"}</AlertSuccess>}
        </div>
      )}
      <div className="grid sm:justify-center">
        <InputClearable
          className="mb-4 max-w-lg"
          placeholder="What's the secret word ?"
          value={secretWord}
          onChange={(event) => {
            setSecretWord(event.target.value);
          }}
          onClear={() => {
            setSecretWord("");
          }}
          clearHidden={!secretWord}
        />
        <Button onClick={handleClick} disabled={addRaffleEntry.isPending || !secretWord}>
          Submit
        </Button>
      </div>

      <div className="grid justify-center pt-12 text-2xl">Karma Board</div>
      <div className="rounded-sm bg-emerald-300 p-2 text-center dark:bg-emerald-950">
        {raffleResp.isLoading ? (
          <span className="inline-block w-8 animate-pulse rounded-sm bg-foreground/10">
            &nbsp;
          </span>
        ) : (
          (raffleResp.data?.withdrawals_count ?? "-")
        )}{" "}
        prizes won by the community
      </div>
      <div className="text-center">
        {`Open a trade with "LootLoot Raffles" in-game and Confirm to make donations.`}
      </div>
      <div className="text-center">
        <u>All donations</u>
        {` will be distributed to members of the Heist community.`}
      </div>
      <div className="relative flex flex-col gap-4 sm:min-w-[520px]">
        <KarmaBoard data={raffleResp.data?.user_deposits ?? []} />
      </div>
    </main>
  );
}

interface KarmaBoardProps {
  data: RaffleUserDepositCount[];
}

function KarmaBoard({ data }: KarmaBoardProps) {
  const columns: ColumnDef<RaffleUserDepositCount, React.ReactNode>[] = [
    {
      accessorKey: "rank",
      header: () => <div className="text-center">Rank</div>,
      cell({ getValue }) {
        return <div className="text-center">{getValue()}</div>;
      },
    },
    {
      accessorKey: "display_name",
      header: () => <div className="text-center">Name</div>,
      cell({ row }) {
        return <div className="text-center">{row.original.display_name || "Anonymous"}</div>;
      },
    },
    {
      accessorKey: "count",
      header: () => <div className="text-right">Items Donated</div>,
      cell({ getValue }) {
        return <div className="text-right">{getValue()}</div>;
      },
    },
  ];

  const table = useReactTable({
    data: data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });
  return renderTable(columns, table);
}
