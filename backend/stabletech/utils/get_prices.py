import logging
from datetime import datetime
from decimal import Decimal

from httpx import HTTPStatusError

from stabletech.indexer.models import Transfer
from stabletech.utils.coingecko_missing_prices import get_prices_for_lost_coingecko_history
from stabletech.utils.pricer import pricer

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def get_coingecko_prices(transfer: Transfer, timestamp: datetime) -> Decimal | None:
    if transfer.asset_price_usd:
        return transfer.asset_price_usd

    if transfer.asset_account.coin.is_dollar:
        return Decimal("1")
    elif (
        price := get_prices_for_lost_coingecko_history(
            transfer.asset_account.coin.uid, timestamp.date()
        )
    ) is not None:
        return price
    try:
        data = pricer.get_coin_history(
            coin_id=transfer.asset_account.alias_coin.uid,
            date=transfer.transaction.confirmed_at.date(),
        )
    except HTTPStatusError as e:
        logger.warning(
            "Failed to fetch coin history for %s on %s error: %s",
            transfer.asset_account.alias_coin.uid,
            transfer.transaction.confirmed_at.date(),
            e,
            exc_info=True,
        )
        return None
    if not data.market_data or not data.market_data.current_price.get("usd", None):
        transfer.asset_price_usd = Decimal(0)
        logger.warning(
            "Missing asset_price_usd for transfer: %r asset_price_usd %s",
            transfer.asset_account.alias_coin,
            transfer.asset_price_usd,
        )
        return None
    return Decimal(str(data.market_data.current_price["usd"]))
