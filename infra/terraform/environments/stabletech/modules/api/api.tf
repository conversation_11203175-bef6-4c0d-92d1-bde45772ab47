locals {
  api_container_args = compact([
    "uvicorn",
    "stabletech.api:app",
    "--forwarded-allow-ips=*",
    "--host=0.0.0.0",
    "--port=80",
    var.enable_dev_mode ? "--reload" : ""
  ])

  jobs_consumer_script = var.enable_dev_mode ? "stabletech/indexer/dev_jobs_consumer.py" : "stabletech/indexer/jobs_consumer.py"
}

resource "kubernetes_deployment" "api" {
  metadata {
    name      = "api"
    namespace = var.namespace
    labels = {
      app = "api"
    }
  }

  spec {
    replicas = var.replicas
    selector {
      match_labels = {
        app = "api"
      }
    }

    template {
      metadata {
        labels = {
          app = "api"
        }
      }
      spec {
        container {
          name              = "api"
          image             = var.python_runner_image
          image_pull_policy = "IfNotPresent"
          args              = local.api_container_args

          dynamic "env_from" {
            for_each = var.config_maps
            iterator = config_map
            content {
              config_map_ref {
                name = config_map.value
              }
            }
          }
          dynamic "env_from" {
            for_each = var.secrets
            iterator = secret
            content {
              secret_ref {
                name = secret.value
              }
            }
          }
          dynamic "env_from" {
            for_each = var.api_secrets
            iterator = secret
            content {
              secret_ref {
                name = secret.value
              }
            }
          }

          port {
            container_port = 80
          }

          resources {
            requests = {
              cpu    = var.requests.cpu
              memory = var.requests.memory
            }
          }

          dynamic "volume_mount" {
            for_each = var.host_volumes
            content {
              name       = volume_mount.value["name"]
              mount_path = volume_mount.value["mount_path"]
            }
          }

          dynamic "liveness_probe" {
            for_each = var.enable_liveness_probe ? [true] : []
            content {
              http_get {
                path = "/"
                port = 80
              }
              initial_delay_seconds = 15
              period_seconds        = 30
            }
          }
        }
        dynamic "volume" {
          for_each = var.host_volumes
          content {
            name = volume.value["name"]
            host_path {
              path = volume.value["host_path"]
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_service" "api" {
  metadata {
    name      = "api"
    namespace = var.namespace
  }
  spec {
    selector = {
      app = kubernetes_deployment.api.metadata.0.labels.app
    }
    port {
      port        = 80
      target_port = 80
    }

    type = "NodePort"
  }
}

resource "kubernetes_service" "jobs_consumer" {
  metadata {
    name      = "api-jobs-consumer"
    namespace = var.namespace
  }
  spec {
    cluster_ip = "None"
  }
}

resource "kubernetes_stateful_set" "jobs_consumer" {
  metadata {
    name      = "api-jobs-consumer"
    namespace = var.namespace
    labels = {
      app = "api-jobs-consumer"
    }
  }
  spec {
    selector {
      match_labels = {
        app = "api-jobs-consumer"
      }
    }
    service_name = "api-jobs-consumer"
    replicas     = 1 # Cannot be scaled up more than 1, jobs would be double consumed
    template {
      metadata {
        labels = {
          app = "api-jobs-consumer"
        }
      }
      spec {
        container {
          name              = "api-jobs-consumer"
          image             = var.python_runner_image
          image_pull_policy = "IfNotPresent"
          args              = ["python", local.jobs_consumer_script]

          dynamic "env_from" {
            for_each = var.config_maps
            iterator = config_map
            content {
              config_map_ref {
                name = config_map.value
              }
            }
          }
          dynamic "env_from" {
            for_each = var.secrets
            iterator = secret
            content {
              secret_ref {
                name = secret.value
              }
            }
          }
          dynamic "env_from" {
            for_each = var.jobs_consumer_secrets
            iterator = secret
            content {
              secret_ref {
                name = secret.value
              }
            }
          }

          resources {
            requests = {
              cpu    = var.requests.cpu
              memory = var.requests.memory
            }
          }

          dynamic "volume_mount" {
            for_each = var.host_volumes
            content {
              name       = volume_mount.value["name"]
              mount_path = volume_mount.value["mount_path"]
            }
          }
        }
        dynamic "volume" {
          for_each = var.host_volumes
          content {
            name = volume.value["name"]
            host_path {
              path = volume.value["host_path"]
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_cron_job_v1" "update_prices" {
  count = var.enable_update_prices ? 1 : 0
  metadata {
    name      = "api-update-prices"
    namespace = var.namespace
  }

  spec {
    concurrency_policy            = "Forbid"
    schedule                      = "* * * * *"
    successful_jobs_history_limit = 1
    job_template {
      metadata {}
      spec {
        template {
          metadata {}
          spec {
            container {
              name              = "api-update-prices"
              image             = var.python_runner_image
              image_pull_policy = "IfNotPresent"
              args              = ["python", "stabletech/market/update_prices.py"]

              dynamic "env_from" {
                for_each = var.config_maps
                iterator = config_map
                content {
                  config_map_ref {
                    name = config_map.value
                  }
                }
              }
              dynamic "env_from" {
                for_each = var.secrets
                iterator = secret
                content {
                  secret_ref {
                    name = secret.value
                  }
                }
              }

              resources {
                requests = {
                  cpu    = var.requests.cpu
                  memory = var.requests.memory
                }
              }

              dynamic "volume_mount" {
                for_each = var.host_volumes
                content {
                  name       = volume_mount.value["name"]
                  mount_path = volume_mount.value["mount_path"]
                }
              }
            }
            dynamic "volume" {
              for_each = var.host_volumes
              content {
                name = volume.value["name"]
                host_path {
                  path = volume.value["host_path"]
                }
              }
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_cron_job_v1" "calculate_cost_basis" {
  count = var.enable_calculate_cost_basis ? 1 : 0
  metadata {
    name      = "api-calculate-cost-basis"
    namespace = var.namespace
  }

  spec {
    concurrency_policy            = "Forbid"
    schedule                      = "* * * * *"
    successful_jobs_history_limit = 1
    job_template {
      metadata {}
      spec {
        template {
          metadata {}
          spec {
            container {
              name              = "api-calculate-cost-basis"
              image             = var.python_runner_image
              image_pull_policy = "IfNotPresent"
              args              = ["python", "stabletech/market/calculate_cost_basis.py"]

              dynamic "env_from" {
                for_each = var.config_maps
                iterator = config_map
                content {
                  config_map_ref {
                    name = config_map.value
                  }
                }
              }
              dynamic "env_from" {
                for_each = var.secrets
                iterator = secret
                content {
                  secret_ref {
                    name = secret.value
                  }
                }
              }

              resources {
                requests = {
                  cpu    = var.requests.cpu
                  memory = var.requests.memory
                }
              }

              dynamic "volume_mount" {
                for_each = var.host_volumes
                content {
                  name       = volume_mount.value["name"]
                  mount_path = volume_mount.value["mount_path"]
                }
              }
            }
            dynamic "volume" {
              for_each = var.host_volumes
              content {
                name = volume.value["name"]
                host_path {
                  path = volume.value["host_path"]
                }
              }
            }
          }
        }
      }
    }
  }
}
