"use client";
import type { User } from "@/types/schemas";
import { usePathname, useRouter } from "next/navigation";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { boolean, create, defaulted, nonempty, nullable, string, type } from "superstruct";

const NullableString = defaulted(nullable(string()), null);

const User = type({
  email: NullableString,
  first_name: NullableString,
  last_name: NullableString,
  picture_url: NullableString,
  display_name: NullableString,
  matrica_username: NullableString,
  is_admin: defaulted(boolean(), false),
});

/**
 * Struct for validating the (homonymous) AuthParams interface.
 */
const AuthParams = type({
  user: User,
  token: nonempty(string()),
});

interface AuthParams {
  readonly user: User;
  readonly token: string;
}

interface AuthContext {
  /** Useful to differ if user is logged out or just not yet initialized */
  readonly initialized: boolean;
  readonly user: User | undefined;
}

const INITIAL_AUTH_CONTEXT: AuthContext = {
  user: undefined,
  initialized: false,
};

interface PrivateAuthContext extends AuthContext {
  readonly set: (params: AuthParams | null) => void;
}

const AuthContext = createContext<PrivateAuthContext>({
  ...INITIAL_AUTH_CONTEXT,
  set: () => {
    throw new Error("AuthProvider is not initialized");
  },
});
AuthContext.displayName = "AuthContext";

export function useAuth(): AuthContext {
  return useContext(AuthContext);
}

const AUTH_DATA_KEY = "auth.data";
const AUTH_REDIRECT_KEY = "auth.redirect";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Cannot initialize user here using localStorage, as it could differ from the server render
  const [state, setState] = useState(INITIAL_AUTH_CONTEXT);

  const internalSetAuth = useCallback((broadcast: boolean, data: AuthParams | null) => {
    if (broadcast) {
      broadcastChannelRef.current?.postMessage(data);
    }
    setState((prev) =>
      prev.user !== data?.user || !prev.initialized
        ? { user: data?.user, initialized: true }
        : prev,
    );
    setAuthToken(data?.token ?? "");
  }, []);

  // Initialize from localStorage
  useEffect(() => {
    internalSetAuth(false, loadAuthData());
  }, [internalSetAuth]);

  // Keep the auth data in sync between tabs/windows
  const broadcastChannelRef = useRef<BroadcastChannel>();
  useEffect(() => {
    const broadcastChannel = new BroadcastChannel("auth");
    broadcastChannelRef.current = broadcastChannel;
    broadcastChannel.addEventListener("message", (event: MessageEvent<AuthParams | null>) => {
      internalSetAuth(false, event.data);
    });
    return () => {
      broadcastChannel.close();
    };
  }, [internalSetAuth]);

  // Exposed setter for login/logout flows
  const setAuth = useCallback(
    (data: AuthParams | null) => {
      // Remove any extra fields that might be in the object passed as argument
      data = data == null ? null : { user: data.user, token: data.token };
      if (data == null) {
        localStorage.removeItem(AUTH_DATA_KEY);
      } else {
        localStorage.setItem(AUTH_DATA_KEY, JSON.stringify(data));
      }
      internalSetAuth(true, data);
    },
    [internalSetAuth],
  );

  // Logout when the token becomes invalid
  useEffect(() => {
    return tokenInvalidTopic.subscribe(() => {
      setAuth(null);
    });
  }, [setAuth]);

  const value = useMemo(() => ({ ...state, set: setAuth }), [state, setAuth]);

  return (
    <AuthContext.Provider value={value}>
      <AuthEnforcer>{children}</AuthEnforcer>
    </AuthContext.Provider>
  );
}

const AUTH_PATH_REGEX = /^\/auth(\/.*)?$/u;
const PUBLIC_PATHS: ReadonlySet<string> = new Set([
  "/holders/",
  "/heists/",
  "/heists/claims-history/",
]);

function AuthEnforcer({ children }: { children: React.ReactNode }): React.ReactNode {
  const { user, initialized } = useContext(AuthContext);
  const pathname = usePathname();
  const logOut = useLogOut();

  useEffect(() => {
    let cleaner;
    if (
      user == null &&
      initialized &&
      !AUTH_PATH_REGEX.test(pathname) &&
      !PUBLIC_PATHS.has(pathname)
    ) {
      // Wait a short timeout to avoid double log-out and flickering
      const timeout = setTimeout(() => {
        // Doing a log-out to redirect to the auth page
        logOut();
      }, 250);
      cleaner = () => {
        clearTimeout(timeout);
      };
    }
    return cleaner;
  }, [user, initialized, pathname, logOut]);

  return children;
}

export function useLogOut() {
  const { set: setAuth } = useContext(AuthContext);
  const router = useRouter();

  return useCallback(() => {
    const currentPath = location.href.slice(location.origin.length) || "/";

    // Avoid circular redirect to auth page
    const redirectPath = AUTH_PATH_REGEX.test(currentPath) ? "/" : currentPath;
    localStorage.setItem(AUTH_REDIRECT_KEY, redirectPath);

    router.replace("/auth");
    setAuth(null);
  }, [router, setAuth]);
}

export function useLogIn() {
  const { set: setAuth } = useContext(AuthContext);
  const router = useRouter();

  return useCallback(
    (data: AuthParams) => {
      router.replace(localStorage.getItem(AUTH_REDIRECT_KEY) ?? "/");
      setAuth(data);
      localStorage.removeItem(AUTH_REDIRECT_KEY);
    },
    [router, setAuth],
  );
}

export function useUpdateUser() {
  const { set: setAuth } = useContext(AuthContext);
  return useCallback(
    (user: User) => {
      setAuth({ user, token: authToken.current });
    },
    [setAuth],
  );
}

function loadAuthData(): AuthParams | null {
  const data = localStorage.getItem(AUTH_DATA_KEY);
  if (data == null) return null;
  try {
    const dataObj: unknown = JSON.parse(data);
    return create(dataObj, AuthParams);
  } catch {
    return null;
  }
}

// Readonly token and a setter exclusive for this module's internal use
const [authToken, setAuthToken] = (() => {
  let token = typeof window === "undefined" ? "" : (loadAuthData()?.token ?? "");
  function set(value: string) {
    token = value;
  }
  const readable = {
    get current() {
      return token;
    },
  } as const;
  return [readable, set] as const;
})();

export { authToken };

/** Topic to notify when the token becomes invalid. */
export const tokenInvalidTopic = (() => {
  const subscribers = new Set<() => void>();
  function subscribe(callback: () => void) {
    subscribers.add(callback);
    return () => {
      subscribers.delete(callback);
    };
  }
  function notify() {
    for (const callback of subscribers) {
      callback();
    }
  }
  return { subscribe, notify } as const;
})();
