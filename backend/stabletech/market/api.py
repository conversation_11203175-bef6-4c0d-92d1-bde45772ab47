import re
from typing import Annotated

import psycopg.errors
from fastapi import APIRouter, HTTPException, Query
from sqlalchemy.exc import IntegrityError, NoResultFound

from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from stabletech import schemas
from stabletech.indexer.crud import NetworkManager
from stabletech.market import crud
from stabletech.utils.fastapi import depends_admin_role

router = APIRouter()


@router.post(
    "/coin/add",
    dependencies=[depends_admin_role],
    response_model=schemas.CoinWithNetworks,
)
def add_coin(coin_schema: schemas.AddCoin, db: DatabaseSession):
    coin = crud.CoinManager(db).add(coin_schema)
    return coin


@router.post(
    "/coin/update",
    dependencies=[depends_admin_role],
    response_model=schemas.CoinWithNetworks,
)
def update_coin_has_prices(update_coin_schema: schemas.UpdateCoin, db: DatabaseSession):
    try:
        coin = crud.CoinManager(db).get_coin_by_id(update_coin_schema.id)
        updated = False
        if update_coin_schema.has_prices is not None:
            coin.has_prices = update_coin_schema.has_prices
            updated = True
        if update_coin_schema.is_usable_by_clients is not None:
            coin.is_usable_by_clients = update_coin_schema.is_usable_by_clients
            updated = True
        if update_coin_schema.name is not None:
            coin.name = update_coin_schema.name
            updated = True
        if update_coin_schema.network_ids is not None:
            coin.networks = NetworkManager(db).get_by_ids(update_coin_schema.network_ids)
            updated = True
        if update_coin_schema.ticker is not None:
            coin.ticker = update_coin_schema.ticker
            updated = True
        if update_coin_schema.uid is not None:
            coin.uid = update_coin_schema.uid
            updated = True
        if updated:
            db.commit()
        return coin
    except (NoResultFound, IntegrityError):
        raise HTTPException(status_code=400, detail="Coin update failed")


@router.get("/coins", response_model=list[schemas.CoinWithNetworks])
def get_coins(db: DatabaseSession, search: str | None = None, with_prices: bool = False):
    if not search:
        return []
    return crud.CoinManager(db).search_coin(search, with_prices=with_prices)


@router.delete("/coins/{coin_id}", dependencies=[depends_admin_role])
def delete_coin(coin_id: int, db: DatabaseSession) -> None:
    try:
        crud.CoinManager(db).delete(coin_id)
        db.commit()
    except IntegrityError as e:
        if isinstance(e.orig, psycopg.errors.ForeignKeyViolation):
            raise HTTP400Exception("Coin cannot be deleted because it is in use")
        raise HTTP400Exception("Coin cannot not be deleted due to a constraint violation")


VALIDATE_SEARCH_REGEX = re.compile(r"\W")


@router.get("/coins/asset-address", response_model=list[schemas.CoinWithAssetAddress])
def get_coins_with_asset_address(search: str, db: DatabaseSession, network_id: int = None):
    if invalid_chars := VALIDATE_SEARCH_REGEX.findall(search):
        raise HTTP400Exception(f"Invalid characters in search: {invalid_chars}")
    return [
        schemas.CoinWithAssetAddress(
            **dict(schemas.Coin.model_validate(coin)),
            asset_address=asset_address,
            network_id=network,
        )
        for coin, asset_address, network in crud.CoinManager(db).search_coin_with_asset_address(
            search, network_id=network_id
        )
    ]


@router.get("/coins/usable-by-clients", response_model=list[schemas.CoinWithNetworks])
def get_coins_usable_by_clients(db: DatabaseSession):
    return crud.CoinManager(db).get_coins_usable_by_clients()


@router.get("/coins/with-prices", response_model=list[schemas.CoinWithNetworks])
def get_coins_with_prices(
    db: DatabaseSession,
    exclude_alias_coins: bool = False,
    exclude_coins_ids: Annotated[list[str], Query()] = [],
    include_without_prices: bool = False,
    search: str = "",
):
    try:
        exclude_coins_ids = [int(coin_id) for coin_id in exclude_coins_ids]
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid exclude coin id provided")

    return crud.CoinManager(db).get_coins_with_prices(
        exclude_alias_coins=exclude_alias_coins,
        exclude_coins_ids=exclude_coins_ids,
        include_without_prices=include_without_prices,
        limit=200 if include_without_prices else 0,
        search=search,
    )


@router.post(
    "/alias-coins",
    dependencies=[depends_admin_role],
    response_model=schemas.AliasCoin,
)
def add_alias_coin(add_alias_coin_schema: schemas.AddAliasCoin, db: DatabaseSession):
    return crud.AliasCoinManager(db).add(add_alias_coin_schema)


@router.get(
    "/alias-coins",
    response_model=list[schemas.AliasCoin],
)
def get_alias_coins(db: DatabaseSession):
    return crud.AliasCoinManager(db).get_all()


@router.delete("/alias-coins/{alias_coin_id}", dependencies=[depends_admin_role])
def delete_alias_coin(alias_coin_id: int, db: DatabaseSession):
    crud.AliasCoinManager(db).delete(alias_coin_id)
    db.commit()


@router.patch(
    "/alias-coins/{alias_coin_id}",
    dependencies=[depends_admin_role],
    response_model=schemas.AliasCoin,
)
def update_alias_coin(
    alias_coin_id: int, update_alias_coin_schema: schemas.UpdateAliasCoin, db: DatabaseSession
):
    alias_coin = crud.AliasCoinManager(db).update_aliased_coins(
        alias_coin_id, update_alias_coin_schema.aliased_coins_ids
    )
    db.commit()
    return alias_coin
