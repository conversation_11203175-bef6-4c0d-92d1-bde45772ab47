from datetime import datetime
from typing import Literal

from sqlalchemy import <PERSON><PERSON>onstraint, DateTime, ForeignKey, UniqueConstraint, func
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    MappedAsDataclass,
    mapped_column,
    relationship,
)

from common.models import BaseModel
from lootloot.auth.models import User
from lootloot.heist.bots.schemas import Rarity


class Base(MappedAsDataclass, DeclarativeBase, BaseModel):
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, init=False)


class Raffle(Base):
    """Raffle users can join by using the configured secret word."""

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    ended_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    entries_count: Mapped[int]
    secret_word: Mapped[str] = mapped_column(unique=True)
    winners_count: Mapped[int]
    is_active: Mapped[bool] = mapped_column(default=True, server_default="true")


"""
History of all transfers from and to the Bot.
Includes pending withdrawals where `wallet_address` is null.
"""


class FungibleTransfer(Base):
    amount: Mapped[int]  # Withdrawals are negative
    asset_metadata: Mapped[dict] = mapped_column(JSONB, repr=False)
    asset_type: Mapped[str] = mapped_column(index=True)
    at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    # Wallet address is None when waiting to be withdrawn by the withdrawal_user
    wallet_address: Mapped[str | None] = mapped_column(index=True)
    withdrawal_user_id: Mapped[int | None] = mapped_column(ForeignKey(User.id))
    withdrawal_user: Mapped[User | None] = relationship(init=False, repr=False)

    __table_args__ = (
        # Either a Wallet address or User ID is required
        CheckConstraint("NOT(wallet_address IS NULL AND withdrawal_user_id IS NULL)"),
    )

    @property
    def is_deposit(self) -> bool:
        return self.amount > 0


class NonFungibleTransfer(Base):
    asset_metadata: Mapped[dict] = mapped_column(JSONB, repr=False)
    asset_name: Mapped[str] = mapped_column(index=True)
    asset_type: Mapped[Literal["consumable", "cosmetic", "item"]] = mapped_column(index=True)
    at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), init=False, server_default=func.now()
    )
    game_id: Mapped[str] = mapped_column(index=True)
    is_deposit: Mapped[bool]
    rarity: Mapped[Rarity] = mapped_column(repr=False)
    # Wallet address is None when waiting to be withdrawn by the withdrawal_user
    wallet_address: Mapped[str | None] = mapped_column(index=True)
    withdrawal_user_id: Mapped[int | None] = mapped_column(ForeignKey(User.id))
    withdrawal_user: Mapped[User | None] = relationship(init=False, repr=False)
    prize_for_raffle_id: Mapped[int | None] = mapped_column(ForeignKey(Raffle.id), default=None)

    __table_args__ = (
        # Either a Wallet address or User ID is required
        CheckConstraint("NOT(wallet_address IS NULL AND withdrawal_user_id IS NULL)"),
    )


"""Bot's current inventory of fungibles and non-fungibles."""


class FungibleInventory(Base):
    """Only saves one entry per asset type."""

    amount: Mapped[int]
    asset_type: Mapped[str] = mapped_column(index=True, unique=True)


class NonFungibleInventory(Base):
    """Non-fungibles are deleted from the inventory when withdrawn."""

    asset_type: Mapped[Literal["consumable", "cosmetic", "item"]] = mapped_column(index=True)
    game_id: Mapped[str] = mapped_column(index=True)


class RaffleEntry(Base):
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    non_fungible_transfer_id: Mapped[int | None] = mapped_column(
        ForeignKey(NonFungibleTransfer.id)
    )
    non_fungible_transfer: Mapped[NonFungibleTransfer | None] = relationship(
        init=False, repr=False
    )
    raffle_id: Mapped[int] = mapped_column(ForeignKey(Raffle.id))
    raffle: Mapped[Raffle] = relationship(init=False, repr=False)
    user_id: Mapped[int] = mapped_column(ForeignKey(User.id))
    user: Mapped[User] = relationship(init=False, repr=False)

    __table_args__ = (
        # A User can't have more than one entry per Raffle
        UniqueConstraint("raffle_id", "user_id"),
    )
