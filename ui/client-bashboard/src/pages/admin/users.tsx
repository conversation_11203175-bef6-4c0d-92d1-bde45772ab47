// ** MUI Imports
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import Grid from "@mui/material/Grid";

import { Toast, useToast } from "@/components/Toast";
import { UserSearch } from "@/components/autocomplete/UserSearch";
import { enablePasswordLogin, enablePooledFund } from "@/context/env";
import {
  useAuthUserAddMutation,
  useAuthUserAddWithPasswordMutation,
  useAuthUserDeleteMutation,
  useAuthUserUpdateMutation,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { User } from "@/types/schemas";
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Input,
  InputAdornment,
  Select,
  Typography,
} from "@mui/material";
import Button from "@mui/material/Button";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import { useCallback, useState } from "react";

const Clients = () => {
  const [newUserEmail, setNewUserEmail] = useState<string>("");
  const [newUserPassword, setNewUserPassword] = useState<string>("");
  const [selectedUser, _setSelectedUser] = useState<User | null>(null);
  const [newInterestRate, setNewInterestRate] = useState<string | null>(null);

  const setSelectedUser = useCallback(
    (user: User | null) => {
      if (user?.id !== selectedUser?.id) {
        setNewInterestRate(null);
      }
      _setSelectedUser(user);
    },
    [selectedUser?.id],
  );

  const [addUser] = useAuthUserAddMutation();
  const [addUserWithPassword] = useAuthUserAddWithPasswordMutation();
  const [deleteUser] = useAuthUserDeleteMutation();
  const [updateUser] = useAuthUserUpdateMutation();

  const [toastProps, toast] = useToast();

  return (
    <Grid container spacing={6}>
      <Grid item xs={7}>
        <Stack spacing={2}>
          <Card>
            {!selectedUser && <CardHeader title="Select a User to Edit"></CardHeader>}
            <CardContent>
              <UserSearch
                includeAdmins
                selectedUser={selectedUser}
                setSelectedUser={setSelectedUser}
              />
            </CardContent>
          </Card>
          {selectedUser && (
            <Card>
              <CardContent>
                <Grid container spacing={4}>
                  <Grid item xs={6}>
                    <TextField
                      disabled={true}
                      label={"First Name"}
                      fullWidth={true}
                      value={selectedUser.firstName || ""}
                      onChange={(event) => {
                        setSelectedUser({ ...selectedUser, firstName: event.target.value });
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      disabled={true}
                      label={"Last Name"}
                      fullWidth={true}
                      value={selectedUser.lastName || ""}
                      onChange={(event) => {
                        setSelectedUser({ ...selectedUser, lastName: event.target.value });
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label={"Email"}
                      fullWidth={true}
                      value={selectedUser.email || ""}
                      onChange={(event) => {
                        setSelectedUser({ ...selectedUser, email: event.target.value });
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <FormControl fullWidth>
                      <InputLabel>Role</InputLabel>
                      <Select
                        label={"Role"}
                        value={selectedUser.role || ""}
                        onChange={(event) => {
                          setSelectedUser({ ...selectedUser, role: event.target.value });
                        }}
                      >
                        <MenuItem value={"admin"}>Admin</MenuItem>
                        <MenuItem value={"client"}>Client</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Interest Rate (%)"
                      fullWidth
                      value={
                        newInterestRate ??
                        (Number.parseFloat(selectedUser.interestRate) * 100).toFixed(2)
                      }
                      onChange={(event) => {
                        setNewInterestRate(event.target.value);
                      }}
                      onBlur={() => {
                        if (newInterestRate != null) {
                          const interestRateNumber = Number.parseFloat(newInterestRate);
                          if (Number.isFinite(interestRateNumber)) {
                            // Round percentage to 2 decimal places
                            setNewInterestRate(
                              (Math.round(interestRateNumber * 100) / 100).toFixed(2),
                            );
                          }
                        }
                      }}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">%</InputAdornment>,
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    {enablePooledFund ? (
                      <TextField
                        label="Invested Amount (USD)"
                        fullWidth
                        value={selectedUser.usdInvested}
                        onChange={(event) => {
                          setSelectedUser({ ...selectedUser, usdInvested: event.target.value });
                        }}
                      />
                    ) : (
                      <Stack>
                        <FormGroup>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={selectedUser.earnsInterest}
                                onChange={(event) => {
                                  setSelectedUser({
                                    ...selectedUser,
                                    earnsInterest: event.target.checked,
                                  });
                                }}
                              />
                            }
                            label="Earns interest"
                          />
                        </FormGroup>
                        <Typography variant={"caption"}>
                          Interest will be calculated based on client&apos;s transactions time,
                          not from the time this was enabled.
                        </Typography>
                      </Stack>
                    )}
                  </Grid>

                  <Grid item xs>
                    <Button
                      fullWidth={true}
                      variant={"outlined"}
                      onClick={() => {
                        let interestRateNumber =
                          newInterestRate != null ? Number.parseFloat(newInterestRate) : null;
                        if (
                          interestRateNumber != null &&
                          !Number.isFinite(interestRateNumber)
                        ) {
                          toast({ message: "Invalid interest rate", severity: "error" });
                          return;
                        }
                        interestRateNumber =
                          interestRateNumber != null
                            ? Math.round(interestRateNumber * 100) / 10_000 // round rate to 4 decimal places
                            : null;
                        const interestRate =
                          interestRateNumber != null ? interestRateNumber.toFixed(4) : null;
                        const updatedUser =
                          interestRate != null
                            ? { ...selectedUser, interestRate }
                            : selectedUser;
                        updateUser(updatedUser)
                          .unwrap()
                          .then(() => {
                            toast({
                              message: "Updated user",
                              severity: "success",
                              timeout: 3000,
                            });
                          })
                          .catch((error: unknown) => {
                            toast({ message: getErrorDetail(error), severity: "error" });
                          });
                      }}
                    >
                      Update
                    </Button>
                  </Grid>

                  <Grid item xs>
                    <Button
                      fullWidth={true}
                      color={"error"}
                      variant={"outlined"}
                      onClick={() => {
                        deleteUser(selectedUser.id);
                        setSelectedUser(null);
                      }}
                    >
                      Delete
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </Stack>
      </Grid>

      <Grid item xs={5}>
        <Card>
          <CardHeader title="New User"></CardHeader>
          <CardContent>
            <Stack spacing={5}>
              <Input
                placeholder={"Email"}
                type={"email"}
                value={newUserEmail}
                onChange={(event) => {
                  setNewUserEmail(event.target.value);
                }}
                fullWidth={true}
                required
              />
              {enablePasswordLogin && (
                <Input
                  placeholder={"Password (optional)"}
                  type={"password"}
                  value={newUserPassword}
                  onChange={(event) => {
                    setNewUserPassword(event.target.value);
                  }}
                  fullWidth={true}
                />
              )}

              <Button
                disabled={!newUserEmail}
                variant={"outlined"}
                onClick={() => {
                  if (newUserEmail) {
                    console.log(newUserPassword);
                    if (enablePasswordLogin && newUserPassword) {
                      addUserWithPassword({
                        email: newUserEmail,
                        password: newUserPassword,
                      })
                        .unwrap()
                        .then(() => {
                          setNewUserEmail("");
                          setNewUserPassword("");
                          toast({ message: "Added user", severity: "success" });
                        })
                        .catch((error: unknown) =>
                          toast({ message: getErrorDetail(error), severity: "error" }),
                        );
                    } else {
                      addUser(newUserEmail)
                        .unwrap()
                        .then(() => {
                          setNewUserEmail("");
                          toast({ message: "Added user", severity: "success" });
                        })
                        .catch((error: unknown) =>
                          toast({ message: getErrorDetail(error), severity: "error" }),
                        );
                    }
                  }
                }}
              >
                Add
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
      <Toast {...toastProps} />
    </Grid>
  );
};

Clients.acl = {
  action: "manage",
  subject: "User",
};

export default Clients;
