# Variables shared by all cloud environments

variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "project_name" {
  description = "project name"
  type        = string
  nullable    = false
}

variable "project_uid" {
  description = "project UID from project ID"
  type        = string
  nullable    = false
}

variable "region" {
  description = "geographical location where to host resources"
  type        = string
  nullable    = false
}

variable "zone" {
  description = "primary region zone to use"
  type        = string
  nullable    = false
}

variable "zone_secondary" {
  description = "secondary region zone to use"
  type        = string
  nullable    = false
}

# HCP Terraform variables
# https://developer.hashicorp.com/terraform/cloud-docs/run/run-environment#environment-variables

variable "TFC_CONFIGURATION_VERSION_GIT_COMMIT_SHA" {
  description = "The full commit hash of the code"
  type        = string
  nullable    = false
}
