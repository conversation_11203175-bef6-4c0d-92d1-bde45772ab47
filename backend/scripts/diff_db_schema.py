"""Differential database schema and models comparator.

Exit with code 0 if the schema of the database is the same as the schema
of the models. Otherwise, output the differences and exit with code 1.
"""

import sys
from argparse import ArgumentParser
from pprint import pprint

from alembic.autogenerate import compare_metadata
from alembic.config import Config
from alembic.environment import EnvironmentContext, MigrationContext
from alembic.script import ScriptDirectory
from sqlalchemy.sql.schema import Index

arg_parser = ArgumentParser(description=__doc__)
arg_parser.add_argument("alembic_config", help="Path to Alembic config file", type=str)
arg_parser.add_argument(
    "--exclude-tables",
    help="Comma-separated list of tables to exclude from migration check",
    type=str,
)
args = arg_parser.parse_args()

alembic_cfg = Config(args.alembic_config)
script = ScriptDirectory.from_config(alembic_cfg)


def get_excluded_tables():
    if not args.exclude_tables:
        return set()
    return {table.strip().lower() for table in args.exclude_tables.split(",")}


def get_table_name_from_diff(diff_item):
    if isinstance(diff_item, tuple) and len(diff_item) > 1:
        item = diff_item[1]
        if isinstance(item, str):
            return item.lower()
        elif isinstance(item, Index):
            return item.table.name.lower()
        elif hasattr(item, "name"):
            return item.name.lower()
    return None


def run_diff(rev, migration_ctx: MigrationContext):
    excluded_tables = get_excluded_tables()
    diff = compare_metadata(migration_ctx, None)

    if diff:
        # Filter out differences for excluded tables
        filtered_diff = []
        for d in diff:
            table_name = get_table_name_from_diff(d)
            if table_name and table_name in excluded_tables:
                continue
            filtered_diff.append(d)

        if filtered_diff:
            pprint(filtered_diff, indent=2, width=100)
            print("Database schema and models differ")
            sys.exit(1)
    sys.exit(0)


with EnvironmentContext(alembic_cfg, script, fn=run_diff):
    script.run_env()
