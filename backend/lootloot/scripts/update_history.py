import logging
from datetime import datetime, timezone
from typing import Any

import httpx
from sqlalchemy import select, text, update
from sqlalchemy.orm import Session
from tenacity import (
    Retrying,
    before_sleep_log,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential_jitter,
)

from common.psql import SessionLocal
from lootloot.external.heist import HeistClient, HeistEvent
from lootloot.heist.crud import HeistClaimManager, HeistLocationManager
from lootloot.models import NFT, HeistClaim, RawHeistClaim

logger = logging.getLogger(__name__)


default_retry = Retrying(
    retry=retry_if_exception_type(httpx.HTTPError),
    wait=wait_exponential_jitter(),
    stop=stop_after_attempt(3),
    before_sleep=before_sleep_log(logger, logging.INFO),
)


def upsert_locations(client: HeistClient, db: Session):
    locations = client.get_locations()
    logger.info("Got %d locations", len(locations))

    location_manager = HeistLocationManager(db)
    for api_location in locations:
        with db.begin():
            result = location_manager.update_from_api(api_location)
            if result.rowcount == 0:
                location_manager.create_from_api(api_location)
                logger.info("Got new location '%s'", api_location.name)


def consume_claim_feed(client: HeistClient, db: Session):
    API_LIMIT = 1000
    DB_LIMIT = 1100  # preferably > API_LIMIT
    REQUIRED_MATCHES = 100  # should be <<< DB_LIMIT

    requested_at = datetime.now(timezone.utc)
    logger.info("Consuming heist claim feed at %s", requested_at)

    with db.begin():
        prev_claims = db.scalars(
            select(RawHeistClaim.data)
            .order_by(RawHeistClaim.requested_at.desc(), RawHeistClaim.id.asc())
            .limit(DB_LIMIT)
        )

    prev_claims_ids = [get_heist_claim_id(claim) for claim in prev_claims]
    prev_claims_ids = set(claim_id for claim_id in prev_claims_ids if claim_id is not None)
    logger.info("Using %d previous heist claims", len(prev_claims_ids))

    offset = 0
    is_last_request = False

    while not is_last_request:
        resp = client.get_claim_feed_raw(limit=API_LIMIT, offset=offset)

        body = resp.json()
        claims = body["claims"]
        assert isinstance(claims, list), f"Got {type(claims)} instead of list"
        logger.info("Got %d heist claims", len(claims))

        error_count = 0
        existing_count = 0
        added_count = 0
        with db.begin():
            for claim in claims:
                if not isinstance(claim, dict):
                    error_count += 1
                    logger.error(
                        "Got unexpected type for heist claim",
                        extra={"value": claim, "type": type(claim)},
                    )
                    continue
                claim_id = get_heist_claim_id(claim)
                if claim_id in prev_claims_ids:
                    existing_count += 1
                    continue
                db.add(RawHeistClaim(requested_at=requested_at, data=claim))
                added_count += 1

        logger.info(
            "Processed %d claims, added %d, existing %d, error %d",
            len(claims),
            added_count,
            existing_count,
            error_count,
        )

        offset += len(claims)

        if body["hasMore"]:
            is_last_request = existing_count >= REQUIRED_MATCHES
        else:
            is_last_request = True


HEIST_CLAIM_ID_KEYS = ("id", "startedAt", "endedAt", "nftId", "walletId", "locationId")


def get_heist_claim_id(claim: Any):
    if not isinstance(claim, dict):
        return None

    heist_claim = claim.get("heist")
    if not isinstance(heist_claim, dict):
        return None

    return tuple(heist_claim.get(key) for key in HEIST_CLAIM_ID_KEYS)


def process_raw_heist_claims(db: Session):
    logger.info("Starting to process heist claims")
    claim_manager = HeistClaimManager(db)
    while True:
        with db.begin():
            claims_to_process = db.scalars(
                select(RawHeistClaim)
                .where(RawHeistClaim.processed_id == None, ~RawHeistClaim.unprocessable)
                .order_by(RawHeistClaim.requested_at, RawHeistClaim.id.desc())
                .limit(100)
            ).all()
        if not claims_to_process:
            logger.info("Finished heist claims processing")
            break
        logger.info("Got %d heist claims to process", len(claims_to_process))
        for raw_claim in claims_to_process:
            with db.begin():
                claim_manager.process_raw(raw_claim)


def process_rekt_nfts(db: Session) -> None:
    logger.info("Flagging rekt NFTs with off_chain_data")
    with db.begin():
        db.execute(
            update(NFT)
            .values({NFT.off_chain_data: text("jsonb_set(off_chain_data, '{rekt}', 'true')")})
            .where(
                NFT.id.in_(
                    select(NFT.id)
                    .join(HeistClaim, NFT.address_uri == HeistClaim.nft_address_uri_sql())
                    .where(
                        HeistClaim.event_id == HeistEvent.rekt,
                        ~NFT.is_rekt_sql(),
                    )
                )
            )
        )


def main():
    with (
        SessionLocal() as db,
        HeistClient(retry=default_retry) as client,
    ):
        client.authenticate_with_keypairs_file()
        upsert_locations(client, db)
        consume_claim_feed(client, db)
        process_raw_heist_claims(db)
        process_rekt_nfts(db)


main()
