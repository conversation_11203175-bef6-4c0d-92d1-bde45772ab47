import { disableUserAgreement } from "@/context/env";
import { useAppDispatch } from "@/hooks/store";
import { useAuth } from "@/hooks/useAuth";
import UserLayout from "@/layouts/UserLayout";
import { setUser } from "@/store/apps/auth";
import { useAuthUserAcceptAgreementMutation } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import {
  Alert,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  FormGroup,
  Link,
  List,
  ListItem,
  ListProps,
  Stack,
  Typography,
  styled,
} from "@mui/material";
import React, { ReactNode, useState } from "react";

export function AgreementGuard({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  const [accepted, setAccepted] = useState(false);
  const [error, setError] = useState("");

  const [submitAccept, submitAcceptState] = useAuthUserAcceptAgreementMutation();

  if (
    disableUserAgreement ||
    user == null ||
    user.agreementAcceptedAt ||
    user.role === "admin"
  ) {
    return children;
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!accepted) {
      setError("You must accept the terms and conditions to continue.");
      return;
    }

    submitAccept()
      .unwrap()
      .then((updatedUser) => {
        dispatch(setUser(updatedUser));
      })
      .catch((error: unknown) => {
        setError(
          getErrorDetail(error, {
            fallback: "An error occurred while accepting the agreement.",
          }),
        );
      });
  };

  return (
    <UserLayout>
      <Dialog open scroll="paper" aria-labelledby="terms-of-service-title" maxWidth="md">
        <DialogTitle id="terms-of-service-title">Terms of Service</DialogTitle>

        <DialogContent dividers>
          <Typography>
            These Terms of Service (referred to as the “agreement”) explain the terms and
            conditions by which you may access and use this service (https://stabletech.capital)
            provided by Stable Tech LP (referred to as “<strong>Stable Tech</strong>”, “
            <strong>we</strong>”, “<strong>our</strong>”, or “<strong>us</strong>”). Reading
            this agreement will help you understand your rights and choices. If you still have
            any questions or concerns, please contact us at{" "}
            <Link href="mailto:<EMAIL>">
              <strong><EMAIL></strong>
            </Link>
            .
          </Typography>
          <StyledList component="ol">
            <NumberedItem>
              <ListItemHeading>DEPOSITS</ListItemHeading>
              <Typography>
                A user, herein referred to as the “Lender”, is authorized to deposit supported
                assets with our platform. Stable Tech, in turn, assumes the role of the
                “Borrower” for these assets. The initiation of the deposit transaction by the
                Lender marks the “Start Date” as stipulated herein. Following the successful
                clearance of the deposit, the Lender can access a comprehensive overview of the
                deposited assets via their personalized “Dashboard”.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>INTEREST</ListItemHeading>
              <Typography>
                The Lender’s Dashboard will indicate the interest accruing on the Borrowed
                Assets at a rate of 8 percent (%) per annum, referred to as the “Interest Rate”.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>PAYMENTS</ListItemHeading>
              <Typography>
                All accrued interest on the Borrowed Assets is due and shall be paid to the
                Lender every year from the Start Date. In addition, assets that are not paid by
                the Borrower on time for any installment will continue to be charged the
                Interest Rate stated in this agreement.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>WITHDRAWALS</ListItemHeading>
              <Typography>
                The Lender may request to withdraw, in part or in whole, their principal assets
                at any time, at which point the Borrower shall repay the principal within 2
                weeks of the request.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>LOAN</ListItemHeading>
              <Typography>
                The Borrower agrees to provide Stable Tech LP assets and holdings, referred to
                as the “Loan”, which shall transfer to the possession and ownership of the
                Lender IMMEDIATELY if this agreement should be in default. The Loan may not be
                sold or transferred without the Lender’s consent during the course of this
                agreement. If the Borrower breaches this provision, the Lender may declare all
                sums due under this agreement immediately due and payable, unless prohibited by
                applicable law.
              </Typography>
              <Typography mt={3}>
                If the Borrower defaults under this agreement, the Lender shall have the right
                to obtain ownership and possession of the Loan. The Lender shall have the sole
                option to accept it as full payment for the Borrowed Assets without further
                liabilities or obligations. If the market value of the Loan is below the market
                value of the Borrowed Assets, the Borrower shall remain liable for the balance
                due.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>INTEREST DUE IN THE EVENT OF DEFAULT</ListItemHeading>
              <Typography>
                In the event the Borrower fails to pay the Borrowed Assets in full upon a
                withdrawal request, the unpaid principal shall accrue interest at the same 8%
                interest rate per annum until the Borrower is no longer in default.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>ALLOCATION OF PAYMENTS</ListItemHeading>
              <Typography>
                Payments shall be first credited to any interest due, then any remainder will be
                credited to principal.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>ACCELERATION</ListItemHeading>
              <Typography>
                If the Borrower is in default under this agreement, and such default is not
                cured within the minimum allotted time by law after written notice of such
                default, then Lender may, at its option, declare all outstanding sums owed on
                this agreement to be immediately due and payable. This includes rights of
                possession to the Loan mentioned in Section 5.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>ATTORNEYS’ FEES AND COSTS</ListItemHeading>
              <Typography>
                Borrower shall pay all costs incurred by Lender in collecting sums due under
                this agreement after a default, including reasonable attorneys’ fees. If Lender
                or Borrower sues to enforce this agreement or obtain a declaration of its rights
                hereunder, the prevailing party in any such proceeding shall be entitled to
                recover its reasonable attorneys’ fees and costs incurred in the proceeding
                (including those incurred in any bankruptcy proceeding or appeal) from the
                non-prevailing party.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>WAIVER OF PRESENTMENTS</ListItemHeading>
              <Typography>
                Borrower waives presentment for payment, a notice of dishonor, protest, and
                notice of protest.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>NON-WAIVER</ListItemHeading>
              <Typography>
                No failure or delay by Lender in exercising Lender’s rights under this agreement
                shall be considered a waiver of such rights.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>SEVERABILITY</ListItemHeading>
              <Typography>
                In the event that any provision herein is determined to be void or unenforceable
                for any reason, such determination shall not affect the validity or
                enforceability of any other provision, all of which shall remain in full force
                and effect.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>INTEGRATION</ListItemHeading>
              <Typography>
                There are no verbal or other agreements that modify or affect the terms of this
                agreement. This agreement may be amended by a written agreement signed by
                Borrower and Lender.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>CONFLICTING TERMS</ListItemHeading>
              <Typography>
                The terms of this agreement shall have authority and precedence over any
                conflicting terms in any referenced agreement or document.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>NOTICE</ListItemHeading>
              <Typography>
                Any notices required or permitted to be given hereunder shall be given in
                writing and shall be delivered by email.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>EXECUTION</ListItemHeading>
              <Typography>
                The Borrower executes this agreement as a principal and not as a surety.
              </Typography>
            </NumberedItem>
            <NumberedItem>
              <ListItemHeading>GOVERNING LAW</ListItemHeading>
              <Typography>
                This note shall be governed under the laws in the State of California.
              </Typography>
            </NumberedItem>
          </StyledList>
        </DialogContent>

        <DialogActions>
          <Stack
            mt={3}
            gap={3}
            width="100%"
            alignItems="start"
            component="form"
            onSubmit={handleSubmit}
          >
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={accepted}
                    onChange={(event) => {
                      setError("");
                      setAccepted(event.target.checked);
                    }}
                  />
                }
                label="I accept the above terms and conditions"
              />
            </FormGroup>
            <Stack direction="row" gap={4} alignItems="center">
              <Button
                type="submit"
                disabled={submitAcceptState.isLoading}
                variant="contained"
                size="small"
                sx={{ height: "32px" }}
              >
                Submit
              </Button>
              {error && (
                <Alert
                  severity="error"
                  onClose={() => {
                    setError("");
                  }}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    py: 0,
                    ".MuiAlert-icon, .MuiAlert-message, .MuiAlert-action": {
                      py: "1px",
                    },
                  }}
                >
                  {error}
                </Alert>
              )}
            </Stack>
          </Stack>
        </DialogActions>
      </Dialog>
    </UserLayout>
  );
}

const StyledList = styled(List)<ListProps>(({ theme }) => ({
  listStyle: "decimal",
  paddingLeft: theme.spacing(10),
}));

const NumberedItem = styled(ListItem)(() => ({
  display: "list-item",
}));

const ListItemHeading = styled(Typography)(({ theme }) => ({
  textDecoration: "underline",
  textUnderlineOffset: 3,
  marginBottom: theme.spacing(3),
}));
