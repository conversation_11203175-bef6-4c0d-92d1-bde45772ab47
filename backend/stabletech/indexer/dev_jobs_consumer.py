import logging
import subprocess
import sys
import time
from pathlib import Path

from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CodeChangeHandler(FileSystemEventHandler):
    def __init__(self):
        self.process = None
        self.restart_process()

    def restart_process(self):
        if self.process:
            logger.info("Terminating existing process...")
            self.process.terminate()
            self.process.wait()

        logger.info("Starting jobs_consumer.py...")
        self.process = subprocess.Popen(
            [sys.executable, "-m", "stabletech.indexer.jobs_consumer"],
            cwd=str(Path(__file__).parent.parent.parent),
        )

    def on_modified(self, event):
        if event.src_path.endswith(".py"):
            logger.info(f"Detected change in {event.src_path}")
            self.restart_process()


def main():
    logger.info("Starting development server with auto-reload...")

    # Create an observer and event handler
    event_handler = CodeChangeHandler()
    observer = Observer()

    # Get the backend directory path
    backend_dir = Path(__file__).parent.parent.parent

    # Start watching the backend directory
    observer.schedule(event_handler, str(backend_dir), recursive=True)
    observer.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down...")
        if event_handler.process:
            event_handler.process.terminate()
            event_handler.process.wait()
        observer.stop()
    observer.join()


if __name__ == "__main__":
    main()
