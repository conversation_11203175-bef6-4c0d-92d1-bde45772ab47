/**
 * This file was automatically generated by running common/pydantic2ts.py.
 * Do not modify it by hand - just update the pydantic models and then re-run the script
 */

export interface Credential {
  id: string;
  name: string;
  created_at: string;
  latest_use_at: string;
  use_for: "2fa" | "wrap-key";
}
export interface Credential2FaResponse {
  kind: "2fa";
  token: string;
}
export interface CredentialPatch {
  name: string;
}
export interface CredentialWrapKeyResponse {
  kind: "wrap-key";
  secret_key: string;
}
export interface CubistCreateKeyRequest {
  key_type: string;
}
export interface CubistIdentityProof {
  aud: string;
  email: string;
  exp_epoch: number;
  id: string;
  identity: CubistOidcIdentity;
  user_info: CubistOidcUserInfo | null;
}
export interface CubistOidcIdentity {
  iss: string;
  sub: string;
}
export interface CubistOidcUserInfo {
  configured_mfa: CubistMFA[];
  initialized: boolean;
  name: string;
  user_id: string;
}
export interface CubistMFA {
  type: string;
}
export interface CubistRegisterOidcUserResp {
  user_id: string;
}
export interface IndexTransfersResp {
  transfers_processed: number;
}
export interface LoginResponse {
  expected_2fa: unknown;
  token: string | null;
  user: User;
}
export interface User {
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  picture_url?: string | null;
}
export interface NonPaginatedRespTransfer {
  items: Transfer[];
}
export interface Transfer {
  id: string;
  block_number: string;
  to_address: string;
  from_address: string;
  value: string;
  contract_address: string;
  network: string;
}
export interface OidcLogin {
  id_token: string;
  access_token: string;
}
export interface TransfersResp {
  items: Transfer[];
}
export interface WrapKeyAuthOptionsRequest {
  credential_id: string;
}
export interface WrapKeyAuthOptionsResponse {
  auth_options: unknown;
}
