import { setDefaultToken, tokenInvalidTopic } from "@/lib/api";
import { User } from "@/types/schemas";
import { createContext, useCallback, useEffect, useState } from "react";
import * as v from "valibot";

interface AuthParams {
  readonly user: User;
  readonly token: string;
}

interface AuthContextValue {
  readonly user: User | null;
  readonly set: (params: AuthParams | null) => void;
}

export const AuthContext = createContext<AuthContextValue>({
  user: null,
  set: () => {
    throw new Error("AuthContext not initialized");
  },
});
AuthContext.displayName = "Auth";

interface AuthProviderProps {
  readonly initialUser: User | null;
  readonly children: React.ReactNode;
}

export function AuthProvider(props: AuthProviderProps) {
  const { initialUser, children } = props;

  const [user, _setUser] = useState<User | null>(initialUser);

  // Keep the auth data in sync between tabs/windows
  const setUser = useCallback((broadcast: boolean, params: AuthParams | null) => {
    if (broadcast) {
      getBroadcastChannel().postMessage(params);
    }
    _setUser(params?.user ?? null);
    setDefaultToken(params?.token ?? "");
  }, []);

  // Keep the auth data in sync between tabs/windows
  useEffect(() => {
    const broadcastChannel = getBroadcastChannel();
    const messageHandler = (event: MessageEvent<AuthParams | null>) => {
      setUser(false, event.data);
    };
    broadcastChannel.addEventListener("message", messageHandler);
    return () => {
      broadcastChannel.removeEventListener("message", messageHandler);
    };
  }, [setUser]);

  // Exposed setter for login/logout flows
  const setContext = useCallback(
    (params: AuthParams | null) => {
      setUser(true, params);
      saveData(params);
    },
    [setUser],
  );

  // Logout when the token becomes invalid
  useEffect(() => {
    return tokenInvalidTopic.subscribe(() => {
      setContext(null);
    });
  }, [setContext]);

  return <AuthContext value={{ user, set: setContext }}>{children}</AuthContext>;
}

const optionalString = v.exactOptional(v.nullable(v.string()));

const UserSchema = v.object({
  email: v.string(),
  first_name: optionalString,
  last_name: optionalString,
  picture_url: optionalString,
}) satisfies v.GenericSchema<User>;

const StoredAuthDataSchema = v.object({
  user: UserSchema,
  token: v.string(),
});

const STORAGE_KEY = "auth.data";

type StoredAuthData = v.InferOutput<typeof StoredAuthDataSchema>;

export function loadStoredAuthData(): StoredAuthData | null {
  const storedData = localStorage.getItem(STORAGE_KEY);
  if (!storedData) return null;

  const result = v.safeParse(StoredAuthDataSchema, JSON.parse(storedData));
  return result.success ? result.output : null;
}

type StoredAuthDataInput = v.InferInput<typeof StoredAuthDataSchema>;

function saveData(data: StoredAuthDataInput | null): void {
  if (data == null) {
    localStorage.removeItem(STORAGE_KEY);
  } else {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  }
}

let _broadcastChannel: BroadcastChannel | undefined;

function getBroadcastChannel() {
  _broadcastChannel ??= new BroadcastChannel("auth");
  return _broadcastChannel;
}
