import { getCubeSignerAuthStore } from "@/auth/cubesigner-auth-store";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { Label } from "@/lib/components/form";
import { Input } from "@/lib/components/input";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { useMutation } from "@tanstack/react-query";

export function CubistTOTP() {
  const authStore = getCubeSignerAuthStore();

  const totpSubmission = useMutation({ mutationFn: authStore.submitTotp });

  return (
    <div className="mx-auto max-w-sm">
      <form
        className="grid gap-4"
        onSubmit={(e) => {
          e.preventDefault();
          const code = new FormData(e.currentTarget).get("code") as string;
          totpSubmission.mutate(code);
        }}
      >
        <div>
          <Label htmlFor="totp-code">CubeSigner Authentication Code</Label>
          <Input
            id="totp-code"
            name="code"
            type="text"
            pattern="[0-9]*"
            inputMode="numeric"
            autoComplete="one-time-code"
            required
            maxLength={6}
            className="text-center tracking-widest"
            placeholder="000000"
          />
        </div>
        {totpSubmission.error && (
          <AlertError>Error verifying code: {getErrorMessage(totpSubmission.error)}</AlertError>
        )}
        <Button type="submit" disabled={totpSubmission.isPending} className="w-full">
          {totpSubmission.isPending ? <Spinner className="mx-auto" size={20} /> : "Verify"}
        </Button>
      </form>
    </div>
  );
}
