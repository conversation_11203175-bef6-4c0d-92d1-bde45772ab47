"""
NFT Register

- Gets all stored NFTCollections.
- For each NFTCollection we fetch the entire collection 1000 NFTs at a time.
- Fetched NFTs are upserted on the database.
"""

import httpx
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_random_exponential

from common.conf import logger
from common.helius.client import helius_client
from common.helius.models import CollectionResponse
from common.psql import SessionLocal
from lootloot.nfts.crud import NFTCollectionManager, NFTManager


@retry(
    retry=retry_if_exception_type(httpx.HTTPError),
    wait=wait_random_exponential(multiplier=2, max=30),
    stop=stop_after_attempt(20),
)
def get_assets_by_group_call(collection: str, page: int = 1) -> CollectionResponse:
    return helius_client.get_assets_by_group(collection, page=page)


def main() -> None:
    with SessionLocal() as db:
        nft_manager = NFTManager(db)
        nft_collection_manager = NFTCollectionManager(db)
        try:
            nft_collections = nft_collection_manager.get_all()
            for nft_collection in nft_collections:
                try:
                    data = get_assets_by_group_call(nft_collection.address)
                    nft_manager.save_nfts_from_helius(nft_collection, data.result.items)
                    processed_nfts_address_uris = [
                        f"solana:{nft.id}" for nft in data.result.items
                    ]
                    if data.result.grand_total == 0 or len(data.result.items) == 0:
                        logger.info(
                            "No NFTs found for collection",
                            extra={"nft_collection": nft_collection.uid},
                        )
                        continue

                    grand_total = data.result.grand_total
                    page = data.result.page
                    while grand_total > len(processed_nfts_address_uris):
                        page += 1
                        data = get_assets_by_group_call(nft_collection.address, page=page)
                        nft_manager.save_nfts_from_helius(nft_collection, data.result.items)
                        processed_nfts_address_uris.extend(
                            f"solana:{nft.id}" for nft in data.result.items
                        )

                        # Update grand_total (it could change while fetching pages)
                        grand_total = data.result.grand_total

                    # Clean up NFTs that are no longer in the collection
                    nft_count = nft_manager.get_nft_collection_count(nft_collection.uid)
                    if nft_count > grand_total:
                        nft_manager.delete_missing_nfts(processed_nfts_address_uris)

                except Exception as e:
                    logger.exception(e, extra={"nft_collection": nft_collection.uid})
                    continue
        except Exception as e:
            logger.exception(e)


main()
