import {
  useClientNetworksQuery,
  useIndexerNetworksQuery,
  useIndexerNetworksWithAdminWalletsQuery,
} from "@/store/services/api";
import { Network } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { Dispatch, SyntheticEvent } from "react";

interface NetworkSearchProps {
  isAdmin?: boolean;
  label?: string;
  networksToSearch?: Network[];
  selectedNetwork: Network | null;
  setSelectedNetwork: Dispatch<Network | null>;
}

export const NetworkSearch = (props: NetworkSearchProps) => {
  const { data: adminNetworks } = useIndexerNetworksQuery(undefined, {
    skip: !props.isAdmin || !!props.networksToSearch,
  });
  const { data: clientNetworks } = useClientNetworksQuery(undefined, {
    skip: props.isAdmin || !!props.networksToSearch,
  });

  const label = props.label || "Network";

  return (
    <Autocomplete
      fullWidth
      getOptionLabel={(option) => option.name}
      renderInput={(params) => <TextField {...params} label={label} />}
      options={
        props.networksToSearch || (props.isAdmin && adminNetworks) || clientNetworks || []
      }
      onChange={(event: unknown, newNetwork: Network | null) => {
        props.setSelectedNetwork(newNetwork);
      }}
      noOptionsText={"No search results"}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      value={props.selectedNetwork}
    />
  );
};

interface NetworkSearchWithAdminWalletsProps {
  selectedNetwork: Network | null;
  setSelectedNetwork: Dispatch<Network | null>;
}

export const NetworkSearchWithAdminWallets = (props: NetworkSearchWithAdminWalletsProps) => {
  const { data: networks } = useIndexerNetworksWithAdminWalletsQuery();

  return (
    <Autocomplete
      fullWidth
      getOptionLabel={(option) => option.name}
      renderInput={(params) => <TextField {...params} label="Network" />}
      options={networks || []}
      onChange={(_: SyntheticEvent, newNetwork: Network | null) => {
        props.setSelectedNetwork(newNetwork);
      }}
      noOptionsText={"No search results"}
      isOptionEqualToValue={(option: Network, value: Network) => option.id === value.id}
      value={props.selectedNetwork}
    />
  );
};
