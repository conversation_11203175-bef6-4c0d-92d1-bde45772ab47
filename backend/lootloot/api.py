import logging

from fastapi import FastAP<PERSON>, Response

import lootloot.admin.api
import lootloot.auth.api
import lootloot.auth.profile_api
import lootloot.heist.api
import lootloot.holders.api
import lootloot.nfts.api
import lootloot.public.api
import lootloot.wallets.api
from lootloot.auth.fastapi import depends_authentication, depends_current_admin

app = FastAPI()
app.include_router(
    lootloot.admin.api.router,
    prefix="/admin",
    tags=["admin"],
    dependencies=[depends_current_admin],
)
app.include_router(
    lootloot.auth.api.router,
    prefix="/auth",
    tags=["auth"],
)
app.include_router(
    lootloot.auth.profile_api.router,
    prefix="/profile",
    tags=["profile"],
    dependencies=[depends_authentication],
)
app.include_router(
    lootloot.heist.api.router,
    prefix="/heist",
    tags=["heist"],
    dependencies=[depends_authentication],
)
app.include_router(
    lootloot.nfts.api.router,
    prefix="/nfts",
    tags=["nfts"],
    dependencies=[depends_authentication],
)
app.include_router(
    lootloot.holders.api.router,
    prefix="/holders",
    tags=["holders"],
)
app.include_router(
    lootloot.public.api.router,
    prefix="/public",
    tags=["public"],
)
app.include_router(
    lootloot.wallets.api.router,
    prefix="/wallets",
    tags=["wallets"],
    dependencies=[depends_authentication],
)


@app.get("/")
async def health_check():
    return Response(status_code=200)  # empty response


class AccessLoggerFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        method = record.args[1]  # type: ignore
        route = record.args[2]  # type: ignore
        # filter-out health checks
        return not (method == "GET" and route == "/")


logging.getLogger("uvicorn.access").addFilter(AccessLoggerFilter())

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("lootloot.api:app", host="localhost", port=8080, reload=True)
