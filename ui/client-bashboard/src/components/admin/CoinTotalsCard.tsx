import { CoinTotalProcessed, getCoinDisplayName } from "@/helpers/coin";
import { currencyFormatter } from "@/helpers/formatters";
import { Card, CardContent, CardHeader } from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { useCallback, useMemo } from "react";

type Row = CoinTotalProcessed;

interface Props {
  readonly title: string;
  readonly rows: readonly Row[];
  readonly selectedRowId?: number;
  readonly onRowClick: (coin: Row["coin"]) => void;
}

const INITIAL_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "coin", sort: "asc" }] },
};

export function CoinTotalsCard(props: Props) {
  const { title, rows, selectedRowId, onRowClick } = props;

  const columns = useMemo(computeColumns, []);

  const handleRowClick = useCallback(
    ({ row }: { row: Row }) => {
      onRowClick(row.coin);
    },
    [onRowClick],
  );

  return (
    <Card>
      <CardHeader title={title} />
      <CardContent>
        <DataGridPro
          autoHeight
          columns={columns}
          rows={rows}
          initialState={INITIAL_STATE}
          selectionModel={selectedRowId}
          onRowClick={handleRowClick}
          getRowId={getRowId}
        />
      </CardContent>
    </Card>
  );
}

function computeColumns(): GridColDef<Row, string | number>[] {
  return [
    {
      field: "coin",
      headerName: "Coin",
      flex: 1,
      valueGetter: ({ row }) => getCoinDisplayName(row.coin),
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row }) => currencyFormatter.format(row.amount),
    },
    {
      field: "usdAmount",
      headerName: "$ Value",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => row.amount * row.coin.latestUsdPrice,
      renderCell: ({ value }) => currencyFormatter.format(value as number),
    },
    {
      field: "price",
      headerName: "$ Market Price",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => row.coin.latestUsdPrice,
      renderCell: ({ row }) => currencyFormatter.format(row.coin.latestUsdPrice),
    },
  ];
}

function getRowId(row: Row) {
  return row.coin.id;
}
