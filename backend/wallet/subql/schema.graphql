# To improve query performance, we strongly suggest adding indexes to any field that you plan to filter or sort by
# Add the `@index` or `@index(unique: true)` annotation after any non-key field
# https://academy.subquery.network/build/graphql.html#indexing-by-non-primary-key-field

type Transfer @entity {
  id: ID! # sha256(network-blockNumber-logIndex-txHash-fromAddress-toAddress)
  tx_hash: String! @index # Transaction hash
  blockNumber: BigInt
  to_address: String! @index
  from_address: String! @index
  value: BigInt!
  contractAddress: String!
  network: String!
  timestamp: BigInt! @index # Block timestamp
}
