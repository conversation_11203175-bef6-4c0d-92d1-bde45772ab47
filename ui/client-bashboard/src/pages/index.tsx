// ** React Imports
import { useEffect } from "react";

// ** Next Imports
import { useRouter } from "next/router";

// ** Spinner Import
import Spinner from "@/@core/components/spinner";

// ** Hook Imports
import { useAuth } from "@/hooks/useAuth";

const Home = () => {
  // ** Hooks
  const auth = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (auth.user && router.isReady) {
      // Redirect user to Dashboard
      router.replace("/dashboard");
    }
  }, [auth.user, router]);

  return <Spinner />;
};

export default Home;
