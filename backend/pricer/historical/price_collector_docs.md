# Price Data Collection System

This document describes the implementation of the price data collection system, which fetches
cryptocurrency price data from CoinGecko API and stores it in our historical price database.

## Overview

The price collection system is responsible for:

- Fetching historical price data from CoinGecko API at regular intervals
- Storing price data in the `CoinHistoricalPrice` table
- Managing collection locks to prevent duplicate work across multiple processes
- Handling API rate limits and failures gracefully
- Providing monitoring and alerting for collection failures

## Key Features

### Distributed Locking

- Each coin can only be processed by one instance at a time
- Uses PostgreSQL's `SELECT ... FOR UPDATE NOWAIT` for non-blocking locks
- Automatically creates lock records for new coins

### Intelligent Data Collection

- Only fetches data newer than the latest stored price point
- Falls back to 1-day lookback for coins with no historical data
- Respects 30-minute collection intervals to avoid unnecessary API calls

### Error Handling and Monitoring

- Tracks failure counts per coin in the database
- Log errors when failure count reaches threshold (5 failures)

### Processing

- Introduces random delays between batches to avoid thundering herd
- Concurrent processing between pricer service instances

## Configuration

### Active Coins List

Currently hard-coded in `price_collector.py`:

```python
ACTIVE_COINS = [
    "bitcoin",
    "ethereum",
    "binancecoin",
    "solana",
    "cardano",
    "avalanche-2",
    "polygon",
    "chainlink",
    "uniswap",
    "litecoin",
]
```

### Collection Parameters

- **Collection Interval**: 30 minutes
- **Max Failure Count**: 5 failures for alerting
- **Default Lookback**: 1 day for new coins
