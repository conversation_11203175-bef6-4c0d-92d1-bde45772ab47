"use client";
import { apiQuery } from "@/lib/api";
import { AlertError } from "@/lib/components/alert";
import { renderTable, sortableHeader } from "@/lib/components/data-table";
import { InputClearable } from "@/lib/components/input";
import { ScrollArea } from "@/lib/components/scroll-area";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { ListUsersResp, User } from "@/types/schemas";
import { useQuery } from "@tanstack/react-query";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";

export function UsersTable() {
  const usersQuery = useQuery({
    queryKey: ["admin", "users"],
    queryFn: apiQuery<ListUsersResp>,
  });

  return usersQuery.isLoading ? (
    <div className="p-10">
      <Spinner priority size={50} />
    </div>
  ) : usersQuery.isError ? (
    <AlertError>{getErrorMessage(usersQuery.error)}</AlertError>
  ) : (
    <UsersDataTable users={usersQuery.data?.items ?? []} />
  );
}

function computeColumns(): ColumnDef<User>[] {
  return [
    {
      accessorKey: "email",
      header: sortableHeader("Email"),
    },
    {
      id: "name",
      accessorFn: (user) => `${user.first_name ?? ""} ${user.last_name ?? ""}`.trim(),
      header: sortableHeader("Name"),
    },
    {
      accessorKey: "is_admin",
      header: sortableHeader("Admin"),
    },
  ];
}

function UsersDataTable({ users }: { users: User[] }) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const columns = useMemo(computeColumns, []);
  const table = useReactTable({
    data: users,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  const filterValue = (table.getColumn("email")?.getFilterValue() ?? "") as string;
  const filterInput = (
    <InputClearable
      placeholder="Filter by email"
      value={filterValue}
      onChange={(event) => {
        table.getColumn("email")?.setFilterValue(event.target.value);
      }}
      onClear={() => {
        table.getColumn("email")?.setFilterValue("");
      }}
      clearHidden={!filterValue}
    />
  );

  return (
    <div className="flex flex-col gap-4">
      {filterInput}
      <ScrollArea className="w-full">
        <div className="min-w-[420px] rounded-md border">{renderTable(columns, table)}</div>
      </ScrollArea>
    </div>
  );
}
