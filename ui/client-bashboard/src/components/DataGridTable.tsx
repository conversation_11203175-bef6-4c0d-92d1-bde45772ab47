import { formatDecimal } from "@/helpers/formatters";
import Check<PERSON>con from "@mui/icons-material/Check";
import ClearIcon from "@mui/icons-material/Clear";
import { Stack } from "@mui/material";
import {
  DataGridPro,
  GridCellParams,
  GridColDef,
  GridEventListener,
  GridEvents,
  GridToolbarContainer,
  GridToolbarFilterButtonProps,
  GridToolbarFilterButton as brokenTypes_GridToolbarFilterButton,
} from "@mui/x-data-grid-pro";
import * as React from "react";

export interface TableColumns<R extends object = object> {
  editable?: boolean;
  field: string;
  header: string;
  isDecimal?: boolean;
  maxWidth?: number;
  renderCell?: (params: GridCellParams<unknown, R>) => React.ReactNode;
  sortable?: boolean;
  sortByField?: string;
  type?: string;
}

export interface DataGridTableProps<R extends object> {
  hideIdColumn?: boolean;
  onRowClick?: GridEventListener<GridEvents.cellClick>;
  processRowUpdate?: ((newRow: R, oldRow: R) => R | Promise<R>) | undefined;

  rows: readonly R[];
  sortByField?: string;
  sortDescending?: boolean;
  tableColumns: TableColumns<R>[];
}

// This is a hack to fix the broken types in GridToolbarFilterButton
// Proper fix is to upgrade the major version of @mui/x-data-grid-pro
const GridToolbarFilterButton =
  brokenTypes_GridToolbarFilterButton as React.ForwardRefExoticComponent<
    GridToolbarFilterButtonProps & React.RefAttributes<HTMLButtonElement>
  >;

const CustomToolbar: React.FunctionComponent<{
  setFilterButtonEl: React.Dispatch<React.SetStateAction<HTMLButtonElement | null>>;
}> = ({ setFilterButtonEl }) => (
  <GridToolbarContainer>
    <GridToolbarFilterButton ref={setFilterButtonEl} />
  </GridToolbarContainer>
);

export function DataGridTable<R extends object>(props: DataGridTableProps<R>) {
  const [filterButtonEl, setFilterButtonEl] = React.useState<HTMLButtonElement | null>(null);

  const hideIdColumn = props.hideIdColumn;
  const tableColumns = props.tableColumns;
  const tableRows = props.rows;

  const getRowId = React.useMemo(() => {
    if (
      !tableColumns.some((tableColumn) => tableColumn.field == "id") &&
      tableRows.some((row) => !("id" in row))
    ) {
      // calculate unique ID for each row and return a function to get it
      const weakMap = new WeakMap<R, number>();
      let id = 0;
      for (const row of tableRows) {
        weakMap.set(row, id++);
      }
      return (row: R) => weakMap.get(row) ?? -1;
    }
    return;
  }, [tableColumns, tableRows]);

  const gridTableColumns: GridColDef[] = React.useMemo(
    () =>
      tableColumns.map((value) => {
        if (value.type && value.type == "boolean") {
          value.renderCell = (params: GridCellParams) =>
            params.value ? <CheckIcon /> : <ClearIcon />;
        }
        const tableColumn: GridColDef = {
          align: "center",
          editable: value.editable || false,
          field: value.field,
          flex: 1,
          headerName: value.header,
          headerAlign: "center",
          maxWidth: value.maxWidth,
          minWidth: 130,
          renderCell:
            value.renderCell ||
            (value.isDecimal ? (params) => formatDecimal(params.value) : undefined),
          sortable: value.sortable !== false,
          type: value.type || "string",
        };
        if (value.sortByField) {
          tableColumn.sortComparator = (a, b) =>
            a[value.sortByField as string].localeCompare(b[value.sortByField as string]);
        }

        return tableColumn;
      }),
    [tableColumns],
  );

  return (
    <DataGridPro
      autoHeight
      columns={gridTableColumns}
      columnVisibilityModel={hideIdColumn ? { id: false } : undefined}
      disableSelectionOnClick
      experimentalFeatures={props.processRowUpdate ? { newEditingApi: true } : undefined}
      rowThreshold={0}
      getRowHeight={() => "auto"}
      getRowId={getRowId}
      initialState={
        (props.sortByField && {
          sorting: {
            sortModel: [
              { field: props.sortByField, sort: props.sortDescending ? "desc" : "asc" },
            ],
          },
        }) ||
        {}
      }
      onRowClick={props.onRowClick}
      processRowUpdate={props.processRowUpdate}
      rows={tableRows}
      components={{
        NoRowsOverlay: () => (
          <Stack height="100%" alignItems="center" justifyContent="center">
            Empty
          </Stack>
        ),
        Toolbar: CustomToolbar,
      }}
      componentsProps={{
        panel: {
          anchorEl: filterButtonEl,
        },
        toolbar: {
          setFilterButtonEl,
        },
      }}
      sx={{
        alignContent: "center",
        paddingLeft: 2,
        textAlign: "center",
      }}
    />
  );
}
