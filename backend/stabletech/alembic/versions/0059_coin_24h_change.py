"""Coin 24h Change

Revision ID: 0059
Revises: 0058
Create Date: 2024-12-04 07:23:36.250180

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0059"
down_revision = "0058"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("coin", sa.Column("usd_24h_change", sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("coin", "usd_24h_change")
    # ### end Alembic commands ###
