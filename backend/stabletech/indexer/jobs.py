from sqlalchemy.orm import Session

from stabletech import schemas
from stabletech.indexer.covalent.queries import sync_transactions as sync_transactions_covalent
from stabletech.indexer.crud import WalletManager
from stabletech.indexer.helius.queries import save_solana_transactions
from stabletech.indexer.mintscan.queries import sync_transactions as sync_transactions_mintscan
from stabletech.indexer.models import Job


def create_wallet_sync(wallet_id: int, *, initial: bool = False) -> Job:
    params = schemas.WalletSyncJobParams(wallet_id=wallet_id, initial=initial)
    return Job.create(
        kind="sync_wallet",
        data=params.model_dump(mode="json", round_trip=True),
    )


def sync_wallet(db: Session, job: Job) -> str | schemas.WalletSyncJobResult:
    assert job.kind == "sync_wallet", f"Unexpected job kind '{job.kind}'"
    params = schemas.WalletSyncJobParams.model_validate(job.data)
    wallet_id = params.wallet_id

    # Keep in mind that Solana is not supported for sync as the below networks are
    # For Solana we use Helius webhooks to keep up to date with new transactions
    # We only sync Solana transactions when initializing a new wallet (see below)
    covalent_networks = ["Ethereum"]
    mintscan_networks = ["Axelar", "Cosmos", "Celestia", "Evmos", "Osmosis", "Stride"]

    networks_per_sync_fn = (
        (sync_transactions_covalent, covalent_networks),
        (sync_transactions_mintscan, mintscan_networks),
    )

    wallet_manager = WalletManager(db)
    wallet = None

    if wallet_id > 0:
        wallet = wallet_manager.get_by_id(wallet_id)
        if not wallet.is_active:
            return f"Wallet '{wallet_id}' is not active"
        wallets_per_sync_fn = [
            (sync_fn, [wallet])
            for sync_fn, network_list in networks_per_sync_fn
            if any(network.name in network_list for network in wallet.networks)
        ]
    elif wallet_id == -1:
        wallets_per_sync_fn = [
            (sync_fn, wallet_manager.get_all_active_from_network_names(network_list))
            for sync_fn, network_list in networks_per_sync_fn
        ]
    else:
        return f"Invalid wallet_id '{wallet_id}'"

    results = [
        (wallet.id, sync_fn(db, wallet))
        for sync_fn, wallet_list in wallets_per_sync_fn
        for wallet in wallet_list
    ]

    if params.initial and wallet:
        # Initialize Solana wallet transactions
        # Note that Solana transactions are kept synced with Helius webhooks
        if any(network.is_solana for network in wallet.networks):
            result = save_solana_transactions(db, wallet.address)
            results.append((wallet.id, [result]))

    return schemas.WalletSyncJobResult(results=results)
