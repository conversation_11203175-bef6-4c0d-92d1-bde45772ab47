# Cline Documentation Directory

This directory contains documentation files that provide essential context to <PERSON><PERSON>, an AI
Language Model assistant, about the project's various aspects. These files help <PERSON><PERSON> better
understand the project and provide more accurate and contextual assistance.

## Contents

### Core Documentation

- `businessObjectives.md` - Overview of the business context and objectives
- `codebaseSummary.md` - Technical overview of the codebase structure and architecture
- `customInstructions.md` - Custom behavioral instructions for Cline
- `projectRoadmap.md` - Future plans and development roadmap
- `techStack.md` - Details about the technology stack used in the project

### System Architecture Documentation

- `blockchainIntegration.md` - Multi-chain transaction tracking and blockchain service
  integrations
- `investmentPrograms.md` - Earn Program and LP Fund implementation details
- `pricingSystem.md` - Price tracking, caching, and refresh mechanisms
- `transactionSystem.md` - Transaction classification and accounting system

## Purpose

The files in this directory serve as a knowledge base for Cline, enabling it to:

- Understand the project's business context and goals
- Navigate and comprehend the codebase structure
- Follow specific guidelines and practices
- Make informed suggestions aligned with the project's roadmap
- Provide assistance that's consistent with the project's technology choices
- Understand detailed system implementations and architectures

## Documentation Structure

### Core Documentation

Contains high-level project information, objectives, and guidelines that provide overall
context.

### System Architecture Documentation

Provides detailed technical documentation for specific system components:

- Blockchain integrations and multi-chain support
- Investment program implementations
- Price tracking and market data systems
- Transaction processing and accounting systems

## Usage

Enable using `cline_docs` by adding the following to the "Custom Instructions" setting in Cline:

```
Always include the contents of this file from the git repo:
cline_docs/customInstructions.md
```

This documentation helps ensure that Cline's responses and assistance are well-aligned with the
project's context, requirements, and objectives.

## Documentation Updates

When making significant changes to system components, corresponding documentation should be
updated to maintain accuracy and completeness. All documentation files should be kept in sync
with the current state of the project.
