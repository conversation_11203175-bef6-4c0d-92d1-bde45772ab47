variable "namespace" {
  description = "Namespace for naming resources"
  type        = string
  nullable    = false
}

variable "network" {
  description = "The network that the resources will be attached to"
  type        = string
  nullable    = false
}

variable "network_tags" {
  description = "Tags for firewall rules"
  type        = list(string)
  nullable    = false
}

variable "api_negs" {
  description = "List of NEGs for the api backend service (e.g. [\"projects/{{project}}/zones/{{zone}}/networkEndpointGroups/{{name}}\"])"
  type        = list(string)
  nullable    = false
}

variable "bucket_name" {
  description = "The name of the bucket with the static website"
  type        = string
  nullable    = false
}

variable "dns_managed_zone" {
  description = "Google DNS Managed Zone object"
  type = object({
    name     = string
    dns_name = string
  })
  nullable = false
}

variable "website_is_spa" {
  description = "Whether the website should handle Single Page Application routing by serving index.html for all non-API routes"
  type        = bool
  default     = false
  nullable    = false
}
