from typing import Sequence

from eth_utils import is_address, to_checksum_address
from solders.pubkey import Pubkey
from sqlalchemy import ScalarSelect, delete, func, literal_column, select, tuple_

from lootloot import schemas
from lootloot.base import BaseManager
from lootloot.kinds import NetworkGroup, WalletOrigin
from lootloot.models import UserWallet


class UserWalletManager(BaseManager):
    def all_from_user(self, user_id: int):
        """List all wallets from a user.

        The wallets are in order they were added.
        """
        return self.db.scalars(
            select(UserWallet)
            .where(UserWallet.user_id == user_id)
            .order_by(UserWallet.sequence_number)
        ).all()

    def all_addresses_from_user(self, user_id: int, network_group: NetworkGroup):
        """List all wallet addresses from a user and network group."""
        return self.db.scalars(
            select(UserWallet.address).where(
                UserWallet.user_id == user_id,
                UserWallet.network_group == network_group,
            )
        ).all()

    _next_sequence_exp = 1 + func.coalesce(func.max(UserWallet.sequence_number), 0)

    def _next_sequence_subquery(self, user_id: int) -> ScalarSelect[int]:
        """Return the subquery for the next sequence number of a user."""
        return (
            select(self._next_sequence_exp)
            .where(UserWallet.user_id == user_id)
            .scalar_subquery()
        )

    def create(self, user_id: int, data: schemas.AddUserWallet):
        """Create a wallet for a user."""
        address = validate_and_normalize_address(data)
        wallet = UserWallet(
            user_id=user_id,
            name=data.name,
            origin=data.origin.value,
            address=address,
            network_group=data.network_group,
            sequence_number=self._next_sequence_subquery(user_id),
        )
        self.db.add(wallet)
        return wallet

    def create_with_auto_name(
        self, *, user_id: int, origin: WalletOrigin, network_group: NetworkGroup, address: str
    ) -> UserWallet:
        """Create a wallet for a user with an auto-generated name."""
        name_subquery = (
            select(func.concat(literal_column("'Wallet '"), self._next_sequence_exp))
            .where(UserWallet.user_id == user_id)
            .scalar_subquery()
        )
        wallet = UserWallet(
            user_id=user_id,
            name=name_subquery,
            origin=origin.value,
            address=address,
            network_group=network_group,
            sequence_number=self._next_sequence_subquery(user_id),
        )
        self.db.add(wallet)
        return wallet

    def get_by_address(self, network_group: NetworkGroup, address: str) -> UserWallet | None:
        """Return a wallet by address and network group."""
        return self.db.scalar(
            select(UserWallet).where(
                UserWallet.address == address, UserWallet.network_group == network_group
            )
        )

    def get_by_address_from_user(
        self, user_id: int, network_group: NetworkGroup, address: str
    ) -> UserWallet | None:
        """Return the wallet by address, network group, and owned by the user."""
        return self.db.scalar(
            select(UserWallet).where(
                UserWallet.user_id == user_id,
                UserWallet.address == address,
                UserWallet.network_group == network_group,
            )
        )

    def list_by_network_group_and_address(
        self, keys: list[tuple[NetworkGroup, str]]
    ) -> Sequence[UserWallet]:
        """Return a list of wallets matching the network group and address pairs."""
        return self.db.scalars(
            select(UserWallet).where(
                tuple_(UserWallet.network_group, UserWallet.address).in_(keys)
            )
        ).all()

    def delete_for_user(self, user_id: int, wallet_id: int) -> None:
        """Delete a wallet from a user."""
        self.db.execute(
            delete(UserWallet).where(UserWallet.user_id == user_id, UserWallet.id == wallet_id)
        )

    def delete_extra_wallets_for_user_and_origin(
        self, *, user_id: int, origin: WalletOrigin, keep: list[tuple[NetworkGroup, str]]
    ) -> None:
        self.db.execute(
            delete(UserWallet).where(
                UserWallet.user_id == user_id,
                UserWallet.origin == origin.value,
                tuple_(UserWallet.network_group, UserWallet.address).not_in(keep),
            )
        )


def validate_and_normalize_address(data: schemas.AddUserWallet):
    match data.network_group:
        case "evm":
            if not data.address.startswith("0x"):
                raise ValueError("EVM addresses must start with '0x'")
            if not is_address(data.address):
                raise ValueError("EVM address must be a valid hex address")
            return str(to_checksum_address(data.address))

        case "solana":
            try:
                public_key = Pubkey.from_string(data.address)
            except ValueError as e:
                raise ValueError("Solana address must be a valid base58 public key") from e
            if not public_key.is_on_curve():
                raise ValueError("Solana address must be a valid base58 public key")
            return str(public_key)

        case _:
            raise ValueError(f"Unknown network group '{data.network_group}'")
