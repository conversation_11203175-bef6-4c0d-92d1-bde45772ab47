"use client";
import { useEffect } from "react";

export function UrlNormalizer() {
  useEffect(() => {
    removeIndexHtmlFromPath();
  }, []);
  return null;
}

/**
 * Remove index.html from the end of the path (if present).
 *
 * At the time of writing, Google bucket serving doesn't seem to support
 * configuration to change its behavior of redirecting to /a/b/index.html
 * when trying to access resource /a/b (no ending slash). So we edit the
 * URL here to replace the ending /index.html with just /.
 *
 * This fixes the redirect problem when doing the oauth login flow.
 */
function removeIndexHtmlFromPath() {
  const path = location.pathname;
  if (path.endsWith("/index.html")) {
    history.replaceState(
      undefined,
      "",
      `${path.slice(0, -10)}${location.search}${location.hash}`,
    );
  }
}
