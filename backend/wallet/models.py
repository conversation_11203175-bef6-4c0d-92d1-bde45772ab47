import secrets
from datetime import datetime, timedelta, timezone
from uuid import UUID

from sqlalchemy import BigInteger, DateTime, ForeignKey, String, func
from sqlalchemy.dialects.postgresql import ARRAY, BYTEA
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    MappedAsDataclass,
    Session,
    declared_attr,
    mapped_column,
    object_session,
    relationship,
)

from wallet.kinds import CredentialUse


class BaseCustomId(MappedAsDataclass, DeclarativeBase, kw_only=True):
    """Base model class that provides SQLAlchemy model functionality without enforcing an id field.

    This class serves as the foundation for all wallet models, providing:
    - Automatic table name generation from class names
    - SQLAlchemy session access via the db property
    - Support for different primary key types per model
    - Dataclass functionality for easy instance creation
    - Type hints for better IDE support

    Unlike common base models that enforce an integer id field, this base class allows
    models to define their own primary key fields with different types (e.g., bytes,
    str, UUID, etc.).
    """

    @classmethod
    @declared_attr.directive
    def __tablename__(cls) -> str:
        """Generate the table name from the class name in lowercase."""
        return cls.__name__.lower()

    @property
    def db(self) -> Session | None:
        """Get the SQLAlchemy session associated with this instance."""
        return object_session(self)


class Base(BaseCustomId):
    """Primary base class for models.

    This class provides a default integer primary key (id) suitable for most models.
    Models should inherit from this class unless they require a different type of
    primary key, in which case they should inherit from BaseCustomId directly.

    This is an abstract base class and won't create its own table.

    Attributes:
        id: Auto-incrementing integer primary key
    """

    __abstract__ = True
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, init=False)


class User(Base):
    """Core user model for the wallet service.

    Represents a user in the wallet system with authentication and profile information.
    This model is central to the wallet service and is referenced by most other models.
    """

    email: Mapped[str] = mapped_column(unique=True, index=True)
    credentials_id: Mapped[UUID] = mapped_column(
        unique=True, index=True, repr=False, init=False, server_default=func.gen_random_uuid()
    )
    first_name: Mapped[str | None] = mapped_column(default=None)
    last_name: Mapped[str | None] = mapped_column(default=None)
    picture_url: Mapped[str | None] = mapped_column(default=None, repr=False)
    cubist_user_id: Mapped[str | None] = mapped_column(
        default=None, unique=True, index=True, repr=False
    )

    def get_display_name(self) -> str | None:
        """Get the user's display name, combining first and last name if available."""
        names = [name for name in (self.first_name, self.last_name) if name]
        return " ".join(names) if names else None


class UserSession(Base):
    """User session management for the wallet service.

    Tracks active user sessions with authentication tokens and metadata.
    Handles session expiration and device tracking.
    """

    user_id: Mapped[int] = mapped_column(
        ForeignKey(User.id), index=True, init=False, repr=False
    )
    user: Mapped[User] = relationship()
    user_agent: Mapped[str] = mapped_column()
    ip_address: Mapped[str] = mapped_column()
    token: Mapped[str] = mapped_column(index=True, unique=True, repr=False, deferred=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), init=False, server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), init=False, server_default=func.now()
    )
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), repr=False)

    TOKEN_LENGTH = 128

    @staticmethod
    def create(*, user: User, user_agent: str, ip_address: str) -> "UserSession":
        """Create a new session for the given user with a secure random token."""
        token = secrets.token_urlsafe(UserSession.TOKEN_LENGTH)
        return UserSession(
            user=user,
            user_agent=user_agent,
            ip_address=ip_address,
            token=token,
            expires_at=datetime.now(timezone.utc) + timedelta(days=7),
        )


class Credential(BaseCustomId):
    """WebAuthn credential management for secure authentication.

    Stores WebAuthn (FIDO2) credentials for passwordless authentication.
    Each credential represents a registered authenticator device.
    """

    id: Mapped[bytes] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column()
    public_key: Mapped[bytes] = mapped_column()
    backed_up: Mapped[bool] = mapped_column()
    device_type: Mapped[str] = mapped_column()
    transports: Mapped[list[str]] = mapped_column(ARRAY(String))
    sign_count: Mapped[int] = mapped_column(BigInteger)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    latest_use_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    user_credentials_id: Mapped[UUID] = mapped_column(
        ForeignKey(User.credentials_id), index=True, repr=False
    )
    user: Mapped[User] = relationship()
    use_for: Mapped[CredentialUse] = mapped_column()
    wrap_key: Mapped["CredentialWrapKey | None"] = relationship()


class OngoingCredentialRegistration(Base):
    """Tracks in-progress WebAuthn credential registration.

    Manages the challenge-response flow during credential registration,
    ensuring secure device enrollment.
    """

    challenge: Mapped[bytes] = mapped_column()
    require_user_verification: Mapped[bool] = mapped_column()
    user_id: Mapped[int] = mapped_column(
        ForeignKey(User.id), index=True, unique=True, repr=False
    )
    user: Mapped[User] = relationship()
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )


class OngoingCredentialAuthentication(Base):
    """Tracks in-progress WebAuthn authentication attempts.

    Manages the challenge-response flow during authentication,
    ensuring secure device verification.
    """

    challenge: Mapped[bytes] = mapped_column()
    require_user_verification: Mapped[bool] = mapped_column()
    user_id: Mapped[int] = mapped_column(
        ForeignKey(User.id), index=True, unique=True, repr=False
    )
    user: Mapped[User] = relationship()
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )


class CredentialWrapKey(Base):
    """Stores the wrapping key used for encrypting user wallet."""

    secret_key: Mapped[bytes] = mapped_column(BYTEA)
    credential_id: Mapped[bytes] = mapped_column(
        BYTEA, ForeignKey(Credential.id), index=True, unique=True
    )
    credential: Mapped[Credential] = relationship(back_populates="wrap_key")


class CubeSignerSession(Base):
    """Stores CubeSigner session data for blockchain transaction signing.

    Manages session data for the CubeSigner integration, which provides
    secure transaction signing capabilities.
    """

    session_name: Mapped[str] = mapped_column(unique=True)
    data: Mapped[bytes] = mapped_column(BYTEA)
