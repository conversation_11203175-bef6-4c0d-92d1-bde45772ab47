import { useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { getErrorMessage } from "@/lib/error";
import { createKey } from "@/lib/secret-store";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";

export function CreateTestAccount({
  className,
  onCreate,
}: {
  className?: string | undefined;
  onCreate: () => void;
}) {
  const [opened, setOpened] = useState(false);

  const accountCreation = useMutation({
    async mutationFn({ password }: { password: Promise<CryptoKey> }) {
      return await createKey(await password);
    },
  });

  const getPassword = useGetPassword();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    accountCreation.mutate(
      { password: getPassword(formData) },
      {
        onSuccess() {
          onCreate();
          setOpened(false);
        },
      },
    );
  };

  return (
    <Dialog.Root open={opened} onOpenChange={setOpened}>
      <Dialog.Trigger asChild>
        <Button variant="outline" className={className}>
          Create test account
        </Button>
      </Dialog.Trigger>
      <Dialog.Content title="Create test account" aria-describedby="">
        <div>
          This will create a new Solana account on your wallet. <strong>Don&apos;t</strong> use
          it on mainnet or with real funds. Currently we <strong>don&apos;t</strong> provide a
          way to export or back-up the account or seed.
        </div>
        <hr className="my-4" />
        <form className="grid gap-2" onSubmit={handleSubmit}>
          <PasswordInput disabled={accountCreation.isPending} />
          {accountCreation.error && (
            <AlertError>{getErrorMessage(accountCreation.error)}</AlertError>
          )}
          <div className="mt-2 grid grid-cols-2 gap-4">
            <Button type="submit" disabled={accountCreation.isPending}>
              Create
            </Button>
            <Dialog.Close asChild>
              <Button variant="secondary">Cancel</Button>
            </Dialog.Close>
          </div>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
}
