import { CoinProcessed, getCoinDisplayName } from "@/helpers/coin";
import { currencyFormatter } from "@/helpers/formatters";
import { Coin } from "@/types/schemas";
import { FilterAlt } from "@mui/icons-material";
import { Chip, Typography } from "@mui/material";

interface Props {
  readonly selectedCoin: Coin | CoinProcessed | null | undefined;
  readonly averageCost: number | null | undefined;
  readonly onClearSelectedCoin: () => void;
}

export default function FilteredByCoinDisplay(props: Props) {
  const { selectedCoin, averageCost, onClearSelectedCoin } = props;
  return (
    <>
      {selectedCoin != null && (
        <Chip
          sx={{ marginY: "-2px", alignSelf: "flex-start" }}
          icon={<FilterAlt />}
          size="small"
          color="primary"
          label={getCoinDisplayName(selectedCoin)}
          onDelete={onClearSelectedCoin}
        />
      )}
      {averageCost != null && (
        <Chip
          sx={{ marginY: "-2px", alignSelf: "flex-start" }}
          size="small"
          label={
            <span>
              <Typography component="span" fontSize={13}>
                Weighted Average
              </Typography>{" "}
              $ {currencyFormatter.format(averageCost)}
            </span>
          }
        />
      )}
    </>
  );
}
