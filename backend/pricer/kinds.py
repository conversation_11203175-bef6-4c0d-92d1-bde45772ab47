from typing import Annotated

from pydantic import AfterValidator


def check_history_date(date: str):
    try:
        if len(date) != 10:
            raise ValueError
        day, month, year = date.split("-")
        if len(day) != 2 or len(month) != 2 or len(year) != 4:
            raise ValueError
        int(day)
        int(month)
        int(year)
    except ValueError as e:
        raise ValueError("date doesn't match format DD-MM-YYYY") from e
    return date


HistoryDate = Annotated[str, AfterValidator(check_history_date)]
