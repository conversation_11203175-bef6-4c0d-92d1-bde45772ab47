from datetime import datetime, timezone
from decimal import Decimal

from pydantic import BaseModel, ValidationError
from sqlalchemy.orm import Session

from stabletech.accounting.bitcointax.transaction_matching import (
    match_imported_capital_positions,
)
from stabletech.accounting.crud import CapitalPositionManager
from stabletech.indexer.cointracking import read_csv
from stabletech.indexer.models import Job
from stabletech.market.crud import CoinManager
from stabletech.schemas import ImportFailedRow, ImportResult


class Entry(BaseModel):
    volume: Decimal
    symbol: str
    date_acquired: datetime
    date_sold: datetime
    proceeds: Decimal
    cost_basis: Decimal
    currency: str
    acquired_id: str
    sold_id: str
    row: int


def import_capital_gains_csv(db: Session, csv_text: str) -> str | ImportResult:
    header, rows = read_csv(csv_text.splitlines(), csv_header_check=is_csv_header)

    resp = ImportResult(ok=False, imported_rows_count=0, header=header, failed_rows=[])

    # Parse CSV into Entries
    try:
        entries: list[Entry] = list()
        for index, row in enumerate(rows):
            if row:
                entries.append(
                    Entry(
                        acquired_id=row[10],
                        cost_basis=row[5],
                        currency=row[7],
                        date_acquired=str_to_datetime(row[2]),
                        date_sold=str_to_datetime(row[3]),
                        proceeds=row[4],
                        row=index,
                        sold_id=row[12],
                        symbol=row[1],
                        volume=row[0],
                    )
                )
    except ValidationError as e:
        return f"Error parsing CSV - please ensure it is a valid BitcoinTax CSV {e}"

    # Validate data
    entries_year: int = entries[0].date_sold.year
    for entry in entries:
        if entry.currency != "USD":
            # Expects USD as currency
            return "Only USD as Currency is supported"
        if entry.date_sold.year != entries_year:
            # All entries' sale date need to be from the same year
            return f"All entries have to be from the same year ({entries_year})"

    # Add new entries converting dates to datetime
    capital_positions: list[dict] = []
    capital_position_manager = CapitalPositionManager(db)
    coin_manager = CoinManager(db)
    missing_coins = set()
    for entry in entries:
        coin = coin_manager.get_priced_coin_by_ticker(entry.symbol)
        if not coin:
            missing_coins.add(entry.symbol)
            continue

        capital_positions.append(
            dict(
                amount=entry.volume,
                bought_at=entry.date_acquired,
                bought_usd_amount=entry.cost_basis,
                coin_id=coin.id,
                csv_row=entry.row + 1,
                external_buy_transaction_id=entry.acquired_id,
                external_sell_transaction_id=entry.sold_id,
                sold_at=entry.date_sold,
                sold_usd_amount=entry.proceeds,
            )
        )
    if missing_coins:
        return f"Could not find coins with tickers {missing_coins} that have prices enabled"

    capital_position_manager.bulk_insert(capital_positions)
    db.commit()

    failed_matchings = match_imported_capital_positions(db)
    if failed_matchings:
        resp.failed_rows = [
            ImportFailedRow(
                error=failed_match.err,
                index=failed_match.csv_row,
                row=rows[failed_match.csv_row - 1],
            )
            # Hack to avoid out of range matches that belong to another CSV
            # TODO: generate a unique hash for the file and track that along with `csv_row`
            for failed_match in failed_matchings
            if failed_match.csv_row <= len(rows)
        ]
        return resp
    resp.ok = True
    resp.imported_rows_count = len(capital_positions)
    return resp


def is_csv_header(row: list[str]) -> bool:
    """
    The BitcoinTax CSV header has the following items but only the ones until "Currency" are used:
    ['Volume', 'Symbol', 'Date Acquired', 'Date Sold', 'Proceeds', 'Cost Basis', 'Gain', 'Currency',
    'Unmatched', 'AcquiredAccount', 'AcquiredId', 'SoldAccount', 'SoldId']
    """
    return row[0].lower() == "volume" and row[1].lower() == "symbol"


def process_import_job(db: Session, job: Job) -> str | ImportResult:
    if job.kind != "import_bitcointax_csv":
        raise ValueError(f"Unexpected job kind: {job.kind}")

    resp = import_capital_gains_csv(db, job.data_text)
    return resp


def str_to_datetime(date_str: str) -> datetime:
    return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)
