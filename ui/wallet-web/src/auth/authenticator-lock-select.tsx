import { useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import { createWrapKeyAuthOptions, RequestOptions, verifyCredential } from "@/lib/auth";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import {
  credentialWrapKey,
  credentialWrapKeyRemove,
  getCredentialRegistered,
  getKeysWithCredentialStatus,
} from "@/lib/secret-store";
import { getShortenedAddress } from "@/lib/utils/keys";
import { base64URLStringToBuffer, startAuthentication } from "@simplewebauthn/browser";
import { useMutation, useQuery } from "@tanstack/react-query";
import { cx } from "class-variance-authority";
import { Penci<PERSON>, Shield, ShieldCheck, Wallet } from "lucide-react";
import { useState } from "react";

export function AuthenticatorLockSelect(props: { onDone: () => void }) {
  const { onDone } = props;
  const getPassword = useGetPassword();
  const [changeMap, setChangeMap] = useState<ReadonlyMap<string, boolean>>(new Map());

  const credential = useQuery({
    queryKey: ["credentialRegistered"],
    queryFn: getCredentialRegistered,
  });

  const keysWithStatus = useQuery({
    queryKey: ["keysWithCredentialStatus"],
    queryFn: getKeysWithCredentialStatus,
  });

  const saveKeys = useMutation({
    async mutationFn(formData: FormData) {
      if (credential.data == null) {
        throw new Error("No credential is active");
      }
      const credentialId = credential.data.id;
      const password = await getPassword(formData, { testPassword: true });
      try {
        await processCredentialChanges({ changeMap, credentialId, password });
      } finally {
        await keysWithStatus.refetch();
      }
      setChangeMap(new Map());
      onDone();
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    saveKeys.mutate(new FormData(e.currentTarget));
  };

  const setKeySelection = (keyId: string, value: boolean | null) => {
    setChangeMap((prev) => {
      if (value == null) {
        // Keep unchanged
        if (!prev.has(keyId)) return prev;
        const next = new Map(prev);
        next.delete(keyId);
        return next;
      }

      if (value) {
        // Change to add credential wrapped key
        if (prev.get(keyId)) return prev;
        const next = new Map(prev);
        next.set(keyId, true);
        return next;
      }

      // Change to remove credential wrapped key
      if (prev.get(keyId) === false) return prev;
      const next = new Map(prev);
      next.set(keyId, false);
      return next;
    });
  };

  const isCredentialActive = credential.isSuccess && credential.data != null;

  if (keysWithStatus.isPending || credential.isPending) {
    return (
      <section className="grid place-items-center gap-4 *:min-w-0">
        <Spinner size={32} />
        <p>Loading wallets...</p>
      </section>
    );
  }

  if (keysWithStatus.isError || credential.isError) {
    return (
      <section className="grid gap-4 *:min-w-0">
        <h2 className="text-lg">Select wallets to protect</h2>
        <AlertError>{getErrorMessage(keysWithStatus.error ?? credential.error)}</AlertError>
      </section>
    );
  }

  const keys = keysWithStatus.data;

  return (
    <section className="grid gap-4 *:min-w-0">
      <h2 className="text-lg">Select local wallets to protect</h2>

      {!isCredentialActive && (
        <AlertError>
          No authenticator is active. Please activate an authenticator first.
        </AlertError>
      )}

      {keys.length === 0 ? (
        <p>No wallets found</p>
      ) : (
        <>
          <p className="text-sm text-foreground/70">
            Select the wallets you want to protect with your authenticator.
          </p>
          <ul className="grid gap-3 *:min-w-0">
            {keys.map(({ key, isCredentialWrapped }) => {
              const change = changeMap.get(key.id);
              const isChanging = change != null;
              const finalCredentialWrapped = isChanging ? change : isCredentialWrapped;
              const credentialProtectionStatus = finalCredentialWrapped
                ? "Protected"
                : "Not protected";
              return (
                <li key={key.id}>
                  <button
                    className={cx(
                      "grid w-full grid-cols-[auto_1fr_auto] items-center gap-3 rounded-sm border px-3 py-2 text-left *:min-w-0",
                      isChanging ? "bg-accent/20 hover:bg-accent/50" : "hover:bg-accent/35",
                    )}
                    onClick={() => {
                      setKeySelection(key.id, isChanging ? null : !isCredentialWrapped);
                    }}
                    disabled={!isCredentialActive}
                  >
                    <span className="text-foreground/80">
                      <Wallet size={18} />
                    </span>
                    <div className="grid gap-0.5 *:min-w-0">
                      <span className="font-medium">{getShortenedAddress(key)}</span>
                      <span className="text-sm text-foreground/70">{key.metadata.network}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {isChanging && (
                        <span title="Pending change">
                          <span className="sr-only">Pending change</span>
                          <Pencil aria-hidden className="text-foreground/50" size={20} />
                        </span>
                      )}
                      <span title={credentialProtectionStatus}>
                        <span className="sr-only">{credentialProtectionStatus}</span>
                        {finalCredentialWrapped ? (
                          <ShieldCheck aria-hidden className="text-green-500" size={24} />
                        ) : (
                          <Shield aria-hidden className="text-muted-foreground" size={24} />
                        )}
                      </span>
                    </div>
                  </button>
                </li>
              );
            })}
          </ul>

          <form className="grid gap-4 *:min-w-0" onSubmit={handleSubmit}>
            <PasswordInput disabled={saveKeys.isPending} />

            <div className="mt-2 grid grid-cols-2 gap-4">
              <Button
                type="button"
                variant="secondary"
                disabled={saveKeys.isPending}
                onClick={() => {
                  if (changeMap.size > 0) {
                    setChangeMap(new Map());
                  } else {
                    onDone();
                  }
                }}
              >
                {changeMap.size > 0 ? "Cancel" : "Back"}
              </Button>
              <Button
                type="submit"
                disabled={!isCredentialActive || changeMap.size === 0 || saveKeys.isPending}
              >
                {saveKeys.isPending ? <Spinner /> : "Save"}
              </Button>
            </div>

            {saveKeys.isError && (
              <AlertError onClear={saveKeys.reset}>
                {getErrorMessage(saveKeys.error)}
              </AlertError>
            )}
          </form>
        </>
      )}
    </section>
  );
}

async function processCredentialChanges({
  changeMap,
  credentialId,
  password,
}: {
  changeMap: ReadonlyMap<string, boolean>;
  credentialId: string;
  password: CryptoKey;
}) {
  // Get wrapping key through authentication
  const optionsResp = await createWrapKeyAuthOptions(credentialId);
  const authResp = await startAuthentication({
    optionsJSON: optionsResp.auth_options as RequestOptions,
  });
  const verificationResp = await verifyCredential(authResp);
  if (verificationResp.kind !== "wrap-key") {
    throw new Error("Unexpected authenticator verification response");
  }

  const secretKey = new Uint8Array(base64URLStringToBuffer(verificationResp.secret_key));

  // Process each key in the changeMap
  for (const [keyId, shouldProtect] of changeMap.entries()) {
    await (shouldProtect
      ? credentialWrapKey(keyId, secretKey, password)
      : credentialWrapKeyRemove(keyId));
  }
}
