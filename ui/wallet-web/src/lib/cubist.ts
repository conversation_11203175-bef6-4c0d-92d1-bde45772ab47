import { apiMutation } from "@/lib/api";
import { CubistRegisterOidcUserResp } from "@/types/schemas";
import * as cs from "@cubist-labs/cubesigner-sdk";

/**
 * Associate a user with a Cubist user ID
 */
export async function associateCubistUser(cubistUserId: string) {
  return apiMutation<null>({
    method: "POST",
    resource: `cubist/users?cubist_user_id=${encodeURIComponent(cubistUserId)}`,
  });
}

/**
 * Register the user with Cubist
 */
export async function registerCubistUser(identityProof: cs.IdentityProof) {
  return await apiMutation<CubistRegisterOidcUserResp>({
    method: "POST",
    resource: "cubist/register-oidc-user",
    body: identityProof,
  });
}
