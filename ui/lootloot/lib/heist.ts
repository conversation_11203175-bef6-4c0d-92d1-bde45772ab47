import { apiMutation, apiQuery } from "@/lib/api";
import { useAuth } from "@/lib/auth-ctx";
import {
  AddRaffle,
  HeistPrices,
  HeistStats,
  HeistStatsPublic,
  HeistWardrobe,
  NFTClaimsHistoryResp,
  NanaByDayResp,
  RaffleResp,
  TopClaimsResp,
} from "@/types/schemas";
import { useMutation, useQuery } from "@tanstack/react-query";

// From backend/lootloot/external/heist.py
export const HEIST_EVENTS: Record<number, { name: string; isBad: boolean; order: number }> = {
  // Special event for ambush filtering
  99: { name: "Ambush", isBad: false, order: 0 },
  14: { name: "50x", isBad: false, order: 1 },
  9: { name: "20x", isBad: false, order: 2 },
  1: { name: "10x", isBad: false, order: 3 },
  2: { name: "5x", isBad: false, order: 4 },
  10: { name: "3x", isBad: false, order: 5 },
  3: { name: "2x", isBad: false, order: 6 },
  4: { name: "Expected", isBad: false, order: 7 },
  5: { name: "Fumbled", isBad: true, order: 8 },
  6: { name: "Confiscation", isBad: true, order: 9 },
  7: { name: "Arrested", isBad: true, order: 10 },
  8: { name: "Rekt", isBad: true, order: 11 },
};

async function addHeistRaffle(params: AddRaffle) {
  return apiMutation({
    method: "PUT",
    resource: "admin/raffle",
    body: params,
  });
}
export function useAddHeistRaffle() {
  return useMutation({
    mutationFn: addHeistRaffle,
  });
}

async function addHeistRaffleEntry(secretWord: string) {
  return apiMutation({
    method: "POST",
    resource: `heist/raffle-entry?secret_word=${secretWord}`,
  });
}
export function useHeistAddRaffleEntry() {
  return useMutation({
    mutationFn: addHeistRaffleEntry,
  });
}

async function endHeistRaffle(secretWord: string) {
  return apiMutation({
    method: "POST",
    resource: `admin/raffle/end?secret_word=${secretWord}`,
  });
}
export function useHeistEndRaffle() {
  return useMutation({
    mutationFn: endHeistRaffle,
  });
}

export function useGetHeistNanaByDayQuery({
  enabled = true,
  timezoneOffset,
}: { enabled?: boolean; timezoneOffset?: number | undefined } = {}) {
  return useQuery({
    queryKey: ["heist", "nana-by-day", { timezone_offset: timezoneOffset }],
    queryFn: apiQuery<NanaByDayResp>,
    enabled,
  });
}

export function useGetRaffle() {
  return useQuery({
    queryKey: ["heist", "raffle"],
    queryFn: apiQuery<RaffleResp>,
  });
}

export function useGetHeistStatsQuery({ enabled = true }: { enabled?: boolean } = {}) {
  return useQuery({
    queryKey: ["heist", "stats"],
    queryFn: apiQuery<HeistStats>,
    enabled,
  });
}

export function useGetPublicHeistPrices() {
  return useQuery({
    queryKey: ["public", "heist", "prices"],
    queryFn: apiQuery<HeistPrices>,
  });
}

export function useGetPublicHeistNanaByDayQuery({
  enabled = true,
  timezoneOffset,
}: { enabled?: boolean; timezoneOffset?: number | undefined } = {}) {
  return useQuery({
    queryKey: ["public", "heist", "nana-by-day", { timezone_offset: timezoneOffset }],
    queryFn: apiQuery<NanaByDayResp>,
    enabled,
  });
}

export function useGetPublicHeistStatsQuery({ enabled = true }: { enabled?: boolean } = {}) {
  return useQuery({
    queryKey: ["public", "heist", "stats"],
    queryFn: apiQuery<HeistStatsPublic>,
    enabled,
  });
}

export function useGetPublicHeistWardrobesQuery() {
  return useQuery({
    queryKey: ["public", "heist", "wardrobes"],
    queryFn: apiQuery<HeistWardrobe[]>,
  });
}

export function useGetNftClaimsHistoryQuery(nftAddress: string) {
  const auth = useAuth();
  return useQuery({
    queryKey: ["heist", "nft", nftAddress, "claims"],
    queryFn: apiQuery<NFTClaimsHistoryResp>,
    enabled: Boolean(auth.user),
    retry: false,
  });
}

export type HeistSpecies = "Chimp" | "Orangutan" | "Gorilla";

export function useGetClaimsHistoryQuery({
  enabled = true,
  timeframe = "daily",
  isOwn = false,
  sortBy = "total_amount_claimed",
  sortOrder = "desc",
  species,
  eventId,
  locationId,
}: {
  enabled?: boolean;
  timeframe?: "daily" | "weekly" | "monthly" | "all-time";
  isOwn?: boolean;
  sortBy?: "total_amount_claimed" | "duration_hours" | "base_multiplier" | "ended_at";
  sortOrder?: "asc" | "desc";
  species?: HeistSpecies[] | undefined;
  eventId?: number[] | undefined;
  locationId?: number[] | undefined;
} = {}) {
  const auth = useAuth();
  // Only allow isOwn when authenticated
  const effectiveIsOwn = isOwn && Boolean(auth.user);
  const params = {
    timeframe,
    sort_by: sortBy,
    sort_order: sortOrder,
    ...(species?.length && { species: species.join(",") }),
    ...(eventId?.length && { event_id: eventId.join(",") }),
    ...(locationId?.length && { location_id: locationId.join(",") }),
  };
  return useQuery({
    enabled: enabled && auth.initialized,
    queryKey: effectiveIsOwn
      ? ["heist", "claims-history", params]
      : ["public", "heist", "claims-history", params],
    queryFn: apiQuery<TopClaimsResp>,
    placeholderData: (previousData) => previousData, // Use previous data while loading new data
  });
}
