import httpx
from pydantic import RootModel

from common.conf import Environment, settings
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.internal_sync.schemas import InternalTransaction
from stabletech.indexer.models import Transaction

INTERNAL_SYNC_ACTIONS = [
    TransactionAction.income.value,
    TransactionAction.spend.value,
    TransactionAction.swap.value,
]

# Protect cloud envs when running locally
if settings.is_local:
    SUBSIDIARY_ENVS = []
else:
    SUBSIDIARY_ENVS = [Environment.apollo, Environment.lp]


def get_internal_sync_url(company: Environment):
    if settings.is_local:
        company_name = "stabletech"
    else:
        assert company in SUBSIDIARY_ENVS + [Environment.production]
        company_name = company.value
    return f"http://api.stabletech-{company_name}.svc.cluster.local/indexer/internal-sync"


def is_subsidiary():
    return settings.env_name in SUBSIDIARY_ENVS


# Type aliases for clarity
InternalTransactionList = RootModel[list[InternalTransaction]]
TransactionIdList = RootModel[list[str]]


class InternalSyncClient:
    company: Environment

    def __init__(self, company: Environment) -> None:
        self.company = company
        self._client = httpx.Client(
            base_url=get_internal_sync_url(company),
            headers=[("Authorization", f"Bearer {settings.internal_sync_secret}")],
        )

    def get_transaction_ids(self) -> list[str]:
        resp = self._client.get("/transactions/ids")
        resp.raise_for_status()
        return TransactionIdList.model_validate_json(resp.content).root

    def get_transactions(self, ids: list[str]) -> list[InternalTransaction]:
        resp = self._client.get("/transactions", params={"ids": ",".join(ids)})
        resp.raise_for_status()
        return InternalTransactionList.model_validate_json(resp.content).root

    def sync_transaction(self, transaction: Transaction) -> httpx.Response:
        """Sync a transaction to the main company."""
        transaction_schema = InternalTransaction.from_transaction(transaction)

        resp = self._client.post(
            "/transactions/add",
            json=transaction_schema.model_dump(mode="json"),
        )
        return resp

    def delete_transaction(self, transaction_id: int) -> httpx.Response:
        """Delete a transaction from the main company."""
        internal_id = f"{settings.env_name}:{transaction_id}"
        resp = self._client.delete(
            "/transactions/delete",
            params={"internal_id": internal_id},
        )
        return resp
