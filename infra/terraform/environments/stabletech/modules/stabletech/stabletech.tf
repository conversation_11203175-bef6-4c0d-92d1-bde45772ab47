locals {
  python_runner_image = "${var.docker_repository}/python-runner:${var.release}"
}

module "postgres_secret" {
  source = "../../../../modules/immutable_secret"

  prefix    = "postgres"
  namespace = var.namespace
  data = {
    POSTGRESQL_DB       = var.postgresql_db
    POSTGRESQL_HOST     = var.secrets.postgresql_host
    POSTGRESQL_PASSWORD = var.secrets.postgresql_password
    POSTGRESQL_USER     = var.secrets.postgresql_user
  }
}

module "slack_secret" {
  source = "../../../../modules/immutable_secret"

  prefix    = "slack"
  namespace = var.namespace
  data = {
    SLACK_BOT_TOKEN      = var.secrets.slack_bot_token
    SLACK_SIGNING_SECRET = var.secrets.slack_signing_secret
  }
}

module "blockchain_rpc_secret" {
  source = "../../../../modules/immutable_secret"

  prefix    = "blockchain-rpc"
  namespace = var.namespace
  data = {
    BITQUERY_API_KEY     = var.secrets.bitquery_api_key
    BLOCK_FILLS_API_KEY  = var.secrets.block_fills_api_key
    COVALENT_API_KEY     = var.secrets.covalent_api_key
    HELIUS_API_KEY       = var.secrets.helius_api_key
    INTERNAL_SYNC_SECRET = var.secrets.internal_sync_secret
    MINTSCAN_API_KEY     = var.secrets.mintscan_api_key
  }
}

module "sendgrid_secret" {
  source = "../../../../modules/immutable_secret"

  prefix    = "sendgrid"
  namespace = var.namespace
  data = {
    SENDGRID_API_KEY = var.secrets.sendgrid_api_key
  }
}

module "common_config_map" {
  source = "../../../../modules/immutable_config_map"

  prefix    = "common"
  namespace = var.namespace
  data = {
    NAMESPACE  = var.namespace
    ENV_NAME   = var.env_name
    SENTRY_DSN = "https://<EMAIL>/4504199259422720"
  }
}

module "init" {
  source = "../init"

  namespace           = var.namespace
  python_runner_image = local.python_runner_image

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name]
}

module "api" {
  source     = "../api"
  depends_on = [module.init]

  namespace           = var.namespace
  python_runner_image = local.python_runner_image
  replicas            = 2
  requests            = var.api_requests

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name]
  api_secrets = [
    module.slack_secret.name,
    module.blockchain_rpc_secret.name,
    module.sendgrid_secret.name,
  ]
  jobs_consumer_secrets = [module.blockchain_rpc_secret.name]
}

module "api_ingress" {
  source     = "../ingress"
  depends_on = [module.api]

  domain            = var.api_domain
  managed_zone_name = var.managed_zone_name
  namespace         = var.namespace
  service_name      = module.api.service_name
  service_port      = module.api.service_port
}

module "liabilities_reminder" {
  source     = "../liabilities_reminder"
  depends_on = [module.init]

  namespace           = var.namespace
  python_runner_image = local.python_runner_image

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name, module.slack_secret.name]
}

module "market" {
  source     = "../market"
  depends_on = [module.init]

  namespace           = var.namespace
  python_runner_image = local.python_runner_image

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name, module.slack_secret.name]
}
