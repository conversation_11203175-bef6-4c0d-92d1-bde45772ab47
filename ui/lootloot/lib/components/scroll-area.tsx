"use client";

import * as RadixScrollArea from "@radix-ui/react-scroll-area";
import { cva, cx } from "class-variance-authority";

const scrollBar = cva("flex touch-none p-[1px] transition-colors select-none", {
  variants: {
    orientation: {
      vertical: "h-full w-2.5 border-l border-l-transparent",
      horizontal: "h-2.5 flex-col border-t border-t-transparent",
    },
  },
});

const SCROLLBAR_H_CLASS = scrollBar({ orientation: "horizontal" });
const SCROLLBAR_V_CLASS = scrollBar({ orientation: "vertical" });

export function ScrollArea({ children, className, ...props }: RadixScrollArea.ScrollAreaProps) {
  return (
    <RadixScrollArea.Root className={cx("relative overflow-hidden", className)} {...props}>
      <RadixScrollArea.Viewport className="h-full w-full rounded-[inherit]">
        {children}
      </RadixScrollArea.Viewport>
      <RadixScrollArea.Scrollbar orientation="horizontal" className={SCROLLBAR_H_CLASS}>
        <RadixScrollArea.Thumb className="relative flex-1 rounded-full bg-border" />
      </RadixScrollArea.Scrollbar>
      <RadixScrollArea.Scrollbar orientation="vertical" className={SCROLLBAR_V_CLASS}>
        <RadixScrollArea.Thumb className="relative flex-1 rounded-full bg-border" />
      </RadixScrollArea.Scrollbar>
      <RadixScrollArea.Corner />
    </RadixScrollArea.Root>
  );
}
