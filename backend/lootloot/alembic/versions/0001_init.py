"""init

Revision ID: 0001
Revises:
Create Date: 2023-11-09 18:12:30.512499

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0001"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("email", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_user_email"), "user", ["email"], unique=True)
    op.create_index(op.f("ix_user_id"), "user", ["id"], unique=False)
    op.create_table(
        "bridge",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_bridge_created_by_user_id"), "bridge", ["created_by_user_id"], unique=False
    )
    op.create_index(op.f("ix_bridge_id"), "bridge", ["id"], unique=False)
    op.create_index(op.f("ix_bridge_name"), "bridge", ["name"], unique=True)
    op.create_table(
        "coin",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("has_prices", sa.Boolean(), nullable=True),
        sa.Column("is_usable_by_clients", sa.Boolean(), server_default="false", nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("ticker", sa.String(), nullable=False),
        sa.Column("uid", sa.String(), nullable=True),
        sa.Column(
            "latest_usd_price",
            sa.Numeric(precision=32, scale=18),
            server_default="0",
            nullable=False,
        ),
        sa.Column("latest_usd_price_updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_coin_id"), "coin", ["id"], unique=False)
    op.create_index(
        op.f("ix_coin_is_usable_by_clients"), "coin", ["is_usable_by_clients"], unique=False
    )
    op.create_index(op.f("ix_coin_uid"), "coin", ["uid"], unique=True)
    op.create_table(
        "exchange",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_exchange_created_by_user_id"), "exchange", ["created_by_user_id"], unique=False
    )
    op.create_index(op.f("ix_exchange_id"), "exchange", ["id"], unique=False)
    op.create_index(op.f("ix_exchange_name"), "exchange", ["name"], unique=True)
    op.create_table(
        "farm",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_farm_created_by_user_id"), "farm", ["created_by_user_id"], unique=False
    )
    op.create_index(op.f("ix_farm_id"), "farm", ["id"], unique=False)
    op.create_index(op.f("ix_farm_name"), "farm", ["name"], unique=True)
    op.create_table(
        "job",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("kind", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("completed_at", sa.DateTime(), nullable=True),
        sa.Column("data", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("data_text", sa.String(), nullable=True),
        sa.Column("error", sa.String(), nullable=True),
        sa.Column("result", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_job_status_id", "job", ["status", "id"], unique=True)
    op.create_table(
        "lendingprotocol",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_lendingprotocol_created_by_user_id"),
        "lendingprotocol",
        ["created_by_user_id"],
        unique=False,
    )
    op.create_index(op.f("ix_lendingprotocol_id"), "lendingprotocol", ["id"], unique=False)
    op.create_index(op.f("ix_lendingprotocol_name"), "lendingprotocol", ["name"], unique=True)
    op.create_table(
        "nft",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("attributes", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("image_url", sa.String(), nullable=True),
        sa.Column("uid", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_nft_id"), "nft", ["id"], unique=False)
    op.create_table(
        "wallet",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("address", sa.String(), nullable=False),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("is_usable_by_clients", sa.Boolean(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["user.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_wallet_address"), "wallet", ["address"], unique=False)
    op.create_index(
        op.f("ix_wallet_created_by_user_id"), "wallet", ["created_by_user_id"], unique=False
    )
    op.create_index(op.f("ix_wallet_id"), "wallet", ["id"], unique=False)
    op.create_index(op.f("ix_wallet_is_active"), "wallet", ["is_active"], unique=False)
    op.create_index(
        op.f("ix_wallet_is_usable_by_clients"), "wallet", ["is_usable_by_clients"], unique=False
    )
    op.create_index(op.f("ix_wallet_name"), "wallet", ["name"], unique=False)
    op.create_index(op.f("ix_wallet_user_id"), "wallet", ["user_id"], unique=False)
    op.create_table(
        "network",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("covalent_chain_id", sa.Integer(), nullable=True),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True),
        sa.Column("explorer_url", sa.String(), nullable=True),
        sa.Column("is_usable_by_clients", sa.Boolean(), server_default="false", nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("native_coin_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["user.id"],
        ),
        sa.ForeignKeyConstraint(
            ["native_coin_id"],
            ["coin.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_network_created_by_user_id"), "network", ["created_by_user_id"], unique=False
    )
    op.create_index(op.f("ix_network_id"), "network", ["id"], unique=False)
    op.create_index(
        op.f("ix_network_is_usable_by_clients"),
        "network",
        ["is_usable_by_clients"],
        unique=False,
    )
    op.create_index(op.f("ix_network_name"), "network", ["name"], unique=True)
    op.create_index(
        op.f("ix_network_native_coin_id"), "network", ["native_coin_id"], unique=False
    )
    op.create_table(
        "coin_network_association",
        sa.Column("coin_id", sa.Integer(), nullable=False),
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["coin_id"],
            ["coin.id"],
        ),
        sa.ForeignKeyConstraint(
            ["network_id"],
            ["network.id"],
        ),
        sa.PrimaryKeyConstraint("coin_id", "network_id"),
    )
    op.create_table(
        "network_wallet_association",
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.Column("wallet_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["wallet_id"], ["wallet.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("network_id", "wallet_id"),
        sa.UniqueConstraint("network_id", "wallet_id"),
    )
    op.create_table(
        "pool",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("exchange_id", sa.Integer(), nullable=True),
        sa.Column("farm_id", sa.Integer(), nullable=False),
        sa.Column("is_closed", sa.Boolean(), server_default="false", nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["exchange_id"],
            ["exchange.id"],
        ),
        sa.ForeignKeyConstraint(
            ["farm_id"],
            ["farm.id"],
        ),
        sa.ForeignKeyConstraint(
            ["network_id"],
            ["network.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_pool_farm_id"), "pool", ["farm_id"], unique=False)
    op.create_index(op.f("ix_pool_id"), "pool", ["id"], unique=False)
    op.create_index(op.f("ix_pool_network_id"), "pool", ["network_id"], unique=False)
    op.create_table(
        "account",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("address", sa.String(), nullable=False),
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.Column("wallet_id", sa.Integer(), nullable=True),
        sa.Column("api_name", sa.String(), nullable=True),
        sa.Column("ticker_symbol", sa.String(), nullable=True),
        sa.Column("bridge_id", sa.Integer(), nullable=True),
        sa.Column("coin_id", sa.Integer(), nullable=True),
        sa.Column("exchange_id", sa.Integer(), nullable=True),
        sa.Column("farm_id", sa.Integer(), nullable=True),
        sa.Column("lending_protocol_id", sa.Integer(), nullable=True),
        sa.Column("nft_id", sa.Integer(), nullable=True),
        sa.Column("pool_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["bridge_id"],
            ["bridge.id"],
        ),
        sa.ForeignKeyConstraint(
            ["coin_id"],
            ["coin.id"],
        ),
        sa.ForeignKeyConstraint(
            ["exchange_id"],
            ["exchange.id"],
        ),
        sa.ForeignKeyConstraint(
            ["farm_id"],
            ["farm.id"],
        ),
        sa.ForeignKeyConstraint(
            ["lending_protocol_id"],
            ["lendingprotocol.id"],
        ),
        sa.ForeignKeyConstraint(
            ["network_id"],
            ["network.id"],
        ),
        sa.ForeignKeyConstraint(
            ["nft_id"],
            ["nft.id"],
        ),
        sa.ForeignKeyConstraint(
            ["pool_id"],
            ["pool.id"],
        ),
        sa.ForeignKeyConstraint(
            ["wallet_id"],
            ["wallet.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("address", "network_id"),
    )
    op.create_index(op.f("ix_account_address"), "account", ["address"], unique=False)
    op.create_index(op.f("ix_account_api_name"), "account", ["api_name"], unique=False)
    op.create_index(op.f("ix_account_coin_id"), "account", ["coin_id"], unique=False)
    op.create_index(op.f("ix_account_id"), "account", ["id"], unique=False)
    op.create_index(op.f("ix_account_network_id"), "account", ["network_id"], unique=False)
    op.create_index(op.f("ix_account_nft_id"), "account", ["nft_id"], unique=False)
    op.create_index(op.f("ix_account_pool_id"), "account", ["pool_id"], unique=False)
    op.create_index(
        op.f("ix_account_ticker_symbol"), "account", ["ticker_symbol"], unique=False
    )
    op.create_index(op.f("ix_account_wallet_id"), "account", ["wallet_id"], unique=False)
    op.create_table(
        "transaction",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("action", sa.String(), nullable=True),
        sa.Column("api_type", sa.String(), nullable=True),
        sa.Column("confirmed_at", sa.DateTime(), nullable=False),
        sa.Column("created_by_user_id", sa.Integer(), nullable=True),
        sa.Column("json_data", sa.String(), nullable=True),
        sa.Column("fees_paid", sa.Numeric(precision=32, scale=18), nullable=True),
        sa.Column("has_approval", sa.Boolean(), server_default="false", nullable=False),
        sa.Column("is_collateral", sa.Boolean(), server_default="false", nullable=False),
        sa.Column("is_deposit", sa.Boolean(), nullable=True),
        sa.Column(
            "native_coin_dollar_price", sa.Numeric(precision=32, scale=18), nullable=True
        ),
        sa.Column("network_id", sa.Integer(), nullable=False),
        sa.Column("signer_account_id", sa.Integer(), nullable=False),
        sa.Column("tx_hash", sa.String(), nullable=False),
        sa.Column("wallet_id", sa.Integer(), nullable=False),
        sa.Column("comment", sa.String(), server_default="", nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["user.id"],
        ),
        sa.ForeignKeyConstraint(
            ["network_id"],
            ["network.id"],
        ),
        sa.ForeignKeyConstraint(
            ["signer_account_id"],
            ["account.id"],
        ),
        sa.ForeignKeyConstraint(
            ["wallet_id"],
            ["wallet.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("network_id", "tx_hash"),
    )
    op.create_index(op.f("ix_transaction_action"), "transaction", ["action"], unique=False)
    op.create_index(
        op.f("ix_transaction_created_by_user_id"),
        "transaction",
        ["created_by_user_id"],
        unique=False,
    )
    op.create_index(op.f("ix_transaction_id"), "transaction", ["id"], unique=False)
    op.create_index(
        op.f("ix_transaction_network_id"), "transaction", ["network_id"], unique=False
    )
    op.create_index(
        op.f("ix_transaction_signer_account_id"),
        "transaction",
        ["signer_account_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_transaction_wallet_id"), "transaction", ["wallet_id"], unique=False
    )
    op.create_table(
        "transfer",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("asset_account_id", sa.Integer(), nullable=False),
        sa.Column("asset_price_usd", sa.Numeric(precision=32, scale=18), nullable=True),
        sa.Column("asset_prices_json", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("amount", sa.Numeric(precision=48, scale=18), nullable=False),
        sa.Column("nft_id", sa.Numeric(precision=32, scale=0), nullable=True),
        sa.Column("from_account_id", sa.Integer(), nullable=False),
        sa.Column("to_account_id", sa.Integer(), nullable=False),
        sa.Column("transaction_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["asset_account_id"],
            ["account.id"],
        ),
        sa.ForeignKeyConstraint(
            ["from_account_id"],
            ["account.id"],
        ),
        sa.ForeignKeyConstraint(
            ["to_account_id"],
            ["account.id"],
        ),
        sa.ForeignKeyConstraint(
            ["transaction_id"],
            ["transaction.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_transfer_asset_account_id"), "transfer", ["asset_account_id"], unique=False
    )
    op.create_index(
        op.f("ix_transfer_from_account_id"), "transfer", ["from_account_id"], unique=False
    )
    op.create_index(op.f("ix_transfer_id"), "transfer", ["id"], unique=False)
    op.create_index(
        op.f("ix_transfer_to_account_id"), "transfer", ["to_account_id"], unique=False
    )
    op.create_index(
        op.f("ix_transfer_transaction_id"), "transfer", ["transaction_id"], unique=False
    )
    # ### end Alembic commands ###

    op.create_unique_constraint(None, "network_wallet_association", ["network_id", "wallet_id"])
    op.create_index(
        "ix_wallet_user_id_address",
        "wallet",
        [sa.text("coalesce(user_id, 0)"), "address"],
        unique=True,
    )
    op.create_index(
        "ix_wallet_user_id_name",
        "wallet",
        [sa.text("coalesce(user_id, 0)"), "name"],
        unique=True,
    )


def downgrade() -> None:
    op.drop_index("ix_wallet_user_id_name", table_name="wallet")
    op.drop_index("ix_wallet_user_id_address", table_name="wallet")
    op.drop_constraint(None, "network_wallet_association", type_="unique")

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_transfer_transaction_id"), table_name="transfer")
    op.drop_index(op.f("ix_transfer_to_account_id"), table_name="transfer")
    op.drop_index(op.f("ix_transfer_id"), table_name="transfer")
    op.drop_index(op.f("ix_transfer_from_account_id"), table_name="transfer")
    op.drop_index(op.f("ix_transfer_asset_account_id"), table_name="transfer")
    op.drop_table("transfer")
    op.drop_index(op.f("ix_transaction_wallet_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_signer_account_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_network_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_created_by_user_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_action"), table_name="transaction")
    op.drop_table("transaction")
    op.drop_index(op.f("ix_account_wallet_id"), table_name="account")
    op.drop_index(op.f("ix_account_ticker_symbol"), table_name="account")
    op.drop_index(op.f("ix_account_pool_id"), table_name="account")
    op.drop_index(op.f("ix_account_nft_id"), table_name="account")
    op.drop_index(op.f("ix_account_network_id"), table_name="account")
    op.drop_index(op.f("ix_account_id"), table_name="account")
    op.drop_index(op.f("ix_account_coin_id"), table_name="account")
    op.drop_index(op.f("ix_account_api_name"), table_name="account")
    op.drop_index(op.f("ix_account_address"), table_name="account")
    op.drop_table("account")
    op.drop_index(op.f("ix_pool_network_id"), table_name="pool")
    op.drop_index(op.f("ix_pool_id"), table_name="pool")
    op.drop_index(op.f("ix_pool_farm_id"), table_name="pool")
    op.drop_table("pool")
    op.drop_table("network_wallet_association")
    op.drop_table("coin_network_association")
    op.drop_index(op.f("ix_network_native_coin_id"), table_name="network")
    op.drop_index(op.f("ix_network_name"), table_name="network")
    op.drop_index(op.f("ix_network_is_usable_by_clients"), table_name="network")
    op.drop_index(op.f("ix_network_id"), table_name="network")
    op.drop_index(op.f("ix_network_created_by_user_id"), table_name="network")
    op.drop_table("network")
    op.drop_index(op.f("ix_wallet_user_id"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_name"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_is_usable_by_clients"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_is_active"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_id"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_created_by_user_id"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_address"), table_name="wallet")
    op.drop_table("wallet")
    op.drop_index(op.f("ix_nft_id"), table_name="nft")
    op.drop_table("nft")
    op.drop_index(op.f("ix_lendingprotocol_name"), table_name="lendingprotocol")
    op.drop_index(op.f("ix_lendingprotocol_id"), table_name="lendingprotocol")
    op.drop_index(op.f("ix_lendingprotocol_created_by_user_id"), table_name="lendingprotocol")
    op.drop_table("lendingprotocol")
    op.drop_index("ix_job_status_id", table_name="job")
    op.drop_table("job")
    op.drop_index(op.f("ix_farm_name"), table_name="farm")
    op.drop_index(op.f("ix_farm_id"), table_name="farm")
    op.drop_index(op.f("ix_farm_created_by_user_id"), table_name="farm")
    op.drop_table("farm")
    op.drop_index(op.f("ix_exchange_name"), table_name="exchange")
    op.drop_index(op.f("ix_exchange_id"), table_name="exchange")
    op.drop_index(op.f("ix_exchange_created_by_user_id"), table_name="exchange")
    op.drop_table("exchange")
    op.drop_index(op.f("ix_coin_uid"), table_name="coin")
    op.drop_index(op.f("ix_coin_is_usable_by_clients"), table_name="coin")
    op.drop_index(op.f("ix_coin_id"), table_name="coin")
    op.drop_table("coin")
    op.drop_index(op.f("ix_bridge_name"), table_name="bridge")
    op.drop_index(op.f("ix_bridge_id"), table_name="bridge")
    op.drop_index(op.f("ix_bridge_created_by_user_id"), table_name="bridge")
    op.drop_table("bridge")
    op.drop_index(op.f("ix_user_id"), table_name="user")
    op.drop_index(op.f("ix_user_email"), table_name="user")
    op.drop_table("user")
    # ### end Alembic commands ###
