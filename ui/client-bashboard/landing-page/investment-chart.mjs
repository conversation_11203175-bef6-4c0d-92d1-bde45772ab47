/**
 * Investment Calculator Module
 *
 * This module handles all functionality for the investment calculator including:
 * - Initializing the Chart.js visualization
 * - Calculating investment growth with a fixed 7% annual return
 * - Updating the chart and summary statistics based on user input
 */
/* global document */

import {
  CategoryScale,
  Chart,
  Filler,
  LinearScale,
  LineController,
  LineElement,
  PointElement,
  Tooltip,
} from "chart.js";
import {
  formatCompactCurrency,
  formatCurrencyInput,
  parseCurrencyInput,
} from "./utils/currency-formatter.mjs";

// Register the components we need
Chart.register(
  LineController,
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Filler,
);

// DOM Elements
const investmentAmountInput = document.querySelector("#investmentAmount");
const monthlyContributionInput = document.querySelector("#monthlyContribution");
const depositLengthSelect = document.querySelector("#depositLength");
const compoundInterestToggle = document.querySelector("#compoundInterest");

// Summary Elements
const depositWithoutInterestEl = document.querySelector("#depositWithoutInterest");
const interestIncomeEl = document.querySelector("#interestIncome");
const totalWithInterestEl = document.querySelector("#totalWithInterest");

// Chart Element and Instance
const chartCanvas = document.querySelector("#investmentChart");
let investmentChart;

/**
 * Calculate investment with monthly contributions
 * @param {number} principal - Initial investment
 * @param {number} monthlyContribution - Monthly contribution amount
 * @param {number} annualRate - Annual interest rate (as a percentage)
 * @param {number} years - Investment period in years
 * @param {boolean} compound - Whether to compound interest
 * @returns Object containing yearly values and totals
 */
function calculateInvestment(
  principal,
  monthlyContribution,
  annualRate,
  years,
  compound = false,
) {
  annualRate = annualRate / 100;
  const annualContribution = 12 * monthlyContribution;

  // Assuming end of month contributions
  let yearlyContributionsInterest = 0;
  for (let month = 1; month < 12; month++) {
    const remainingMonths = 12 - month;
    yearlyContributionsInterest += monthlyContribution * annualRate * (remainingMonths / 12);
  }

  // Initialize arrays to track growth
  const yearlyBalances = [];
  const yearlyContributions = [];
  const yearlyLabels = [];

  let balance = principal;
  let totalContribution = principal;

  for (let year = 0; year < years; year++) {
    const yearInterest = (compound ? balance : totalContribution) * annualRate;

    balance += annualContribution + yearInterest + yearlyContributionsInterest;
    totalContribution += annualContribution;

    yearlyLabels.push(`Year ${year + 1}`);
    yearlyBalances.push(Math.round(balance));
    yearlyContributions.push(Math.round(totalContribution));
  }

  return {
    yearlyLabels,
    yearlyBalances,
    yearlyContributions,
    totalContribution: Math.round(totalContribution),
    interestEarned: Math.round(balance - totalContribution),
    finalBalance: Math.round(balance),
  };
}

/**
 * Initialize the investment chart using Chart.js
 */
const initializeChart = () => {
  const ctx = chartCanvas.getContext("2d");

  investmentChart = new Chart(ctx, {
    type: "line",
    data: {
      labels: [],
      datasets: [
        {
          label: "Principal + Contributions",
          data: [],
          backgroundColor: "rgba(160, 160, 160, 0.2)",
          borderColor: "rgba(160, 160, 160, 1)",
          borderWidth: 2,
          pointRadius: 4,
          pointBackgroundColor: "rgba(160, 160, 160, 1)",
          fill: true,
        },
        {
          label: "Total with Returns",
          data: [],
          backgroundColor: "rgba(21, 93, 252, 0.2)",
          borderColor: "rgba(21, 93, 252, 1)",
          borderWidth: 2,
          pointRadius: 4,
          pointBackgroundColor: "rgba(21, 93, 252, 1)",
          fill: true,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          mode: "index",
          callbacks: {
            label: (context) => {
              return `${context.dataset.label}: ${formatCompactCurrency(context.raw)}`;
            },
          },
        },
      },
      scales: {
        x: {
          grid: {
            display: true,
            color: "rgba(0, 0, 0, 0.05)",
          },
        },
        y: {
          grid: {
            display: true,
            color: "rgba(0, 0, 0, 0.05)",
          },
          ticks: {
            callback: (value) => {
              if (value >= 1e12) {
                return `$${value / 1e12}T`;
              } else if (value >= 1e9) {
                return `$${value / 1e9}B`;
              } else if (value >= 1e6) {
                return `$${value / 1e6}M`;
              } else if (value >= 1000) {
                return `$${value / 1000}K`;
              }
              return `$${value}`;
            },
          },
        },
      },
      animation: {
        duration: 800,
        easing: "easeOutQuart",
      },
    },
  });
};

/**
 * Update the chart and summary statistics based on current input values
 */
const updateCalculations = () => {
  const initialInvestment = parseCurrencyInput(investmentAmountInput.value);
  const monthlyContribution = parseCurrencyInput(monthlyContributionInput.value);
  const years = Number.parseInt(depositLengthSelect.value) || 5;
  // Fixed 7% annual interest rate for Stable Tech's product
  const annualInterestRate = 7;
  // Get compound interest toggle state
  const isCompounding = compoundInterestToggle.checked;

  // Calculate investment growth
  const result = calculateInvestment(
    initialInvestment,
    monthlyContribution,
    annualInterestRate,
    years,
    isCompounding,
  );

  // Update chart data
  investmentChart.data.labels = result.yearlyLabels;
  investmentChart.data.datasets[0].data = result.yearlyContributions;
  investmentChart.data.datasets[1].data = result.yearlyBalances;
  investmentChart.update();

  // Update summary statistics with compact format for large numbers
  depositWithoutInterestEl.textContent = formatCompactCurrency(result.totalContribution);
  interestIncomeEl.textContent = formatCompactCurrency(result.interestEarned);
  totalWithInterestEl.textContent = formatCompactCurrency(result.finalBalance);
};

/**
 * Initialize the calculator and attach event listeners
 */
const initializeCalculator = () => {
  // Initialize the chart
  initializeChart();

  // Calculate initial values
  updateCalculations();

  // Add event listeners
  investmentAmountInput.addEventListener("input", updateCalculations);
  monthlyContributionInput.addEventListener("input", updateCalculations);
  depositLengthSelect.addEventListener("change", updateCalculations);
  compoundInterestToggle.addEventListener("change", updateCalculations);

  // Add input formatting and validation for text input fields
  for (const input of [investmentAmountInput, monthlyContributionInput]) {
    // Format during input as the user types
    input.addEventListener("input", () => {
      // Get the cursor position
      const cursorPos = input.selectionStart;
      const oldLength = input.value.length;

      // Parse and format the input value
      const numericValue = parseCurrencyInput(input.value);

      // Only format if we have a value
      if (numericValue > 0) {
        const formattedValue = formatCurrencyInput(numericValue.toString());

        // Set the formatted value
        input.value = formattedValue;

        // Calculate cursor position adjustment based on the difference in string length
        const newPos = cursorPos + (input.value.length - oldLength);

        // Restore cursor position, but only if the input is currently focused
        if (document.activeElement === input) {
          input.setSelectionRange(newPos, newPos);
        }
      }

      // Update calculations
      updateCalculations();
    });

    // Format on blur - ensure proper formatting when input loses focus
    input.addEventListener("blur", () => {
      const numericValue = parseCurrencyInput(input.value);

      // Ensure values are not negative
      const validValue = numericValue < 0 ? 0 : Math.round(numericValue);

      // Format the value with commas for display
      input.value = formatCurrencyInput(validValue.toString());

      // Update calculations with the sanitized numeric value
      updateCalculations();
    });
  }
};

// Run initialization when DOM is fully loaded
document.addEventListener("DOMContentLoaded", initializeCalculator);
