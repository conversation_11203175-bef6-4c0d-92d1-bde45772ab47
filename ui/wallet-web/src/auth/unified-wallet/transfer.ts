import { UnifiedAccount } from "@/auth/accounts";
import { unifiedSign } from "@/auth/unified-wallet/sign";
import { EthereumRPC } from "@/lib/networks/ethereum-rpc";
import { PublicKeyInput, SolanaRPC } from "@/lib/networks/solana-rpc";
import { KeyDecoder } from "@/lib/secret-store";
import { Hex, isHex, TransactionSerializable } from "viem";

async function solanaTransfer(
  fromAccount: UnifiedAccount,
  params: { amount: number; to: PublicKeyInput; decoder?: KeyDecoder | undefined },
) {
  const rpc = SolanaRPC.create("devnet");
  return await rpc.transfer({
    amount: params.amount,
    from: fromAccount.address,
    to: params.to,
    sign: async (message: Uint8Array) => {
      const result = await unifiedSign(fromAccount, message, params.decoder);
      if (!(result instanceof Uint8Array)) {
        throw new TypeError("Solana transfer should return Uint8Array");
      }
      return result;
    },
  });
}

async function ethereumTransfer(
  fromAccount: UnifiedAccount,
  params: { amount: number; to: Hex; decoder?: KeyDecoder | undefined },
) {
  const rpc = EthereumRPC.create("sepolia");
  if (!isHex(fromAccount.address)) {
    throw new Error("Ethereum transfer requires Hex sender address");
  }
  return await rpc.transfer({
    amount: params.amount,
    from: fromAccount.address,
    to: params.to,
    sign: async (message: TransactionSerializable) => {
      const result = await unifiedSign(fromAccount, message, params.decoder);
      if (!isHex(result)) {
        throw new TypeError("Ethereum transfer should return Hex signature");
      }
      return result;
    },
  });
}

export async function sendTransfer(
  fromAccount: UnifiedAccount,
  params: { amount: number; to: PublicKeyInput; decoder?: KeyDecoder | undefined },
) {
  switch (fromAccount.network) {
    case "solana":
      return await solanaTransfer(fromAccount, {
        amount: params.amount,
        to: params.to,
        decoder: params.decoder,
      });
    case "ethereum":
      if (!isHex(params.to)) {
        throw new TypeError("Ethereum transfer requires Hex receiver address");
      }
      return await ethereumTransfer(fromAccount, {
        amount: params.amount,
        to: params.to,
        decoder: params.decoder,
      });
    default:
      throw new Error(`Unsupported network: ${fromAccount.network}`);
  }
}
