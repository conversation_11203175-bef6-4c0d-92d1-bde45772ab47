import logging
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from sqlalchemy.orm import Session

from stabletech.accounting.crud import AccountedTransa<PERSON><PERSON><PERSON><PERSON>, CapitalPositionManager
from stabletech.accounting.models import AccountedTransaction, AccountedTransactionAction
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Account, Transaction
from stabletech.market.calculate_cost_basis import calculate_swap_cost_basis
from stabletech.utils.get_prices import get_coingecko_prices

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def calculate_swap_sell_price(transaction: Transaction) -> Decimal | None:
    if transaction.action != TransactionAction.swap:
        logger.warning("Expected swap transaction, got %r", transaction.action)
        return None

    accounts_set: set[Account] = set()
    to_transfer = None
    from_asset_amount = Decimal(0)
    to_asset_amount = Decimal(0)

    for transfer in transaction.transfers:
        asset_account = transfer.asset_account
        accounts_set.add(asset_account)
        if transfer.is_deposit:
            to_asset_amount += transfer.amount
            to_transfer = transfer
        elif transfer.is_withdrawal:
            from_asset_amount += transfer.amount

    if len(accounts_set) != 2:
        logger.warning(
            "Expected only 2 assets for swap transaction %d, got %d",
            transaction.id,
            len(accounts_set),
        )
        return None
    if not to_transfer:
        logger.warning("Missing to_transfer for swap transaction: %r", transaction)
        return None

    if (price := get_coingecko_prices(to_transfer, transaction.confirmed_at)) is not None:
        to_transfer.asset_price_usd = price
    else:
        return None

    return to_asset_amount * to_transfer.asset_price_usd / from_asset_amount


def create_capital_position(
    amount: Decimal,
    buy_transaction: AccountedTransaction,
    sell_transaction: AccountedTransaction,
) -> dict:
    """CapitalPosition like dict, for ease of use in bulk_create"""
    return dict(
        amount=amount,
        bought_at=buy_transaction.confirmed_at,
        bought_usd_amount=buy_transaction.asset_price_usd * amount,
        accounted_buy_transaction_id=buy_transaction.id,
        coin_id=(
            sell_transaction.alias_coin.id
            if sell_transaction.alias_coin
            else sell_transaction.coin_id
        ),
        sold_at=sell_transaction.confirmed_at,
        sold_usd_amount=sell_transaction.asset_price_usd * amount,
        accounted_sell_transaction_id=sell_transaction.id,
        filed=False,  # New positions are not filed by default
        external_buy_transaction_id=None,
        external_sell_transaction_id=None,
        csv_row=None,  # Not an imported position
        user_id=None,  # Fund's position
    )


def match_sell_to_buy(
    coin_id: int,
    buy_transactions_by_coin_id: dict[int, list[dict]],
    sell_transaction_id: int,
    sell_transactions_history: dict[int, dict],
    buy_transactions_history: dict[int, dict],
    long_term_threshold_days: int,
    tolerance: Decimal,
    by_holding_period: bool = False,
) -> list[dict]:
    """
    Mutates the buy_transactions_history and sell_transactions_history dictionaries
    to match the sell transaction to the buy transactions and generate capital positions.
    """
    capital_positions = []
    sold_transaction = sell_transactions_history[sell_transaction_id]
    tolerance = Decimal("0.01") if sold_transaction.amount > 1 else Decimal("0.00001")
    for buy_transaction in buy_transactions_by_coin_id[coin_id]:
        bought_transaction = buy_transactions_history[buy_transaction.id]
        sell_amount = sold_transaction.remaining_amount

        if sell_amount <= 0:
            break
        remaining_amount = bought_transaction.remaining_amount
        if (
            bought_transaction.confirmed_at > sold_transaction.confirmed_at
            or remaining_amount <= 0
        ):
            continue
        if (
            sold_transaction.asset_price_usd is None
            or bought_transaction.asset_price_usd is None
        ):
            continue

        def match() -> dict:
            if remaining_amount + tolerance >= sell_amount:
                buy_transactions_history[bought_transaction.id].remaining_amount -= sell_amount
                sell_transactions_history[sold_transaction.id].remaining_amount = 0
                amount = sell_amount
            else:
                buy_transactions_history[bought_transaction.id].remaining_amount = 0
                sell_transactions_history[
                    sold_transaction.id
                ].remaining_amount -= remaining_amount
                amount = remaining_amount
            return create_capital_position(amount, bought_transaction, sold_transaction)

        if by_holding_period:
            holding_period = (
                sold_transaction.confirmed_at.date() - bought_transaction.confirmed_at.date()
            )
            if holding_period >= timedelta(days=long_term_threshold_days):
                new_capital_position = match()
                capital_positions.append(new_capital_position)
        else:
            new_capital_position = match()
            capital_positions.append(new_capital_position)

    remaining_sell_amount = sell_transactions_history[sell_transaction_id].remaining_amount
    if remaining_sell_amount > tolerance:
        logger.info(
            f"Coin id {coin_id}, Couldn't match all sell amount {remaining_sell_amount} for sell transaction {sell_transaction_id} at {sell_transactions_history[sell_transaction_id].confirmed_at}"
        )
        # raise NotEnoughBuyAmountsForSells(
        #     f"Coin id {coin_id}, Couldn't match all sell amount {remaining_sell_amount} for sell transaction {sell_transaction_id}"
        # )
    return capital_positions


def match_open_transactions(
    transactions: list[AccountedTransaction], long_term_threshold_days: int = 365
) -> list[dict]:
    buy_transactions_by_coin_id = {}
    buy_transactions_history = {}
    sell_transactions_history = {}
    for transaction in transactions:
        if transaction.action == AccountedTransactionAction.buy.value:
            if not transaction.alias_coin:
                logger.warning(f"Skipping transaction {transaction.id} with no alias_coin")
                continue
            coin_id = transaction.alias_coin.id
            if coin_id not in buy_transactions_by_coin_id:
                buy_transactions_by_coin_id[coin_id] = []

            buy_transactions_by_coin_id[coin_id].append(transaction)
            buy_transactions_history[transaction.id] = transaction

        if transaction.action == AccountedTransactionAction.sell.value:
            sell_transactions_history[transaction.id] = transaction

    for coin_id in buy_transactions_by_coin_id.keys():
        buy_transactions_by_coin_id[coin_id].sort(key=lambda x: x.confirmed_at)

    capital_positions = []
    sell_transactions = list(sell_transactions_history.values())
    sell_transactions.sort(
        key=lambda x: (x.confirmed_at, x.alias_coin.id if x.alias_coin else x.coin_id)
    )

    for sell_tx in sell_transactions:
        sell_transaction_id = sell_tx.id
        sold_transaction = sell_transactions_history[sell_transaction_id]
        if not sold_transaction.alias_coin:
            logger.warning(
                f"Skipping sell transaction {sell_transaction_id} with no alias_coin"
            )
            continue
        coin_id = sold_transaction.alias_coin.id
        sell_amount = sold_transaction.remaining_amount
        # Ignore miniscule transactions
        tolerance = Decimal("0.01") if sold_transaction.amount > 1 else Decimal("0.00001")

        if sell_amount <= tolerance:
            continue

        if coin_id not in buy_transactions_by_coin_id:
            logger.info(
                f"Couldn't generate Capital Position, no bought amounts found for {coin_id} But have sell transactions "
            )
            # raise NoBuysForSells(
            #     f"Couldn't generate Capital Position, no bought amounts found for {coin_id}"
            #     " But have sell transactions "
            #     f"Total amount {sum(sell_transaction.remaining_amount for sell_transaction in sell_transactions)}"
            # )
            continue

        for by_holding_period in [True, False]:
            new_capital_positions = match_sell_to_buy(
                coin_id,
                buy_transactions_by_coin_id,
                sell_transaction_id,
                sell_transactions_history,
                buy_transactions_history,
                long_term_threshold_days,
                tolerance,
                by_holding_period=by_holding_period,
            )
            capital_positions.extend(new_capital_positions)

    return capital_positions


def get_price(
    db: Session, ac_transaction: AccountedTransaction, is_buy: bool
) -> Decimal | None:
    if ac_transaction.transaction_action is TransactionAction.swap:
        calculate_price = None
        is_buy = ac_transaction.action is TransactionAction.buy.value
        if is_buy:
            calculate_price = calculate_swap_cost_basis
        else:
            calculate_price = calculate_swap_sell_price
        price = calculate_price(ac_transaction.transfer.transaction)
        if price is not None:
            ac_transaction.asset_price_usd = price
            db.commit()

        return price

    transfer = ac_transaction.transfer
    if (price := get_coingecko_prices(transfer, ac_transaction.confirmed_at)) is not None:
        transfer.asset_price_usd = price
        ac_transaction.asset_price_usd = price

    return transfer.asset_price_usd


def generate_new_capital_positions(
    db: Session,
):
    accounted_transaction_manager = AccountedTransactionManager(db)
    open_ac_transactions = accounted_transaction_manager.get_open_transactions()
    logger.info("Generating new CapitalPositions")
    # Discard existing buy and sell transactions with no remaining amout left to match.
    for open_ac_transaction in open_ac_transactions:
        if open_ac_transaction.asset_price_usd is None:
            try:
                get_price(
                    db,
                    open_ac_transaction,
                    open_ac_transaction.action is AccountedTransactionAction.buy.value,
                )
            except Exception as e:
                logger.info(
                    f"Skipped accounted transaction {open_ac_transaction.id} transfer: {open_ac_transaction.transfer_id}, error: {e}",
                    exc_info=True,
                )
                continue

    capital_positions = match_open_transactions(open_ac_transactions)
    if not capital_positions:
        return

    try:
        CapitalPositionManager(db).bulk_insert(capital_positions)
        db.commit()
        logger.info(f"Generated {len(capital_positions)} new CapitalPositions")
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to insert capital positions: {str(e)}", exc_info=True)
        raise


class NoBuysForSells(Exception):
    pass


class NotEnoughBuyAmountsForSells(Exception):
    pass
