"""Add wallet columns to heist claim

Revision ID: 0023
Revises: 0022
Create Date: 2024-03-19 12:37:25.049023

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0023"
down_revision = "0022"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "heistclaim", sa.Column("arresting_officer_wallet_address", sa.String(), nullable=True)
    )
    op.add_column(
        "heistclaim", sa.Column("opposing_wallet_address", sa.String(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("heistclaim", "opposing_wallet_address")
    op.drop_column("heistclaim", "arresting_officer_wallet_address")
    # ### end Alembic commands ###
