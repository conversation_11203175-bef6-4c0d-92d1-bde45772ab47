// ** MUI Imports
import { CloseOutlined, ExpandMore } from "@mui/icons-material";
import { Alert, Box, Collapse, IconButton, Slide, Typography } from "@mui/material";
import Button from "@mui/material/Button";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import Grid from "@mui/material/Grid";
import Stack from "@mui/material/Stack";

import { SimpleTransactionTable } from "@/components/SimpleTransactionTable";
import { SnackSuccess } from "@/components/SnackSuccess";
import { UserSearch } from "@/components/autocomplete/UserSearch";
import { TransactionAdd } from "@/components/transaction/TransactionAdd";
import { currencyFormatter, formatDate } from "@/helpers/formatters";
import { skipToken } from "@reduxjs/toolkit/query";
import { Minus } from "mdi-material-ui";
import { useCallback, useState } from "react";

import {
  useIndexerClientTransactionDeleteMutation,
  useIndexerClientTransactionsQuery,
} from "@/store/services/api";
import { Transaction, User } from "@/types/schemas";

const ClientTransactions = () => {
  const [errorAlert, setErrorAlert] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showAddClientTransaction, setShowAddClientTransaction] = useState<boolean>(false);
  const [selectedClientTransactionId, setSelectedClientTransactionId] = useState<number | null>(
    null,
  );

  const [showCompletedTransactions, setShowCompletedTransactions] = useState<boolean>(true);
  const [showPendingTransactions, setShowPendingTransactions] = useState<boolean>(true);
  const [showSuccessSnack, setShowSuccessSnack] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);

  const [deleteClientTransaction] = useIndexerClientTransactionDeleteMutation();
  const { data: clientTransactions } = useIndexerClientTransactionsQuery(
    selectedUser ? [selectedUser, false] : skipToken,
  );

  const handleSuccess = useCallback(() => {
    setShowSuccessSnack(true);
    setShowAddClientTransaction(false);
    setSelectedClientTransactionId(null);
  }, []);

  return (
    <Grid container spacing={6}>
      <SnackSuccess
        showSuccessSnack={showSuccessSnack}
        setShowSuccessSnack={setShowSuccessSnack}
      />

      {showErrorAlert && <Alert severity={"error"}>{errorAlert}</Alert>}

      <Grid item xs={12}>
        <Card>
          <CardHeader title="Select User" />
          <CardContent>
            <UserSearch selectedUser={selectedUser} setSelectedUser={setSelectedUser} />
          </CardContent>
        </Card>
      </Grid>
      {selectedUser && (
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Pending Transactions"
              action={
                <Stack direction={"row"} spacing={2}>
                  <Button
                    onClick={() => {
                      setSelectedClientTransactionId(null);
                      setShowAddClientTransaction(!showAddClientTransaction);
                    }}
                  >
                    + Client Transaction
                  </Button>
                  <IconButton
                    onClick={() => setShowPendingTransactions(!showPendingTransactions)}
                    size="small"
                  >
                    <ExpandMore />
                  </IconButton>
                </Stack>
              }
            />
            <CardContent>
              <Collapse in={showPendingTransactions} unmountOnExit>
                <Grid container spacing={4}>
                  {clientTransactions &&
                    clientTransactions.length > 0 &&
                    clientTransactions.map((clientTransaction, rowIndex) => {
                      if (!clientTransaction.reviewedAt) {
                        const amount = clientTransaction.amount
                          ? Number.parseFloat(clientTransaction.amount)
                          : 0;
                        return (
                          <Grid item xs={12} key={rowIndex}>
                            <Card>
                              <CardContent>
                                <Stack
                                  direction={"row"}
                                  alignItems={"center"}
                                  justifyContent={"space-between"}
                                >
                                  <Button
                                    color="warning"
                                    variant="outlined"
                                    onClick={() => {
                                      setSelectedClientTransactionId(clientTransaction.id);
                                      setShowAddClientTransaction(true);
                                    }}
                                  >
                                    + Transaction
                                  </Button>
                                  <Typography
                                    pr={2}
                                    color={
                                      clientTransaction.isDeposit
                                        ? "success.main"
                                        : "error.main"
                                    }
                                  >
                                    {clientTransaction.isDeposit ? "Deposit" : "Withdraw"}
                                  </Typography>
                                  <Typography>
                                    {currencyFormatter.format(Math.abs(amount))}{" "}
                                    {clientTransaction.coin.ticker.toUpperCase()}
                                  </Typography>
                                  <Typography>{clientTransaction.network?.name}</Typography>
                                  <Typography>
                                    {formatDate(clientTransaction.createdAt)}
                                  </Typography>
                                  <Box pt={3}>
                                    <Minus
                                      fontSize={"small"}
                                      color="error"
                                      onClick={() => {
                                        deleteClientTransaction(clientTransaction.id)
                                          .unwrap()
                                          .catch((error) => {
                                            setErrorAlert(error.data.detail);
                                            setShowErrorAlert(true);
                                          });
                                      }}
                                    />
                                  </Box>
                                </Stack>
                              </CardContent>
                            </Card>
                          </Grid>
                        );
                      }
                    })}
                </Grid>
              </Collapse>
            </CardContent>
          </Card>
        </Grid>
      )}

      <Slide
        direction={"left"}
        in={showAddClientTransaction && selectedUser !== null}
        mountOnEnter
        unmountOnExit
      >
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title={
                `${selectedClientTransactionId ? "Link" : "Add"} Transaction for ` +
                `${selectedUser?.firstName} ${selectedUser?.lastName} `
              }
              action={
                <Button color={"error"}>
                  <CloseOutlined onClick={() => setShowAddClientTransaction(false)} />
                </Button>
              }
            />
            <CardContent>
              <TransactionAdd
                clientTransactionId={selectedClientTransactionId}
                enabledActions={["loan", "spend"]}
                isClient
                onSuccess={handleSuccess}
                user={selectedUser}
              />
            </CardContent>
          </Card>
        </Grid>
      </Slide>

      {selectedUser && clientTransactions && clientTransactions.length > 0 && (
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Completed Transactions"
              action={
                <IconButton
                  onClick={() => setShowCompletedTransactions(!showCompletedTransactions)}
                  size="small"
                >
                  <ExpandMore />
                </IconButton>
              }
            />

            <Collapse in={showCompletedTransactions} unmountOnExit>
              <CardContent>
                <SimpleTransactionTable
                  rows={
                    clientTransactions
                      .filter(
                        (clientTransaction) =>
                          clientTransaction.reviewedAt &&
                          clientTransaction.transaction !== undefined,
                      )
                      .map((clientTransaction) => {
                        return clientTransaction.transaction;
                      }) as Transaction[]
                  }
                />
              </CardContent>
            </Collapse>
          </Card>
        </Grid>
      )}
    </Grid>
  );
};

ClientTransactions.acl = {
  action: "manage",
  subject: "ClientTransaction",
};

export default ClientTransactions;
