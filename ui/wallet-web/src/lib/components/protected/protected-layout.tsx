"use client";

import { useCubeSignerAuth } from "@/auth/cubesigner-auth-store";
import { NavBar } from "@/lib/components/protected/nav-bar";
import { Navigate, Outlet } from "react-router";

export default function ProtectedLayout() {
  const cubeSignerAuth = useCubeSignerAuth();

  // Only redirect if not authenticated and not already redirecting
  if (cubeSignerAuth.status !== "authenticated") {
    return <Navigate to="/auth" replace />;
  }

  return (
    <div className="flex min-h-dvh">
      <div className="flex w-full flex-col">
        <NavBar />
        <div className="w-full flex-1 overflow-x-auto">
          <Outlet />
        </div>
      </div>
    </div>
  );
}
