"""USD Ticker

Revision ID: 0031
Revises: 0030
Create Date: 2023-08-09 08:39:21.641037

"""

import os

from alembic import op
from passlib.context import CryptContext
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0031"
down_revision = "0030"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class Coin(Base):
    id = Column(Integer, primary_key=True)
    uid: str = Column(String, index=True, unique=True)
    ticker: str = Column(String, nullable=False)
    __tablename__ = "coin"


class User(Base):
    id = Column(Integer, primary_key=True)
    email: str = Column(String, unique=True, index=True, nullable=False)
    first_name: str = Column(String)
    is_active: bool = Column(Boolean, default=True, index=True)
    last_name: str = Column(String)
    password_hash: str | None = Column(String)
    role: str = Column(String, nullable=False)
    __tablename__ = "user"


def upgrade() -> None:
    session = Session(bind=op.get_bind())

    # Change US Dollar ticker from 'dollar' to 'usd' for consistency
    usd_coin = session.query(Coin).filter_by(uid="dollar").one()
    usd_coin.ticker = "usd"
    usd_coin.uid = "usd-dollar"
    session.commit()

    # Create default admin user if doesn't exist already with password 'admin'
    if os.getenv("ENV_NAME") == "local":
        admin_email = os.getenv("ADMIN_EMAIL")
        if admin_email:
            user = session.query(User).filter_by(email=admin_email).one_or_none()
            if not user:
                session.add(
                    User(
                        email=admin_email,
                        first_name="Default",
                        last_name="Admin",
                        password_hash=pwd_context.hash("admin"),
                        role="admin",
                    )
                )
                session.commit()


def downgrade() -> None:
    session = Session(bind=op.get_bind())
    usd_coin = session.query(Coin).filter_by(uid="usd").one()
    usd_coin.ticker = "dollar"
    usd_coin.uid = "dollar"
    session.commit()
