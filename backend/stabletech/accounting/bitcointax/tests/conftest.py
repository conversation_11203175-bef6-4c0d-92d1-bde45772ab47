from datetime import datetime
from decimal import Decimal

import pytest
from fastapi.testclient import TestClient
from pytest_postgresql.janitor import DatabaseJanitor
from sqlalchemy import create_engine, insert, select
from sqlalchemy.orm import sessionmaker

from common.conf import settings
from common.psql import get_db
from stabletech.accounting.models import CapitalPosition
from stabletech.api import app
from stabletech.auth import crud, models
from stabletech.indexer.crud import (
    AccountManager,
    ExchangeManager,
    LendingProtocolManager,
    NetworkManager,
    TransactionManager,
    WalletManager,
)
from stabletech.indexer.models import Coin, Transaction, TransactionAction, Transfer, User
from stabletech.market.crud import CoinManager

SQLALCHEMY_DATABASE_URL = (
    "postgresql+psycopg://"
    f"{settings.postgresql_user}:{settings.postgresql_password}"
    f"@{settings.postgresql_host}/test"
)


@pytest.fixture(scope="session")
def session():
    with DatabaseJanitor(
        user=settings.postgresql_user,
        password=settings.postgresql_password,
        host=settings.postgresql_host,
        port="5432",
        version=14,
        dbname="test",
    ):
        engine = create_engine(SQLALCHEMY_DATABASE_URL)

        from stabletech.accounting import models as accounting_models
        from stabletech.auth import models as auth_models
        from stabletech.client import models as client_models
        from stabletech.indexer import models as indexer_models
        from stabletech.market import models as market_models

        for model in [
            auth_models,
            indexer_models,
            accounting_models,
            market_models,
            client_models,
        ]:
            model.Base.metadata.create_all(bind=engine)
        TestSessionLocal = sessionmaker(bind=engine, expire_on_commit=False)
        yield TestSessionLocal()


@pytest.fixture(scope="session")
def client(session):
    """Create an authenticated test client that uses the override_get_db method to override the get_db dependency."""

    def override_get_db():
        try:
            yield session
            session.commit()
        finally:
            session.close()

    app.dependency_overrides[get_db] = override_get_db
    test_email = "<EMAIL>"
    test_user = session.query(User).filter_by(email=test_email).first()
    if not test_user:
        test_user = crud.UserManager(session).add(test_email)

    if test_user.role != models.Role.admin.value:
        test_user.set_admin_role()
        session.commit()

    test_user.auth_token = crud.AuthTokenManager(session).get_or_create(test_user.id).token

    with TestClient(
        app, headers={"Authorization": f"Bearer {test_user.auth_token}"}
    ) as test_client:
        yield test_client


CAPITAL_POSITIONS = [
    {
        "amount": Decimal(1),
        "bought_at": datetime(2021, 1, 2, 0, 0, 0),
        "bought_usd_amount": Decimal(1000),
        "currency": "BTC",
        "csv_row": 1,
        "sold_at": datetime(2021, 1, 3, 0, 0, 0),
        "sold_usd_amount": Decimal(2000),
    },
]

TRANSACTIONS = [
    {
        "action": TransactionAction.loan,
        "confirmed_at": datetime(2021, 1, 1, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(1000),
                "from": "Income",
                "to": "Wallet",
            }
        ],
    },
    {
        "action": TransactionAction.swap,
        "confirmed_at": datetime(2021, 1, 2, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(1000),
                "from": "Wallet",
                "to": "Exchange",
            },
            {
                "currency": "BTC",
                "amount": Decimal(1),
                "from": "Exchange",
                "to": "Wallet",
            },
        ],
    },
    {
        "action": TransactionAction.swap,
        "confirmed_at": datetime(2021, 1, 3, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(2000),
                "from": "Exchange",
                "to": "Wallet",
            },
            {
                "currency": "BTC",
                "amount": Decimal(1),
                "from": "Wallet",
                "to": "Exchange",
            },
        ],
    },
    {
        "action": TransactionAction.loan,
        "confirmed_at": datetime(2021, 4, 1, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(1000),
                "from": "Income",
                "to": "Wallet",
            }
        ],
    },
    {
        "action": TransactionAction.swap,
        "confirmed_at": datetime(2021, 1, 5, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(1000),
                "from": "Wallet",
                "to": "Exchange",
            },
            {
                "currency": "BTC",
                "amount": Decimal(1),
                "from": "Exchange",
                "to": "Wallet",
            },
        ],
    },
    {
        "action": TransactionAction.swap,
        "confirmed_at": datetime(2021, 1, 7, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(2000),
                "from": "Exchange",
                "to": "Wallet",
            },
            {
                "currency": "BTC",
                "amount": Decimal(1),
                "from": "Wallet",
                "to": "Exchange",
            },
        ],
    },
    {
        "action": TransactionAction.loan,
        "confirmed_at": datetime(2021, 1, 10, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(1000),
                "from": "Income",
                "to": "Wallet",
            }
        ],
    },
    {
        "action": TransactionAction.spend,
        "confirmed_at": datetime(2021, 1, 12, 0, 0, 0),
        "transfers": [
            {
                "currency": "USDC",
                "amount": Decimal(1000),
                "from": "Wallet",
                "to": "Spend",
            }
        ],
    },
]


def clear_capital_positions_and_transactions(session):
    session.query(CapitalPosition).delete()
    session.query(Transfer).delete()
    session.query(Transaction).delete()
    session.commit()


def create_capital_positions(session, capital_positions_data):
    capital_positions = []
    for capital_position in capital_positions_data:
        coin = CoinManager(session).get_coin_by_ticker(capital_position["currency"])
        del capital_position["currency"]
        capital_position["coin_id"] = coin.id
        capital_positions.append(capital_position)
    session.execute(insert(CapitalPosition), capital_positions)
    session.commit()
    return session.scalars(select(CapitalPosition)).all()


@pytest.fixture
def capital_positions(session):
    yield create_capital_positions(session, CAPITAL_POSITIONS)


def get_asset_account(session, network, currency):
    coin = session.query(Coin).filter_by(ticker=currency).one_or_none()
    if not coin:
        coin = (
            session.execute(
                insert(Coin)
                .values(ticker=currency, name=currency, uid=currency, has_prices=True)
                .returning(Coin)
            )
            .scalars()
            .one()
        )
        session.commit()
    acc = AccountManager(session).get_or_create_with_network_id(
        address=currency, network=network, ticker_symbol=currency, coin=coin
    )
    return acc


def get_txn_hash(transaction, transfers):
    return f"{transaction['confirmed_at']}/{transaction['action']}:{','.join([f'{t.asset_account.coin.ticker}:{t.amount}' for t in transfers])}"


def create_transactions(session, transactions_data):
    NAME = "Test"
    URL = "https://test.info/"
    network = NetworkManager(session).get_or_create_by_name(name=NAME)
    wallet = WalletManager(session).get_or_create_by_address(
        address=NAME, name=NAME, networks=[network]
    )
    lending_protocol = LendingProtocolManager(session).get_or_create_by_name(name=NAME, url=URL)
    exchange = ExchangeManager(session).get_or_create_by_name(name=NAME, url=URL)

    income_acc = AccountManager(session).get_or_create_with_network_id(
        address=f"{NAME}Income", network=network
    )
    loan_acc = AccountManager(session).get_or_create_with_network_id(
        address=f"{NAME}Loan",
        network=network,
        lending_protocol=lending_protocol,
    )
    spend_acc = AccountManager(session).get_or_create_with_network_id(
        address=f"{NAME}Spend", network=network
    )
    exchange_acc = AccountManager(session).get_or_create_with_network_id(
        address=f"{NAME}Exchange", network=network, exchange=exchange
    )
    wallet_acc = AccountManager(session).get_or_create_with_network_id(
        address=NAME, network=network, wallet=wallet
    )
    session.commit()
    get_account_from_name = {
        "Income": income_acc,
        "Loan": loan_acc,
        "Spend": spend_acc,
        "Exchange": exchange_acc,
        "Wallet": wallet_acc,
    }
    transaction_manager = TransactionManager(session)
    transactions = []
    for transaction in transactions_data:
        transfers = [
            Transfer(
                asset_account=get_asset_account(session, network, transfer["currency"]),
                amount=Decimal(transfer["amount"]),
                from_account=get_account_from_name[transfer["from"]],
                to_account=get_account_from_name[transfer["to"]],
            )
            for transfer in transaction["transfers"]
        ]
        has_buy = any(
            transfer.from_account == loan_acc and transfer.to_account == wallet_acc
            for transfer in transfers
        )
        transactions.append(
            Transaction(
                action=transaction["action"],
                confirmed_at=transaction["confirmed_at"],
                network=network,
                signer_account=wallet_acc,
                transfers=transfers,
                tx_hash=get_txn_hash(transaction, transfers),
                wallet=wallet,
                is_deposit=(transaction["action"] == TransactionAction.loan.value and has_buy),
            )
        )

    transactions_to_transfers = {tx: tx.transfers for tx in transactions}
    transaction_manager.add_with_transfers(
        None, transactions_to_transfers, skip_existence_check=True
    )

    return session.scalars(select(Transaction)).all()


@pytest.fixture
def transactions(session):
    yield create_transactions(session, TRANSACTIONS)
