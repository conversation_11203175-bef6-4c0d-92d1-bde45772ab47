"""BlockFills

Revision ID: 0047
Revises: 0046
Create Date: 2024-03-23 21:43:15.112923

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0047"
down_revision = "0046"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "blockfillsinstrument",
        sa.<PERSON>umn("instrument_id", sa.Integer(), nullable=False),
        sa.Column("base_asset_ticker", sa.String(), nullable=False),
        sa.Column("contra_asset_ticker", sa.String(), nullable=True),
        sa.Column("description", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_blockfillsinstrument_id"), "blockfillsinstrument", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_blockfillsinstrument_instrument_id"),
        "blockfillsinstrument",
        ["instrument_id"],
        unique=True,
    )
    op.drop_index("ix_bridge_created_by_user_id", table_name="bridge")
    op.drop_constraint("bridge_created_by_user_id_fkey", "bridge", type_="foreignkey")
    op.drop_column("bridge", "created_by_user_id")
    op.drop_index("ix_clienttransaction_created_by_user_id", table_name="clienttransaction")
    op.drop_constraint(
        "clienttransaction_created_by_user_id_fkey", "clienttransaction", type_="foreignkey"
    )
    op.drop_column("clienttransaction", "created_by_user_id")
    op.drop_index("ix_exchange_created_by_user_id", table_name="exchange")
    op.drop_constraint("exchange_created_by_user_id_fkey", "exchange", type_="foreignkey")
    op.drop_column("exchange", "created_by_user_id")
    op.drop_index("ix_farm_created_by_user_id", table_name="farm")
    op.drop_constraint("farm_created_by_user_id_fkey", "farm", type_="foreignkey")
    op.drop_column("farm", "created_by_user_id")
    op.drop_index("ix_lendingprotocol_created_by_user_id", table_name="lendingprotocol")
    op.drop_constraint(
        "lendingprotocol_created_by_user_id_fkey", "lendingprotocol", type_="foreignkey"
    )
    op.drop_column("lendingprotocol", "created_by_user_id")
    op.drop_index("ix_network_created_by_user_id", table_name="network")
    op.drop_constraint("network_created_by_user_id_fkey", "network", type_="foreignkey")
    op.drop_column("network", "created_by_user_id")
    op.drop_index("ix_transaction_created_by_user_id", table_name="transaction")
    op.drop_constraint("transaction_created_by_user_id_fkey", "transaction", type_="foreignkey")
    op.drop_column("transaction", "created_by_user_id")
    op.drop_index("ix_wallet_created_by_user_id", table_name="wallet")
    op.drop_constraint("wallet_created_by_user_id_fkey", "wallet", type_="foreignkey")
    op.drop_column("wallet", "created_by_user_id")

    # ### end Alembic commands ###

    # Add BlockFills Network - ignores if already exists
    op.execute("INSERT INTO network (name) VALUES ('BlockFills') ON CONFLICT DO NOTHING")


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_blockfillsinstrument_instrument_id"), table_name="blockfillsinstrument"
    )
    op.drop_index(op.f("ix_blockfillsinstrument_id"), table_name="blockfillsinstrument")
    op.drop_table("blockfillsinstrument")
    op.add_column(
        "wallet",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "wallet_created_by_user_id_fkey", "wallet", "user", ["created_by_user_id"], ["id"]
    )
    op.create_index(
        "ix_wallet_created_by_user_id", "wallet", ["created_by_user_id"], unique=False
    )
    op.add_column(
        "transaction",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "transaction_created_by_user_id_fkey",
        "transaction",
        "user",
        ["created_by_user_id"],
        ["id"],
    )
    op.create_index(
        "ix_transaction_created_by_user_id", "transaction", ["created_by_user_id"], unique=False
    )
    op.add_column(
        "network",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "network_created_by_user_id_fkey", "network", "user", ["created_by_user_id"], ["id"]
    )
    op.create_index(
        "ix_network_created_by_user_id", "network", ["created_by_user_id"], unique=False
    )
    op.add_column(
        "lendingprotocol",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "lendingprotocol_created_by_user_id_fkey",
        "lendingprotocol",
        "user",
        ["created_by_user_id"],
        ["id"],
    )
    op.create_index(
        "ix_lendingprotocol_created_by_user_id",
        "lendingprotocol",
        ["created_by_user_id"],
        unique=False,
    )
    op.add_column(
        "farm",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "farm_created_by_user_id_fkey", "farm", "user", ["created_by_user_id"], ["id"]
    )
    op.create_index("ix_farm_created_by_user_id", "farm", ["created_by_user_id"], unique=False)
    op.add_column(
        "exchange",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "exchange_created_by_user_id_fkey", "exchange", "user", ["created_by_user_id"], ["id"]
    )
    op.create_index(
        "ix_exchange_created_by_user_id", "exchange", ["created_by_user_id"], unique=False
    )
    op.add_column(
        "clienttransaction",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "clienttransaction_created_by_user_id_fkey",
        "clienttransaction",
        "user",
        ["created_by_user_id"],
        ["id"],
    )
    op.create_index(
        "ix_clienttransaction_created_by_user_id",
        "clienttransaction",
        ["created_by_user_id"],
        unique=False,
    )
    op.add_column(
        "bridge",
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "bridge_created_by_user_id_fkey", "bridge", "user", ["created_by_user_id"], ["id"]
    )
    op.create_index(
        "ix_bridge_created_by_user_id", "bridge", ["created_by_user_id"], unique=False
    )

    # ### end Alembic commands ###
