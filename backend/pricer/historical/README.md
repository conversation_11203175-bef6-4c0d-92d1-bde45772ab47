# Historical Price Data Management

This module provides functionality for storing and querying historical price data for
cryptocurrencies with customizable time intervals.

## Overview

The historical price storage system is designed to efficiently store and query time-series price
data for cryptocurrencies. It uses PostgreSQL's specialized functions for time-series data
processing, particularly the `date_bin` function for precise interval binning.

## Features

- Store time-series price data with the `CoinHistoricalPrice` model
- Query OHLCV (Open, High, Low, Close, Volume) data for specific time ranges
- Support for different time intervals (1h, 4h, 1d, 1w)
- Efficient data retrieval using PostgreSQL's `date_bin` function

## Technical Implementation

### Database Schema

The `CoinHistoricalPrice` model includes:

- `id`: Auto-incrementing primary key
- `coingecko_id`: String identifier from CoinGecko
- `timestamp`: UTC timestamp of the price point
- `price_usd`: Price in USD (Numeric with precision=32, scale=18)
- `volume_24h_usd`: 24-hour trading volume in USD (Numeric with precision=32, scale=18)
- `market_cap_usd`: Market capitalization in USD (Numeric with precision=32, scale=18)

A composite index on `(coingecko_id, timestamp DESC)` ensures efficient querying.

### Time Interval Aggregation

The system supports the following time intervals:

| Interval | Seconds | Description |
| -------- | ------- | ----------- |
| 1h       | 3,600   | Hourly data |
| 4h       | 14,400  | 4-hour data |
| 1d       | 86,400  | Daily data  |
| 1w       | 604,800 | Weekly data |

PostgreSQL's `date_bin` function is used to aggregate data into these intervals, providing
accurate OHLCV (Open, High, Low, Close, Volume) data for each time period.

### Performance Considerations

- The composite index on `(coingecko_id, timestamp DESC)` optimizes queries by coin ID and time
  range
- Window functions are used for efficient OHLCV calculations within each time interval
- Query performance should be under 500ms for typical requests

## Usage Examples

The following examples demonstrate how to use the CoinHistoricalPriceManager for common
operations:

### Basic Setup

```python
from datetime import datetime, timedelta, timezone
from decimal import Decimal

from common.psql import SessionLocal
from pricer.historical.crud import CoinHistoricalPriceManager

# Get database session
db = SessionLocal()
manager = CoinHistoricalPriceManager(db)
```

### Recording Price Data

#### With Current Timestamp

```python
# Recording a price with current timestamp
manager.record_price(
    coingecko_id="bitcoin",
    price_usd=Decimal("100000.00"),
    volume_24h_usd=Decimal("1000000.00"),
    market_cap_usd=Decimal("580000000.00")
)

# Commit the transaction
db.commit()
```

#### With Specific Timestamp

```python
# Recording a price with a specific timestamp
manager.record_price(
    coingecko_id="bitcoin",
    price_usd=Decimal("29500.00"),
    volume_24h_usd=Decimal("950000.00"),
    market_cap_usd=Decimal("570000000.00"),
    timestamp=datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
)

# Commit the transaction
db.commit()
```

### Querying Historical Data

```python
# Define time range
end_time = datetime.now(timezone.utc)
start_time = end_time - timedelta(days=7)

# Get hourly data for the last week
prices = manager.get_price_series(
    coingecko_id="bitcoin",
    start_time=start_time,
    end_time=end_time,
    interval="1h"
)

# Show the results
for timestamp, open_price, high_price, low_price, close_price, volume in prices:
    print(f"{timestamp} O: {open_price:.2f} H: {high_price:.2f} L: {low_price:.2f} C: {close_price:.2f} V: {volume:.2f}")
```

### Getting Latest Prices

#### Single Latest Price

```python
# Get the latest price record
latest_price = manager.get_latest_price(coingecko_id="bitcoin")

if latest_price:
    print(f"Latest price: {latest_price.price_usd} at {latest_price.timestamp}")
```

#### Latest Prices with Interval Aggregation

```python
# Get latest prices with 1-hour intervals (last 7 days of hourly data)
latest_hourly_prices = manager.get_latest_prices_by_interval(
    coingecko_id="bitcoin",
    interval="1h",
    limit=168  # Last 7 days of hourly data
)

# Process the results
for timestamp, open_price, high_price, low_price, close_price, volume in latest_hourly_prices:
    print(f"{timestamp} O: {open_price:.2f} H: {high_price:.2f} L: {low_price:.2f} C: {close_price:.2f} V: {volume:.2f}")
```

## Integration with Other Services

The historical price storage system is designed to be used by other services that need
historical price data. It provides a clean API for storing and querying price data with
customizable time intervals.

Future integrations will include:

- API endpoints for accessing historical price data
- Integration with price collection services
- Support for additional time intervals and aggregation methods
