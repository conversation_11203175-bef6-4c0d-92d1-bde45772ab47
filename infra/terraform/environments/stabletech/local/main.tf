resource "time_static" "last_build_time" {}

locals {
  docker_build = {
    container_registry = "temet/"
    # Run `terraform taint time_static.last_build_time` to force rebuild
    image_version = time_static.last_build_time.unix
  }

  env_name  = "local"
  namespace = kubernetes_namespace.stabletech.metadata[0].name

  host_volumes = [
    {
      name       = "ipython"
      host_path  = "/data/ipython/"
      mount_path = "/root/.ipython"
    },
    {
      name       = "src"
      host_path  = var.host_path
      mount_path = "/usr/src/app"
    },
  ]
}

resource "kubernetes_namespace" "stabletech" {
  metadata {
    name = var.namespace
  }
}

module "postgres_secret" {
  source = "../../../modules/immutable_secret"

  prefix    = "postgres"
  namespace = local.namespace
  data = {
    POSTGRESQL_HOST     = "postgresql.${local.namespace}.svc.cluster.local"
    POSTGRESQL_PASSWORD = "password"
    POSTGRESQL_USER     = "postgres"
  }
}

module "common_config_map" {
  source = "../../../modules/immutable_config_map"

  prefix    = "common"
  namespace = local.namespace
  data = {
    ADMIN_EMAIL = var.admin_email
    NAMESPACE   = local.namespace
    ENV_NAME    = local.env_name
    PRICER_URL  = "https://internal.stabletech.capital/"
  }
}

module "helm_infra" {
  source = "../../../modules/helm_infra"

  enable_postgresql = true
  namespace         = local.namespace
}

module "python_runner" {
  source = "../../../modules/docker_image"

  app_name     = "python-runner"
  docker_build = local.docker_build
  target       = "dev"
  dockerfile   = "infra/docker/python-runner.dockerfile"
  path         = "../../../../../"
}

module "init" {
  source     = "../modules/init"
  depends_on = [module.helm_infra]

  host_volumes        = local.host_volumes
  namespace           = local.namespace
  python_runner_image = module.python_runner.image_name

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name]
}

module "api" {
  source     = "../modules/api"
  depends_on = [module.helm_infra, module.init]

  host_volumes          = local.host_volumes
  namespace             = local.namespace
  enable_liveness_probe = false
  enable_dev_mode       = true
  python_runner_image   = module.python_runner.image_name

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name]
}

module "liabilities_reminder" {
  source     = "../modules/liabilities_reminder"
  depends_on = [module.helm_infra, module.init]

  host_volumes        = local.host_volumes
  namespace           = local.namespace
  python_runner_image = module.python_runner.image_name

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name]
}

module "market" {
  source     = "../modules/market"
  depends_on = [module.helm_infra, module.init]

  host_volumes        = local.host_volumes
  namespace           = local.namespace
  python_runner_image = module.python_runner.image_name

  config_maps = [module.common_config_map.name]
  secrets     = [module.postgres_secret.name]
}

resource "helm_release" "metabase" {
  // https://artifacthub.io/packages/helm/pmint93/metabase
  depends_on = [module.helm_infra, module.init]
  name       = "metabase"
  namespace  = local.namespace
  repository = "https://pmint93.github.io/helm-charts"
  chart      = "metabase"
  version    = "2.5.0"

  values = [yamlencode({
    database : {
      host : module.postgres_secret.data.POSTGRESQL_HOST
      dbname : "metabase"
      username : module.postgres_secret.data.POSTGRESQL_USER
      password : module.postgres_secret.data.POSTGRESQL_PASSWORD
      type : "postgres"
    }
    service : {
      type : "NodePort"
    }
  })]
}
