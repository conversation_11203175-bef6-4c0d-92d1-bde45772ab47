"""Coin networks

Revision ID: 0029
Revises: 0028
Create Date: 2023-08-04 11:31:11.950516

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0029"
down_revision = "0028"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "coin_network_association",
        sa.<PERSON>n("coin_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("network_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"]),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"]),
        sa.PrimaryKeyConstraint("coin_id", "network_id"),
    )
    op.drop_index("ix_clienttransaction_transaction_id", table_name="clienttransaction")
    op.create_index(
        op.f("ix_clienttransaction_transaction_id"),
        "clienttransaction",
        ["transaction_id"],
        unique=True,
    )
    op.drop_index("ix_wallet_address", table_name="wallet")
    op.create_index(op.f("ix_wallet_address"), "wallet", ["address"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_wallet_address"), table_name="wallet")
    op.create_index("ix_wallet_address", "wallet", ["address"], unique=False)
    op.drop_index(op.f("ix_clienttransaction_transaction_id"), table_name="clienttransaction")
    op.create_index(
        "ix_clienttransaction_transaction_id",
        "clienttransaction",
        ["transaction_id"],
        unique=False,
    )
    op.drop_table("coin_network_association")
    # ### end Alembic commands ###
