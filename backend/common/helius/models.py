from pydantic import AliasChoices, BaseModel, Field, RootModel, field_validator
from typing_extensions import Annotated


class Authorities(BaseModel):
    address: str
    scopes: list[str]


class Compression(BaseModel):
    eligible: bool
    compressed: bool
    data_hash: str
    creator_hash: str
    asset_hash: str
    tree: str
    seq: int
    leaf_id: int


class Grouping(BaseModel):
    group_key: str
    group_value: str
    # collection_metadata: Ignored it is repeated information


class Royalty(BaseModel):
    royalty_model: str
    target: str | None
    percent: float
    basis_points: int
    primary_sale_happened: bool
    locked: bool


class Creator(BaseModel):
    address: str
    share: int
    verified: bool


class Ownership(BaseModel):
    frozen: bool
    delegated: bool
    delegate: str | None
    ownership_model: str
    owner: str


class Image(BaseModel):
    image: str = ""


class Attribute(BaseModel):
    traitType: Annotated[
        str | None, Field(validation_alias=AliasChoices("traitType", "trait_type"))
    ] = None
    value: str | None = None

    @field_validator("value", mode="before")
    @classmethod
    def convert_int_to_str(cls, v):
        return str(v)


class TokenFile(BaseModel):
    type: str | None = None
    uri: str | None = None
    cdn_uri: str | None = None
    mime: str | None = None


class TokenLinks(BaseModel):
    image: str | None = None
    external_url: str | None = None


class ContentMetadata(BaseModel):
    attributes: list[Attribute] = []
    description: str | None = None
    name: str | None = None
    symbol: str | None = None
    token_standard: str | None = None


class File(BaseModel):
    uri: str
    cdn_uri: str
    mime: str


class Content(BaseModel):
    json_uri: str
    files: list[File]
    metadata: ContentMetadata
    links: Image


class NFT(BaseModel):
    interface: str
    id: str
    content: Content
    authorities: list[Authorities]
    compression: Compression
    grouping: list[Grouping]
    royalty: Royalty
    creators: list[Creator]
    ownership: Ownership
    # Removed due to validation error
    # see https://stable-tech.slack.com/archives/C04BH412YEB/p1711368084653319
    # supply: int | None
    mutable: bool
    burnt: bool


class Result(BaseModel):
    grand_total: int
    total: int
    limit: int
    page: int
    items: list[NFT]


class CollectionResponse(BaseModel):
    jsonrpc: str
    result: Result
    id: str


class TokenMetadataContent(BaseModel):
    token_schema: Annotated[str, Field(validation_alias=AliasChoices("$schema"))]
    json_uri: str
    files: list[TokenFile]
    metadata: ContentMetadata
    links: TokenLinks


class TokenMetadataResult(BaseModel):
    interface: str
    id: str
    content: TokenMetadataContent


class TokenMetadataResponse(BaseModel):
    jsonrpc: str
    result: list[TokenMetadataResult | None]
    id: str


class TokenTransfer(BaseModel):
    fromTokenAccount: str
    toTokenAccount: str
    fromUserAccount: str
    toUserAccount: str
    tokenAmount: float
    mint: str
    tokenStandard: str


class NativeTransfer(BaseModel):
    fromUserAccount: str
    toUserAccount: str
    amount: float


class Transaction(BaseModel):
    description: str
    type: str
    source: str
    fee: float
    feePayer: str
    signature: str
    slot: float
    timestamp: int
    tokenTransfers: list[TokenTransfer]
    nativeTransfers: list[NativeTransfer]


TransactionsResponse = RootModel[list[Transaction]]
