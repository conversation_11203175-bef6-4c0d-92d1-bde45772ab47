"use client";

import * as RadixTooltip from "@radix-ui/react-tooltip";
import { cx } from "class-variance-authority";
import { forwardRef } from "react";

export * from "@radix-ui/react-tooltip";

/**
 * Beware that the content on the tooltip won't be visible on touch devices.
 *
 * Use a popover instead if you need to support touch devices.
 */
export const TooltipContent = forwardRef<
  React.ElementRef<typeof RadixTooltip.Content>,
  React.ComponentPropsWithoutRef<typeof RadixTooltip.Content>
>(function TooltipContent({ className, sideOffset = 4, ...props }, ref) {
  return (
    <RadixTooltip.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cx(
        "z-50 animate-in overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md fade-in-0 zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
        className,
      )}
      {...props}
    />
  );
});

export { TooltipContent as Content };
