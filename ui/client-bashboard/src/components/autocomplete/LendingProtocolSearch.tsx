import { addressFormatter } from "@/helpers/formatters";
import { useIndexerLendingProtocolsQuery } from "@/store/services/api";
import { LendingProtocolWithAccountAddress } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { Dispatch, SyntheticEvent } from "react";

interface LendingProtocolSearchProps {
  disabled?: boolean;
  hidden?: boolean;
  label?: string;
  selectedLendingProtocol: LendingProtocolWithAccountAddress | null;
  setSelectedLendingProtocol: Dispatch<LendingProtocolWithAccountAddress | null>;
}

export const LendingProtocolSearch = (props: LendingProtocolSearchProps) => {
  const selectedLendingProtocol = props.selectedLendingProtocol;
  const setSelectedLendingProtocol = props.setSelectedLendingProtocol;
  const { data: lendingProtocolsData } = useIndexerLendingProtocolsQuery();

  return (
    <Autocomplete
      fullWidth
      hidden={props.hidden}
      disabled={props.disabled}
      getOptionLabel={(option) =>
        `${option.name} | ${option.network?.name ?? "—"} | ${option.accountAddress ? addressFormatter(option.accountAddress) : "—"}`
      }
      isOptionEqualToValue={(option, value) =>
        option.id === value.id &&
        option.accountAddress === value.accountAddress &&
        option.network?.id === value.network?.id
      }
      options={lendingProtocolsData || []}
      onChange={(_: SyntheticEvent, newValue: LendingProtocolWithAccountAddress | null) => {
        setSelectedLendingProtocol(newValue);
      }}
      renderInput={(params) => (
        <TextField {...params} label={props.label || "Lending Protocol"} />
      )}
      value={selectedLendingProtocol}
    />
  );
};
