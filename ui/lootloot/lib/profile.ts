import { apiMutation } from "@/lib/api";
import { useUpdateUser } from "@/lib/auth-ctx";
import { UpdateUserInfo, User } from "@/types/schemas";
import { useMutation } from "@tanstack/react-query";

export function useUpdateProfile() {
  const updateUser = useUpdateUser();
  return useMutation({
    mutationFn: updateProfile,
    onSuccess: updateUser,
  });
}

async function updateProfile(params: {
  firstName?: string | undefined | null;
  lastName?: string | undefined | null;
  displayName?: string | undefined | null;
}) {
  const { firstName = null, lastName = null, displayName = null } = params;
  return apiMutation<User>({
    method: "POST",
    resource: "profile",
    body: {
      first_name: firstName,
      last_name: lastName,
      display_name: displayName,
    } satisfies UpdateUserInfo,
  });
}
