import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { getErrorMessage } from "@/lib/error";
import { useState } from "react";

interface State {
  readonly error: string;
  readonly isLoading: boolean;
  readonly open: boolean;
}

const INITIAL_STATE: State = { error: "", isLoading: false, open: false };
const OPEN_STATE: State = { error: "", isLoading: false, open: true };
const LOADING_STATE: State = { error: "", isLoading: true, open: true };

interface ConfirmationButtonProps extends Dialog.DialogTriggerProps {
  readonly action: (() => void) | (() => Promise<unknown>);
  readonly message: React.ReactNode;
  readonly title: string;
}

export function ConfirmationButton(props: ConfirmationButtonProps) {
  const { action, message, title, ...triggerProps } = props;
  const [state, setState] = useState(INITIAL_STATE);

  const close = () => {
    setState((prev) => ({ ...prev, open: false }));
  };

  async function runAction() {
    try {
      setState(LOADING_STATE);
      await action();
      close();
    } catch (error) {
      setState({ error: getErrorMessage(error), isLoading: false, open: true });
    }
  }

  return (
    <Dialog.Root
      open={state.open}
      onOpenChange={(isOpen) => {
        if (isOpen) setState(OPEN_STATE);
        else close();
      }}
    >
      <Dialog.Trigger {...triggerProps} />
      <Dialog.Content title={title} aria-describedby="">
        {message}
        <div className="mt-6">
          {state.error && <AlertError className="mb-3">{state.error}</AlertError>}
          <div className="flex gap-4">
            <Button variant="secondary" disabled={state.isLoading} onClick={close}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              disabled={state.isLoading}
              onClick={() => {
                if (state.isLoading) return;
                void runAction();
              }}
            >
              Confirm
            </Button>
          </div>
        </div>
      </Dialog.Content>
    </Dialog.Root>
  );
}
