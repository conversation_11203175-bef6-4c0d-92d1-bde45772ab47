"""Transaction comment

Revision ID: 0033
Revises: 0032
Create Date: 2023-08-18 19:16:59.716644

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0033"
down_revision = "0032"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "transaction", sa.Column("comment", sa.String(), server_default="", nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("transaction", "comment")
    # ### end Alembic commands ###
