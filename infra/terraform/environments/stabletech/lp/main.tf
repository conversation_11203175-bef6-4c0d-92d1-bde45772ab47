resource "kubernetes_namespace" "namespace" {
  metadata {
    name = var.namespace
  }
}

resource "google_dns_managed_zone" "lp_stabletech_capital" {
  name     = "lp-stabletech-capital"
  dns_name = "lp.stabletech.capital."

  dnssec_config {
    state = "on"
  }
}

module "stabletech" {
  source = "../modules/stabletech"

  api_domain        = "api.lp.stabletech.capital"
  env_name          = "lp"
  managed_zone_name = google_dns_managed_zone.lp_stabletech_capital.name
  namespace         = var.namespace
  postgresql_db     = "lp"
  release           = local.release
  secrets           = local.secrets
}

module "dashboard_ui" {
  source = "../modules/ui"

  dns_managed_zone    = google_dns_managed_zone.lp_stabletech_capital
  namespace           = var.namespace
  project_id          = local.project_id
  website_domain_name = "lp.stabletech.capital"
}
