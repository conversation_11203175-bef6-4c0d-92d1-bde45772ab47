import logging
from typing import Sequence

from sqlalchemy import case, func, select, update
from sqlalchemy.orm import Session

from stabletech.indexer.models import Account, AliasCoin, Coin, Transaction, Transfer
from stabletech.utils import slack

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

coins_in_use_but_disabled_prices = (
    select(func.coalesce(AliasCoin.coin_id, Coin.id))
    .distinct()
    .select_from(Coin)
    .outerjoin(AliasCoin, AliasCoin.id == Coin.alias_coin_id)
    .join(Account, Account.coin_id == Coin.id)
    .join(Transfer, Transfer.asset_account_id == Account.id)
    .join(Transaction, Transaction.id == Transfer.transaction_id)
    .where(
        case(
            (
                AliasCoin.id != None,
                select(Coin.has_prices)
                .correlate(AliasCoin)
                .where(Coin.id == AliasCoin.coin_id)
                .scalar_subquery(),
            ),
            else_=Coin.has_prices,
        ).is_not(True),
        Transaction.action != None,
    )
)


def enable_prices_of_coins_in_use(db: Session) -> Sequence[Coin]:
    return db.scalars(
        update(Coin).where(Coin.id.in_(coins_in_use_but_disabled_prices)).returning(Coin),
        {"has_prices": True},
    ).all()


if __name__ == "__main__":
    from common.psql import SessionLocal

    db = SessionLocal()
    with db.begin():
        coins_enabled = enable_prices_of_coins_in_use(db)
    for coin in coins_enabled:
        ticker = coin.ticker.upper()
        logger.info("Enabled prices for coin %d -- %s (%s)", coin.id, coin.name, ticker)
        slack.post_message(
            slack.stable_tech_channel(),
            f"Auto-enabled prices for {coin.name} ({ticker})",
        )
    if not coins_enabled:
        logger.info("No coins to enable prices for")
