"""Add fees_asset_account to transaction

Revision ID: 0056
Revises: 0055
Create Date: 2024-06-22 07:59:57.560130

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0056"
down_revision = "0055"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "transaction", sa.Column("fees_asset_account_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        "transaction_fees_asset_account_id_fkey",
        "transaction",
        "account",
        ["fees_asset_account_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "transaction_fees_asset_account_id_fkey", "transaction", type_="foreignkey"
    )
    op.drop_column("transaction", "fees_asset_account_id")
    # ### end Alembic commands ###
