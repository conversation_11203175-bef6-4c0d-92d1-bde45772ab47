import { NetworkSearchWithAdminWallets } from "@/components/autocomplete/NetworkSearch";
import { WalletSearch } from "@/components/autocomplete/WalletSearch";
import { DATETIME_FORMAT, formatDateTime } from "@/helpers/formatters";
import { Network, Wallet } from "@/types/schemas";
import { Clear } from "@mui/icons-material";
import { TextField } from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Stack from "@mui/material/Stack";
import ToggleButton from "@mui/material/ToggleButton";
import * as chrono from "chrono-node";
import { useState } from "react";

export interface State {
  comment: string | null;
  confirmedAt: string | null;
  feesPaid: string;
  network: Network | null;
  txHash: string;
  wallet: Wallet | null;
}

interface Props<S extends State> {
  state: State;
  onChange: <K extends keyof S>(key: K, value: S[K]) => void;
}

export function TransactionAttributes<S extends State>(props: Props<S>) {
  const { state, onChange } = props;

  const [confirmedAtText, setConfirmedAtText] = useState<string>("");
  const [isUtc, setIsUtc] = useState(false);

  return (
    <Stack gap={4} flex={1} minWidth={200}>
      <NetworkSearchWithAdminWallets
        selectedNetwork={state.network}
        setSelectedNetwork={(network) => {
          onChange("network", network);
          onChange("wallet", null);
        }}
      />
      <WalletSearch
        isAdmin
        disabled={!state.network}
        network={state.network}
        selectedWallet={state.wallet}
        setSelectedWallet={(wallet) => onChange("wallet", wallet)}
      />
      <TextField
        fullWidth
        label={"Confirmed At"}
        onFocus={() => {
          onChange("confirmedAt", null);
        }}
        onBlur={(event) => {
          let { value } = event.target;
          if (!value) return;
          // Some explorers (e.g. Etherscan) add "+UTC" to the date which
          // causes chrono to fail parsing the date as UTC. Removing the
          // `+` fixes the issue.
          value = value.replace(/(?<=\s)\+(?=UTC(?:$|\W))/u, "");
          const parsedDate = chrono.parseDate(value, isUtc ? { timezone: "UTC" } : undefined);
          if (parsedDate) {
            onChange("confirmedAt", parsedDate.toISOString());
            setConfirmedAtText(formatConfirmedAt(parsedDate, isUtc));
          } else {
            onChange("confirmedAt", null);
            setConfirmedAtText("Invalid Date");
          }
        }}
        onChange={(event) => {
          setConfirmedAtText(event.target.value);
        }}
        value={
          state.confirmedAt ? formatConfirmedAt(state.confirmedAt, isUtc) : confirmedAtText
        }
        InputProps={{
          startAdornment: (
            <ToggleButton
              selected={isUtc}
              onChange={() => {
                const toggledUtc = !isUtc;
                setIsUtc(toggledUtc);
                setConfirmedAtText(
                  state.confirmedAt ? formatConfirmedAt(state.confirmedAt, toggledUtc) : "",
                );
              }}
              value=""
              sx={{ fontSize: "small", py: 0.5, px: 1.5, mr: 2 }}
            >
              UTC
            </ToggleButton>
          ),
          endAdornment: (
            <IconButton
              sx={{ visibility: confirmedAtText ? "visible" : "hidden" }}
              onClick={() => {
                onChange("confirmedAt", null);
                setConfirmedAtText("");
              }}
            >
              <Clear />
            </IconButton>
          ),
        }}
      />
      <TextField
        fullWidth
        label={"Tx Hash"}
        onChange={(event) => {
          onChange("txHash", event.target.value.trim());
        }}
        value={state.txHash || ""}
      />
      <TextField
        fullWidth
        label={"Tx Fee"}
        type={"number"}
        onChange={(event) => {
          onChange("feesPaid", event.target.value);
        }}
        value={state.feesPaid}
      />
      <TextField
        fullWidth
        label="Comment"
        onChange={(event) => {
          onChange("comment", event.target.value);
        }}
        value={state.comment ?? ""}
      />
    </Stack>
  );
}

function formatConfirmedAt(date: Date | string, utc: boolean) {
  return formatDateTime(date, { template: `${DATETIME_FORMAT} Z`, utc });
}
