import { TransferAdd } from "@/components/add/TransferAdd";
import {
  State as AttributesState,
  TransactionAttributes,
} from "@/components/transaction/Attributes";
import {
  Classification,
  State as ClassificationState,
} from "@/components/transaction/Classification";
import { getRelevantEntities } from "@/components/transaction/TransactionClassify";
import { updateStateField } from "@/helpers/state";
import {
  useIndexerTransactionClientTransactionQuery,
  useIndexerTransactionReplaceMutation,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import {
  AddTransfer,
  ClientTransactionWithUser,
  Transaction,
  TransactionAction,
  TransactionUnclassified,
} from "@/types/schemas";
import { Add } from "@mui/icons-material";
import { Alert, Box, Button, CircularProgress, LinearProgress, Stack } from "@mui/material";
import { useState } from "react";

interface TransactionEditProps {
  transaction: Transaction | TransactionUnclassified;
  onSaved: () => void;
}

export interface EditState extends AttributesState, ClassificationState {
  transfers: AddTransfer[];
}

const POSSIBLE_TRANSACTION_ACTIONS: readonly TransactionAction[] = [
  "bridge",
  "income",
  "loan",
  "pool",
  "spend",
  "stake",
  "swap",
];

export function TransactionEdit(props: TransactionEditProps) {
  const { transaction, onSaved } = props;

  const clientTransactionQuery = useClientTransaction(transaction);

  if (clientTransactionQuery.isLoading) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" minHeight={200} p={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (!clientTransactionQuery.isSuccess) {
    return (
      <Box>
        <Alert severity="error">
          Error loading transaction data: {getErrorDetail(clientTransactionQuery.error)}
        </Alert>
      </Box>
    );
  }

  return (
    <TransactionEditForm
      key={transaction.id} // Reset state if transaction changes
      clientTransaction={clientTransactionQuery.data}
      possibleActions={POSSIBLE_TRANSACTION_ACTIONS}
      transaction={transaction}
      onSaved={onSaved}
    />
  );
}

interface TransactionEditFormProps extends TransactionEditProps {
  possibleActions: readonly TransactionAction[];
  clientTransaction: ClientTransactionWithUser | null;
}

function TransactionEditForm(props: TransactionEditFormProps) {
  const { possibleActions, clientTransaction, transaction, onSaved } = props;

  const [replace, replaceStatus] = useIndexerTransactionReplaceMutation();

  const [state, setState] = useState(() => deriveState(transaction, clientTransaction));
  const [validationError, setValidationError] = useState("");

  const handleChange = <K extends keyof EditState>(field: K, value: EditState[K]) => {
    updateStateField(setState, field, value);
  };

  const handleTransferChange = (index: number, transfer: AddTransfer | null) => {
    setState((prevState) => {
      const transfers = [...prevState.transfers];
      if (transfer != null) {
        transfers[index] = transfer;
      } else {
        transfers.splice(index, 1);
      }
      return { ...prevState, transfers };
    });
  };

  const handleSave = () => {
    if (state.action == null) {
      setValidationError("Missing an action");
      return;
    }
    if (state.network == null) {
      setValidationError("Missing a network");
      return;
    }
    if (state.wallet == null) {
      setValidationError("Missing a wallet");
      return;
    }
    setValidationError("");
    replace({
      addTransaction: {
        action: state.action,
        bridgeId: state.bridge?.id ?? null,
        exchangeId: state.exchange?.id ?? null,
        farmId: state.farm?.id ?? null,
        isCollateral: state.isCollateral ?? false,
        isDeposit: state.isDeposit,
        isForPooledFund: state.isForPooledFund,
        lendingProtocolId: state.lendingProtocol?.id ?? null,
        userId: state.user?.id ?? null,
        comment: state.comment,
        confirmedAt: state.confirmedAt ?? "",
        feesPaid: state.feesPaid,
        networkId: state.network.id,
        transfers: state.transfers,
        txHash: state.txHash,
        walletId: state.wallet.id,
      },
      clientTransactionId: state.clientTransactionId,
      transactionId: transaction.id,
    })
      .unwrap()
      .then(() => {
        onSaved();
      });
  };

  return (
    <Stack direction="row" gap={6}>
      <TransactionAttributes<EditState> state={state} onChange={handleChange} />

      <Classification<EditState>
        possibleActions={possibleActions}
        state={state}
        onChange={handleChange}
      >
        <Stack gap={4} width="100%">
          {state.transfers.map((transfer, index) => {
            return (
              <TransferAdd
                index={index}
                key={index}
                transaction={state}
                transfer={transfer}
                onTransferChange={handleTransferChange}
                variant="outlined"
              />
            );
          })}

          <Box height={4} my={-2}>
            {replaceStatus.isLoading && <LinearProgress color="inherit" />}
          </Box>

          <Stack direction="row" gap={6} justifyContent="space-between">
            <Button disabled={replaceStatus.isLoading} variant="outlined" onClick={handleSave}>
              Save
            </Button>
            <Button
              disabled={replaceStatus.isLoading}
              onClick={() => {
                handleChange("transfers", [
                  ...state.transfers,
                  {
                    amount: "0",
                    assetAccount: { address: "" },
                    fromAccount: { address: "" },
                    toAccount: { address: "" },
                  },
                ]);
              }}
            >
              <Add sx={{ mr: 1 }} />
              Transfer
            </Button>
          </Stack>
        </Stack>

        {validationError ? (
          <Alert severity="error">{validationError}</Alert>
        ) : replaceStatus.error ? (
          <Alert severity="error">Error saving: {getErrorDetail(replaceStatus.error)}</Alert>
        ) : null}
      </Classification>
    </Stack>
  );
}

function useClientTransaction(transaction: Transaction | TransactionUnclassified) {
  const isUnclassified = !("action" in transaction);
  const clientTransactionQuery = useIndexerTransactionClientTransactionQuery(transaction.id, {
    skip: isUnclassified,
  });

  if (isUnclassified) {
    return {
      data: null,
      error: null,
      isError: false,
      isLoading: false,
      isSuccess: true,
    };
  }

  return clientTransactionQuery;
}

function deriveState(
  transaction: Transaction | TransactionUnclassified,
  clientTransaction: ClientTransactionWithUser | null,
): EditState {
  const { bridge, exchange, farm, lendingProtocol } = getRelevantEntities(transaction);

  const action = "action" in transaction ? transaction.action : null;

  return {
    transfers: transaction.transfers,

    // Attributes
    comment: transaction.comment,
    confirmedAt: transaction.confirmedAt,
    feesPaid: transaction.feesPaid ?? "",
    network: transaction.network,
    txHash: transaction.txHash,
    wallet: transaction.wallet,

    // Classification
    action,
    bridge,
    clientTransactionId: clientTransaction?.id ?? null,
    exchange,
    farm,
    isClientLoan: clientTransaction != null && action === "loan",
    isCollateral: "isCollateral" in transaction ? transaction.isCollateral : null,
    isDeposit: "isDeposit" in transaction ? transaction.isDeposit : null,
    lendingProtocol,
    user: clientTransaction?.user ?? null,
    isForPooledFund: !!clientTransaction?.id,
  };
}
