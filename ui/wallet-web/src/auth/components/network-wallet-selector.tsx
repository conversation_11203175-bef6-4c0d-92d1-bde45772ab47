"use client";

import { UnifiedAccount, useDerivedWallets, useMasterKeys } from "@/auth/accounts";
import { CopyButton } from "@/lib/components/copy-button";
import { Label } from "@/lib/components/form";
import * as Select from "@/lib/components/select";
import { Network } from "@/lib/mnemonic";
import { shortenAddress } from "@/lib/utils/string";
import { useCallback, useEffect, useId, useState } from "react";

interface NetworkWalletSelectorProps {
  readonly onAccountChange?: (accountId: string, network: Network) => void;
}

export function NetworkWalletSelector({ onAccountChange }: NetworkWalletSelectorProps) {
  const id = useId();
  const accountId = `account-${id}`;
  const networkId = `network-${id}`;

  const { masterAccounts, isLoading: isLoadingAccounts } = useMasterKeys();

  const [selectedNetwork, setSelectedNetwork] = useState<Network>("solana");
  const wrappedSetSelectedNetwork = (network: Network) => {
    setSelectedNetwork(network);
    onAccountChange?.(selectedAccountId, network);
  };

  const [selectedAccountId, setSelectedAccountId] = useState<string>("");
  const wrappedSetSelectedAccountId = useCallback(
    (id: string) => {
      setSelectedAccountId(id);
      onAccountChange?.(id, selectedNetwork);
    },
    [onAccountChange, selectedNetwork],
  );

  // Set initial selected account if none is selected
  useEffect(() => {
    if (selectedAccountId === "" && masterAccounts.length > 0 && masterAccounts[0]?.id) {
      wrappedSetSelectedAccountId(masterAccounts[0].id);
    }
  }, [masterAccounts, selectedAccountId, wrappedSetSelectedAccountId]);

  const selectedAccount = masterAccounts.find((acc) => acc.id === selectedAccountId);
  const { accounts: derivedAccounts } = useDerivedWallets(selectedAccount);

  // Helper function to get the derived address for a network
  const getDerivedAddressForNetwork = (network: Network): string | null => {
    if (!selectedAccount) return null;

    const derivedAcc = derivedAccounts.find((acc) => acc.network === network);
    return derivedAcc?.address ?? null;
  };

  const getAccountLabel = (account: UnifiedAccount) => {
    const address = shortenAddress(account.address);
    const badge = (
      <span className="ml-2 rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-800">
        Master Key
      </span>
    );
    return (
      <div className="flex items-center">
        <span>{address}</span>
        {badge}
      </div>
    );
  };

  return (
    <div className="mt-4 grid w-full min-w-72 justify-items-center gap-6">
      <div className="grid w-full grid-cols-[auto,1fr] items-center gap-2">
        <Label htmlFor={accountId} className="text-right">
          Master Key
        </Label>
        <div className="flex items-center gap-1">
          <div className="flex-grow">
            <Select.Root
              value={selectedAccountId}
              onValueChange={(id) => {
                // When a new master key is selected, update the selectedAccountId
                wrappedSetSelectedAccountId(id);

                // The useEffect hook will handle updating the selected network
                // based on the available networks for the new master key
              }}
            >
              <Select.Trigger
                id={accountId}
                placeholder={isLoadingAccounts ? "Loading accounts..." : "Select a master key"}
                className="w-full"
              />
              <Select.Content key={`network-options-${selectedAccountId}`}>
                {masterAccounts.map((account) => (
                  <Select.Item key={account.id} value={account.id}>
                    {getAccountLabel(account)}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
          </div>
          <CopyButton
            aria-label="Copy Address"
            title="Copy Address"
            className="flex-shrink-0 rounded-md"
            text={selectedAccount?.address ?? null}
          />
        </div>

        <Label htmlFor={networkId} className="text-right">
          Network
        </Label>
        <div className="flex items-center gap-1">
          <div className="flex-grow">
            <Select.Root
              value={selectedNetwork}
              onValueChange={(value) => {
                wrappedSetSelectedNetwork(value as Network);
              }}
            >
              <Select.Trigger
                id={networkId}
                placeholder="Select a network"
                disabled={!selectedAccount}
                className="w-full"
              />
              <Select.Content key={`network-dropdown-${selectedAccountId}`}>
                {/* Only show networks that have derived accounts */}
                {derivedAccounts.some((acc) => acc.network === "solana") && (
                  <Select.Item value="solana">
                    Solana{" "}
                    {(() => {
                      const address = getDerivedAddressForNetwork("solana");
                      return address ? `- ${shortenAddress(address)}` : "(No address)";
                    })()}
                  </Select.Item>
                )}
                {derivedAccounts.some((acc) => acc.network === "ethereum") && (
                  <Select.Item value="ethereum">
                    Ethereum{" "}
                    {(() => {
                      const address = getDerivedAddressForNetwork("ethereum");
                      return address ? `- ${shortenAddress(address)}` : "(No address)";
                    })()}
                  </Select.Item>
                )}
              </Select.Content>
            </Select.Root>
          </div>
          <CopyButton
            aria-label="Copy Derived Address"
            title="Copy Derived Address"
            className="flex-shrink-0 rounded-md"
            text={getDerivedAddressForNetwork(selectedNetwork)}
          />
        </div>

        {selectedAccount && (
          <div className="col-span-full mt-4">
            <div className="rounded-md border p-4 shadow-sm">
              <h3 className="mb-3 text-center text-base font-medium">Wallet Details</h3>

              <div className="grid gap-4">
                {/* Master Key Section */}
                <div className="rounded-md border border-gray-200 p-3">
                  <h4 className="mb-2 text-sm font-semibold text-gray-700">
                    <span className="mr-2 inline-block h-2 w-2 rounded-full bg-blue-500"></span>
                    Master Key
                  </h4>
                  <div className="flex items-center justify-between">
                    <div className="font-mono text-sm">
                      {shortenAddress(selectedAccount.address)}
                    </div>
                    <CopyButton
                      aria-label="Copy Master Address"
                      title="Copy Master Address"
                      className="ml-2 rounded-md"
                      text={selectedAccount.address}
                    />
                  </div>
                </div>

                {/* Derived Keys Section */}
                <div className="rounded-md border border-gray-200 p-3">
                  <h4 className="mb-2 text-sm font-semibold text-gray-700">
                    <span className="mr-2 inline-block h-2 w-2 rounded-full bg-green-500"></span>
                    Derived Keys
                  </h4>

                  <div className="grid gap-3">
                    {/* Solana Address */}
                    <div
                      className={`rounded-md border p-2 ${selectedNetwork === "solana" ? "border-green-300" : "border-gray-200"}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="text-white-600 text-xs font-medium">Solana</div>
                        {selectedNetwork === "solana" && (
                          <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                            Active
                          </span>
                        )}
                      </div>
                      <div className="mt-1 flex items-center justify-between">
                        <div className="font-mono text-sm">
                          {(() => {
                            const address = getDerivedAddressForNetwork("solana");
                            return address ? shortenAddress(address) : "Not available";
                          })()}
                        </div>
                        {getDerivedAddressForNetwork("solana") && (
                          <CopyButton
                            aria-label="Copy Solana Address"
                            title="Copy Solana Address"
                            className="ml-2 rounded-md"
                            text={getDerivedAddressForNetwork("solana")}
                          />
                        )}
                      </div>
                    </div>

                    {/* Ethereum Address */}
                    <div
                      className={`rounded-md border p-2 ${selectedNetwork === "ethereum" ? "border-green-300" : "border-gray-200"}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="text-xs font-medium text-gray-600">Ethereum</div>
                        {selectedNetwork === "ethereum" && (
                          <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                            Active
                          </span>
                        )}
                      </div>
                      <div className="mt-1 flex items-center justify-between">
                        <div className="font-mono text-sm">
                          {(() => {
                            const address = getDerivedAddressForNetwork("ethereum");
                            return address ? shortenAddress(address) : "Not available";
                          })()}
                        </div>
                        {getDerivedAddressForNetwork("ethereum") && (
                          <CopyButton
                            aria-label="Copy Ethereum Address"
                            title="Copy Ethereum Address"
                            className="ml-2 rounded-md"
                            text={getDerivedAddressForNetwork("ethereum")}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedAccount && derivedAccounts.length === 0 && (
          <div className="col-span-full text-center text-sm text-gray-500">
            No derived accounts found for this master key. Please create a multi-chain wallet
            first.
          </div>
        )}
      </div>
    </div>
  );
}
