{"name": "temet-subql", "version": "0.0.1", "description": "Subquery project for EVM Blockchains indexing", "main": "dist/index.js", "scripts": {"build": "subql codegen && subql build", "codegen": "subql codegen", "start:node": "", "start:query": "", "dev": "subql codegen && subql build && docker-compose pull && docker-compose up --remove-orphans", "prepack": "rm -rf dist && npm run build", "test": "subql build && subql-node-ethereum test", "build:develop": "NODE_ENV=develop subql codegen && NODE_ENV=develop subql build"}, "homepage": "https://github.com/subquery/ethereum-subql-starter", "repository": "github:subquery/ethereum-subql-starter", "files": ["dist", "schema.graphql", "project-ethereum.yaml", "subquery-multichain.yaml"], "author": "SubQuery Team", "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.8.0", "@ethersproject/providers": "^5.8.0", "@subql/node-ethereum": "^5.5.0", "@subql/types-core": "^2.0.2", "@subql/types-ethereum": "^4.1.0", "@typechain/ethers-v5": "^11.1.2", "dotenv": "^16.4.7", "ethers": "^5.8.0", "tslib": "^2.8.1"}, "devDependencies": {"@subql/cli": "^5.7.0", "@subql/testing": "^2.2.4", "@types/jest": "^29.5.14", "@types/node": "^22.13.9", "typescript": "^5.8.2"}}