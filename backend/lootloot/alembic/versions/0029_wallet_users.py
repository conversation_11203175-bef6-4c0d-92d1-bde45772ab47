"""Wallet users

Revision ID: 0029
Revises: 0028
Create Date: 2024-09-25 15:51:43.987719

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0029"
down_revision = "0028"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "walletuser",
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("first_name", sa.String(), nullable=True),
        sa.Column("last_name", sa.String(), nullable=True),
        sa.Column("picture_url", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_walletuser_email"), "walletuser", ["email"], unique=True)
    op.create_table(
        "walletsession",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("user_agent", sa.String(), nullable=False),
        sa.Column("ip_address", sa.String(), nullable=False),
        sa.Column("token", sa.String(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["walletuser.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_walletsession_token"), "walletsession", ["token"], unique=True)
    op.create_index(
        op.f("ix_walletsession_user_id"), "walletsession", ["user_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_walletsession_user_id"), table_name="walletsession")
    op.drop_index(op.f("ix_walletsession_token"), table_name="walletsession")
    op.drop_table("walletsession")
    op.drop_index(op.f("ix_walletuser_email"), table_name="walletuser")
    op.drop_table("walletuser")
    # ### end Alembic commands ###
