"use client";

import { cva, cx, type VariantProps } from "cva";
import { X } from "lucide-react";
import { ComponentPropsWithoutRef, forwardRef } from "react";

const input = cva({
  base: "w-full bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-hidden aria-invalid:border-destructive aria-invalid:ring-destructive",
  variants: {
    variant: {
      default:
        "flex h-9 rounded-md border border-border shadow-xs focus-visible:ring-1 focus-visible:ring-ring/60 disabled:cursor-not-allowed disabled:bg-muted disabled:opacity-50 dark:focus-visible:ring-ring/80",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

export interface InputProps
  extends ComponentPropsWithoutRef<"input">,
    VariantProps<typeof input> {}

export const Input = forwardRef<React.ElementRef<"input">, InputProps>(
  function Input(props, ref) {
    const { variant, className, ...rest } = props;
    return <input {...rest} ref={ref} className={input({ variant, className })} />;
  },
);

interface InputClearableProps extends InputProps {
  readonly onClear: React.MouseEventHandler<HTMLButtonElement>;
  readonly clearHidden?: boolean;
}

export const InputClearable = forwardRef<React.ElementRef<"input">, InputClearableProps>(
  function InputClearable(props, ref) {
    const { onClear, clearHidden, disabled, className, hidden, variant, ...rest } = props;
    return (
      <div
        hidden={hidden}
        className={cx(
          "relative flex h-9 rounded-md border shadow-xs transition-colors has-disabled:cursor-not-allowed has-disabled:bg-muted has-disabled:opacity-50 has-[input:focus-visible]:ring-1 has-[input:focus-visible]:ring-ring/60 dark:has-[input:focus-visible]:ring-ring/80",
          className,
        )}
      >
        <input
          {...rest}
          ref={ref}
          className={input({ variant, class: "mr-5" })}
          disabled={disabled}
        />
        <button
          type="button"
          className="absolute top-1 right-1 rounded-full p-1 text-muted-foreground hover:bg-muted focus-visible:bg-muted focus-visible:ring-1 focus-visible:outline-hidden"
          disabled={disabled}
          hidden={clearHidden}
          onClick={onClear}
        >
          <span className="sr-only">Clear</span>
          <X aria-hidden size={18} />
        </button>
      </div>
    );
  },
);

export interface TextAreaProps
  extends ComponentPropsWithoutRef<"textarea">,
    VariantProps<typeof input> {}

export const TextArea = forwardRef<React.ElementRef<"textarea">, TextAreaProps>(
  function TextArea(props, ref) {
    const { variant, className, ...rest } = props;
    return <textarea {...rest} ref={ref} className={input({ variant, className })} />;
  },
);
