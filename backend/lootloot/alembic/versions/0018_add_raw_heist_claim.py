"""Add raw heist claim

Revision ID: 0018
Revises: 0017
Create Date: 2024-03-06 17:30:39.923053

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0018"
down_revision = "0017"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "rawheistclaim",
        sa.Column("requested_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("data", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_raw_heist_claim_requested_at",
        "rawheistclaim",
        [sa.text("requested_at DESC")],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_raw_heist_claim_requested_at", table_name="rawheistclaim")
    op.drop_table("rawheistclaim")
    # ### end Alembic commands ###
