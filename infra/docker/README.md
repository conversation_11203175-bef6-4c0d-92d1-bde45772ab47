### python-runner.dockerfile

The [python-runner.dockerfile] is used to build the docker image for our python backend code. It
has all the dependencies needed to run the python backend. It also has the python code copied
into the image for the default target. There is also a `dev` target that is used for
development, which has to be used with a volume mount as it doesn't include the python code. The
`dev` target uses the [python-runner-dev.entrypoint.sh] entrypoint script to provide some
additional development only functionality.

As this image is used for running multiple different python applications, it must be used with
an explicit command. Failure to override the command will result in the image exiting
immediately with code 1.

[python-runner.dockerfile]: ./python-runner.dockerfile
[python-runner-dev.entrypoint.sh]: ./python-runner-dev.entrypoint.sh
