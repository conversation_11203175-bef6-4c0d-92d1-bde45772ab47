import eslint from "@eslint/js";
import nextPlugin from "@next/eslint-plugin-next";
import pluginQuery from "@tanstack/eslint-plugin-query";
import jsxA11y from "eslint-plugin-jsx-a11y";
import reactPlugin from "eslint-plugin-react";
import hooksPlugin from "eslint-plugin-react-hooks";
import eslintPluginUnicorn from "eslint-plugin-unicorn";
import tseslint from "typescript-eslint";
import baseConfig from "./config/eslint-base-rules-config.mjs";

/** @type {import("eslint").Linter.Config[]} */
const tsConfigs = [
  ...tseslint.configs.strictTypeChecked,
  ...tseslint.configs.stylisticTypeChecked,
  {
    rules: {
      "@typescript-eslint/promise-function-async": "warn",
      // Be more lenient on template expressions
      "@typescript-eslint/restrict-template-expressions": [
        "error",
        {
          allow: [{ from: "lib", name: ["<PERSON><PERSON>r", "URL", "URLSearchParams"] }],
          allowAny: true,
          allowBoolean: true,
          allowNever: true,
          allowNullish: true,
          allowNumber: true,
          allowRegExp: true,
        },
      ],
    },
  },
  {
    languageOptions: {
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
];

const unicornRecommended = eslintPluginUnicorn.configs.recommended;
/** @type {import("eslint").Linter.Config} */
const unicornConfig = {
  ...unicornRecommended,
  rules: {
    // Make all unicorn recommended rules that are "error" into "warn"
    ...Object.fromEntries(
      Object.entries(unicornRecommended.rules ?? {}).map(([id, state]) => [
        id,
        state === "error" ? "warn" : state,
      ]),
    ),
    // Disable rules that are undesirable
    "unicorn/explicit-length-check": "off",
    "unicorn/filename-case": "off",
    "unicorn/no-array-callback-reference": "off",
    "unicorn/no-negated-condition": "off",
    "unicorn/no-nested-ternary": "off",
    "unicorn/no-new-array": "off",
    "unicorn/no-null": "off",
    "unicorn/number-literal-case": "off",
    "unicorn/prefer-global-this": "off",
    "unicorn/prevent-abbreviations": "off",
    "unicorn/switch-case-braces": "off",
  },
};

const reactRecommended = reactPlugin.configs.flat.recommended;
const reactJsxRuntime = reactPlugin.configs.flat["jsx-runtime"];
/** @type {import("eslint").Linter.Config} */
const reactConfig = {
  ...reactRecommended,
  ...reactJsxRuntime,
  rules: {
    ...reactRecommended.rules,
    ...reactJsxRuntime.rules,
    "react/no-unknown-property": "off",
    "react/prop-types": "off",
  },
};

export default tseslint.config(
  baseConfig,
  eslint.configs.recommended,
  ...tsConfigs,
  {
    files: ["app/**", "lib/**", "types/**"],
    rules: {
      "@typescript-eslint/no-restricted-imports": [
        "warn",
        {
          patterns: [
            {
              group: ["./*", "../*"],
              message: "Use '@/*' for first-party modules",
            },
          ],
        },
      ],
    },
  },
  unicornConfig,
  ...pluginQuery.configs["flat/recommended"],
  jsxA11y.flatConfigs.recommended,
  reactConfig,
  {
    plugins: {
      "react-hooks": hooksPlugin,
      "@next/next": nextPlugin,
    },
    rules: {
      ...hooksPlugin.configs.recommended.rules,
      ...nextPlugin.configs.recommended.rules,
      ...nextPlugin.configs["core-web-vitals"].rules,
    },
  },
  {
    settings: {
      react: { version: "detect" },
      "jsx-a11y": {
        attributes: {
          for: ["htmlFor", "for"],
        },
        components: {
          Button: "button",
          Input: "input",
        },
        polymorphicPropName: "component",
      },
    },
  },
  {
    // Allow .config.js files to use CommonJS modules
    files: ["*.config.js"],
    rules: {
      "unicorn/prefer-module": "off",
    },
  },
  { ignores: [".next/", "config/*.mjs", "out/", "*.config.js", "*.config.mjs"] },
);
