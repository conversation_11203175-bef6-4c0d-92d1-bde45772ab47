"""Transaction is_deposit

Revision ID: 0019
Revises: 0018
Create Date: 2023-04-10 14:47:14.543610

"""

from enum import StrEnum, auto

import sqlalchemy as sa
from alembic import op
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0019"
down_revision = "0018"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class TransactionAction(StrEnum):
    bridge = auto()
    income = auto()
    loan = auto()
    pool = auto()
    spend = auto()
    stake = auto()
    swap = auto()


class Transaction(Base):
    id = Column(Integer, primary_key=True)
    action: str = Column(String)
    is_deposit: bool = Column(Boolean)
    __tablename__ = "transaction"


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("transaction", sa.Column("is_deposit", sa.Boolean(), nullable=True))
    # ### end Alembic commands ###

    session = Session(bind=op.get_bind())
    for transaction in session.query(Transaction).filter(
        Transaction.action.in_(
            [
                TransactionAction.bridge.value,
                TransactionAction.loan.value,
                TransactionAction.pool.value,
                TransactionAction.stake.value,
            ]
        )
    ):
        transaction.action = None

    for transaction in session.query(Transaction).filter(
        Transaction.action == TransactionAction.income
    ):
        transaction.is_deposit = True
    for transaction in session.query(Transaction).filter(
        Transaction.action == TransactionAction.spend
    ):
        transaction.is_deposit = False

    session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("transaction", "is_deposit")
    # ### end Alembic commands ###
