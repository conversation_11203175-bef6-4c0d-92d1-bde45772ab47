"""Accounted Transaction

Revision ID: 0058
Revises: 0057
Create Date: 2024-09-30 21:32:29.362169

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0058"
down_revision = "0057"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "accountedtransaction",
        sa.Column("tx_hash", sa.String(), nullable=False),
        sa.Column("confirmed_at", sa.DateTime(), nullable=False),
        sa.Column("transfer_id", sa.Integer(), nullable=False),
        sa.Column("coin_id", sa.Integer(), nullable=True),
        sa.Column("ticker_symbol", sa.String(), nullable=True),
        sa.Column(
            "flattened_transactions", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
        sa.Column("amount", sa.Numeric(precision=48, scale=18), nullable=False),
        sa.Column("remaining_amount", sa.Numeric(precision=48, scale=18), nullable=False),
        sa.Column("action", sa.String(), nullable=False),
        sa.Column("transaction_action", sa.String(), nullable=False),
        sa.Column("loan_withdrawals", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("asset_price_usd", sa.Numeric(precision=32, scale=18), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["coin_id"],
            ["coin.id"],
        ),
        sa.ForeignKeyConstraint(
            ["transfer_id"],
            ["transfer.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "capitalposition",
        sa.Column("accounted_buy_transaction_id", sa.Integer(), nullable=True),
    )
    op.add_column(
        "capitalposition",
        sa.Column("accounted_sell_transaction_id", sa.Integer(), nullable=True),
    )
    op.drop_constraint(
        "capitalposition_buy_transaction_id_fkey", "capitalposition", type_="foreignkey"
    )
    op.drop_constraint(
        "capitalposition_sell_transaction_id_fkey", "capitalposition", type_="foreignkey"
    )
    op.create_foreign_key(
        None,
        "capitalposition",
        "accountedtransaction",
        ["accounted_sell_transaction_id"],
        ["id"],
    )
    op.create_foreign_key(
        None,
        "capitalposition",
        "accountedtransaction",
        ["accounted_buy_transaction_id"],
        ["id"],
    )
    op.drop_column("capitalposition", "sell_transaction_id")
    op.drop_column("capitalposition", "buy_transaction_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "capitalposition",
        sa.Column("buy_transaction_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.add_column(
        "capitalposition",
        sa.Column("sell_transaction_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.drop_constraint(None, "capitalposition", type_="foreignkey")
    op.drop_constraint(None, "capitalposition", type_="foreignkey")
    op.create_foreign_key(
        "capitalposition_sell_transaction_id_fkey",
        "capitalposition",
        "transaction",
        ["sell_transaction_id"],
        ["id"],
    )
    op.create_foreign_key(
        "capitalposition_buy_transaction_id_fkey",
        "capitalposition",
        "transaction",
        ["buy_transaction_id"],
        ["id"],
    )
    op.drop_column("capitalposition", "accounted_sell_transaction_id")
    op.drop_column("capitalposition", "accounted_buy_transaction_id")
    op.drop_table("accountedtransaction")
    # ### end Alembic commands ###
