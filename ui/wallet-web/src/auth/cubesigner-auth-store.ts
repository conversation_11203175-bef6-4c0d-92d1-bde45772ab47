import { AuthContext } from "@/auth/auth-ctx";
import { associateCubistUser, registerCubistUser } from "@/lib/cubist";
import * as cs from "@cubist-labs/cubesigner-sdk";
import { BrowserStorageManager } from "@cubist-labs/cubesigner-sdk-browser-storage";
import { use, useEffect, useSyncExternalStore } from "react";

const ORG_ID = "Org#f9cd3c7f-9e9a-4882-8c2f-128551af3b8e";
const CUBIST_SIGNER_SESSION_KEY = "cubist.cubeSignerSession";

const hour = 3600;
const lifetimes = {
  auth_lifetime: hour, // 1 hour
  refresh_lifetime: hour * 24 * 7, // 1 week
  session_lifetime: hour * 24 * 365, // 1 year
};

async function getSessionClient(idToken: string) {
  return await cs.CubeSignerClient.createOidcSession(
    cs.envs.gamma,
    ORG_ID,
    idToken,
    ["sign:*", "manage:key:list"],
    lifetimes,
  );
}

const getBrowserSessionManager = (() => {
  let manager: BrowserStorageManager | undefined;
  return () => {
    manager ??= new BrowserStorageManager(CUBIST_SIGNER_SESSION_KEY);
    return manager;
  };
})();

/** Check if we have a valid session */
async function checkSession(sessionManager: BrowserStorageManager) {
  try {
    await sessionManager.metadata();
    return true;
  } catch (error) {
    if (error instanceof cs.NoSessionFoundError) {
      return false;
    }
    throw error;
  }
}

async function signInOnCubeSigner(idToken: string) {
  const identityProof = await cs.CubeSignerClient.proveOidcIdentity(
    cs.envs.gamma,
    ORG_ID,
    idToken,
  );

  let association: Promise<null> | null = null;

  if (identityProof.user_info) {
    // Ensure association of the Cubist user with our user
    association = associateCubistUser(identityProof.user_info.user_id);
  } else {
    await registerCubistUser(identityProof);
  }

  const sessionClient = await getSessionClient(idToken);

  // If no MFA requirement, we are already authenticated
  const session = sessionClient.requiresMfa() ? undefined : sessionClient.data();

  // We must setSession even when session is undefined to clear the session
  const sessionManager = getBrowserSessionManager();
  await sessionManager.setSession(session);

  await association;

  if (!session) {
    return { status: "mfa-required" as const, sessionClient };
  }

  const client = await createCubeSignerClient(sessionManager);
  return { status: "authenticated" as const, client };
}

async function submitCubeSignerTotp(
  sessionClient: cs.CubeSignerResponse<cs.SessionData>,
  code: string,
) {
  const mfaClient = await sessionClient.mfaClient();
  if (!mfaClient) throw new Error("Unexpected undefined MFA client");

  const authorizedSessionClient = await sessionClient.totpApprove(mfaClient, code);
  const session = authorizedSessionClient.data();

  const sessionManager = getBrowserSessionManager();
  await sessionManager.setSession(session);

  const client = await createCubeSignerClient(sessionManager);
  return { status: "authenticated" as const, client };
}

async function restoreCubeSignerSession() {
  const sessionManager = getBrowserSessionManager();
  const hasSession = await checkSession(sessionManager);
  if (!hasSession) return { status: "signed-out" as const };

  const client = await createCubeSignerClient(sessionManager);
  return { status: "authenticated" as const, client };
}

async function createCubeSignerClient(sessionManager: cs.SessionManager) {
  return await cs.CubeSignerClient.create(sessionManager);
}

function createCubeSignerAuthStore() {
  const subscribers = new Set<() => void>();

  let state:
    | Awaited<ReturnType<typeof signInOnCubeSigner>>
    | Awaited<ReturnType<typeof restoreCubeSignerSession>>
    | { status: "new" } = { status: "new" };

  function notify() {
    for (const listener of subscribers) {
      listener();
    }
  }

  let locked = false;

  function wrapper<T extends unknown[]>(operation: (...args: T) => Promise<typeof state>) {
    return async function wrapped(...args: T) {
      if (locked) {
        throw new Error("Another operation already in progress");
      }
      locked = true;
      try {
        state = await operation(...args);
      } finally {
        locked = false; // eslint-disable-line require-atomic-updates
      }
      notify();
    };
  }

  async function submitTotp(code: string) {
    if (state.status !== "mfa-required") {
      throw new Error("Cannot submit TOTP at this time");
    }
    return await submitCubeSignerTotp(state.sessionClient, code);
  }

  getBrowserSessionManager().addEventListener("logout", function logoutHandler() {
    // We can ignore the event if we are locked because it means
    // we are in the middle of signing in.
    if (locked) return;
    // no need to lock because no async operations
    state = { status: "signed-out" };
    notify();
  });

  return {
    subscribe: (listener: () => void) => {
      subscribers.add(listener);
      return function clearSubscription() {
        subscribers.delete(listener);
      };
    },
    getSnapshot: () => state,
    restore: wrapper(restoreCubeSignerSession),
    signIn: wrapper(signInOnCubeSigner),
    submitTotp: wrapper(submitTotp),
  };
}

export const getCubeSignerAuthStore = (() => {
  let store: ReturnType<typeof createCubeSignerAuthStore> | undefined;
  return () => {
    store ??= createCubeSignerAuthStore();
    return store;
  };
})();

export function useCubeSignerAuth() {
  const store = getCubeSignerAuthStore();
  const state = useSyncExternalStore(store.subscribe, store.getSnapshot);
  return state;
}

export function useCubeSignerClient() {
  const auth = useCubeSignerAuth();
  return auth.status === "authenticated" ? auth.client : null;
}

/**
 * Watcher to keep the app and CubeSigner sessions in sync.
 */
export function CubeSignerSignOutWatcher() {
  const { set: setAuth, user } = use(AuthContext);

  useEffect(() => {
    const store = getCubeSignerAuthStore();
    if (store.getSnapshot().status === "signed-out") {
      setAuth(null);
      return;
    }
    return store.subscribe(() => {
      const state = store.getSnapshot();
      if (state.status === "signed-out") {
        setAuth(null);
      }
    });
  }, [setAuth]);

  useEffect(() => {
    if (user == null) {
      void getBrowserSessionManager().setSession(undefined);
    }
  }, [user]);

  return null;
}
