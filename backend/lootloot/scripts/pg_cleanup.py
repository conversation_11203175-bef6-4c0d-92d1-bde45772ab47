#!/usr/bin/env python3

"""Remove old data from postgres database.

Requires `kubectl` and `gcloud` to be installed and configured to access
our k8s cluster and gcloud project respectively.

Beware that this can create a spike in resource usage on the database
(including **storage**, CPU, memory, and disk IO).

Example usage:
    ./lootloot/scripts/pg_cleanup.py -n temet-stage
"""

import argparse
import json
import shlex
import subprocess
import sys
from datetime import datetime, timezone

parser = argparse.ArgumentParser()
parser.add_argument(
    "--context",
    default="gke_temet-359508_us-west1-a_temet-359508-gke",
    type=str,
    help="The context to use with kubectl",
)
parser.add_argument(
    "-n", "--namespace", required=True, type=str, help="The namespace to use with kubectl"
)

args = parser.parse_args()
kube_context: str = args.context
kube_namespace: str = args.namespace


def run(args: list[str], **kwargs):
    kwargs.setdefault("check", True)
    kwargs.setdefault("stdout", sys.stdout)
    kwargs.setdefault("stderr", sys.stderr)
    print("$", shlex.join(args))
    return subprocess.run(args, text=True, **kwargs)


def run_kubectl(args: list[str], **kwargs):
    args = ["kubectl", f"--context={kube_context}", f"--namespace={kube_namespace}", *args]
    return run(args, **kwargs)


def get_pg_pod_args(name: str, image: str, pg_secret: str):
    overrides = json.dumps(
        {
            "apiVersion": "v1",
            "spec": {
                "containers": [
                    {
                        "name": name,
                        "image": image,
                        "command": ["sleep", "10800"],
                        "env": [
                            {
                                "name": "PGUSER",
                                "valueFrom": {
                                    "secretKeyRef": {
                                        "name": pg_secret,
                                        "key": "POSTGRESQL_USER",
                                    }
                                },
                            },
                            {
                                "name": "PGPASSWORD",
                                "valueFrom": {
                                    "secretKeyRef": {
                                        "name": pg_secret,
                                        "key": "POSTGRESQL_PASSWORD",
                                    }
                                },
                            },
                            {
                                "name": "PGDATABASE",
                                "valueFrom": {
                                    "secretKeyRef": {
                                        "name": pg_secret,
                                        "key": "POSTGRESQL_DB",
                                    }
                                },
                            },
                            {
                                "name": "PGHOST",
                                "valueFrom": {
                                    "secretKeyRef": {
                                        "name": pg_secret,
                                        "key": "POSTGRESQL_HOST",
                                    }
                                },
                            },
                        ],
                    }
                ]
            },
        }
    )
    return [
        "run",
        name,
        f"--image={image}",
        "--restart=Never",
        f"--overrides={overrides}",
    ]


def get_pg_secret():
    print("Finding postgres secret name...")
    secrets_process = run_kubectl(
        ["get", "secrets", "-o=jsonpath='{.items[*].metadata.name}'"],
        stdout=subprocess.PIPE,
    )
    secrets = secrets_process.stdout.strip("'")
    pg_secret = next(s for s in secrets.split() if s.startswith("postgresql-"))
    print(f"Found postgres secret: {pg_secret}")
    return pg_secret


def create_pg_pod(pg_secret):
    pod_name = "pg-cleanup"
    print(f"Creating postgres pod '{pod_name}'...")
    pg_args = get_pg_pod_args(name=pod_name, image="postgres:14", pg_secret=pg_secret)
    run_kubectl(pg_args)
    return pod_name


def install_deps_on_pod(pod_name):
    print("Installing dependencies...")
    run_kubectl(["exec", pod_name, "--", "apt-get", "update"])
    run_kubectl(
        [
            "exec",
            pod_name,
            "--",
            "apt-get",
            "install",
            "-y",
            "--no-install-recommends",
            "curl",
            "ca-certificates",
        ]
    )


def dump_pg(pod_name: str):
    print("Dumping database...")
    dump_path = "/tmp/dump.sql.zst"
    run_kubectl(
        [
            "exec",
            pod_name,
            "--",
            "bash",
            "-euxo",
            "pipefail",
            "-c",
            f"pg_dump $PGDATABASE | zstd -o {dump_path}",
        ]
    )
    return dump_path


def upload_dump(pod_name: str, dump_path: str):
    print("Generating google storage signed URL...")
    now_str = (
        datetime.now(tz=timezone.utc)
        .replace(microsecond=0, second=0)
        .isoformat()
        .removesuffix(":00+00:00")
        .replace(":", "")
    )
    upload_path = f"{kube_namespace}_{now_str}.sql.zst"
    signed_url = run(
        [
            "gcloud",
            "storage",
            "sign-url",
            f"gs://temet-dumps/{upload_path}",
            "--http-verb=PUT",
            "--duration=1h",
            "--headers=content-type=application/octet-stream",
            "--impersonate-service-account=<EMAIL>",
            "--format=get(signed_url)",
        ],
        stdout=subprocess.PIPE,
    ).stdout.strip()

    print("Uploading dump to google storage...")
    run_kubectl(
        [
            "exec",
            pod_name,
            "--",
            "curl",
            "-X",
            "PUT",
            "--upload-file",
            dump_path,
            "--header",
            "content-type: application/octet-stream",
            signed_url,
        ]
    )


def cleanup_data(pod_name: str):
    print("Cleaning up data...")
    vacuum_query = "VACUUM (FULL, VERBOSE, ANALYZE) {}"

    # Delete old NFT listings
    table = "nftlisting"
    stmt = f"DELETE FROM {table} WHERE at < NOW() - INTERVAL '15 days'"
    run_kubectl(["exec", pod_name, "--", "psql", "-c", stmt])
    run_kubectl(["exec", pod_name, "--", "psql", "-c", vacuum_query.format(table)])

    # Delete old and already processed raw heist data
    raw_heist_data_tables = ("rawheistclaim", "rawheistdeedclaim")
    for table in raw_heist_data_tables:
        stmt = f"DELETE FROM {table} WHERE requested_at < NOW() - INTERVAL '3 days'"
        run_kubectl(["exec", pod_name, "--", "psql", "-c", stmt])
        run_kubectl(["exec", pod_name, "--", "psql", "-c", vacuum_query.format(table)])

    print("Finished data cleanup")


pg_secret = get_pg_secret()
pod_name = create_pg_pod(pg_secret)
try:
    run_kubectl(["wait", "--for=condition=Ready", f"pod/{pod_name}", "--timeout=60s"])
    install_deps_on_pod(pod_name)
    dump_path = dump_pg(pod_name)
    upload_dump(pod_name, dump_path)
    cleanup_data(pod_name)
finally:
    print("Removing postgres pod...")
    run_kubectl(["delete", "pod", pod_name], check=False)
