"""Adjusts in NFT and NFTCollection

Revision ID: 0014
Revises: 0013
Create Date: 2024-02-02 15:31:45.281579

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0014"
down_revision = "0013"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "nft",
        "attributes_data",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
        existing_nullable=False,
    )
    op.create_index(
        op.f("ix_nft_owner_address_uri"), "nft", ["owner_address_uri"], unique=False
    )
    op.create_unique_constraint(None, "nftcollection", ["address_uri"])
    op.alter_column(
        "nftcollection",
        "royalty_perecent",
        existing_nullable=False,
        existing_type=sa.NUMERIC(),
        new_column_name="royalty_percent",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "nftcollection",
        "royalty_percent",
        existing_nullable=False,
        existing_type=sa.NUMERIC(),
        new_column_name="royalty_perecent",
    )
    op.drop_constraint(None, "nftcollection", type_="unique")
    op.drop_index(op.f("ix_nft_owner_address_uri"), table_name="nft")
    op.alter_column(
        "nft",
        "attributes_data",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
