import { apiQuery } from "@/lib/api";
import { ListNFTCollectionsResp, ListNFTResp } from "@/types/schemas";
import { useQuery } from "@tanstack/react-query";

export function useListNftCollectionsQuery() {
  return useQuery({
    queryKey: ["nfts"],
    queryFn: apiQuery<ListNFTCollectionsResp>,
  });
}

export function useListNftsQuery(collectionUid: string) {
  return useQuery({
    queryKey: ["nfts", collectionUid],
    queryFn: apiQuery<ListNFTResp>,
  });
}
