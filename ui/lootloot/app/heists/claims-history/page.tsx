"use client";

import { ClaimsHistoryTable } from "@/app/heists/claims-history/claims-history-table";
import { useAuth } from "@/lib/auth-ctx";
import { SignedOutError, ViewToggle, getView } from "@/lib/components/view-toggle";
import { NavigationProvider, useSearchParams } from "@/lib/navigation";
import { Suspense } from "react";

function ClaimsHistoryContent() {
  const params = useSearchParams();
  const view = params ? getView(params) : "own";
  const isOwn = view === "own";
  const auth = useAuth();
  const isSignedOut = auth.initialized && !auth.user;

  return (
    <main className="grid gap-6 px-2 py-8 sm:justify-center">
      <div className="grid grid-cols-[1fr_auto] gap-y-2">
        <h1 className="text-2xl">Claims History</h1>
        <ViewToggle view={view} params={params} ownLabel="Your claims" allLabel="All claims" />
      </div>
      <SignedOutError params={params} customMessage="to view your claims history" />
      {(!isSignedOut || !isOwn) && <ClaimsHistoryTable isOwn={isOwn} />}
    </main>
  );
}

export default function ClaimsHistory() {
  return (
    <Suspense fallback={<ClaimsHistoryTable isOwn={false} />}>
      <NavigationProvider>
        <ClaimsHistoryContent />
      </NavigationProvider>
    </Suspense>
  );
}
