# Market Data Service

## Pre Start Script

Used to configure MongoDB and Kafka when service is coming up.

## Coin Price Producer

Produces market data from CoinGecko by:

- Fetching the current list of Coins and saving them
- Fetching the current mark pricing data for each Coin and sending them to Kafka

The ConPrice data is saved to MongoDB using a
[MongoDB Kafka Connect](https://docs.mongodb.com/kafka-connector/current/) sink.
