import logging
from datetime import datetime
from enum import StrEnum, auto
from typing import Any

import pydantic
import sentry_sdk
import sentry_sdk.types as sentry_types
from pydantic_settings import BaseSettings, SettingsConfigDict
from sentry_sdk.attachments import Attachment


class Environment(StrEnum):
    local = auto()  # localhost
    apollo = auto()  # apollo.stabletech.capital
    diwan = auto()  # diwan.digital
    lp = auto()  # lp.stabletech.capital
    sahaba = auto()  # sahaba.me
    production = auto()  # stabletech.capital, lootloot.farm
    stage = auto()  # stage.stabletech.capital, stage.temet.tech


class Settings(BaseSettings):
    """Settings for the application.

    These are loaded from .env file and environment variables by the name
    matching the properties' names of this class (case-insensitive).
    Environment variables take precedence over values in .env file.

    See https://docs.pydantic.dev/latest/concepts/pydantic_settings/
    """

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    admin_email: str = ""  # Email address used to bypass Google Auth locally
    env_name: Environment = Environment.local
    env_override: Environment | None = None  # Simulate env on development
    namespace: str = "default"
    sentry_dsn: str | None = None

    # Changes to Postgres config below are also needed in alembic/env.py
    postgresql_db: str = "temet"
    postgresql_host: str = "localhost"
    postgresql_user: str = "postgres"
    postgresql_password: str = "postgres"

    subqlpostgresql_db: str = "subql"
    subqlpostgresql_host: str = "localhost"
    subqlpostgresql_user: str = "postgres"
    subqlpostgresql_password: str = "postgres"

    # Slack configs
    slack_bot_token: str | None = None
    slack_signing_secret: str | None = None

    # Indexer configs
    bitquery_api_key: str | None = None
    block_fills_api_key: str | None = None
    covalent_api_key: str | None = None
    helius_api_key: str | None = None
    helius_domain: str = "https://api.helius.xyz"
    helius_das_domain: str = "https://mainnet.helius-rpc.com"
    internal_sync_secret: str = "<EMAIL>*kv"
    mintscan_api_key: str | None = None
    transactions_start_at: datetime = datetime(year=2023, month=1, day=21)

    # Phantom signature validation config
    secret_box_hexed_key: str | None = None

    # Sendgrid configs
    sendgrid_api_key: str | None = None

    # Heist configs
    heist_bank_bot_key: str | None = None

    # Auth configs
    allowed_issuers: dict[str, str] = {
        "https://accounts.google.com": "************-4rebtccromkrgj5k4fd3cla6l9inuons.apps.googleusercontent.com",
    }
    """Allowed issuers of tokens for authentication. Mapping of issuer to client_id"""

    # Capital Position flag
    capital_positions_enabled: bool = True

    # Webapp configs
    webapp_origin_domain: str = "localhost"
    """Expected origin domain, e.g. stabletech.capital, lootloot.farm, stage.temet.tech, etc..."""

    webapp_dev_port: int = 3002
    """Port serving the webapp when running locally for development"""

    # Cubist configs
    cubist_domain: str = "https://gamma.signer.cubist.dev"
    cubist_org_id: str = "Org#f9cd3c7f-9e9a-4882-8c2f-128551af3b8e"
    cubist_session_secret_name: str = ""
    """Name of the secret in Google Cloud Secret Manager that contains the secret box key"""

    # Google Cloud configs
    gcp_project_id: str = "temet-359508"

    @property
    def is_local(self):
        return self.env_name == Environment.local

    @property
    def is_production(self):
        return self.env_name in (
            Environment.apollo,
            Environment.diwan,
            Environment.lp,
            Environment.production,
            Environment.sahaba,
        )

    @property
    def is_stage(self):
        return self.env_name == Environment.stage

    @property
    def is_apollo(self):
        if self.env_override and self.is_local:
            return self.env_override == Environment.apollo
        return self.env_name == Environment.apollo

    @property
    def is_diwan(self) -> bool:
        if self.env_override and self.is_local:
            return self.env_override == Environment.diwan
        return self.env_name == Environment.diwan

    @property
    def is_lp(self) -> bool:
        if self.env_override and self.is_local:
            return self.env_override == Environment.lp
        return self.env_name == Environment.lp

    @property
    def is_stabletech(self) -> bool:
        if self.env_override and self.is_local:
            return self.env_override == Environment.production
        return self.env_name == Environment.production

    @property
    def is_sahaba(self) -> bool:
        if self.env_override and self.is_local:
            return self.env_override == Environment.sahaba
        return self.env_name == Environment.sahaba

    # Feature flags
    @property
    def enable_pooled_fund(self):
        """
        Adds custom functionality for pooled-fund deployments.
        """
        return self.is_diwan or self.is_lp

    @property
    def enable_password_login(self):
        return not self.is_production

    @property
    def enable_stabletech_auto_signup(self):
        return not self.is_production


logger = logging.getLogger("")
settings = Settings()


# Change for DEBUG logs when running locally if needed
logging.basicConfig(level=logging.INFO)


def traces_sampler(sampling_context: dict[str, Any]) -> float:
    """Determine trace sampling rate based on the request path.

    Returns 0 to disable sampling, 1 for 100% sampling, or a value in between
    """
    asgi_scope: dict[str, Any] | None = sampling_context.get("asgi_scope")
    if asgi_scope:
        path = asgi_scope.get("path")
        method = asgi_scope.get("method")
        # Ignore health checks (GET /)
        if path == "/" and method == "GET":
            return 0

    # Default sample rate for other transactions
    return 0.01


def sentry_before_send(
    event: sentry_types.Event, hint: sentry_types.Hint
) -> sentry_types.Event | None:
    exc_info = hint.get("exc_info")
    if isinstance(exc_info, (tuple, list)) and len(exc_info) > 1:
        exception = exc_info[1]
    else:
        exception = None

    # For pydantic validation errors add attachment with error details
    if isinstance(exception, pydantic.ValidationError):
        attachment_to_add = Attachment(
            bytes=exception.json().encode("utf-8"),
            filename="validation_error.json",
            content_type="application/json",
        )
    else:
        attachment_to_add = None

    if attachment_to_add:
        attachments = hint.get("attachments")
        if not isinstance(attachments, list):
            attachments = []
            hint["attachments"] = attachments
        attachments.append(attachment_to_add)

    return event


if not settings.is_local:
    if not settings.sentry_dsn:
        raise ValueError("Sentry DSN is required for non-local environments")
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        environment=settings.env_name.value,
        # Use traces_sampler to control sampling based on the request
        traces_sampler=traces_sampler,
        before_send=sentry_before_send,
    )
