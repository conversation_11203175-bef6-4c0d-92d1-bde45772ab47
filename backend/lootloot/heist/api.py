from datetime import datetime, timezone
from typing import Annotated, Literal

from fastapi import APIRouter, HTTPException, Query
from sqlalchemy.exc import IntegrityError

from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from lootloot import schemas
from lootloot.auth.fastapi import CurrentUser
from lootloot.heist.bots.crud import (
    NonFungibleTransferManager,
    RaffleEntryManager,
    RaffleManager,
)
from lootloot.heist.bots.models import RaffleEntry
from lootloot.heist.crud import HeistClaimManager
from lootloot.nfts.crud import NFTManager
from lootloot.schemas import RaffleResp
from lootloot.utils import lock_table_command
from lootloot.wallets.crud import UserWalletManager

router = APIRouter()


@router.get("/stats", response_model=schemas.HeistStats)
def get_heist_stats(db: DatabaseSession, user: CurrentUser):
    wallet_addresses = UserWalletManager(db).all_addresses_from_user(user.id, "solana")
    return HeistClaimManager(db).stats_for_wallets(wallet_addresses=wallet_addresses)


TimezoneOffset = Annotated[
    int,
    Query(description="Difference, in minutes, between UTC and local time.", gt=-1440, lt=1440),
]


@router.get("/nana-by-day", response_model=schemas.NanaByDayResp)
def list_gained_and_lost_nana_by_day(
    user: CurrentUser,
    db: DatabaseSession,
    timezone_offset: TimezoneOffset = 0,
):
    timezone_name = timezone_offset_to_str(timezone_offset)

    wallet_addresses = UserWalletManager(db).all_addresses_from_user(user.id, "solana")

    items = HeistClaimManager(db).nana_by_day_for_wallets(
        timezone_name=timezone_name, wallet_addresses=wallet_addresses
    )
    return schemas.NanaByDayResp(items=items)


@router.get("/raffle", response_model=RaffleResp)
def get_raffle(db: DatabaseSession, user: CurrentUser):
    non_fungible_transfer_manager = NonFungibleTransferManager(db)
    deposit_counts = non_fungible_transfer_manager.get_deposit_count_leaderboard()
    withdrawals_count = non_fungible_transfer_manager.get_withdrawals_count() or 0
    user_deposits = [
        schemas.RaffleUserDepositCount(
            count=deposit_count.count,
            display_name=deposit_count.display_name or "",
            rank=deposit_count.rank,
        )
        for deposit_count in deposit_counts
    ]
    return RaffleResp(
        pending_prizes_count=non_fungible_transfer_manager.get_pending_transfers_count_for_user(
            user.id
        ),
        user_deposits=user_deposits,
        withdrawals_count=withdrawals_count,
    )


@router.post("/raffle-entry")
def create_raffle_entry(secret_word: str, db: DatabaseSession, user: CurrentUser):
    days_before_can_join = 0  # Increase as more users join
    secret_word = secret_word.lower()
    raffle = RaffleManager(db).get_by_secret_word(secret_word)
    if not raffle:
        raise HTTP400Exception("Invalid word")
    if not raffle.is_active:
        raise HTTP400Exception("Raffle already over")

    if not NFTManager(db).get_count_for_user(user.id):
        raise HTTP400Exception(
            "You need to have at least one NFT from the Heist collections to join"
        )

    if (datetime.now(tz=timezone.utc) - user.joined_at).days < days_before_can_join:
        raise HTTP400Exception(
            f"Account has to be {days_before_can_join} day{'' if days_before_can_join == 1 else 's'} old to join"
        )

    # Lock table to avoid more entries than allowed
    db.execute(lock_table_command(RaffleEntry))
    raffle_entry_manager = RaffleEntryManager(db)
    entries_count = raffle_entry_manager.get_entries_count_for_raffle(raffle.id)
    if entries_count >= raffle.entries_count:
        raise HTTP400Exception("Raffle is full")
    try:
        raffle_entry_manager.create(user.id, raffle.id)
    except IntegrityError:
        raise HTTP400Exception("You already entered this raffle")
    db.commit()


def timezone_offset_to_str(offset: int | None) -> str:
    """Converts a timezone offset in minutes to a string like `+03:00`."""
    if not offset:
        return "+00:00"
    minutes = offset % 60
    hours, minutes = divmod(offset, 60)
    if hours < 0 and minutes > 0:
        hours += 1
        minutes = 60 - minutes
    sign = "+" if hours >= 0 else "-"
    return f"{sign}{abs(hours):02}:{minutes:02}"


@router.get("/nft/{nft_address}/claims", response_model=schemas.NFTClaimsHistoryResp)
def get_nft_claims_history(
    db: DatabaseSession,
    user: CurrentUser,
    nft_address: str,
):
    """Get claims history for a specific NFT.

    Args:
        db: Database session
        user: Authenticated user
        nft_address: NFT address to get history for

    Returns:
        NFT claims history with NFT details and last 100 claims

    Raises:
        HTTP400Exception: If NFT not found or not owned by user
    """
    # Get user's wallet addresses
    wallet_addresses = UserWalletManager(db).all_addresses_from_user(user.id, "solana")

    # Get NFT details and verify ownership
    nft = NFTManager(db).get(f"solana:{nft_address}")
    if not nft or nft.owner_address_uri not in [f"solana:{addr}" for addr in wallet_addresses]:
        raise HTTP400Exception("NFT not found or not owned by user")

    # Get claims history
    result = HeistClaimManager(db).get_nft_claims_history(nft_address)
    if not result:
        raise HTTP400Exception("NFT not found")
    return result


@router.get("/claims-history", response_model=schemas.TopClaimsResp)
def get_claims_history(
    db: DatabaseSession,
    user: CurrentUser,
    timeframe: Literal["daily", "weekly", "monthly", "all-time"] = "daily",
    sort_by: schemas.TopClaimsSortBy = "total_amount_claimed",
    sort_order: schemas.TopClaimsSortOrder = "desc",
    species: str | None = None,
    event_id: str | None = None,
    location_id: str | None = None,
):
    """Get top 100 claims with most NANA claimed for the given timeframe.

    Args:
        db: Database session
        timeframe: Time period to filter claims (daily, weekly, monthly, all-time)
        user: Optional authenticated user to filter claims by their wallets
        sort_by: Field to sort by (total_amount_claimed, duration_hours, base_multiplier)
        sort_order: Sort direction (asc, desc)
        species: Optional comma-separated list of species (Chimp, Orangutan, Gorilla)
        event_id: Optional comma-separated list of event IDs
        location_id: Optional comma-separated list of location IDs

    Returns:
        Top claims response with ranked claims and NFT details
    """
    wallet_addresses = UserWalletManager(db).all_addresses_from_user(user.id, "solana")

    # Parse comma-separated values into lists
    try:
        event_id_list = [int(eid) for eid in event_id.split(",")] if event_id else None
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid event_id")
    try:
        location_id_list = [int(lid) for lid in location_id.split(",")] if location_id else None
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid location_id")

    # Parse and validate species values
    species_list = None
    if species:
        valid_species = []
        for s in species.split(","):
            if s in ("Chimp", "Orangutan", "Gorilla"):
                valid_species.append(s)
        if valid_species:
            species_list = valid_species

    filters = schemas.TopClaimsFilters(
        species=species_list,
        event_id=event_id_list,
        location_id=location_id_list,
    )

    items = (
        HeistClaimManager(db).get_top_claims(
            timeframe=timeframe,
            wallet_addresses=wallet_addresses,
            sort=schemas.TopClaimsSort(by=sort_by, order=sort_order),
            filters=filters,
        )
        if wallet_addresses
        else []  # Avoid returning global data if User had no Wallets
    )

    return schemas.TopClaimsResp(items=items)
