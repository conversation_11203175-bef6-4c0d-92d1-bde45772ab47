# --------------------
# Backend related
# --------------------
*__pycache__*
*.swp
*.swo
*.pyc
.coverage
backend/.env
.jython_cache
.pytest_cache*
.venv
backend/wallet/subql/src/types/*

# --------------------
# UI related
# --------------------
**/node_modules/
**/.next/
**/out/
**/dist/
*.tsbuildinfo
*.js.map
next-env.d.ts
.env.local
.sentryclirc

# --------------------
# Terraform related
# --------------------
**/.terraform/
*.tfstate
*.tfstate.*
infra/terraform/**/local/terraform.tfvars

# --------------------
# Code editors
# --------------------
**/.vscode/
!.vscode/settings.json
.idea

# --------------------
# Cline - AI Coder
# --------------------
cline_docs/currentTask.md

# --------------------
# Misc
# --------------------
.data
.DS_Store
.volumes
key.json
tmp/*

# Ignore generated credentials from google-github-actions/auth
gha-creds-*.json

# ctags files
tags
.aider*
