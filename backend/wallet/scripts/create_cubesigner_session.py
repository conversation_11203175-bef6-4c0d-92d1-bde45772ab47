#!/usr/bin/env python3

"""Create or update a CubeSigner session.

This script automates the process of creating and managing CubeSigner sessions,
ensuring proper cleanup of sensitive files even in case of failures.

Requires `kubectl` and `cs` (CubeSigner CLI) to be installed and available on
the PATH.

Example usage:
    ./wallet/scripts/create_cubesigner_session.py \
        --context minikube \
        --namespace wallet-stage \
        --pod-name api-8554d7cf6c-2df2p \
        --purpose dev \
        --refresh-lifetime 2700000 \
        --session-name default
"""

import argparse
import atexit
import shlex
import subprocess
import sys
import tempfile
from pathlib import Path

# Constants
SESSION_FILENAME = "session.json"
POD_SESSION_PATH = f"/tmp/{SESSION_FILENAME}"

parser = argparse.ArgumentParser(description="Create or update a CubeSigner session")
parser.add_argument(
    "--context",
    required=True,
    type=str,
    help="The context to use with kubectl",
)
parser.add_argument(
    "--namespace",
    required=True,
    type=str,
    help="The namespace to use with kubectl",
)
parser.add_argument(
    "--pod-name",
    required=True,
    type=str,
    help="The name of the API pod to copy the session file to",
)
parser.add_argument(
    "--purpose",
    required=True,
    type=str,
    help="Purpose for the CubeSigner session (e.g. 'dev')",
)
parser.add_argument(
    "--refresh-lifetime",
    required=True,
    type=int,
    help="Refresh lifetime in seconds for the session",
)
parser.add_argument(
    "--session-name",
    required=True,
    type=str,
    help="Name of the session for upsert_cube_signer_session",
)

args = parser.parse_args()


def run(*command: str, **kwargs):
    """Run a command with proper output handling.

    Args:
        *command: Command and its arguments as positional arguments
        **kwargs: Additional arguments for subprocess.run

    Returns:
        CompletedProcess: Result of the command execution
    """
    kwargs.setdefault("check", True)
    kwargs.setdefault("stdout", sys.stdout)
    kwargs.setdefault("stderr", sys.stderr)
    print("$", shlex.join(command))
    return subprocess.run(command, text=True, **kwargs)


def run_kubectl(*command: str, **kwargs):
    """Run a kubectl command with the configured context and namespace.

    Args:
        *command: Kubectl subcommand and its arguments as positional arguments
        **kwargs: Additional arguments for subprocess.run

    Returns:
        CompletedProcess: Result of the kubectl command execution
    """
    full_command = (
        "kubectl",
        f"--context={args.context}",
        f"--namespace={args.namespace}",
        *command,
    )
    return run(*full_command, **kwargs)


def cleanup_pod_file():
    """Clean up session file from the pod.

    This function ensures the session.json file is removed from the pod,
    even if the script crashes.
    """
    print("Cleaning up pod file...")
    try:
        run_kubectl(
            "exec",
            args.pod_name,
            "--",
            "rm",
            "-f",
            POD_SESSION_PATH,
        )
        print(f"Cleaned up pod {POD_SESSION_PATH}")
    except Exception as e:
        print(f"Error cleaning up pod {POD_SESSION_PATH}: {e}", file=sys.stderr)


def main():
    """Main execution flow for creating/updating CubeSigner session."""
    # Register pod file cleanup to run on exit
    atexit.register(cleanup_pod_file)

    try:
        # Use a temporary directory for the local session file
        with tempfile.TemporaryDirectory() as temp_dir:
            session_path = Path(temp_dir) / SESSION_FILENAME

            # 1. Create session with CubeSigner CLI
            print("Creating CubeSigner session...")
            run(
                "cs",
                "token",
                "create",
                "-p",
                args.purpose,
                "--user",
                "--scope",
                "manage:*",
                "-s",
                str(session_path),
                "--refresh-lifetime",
                str(args.refresh_lifetime),
            )

            # 2. Copy session.json to the pod
            print("Copying session file to pod...")
            run_kubectl(
                "cp",
                str(session_path),
                f"{args.namespace}/{args.pod_name}:{POD_SESSION_PATH}",
            )

            # 3. Save encrypted session to database
            print("Saving session to database...")
            run_kubectl(
                "exec",
                args.pod_name,
                "--",
                "python",
                "-m",
                "wallet.scripts.upsert_cube_signer_session",
                args.session_name,
                POD_SESSION_PATH,
            )

            print("CubeSigner session successfully created and saved")

    except subprocess.CalledProcessError as e:
        print(f"Command failed with exit code {e.returncode}: {e.cmd}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
