import { useDebounce } from "@/lib/hooks/debounce";
import { useMemo, useState } from "react";

interface WalletSearchable {
  readonly name: string;
  readonly address: string;
}

export function useWalletSearch<W extends WalletSearchable>(wallets: readonly W[]) {
  const [search, setSearch] = useState("");

  const debouncedSearch = useDebounce(search ? 250 : 0, search)
    .trim()
    .toUpperCase();

  const filteredWallets = useMemo(() => {
    if (!debouncedSearch) return wallets;
    return wallets.filter(
      (wallet) =>
        wallet.name.toUpperCase().includes(debouncedSearch) ||
        wallet.address.toUpperCase().includes(debouncedSearch),
    );
  }, [debouncedSearch, wallets]);

  return { search, setSearch, wallets: filteredWallets } as const;
}
