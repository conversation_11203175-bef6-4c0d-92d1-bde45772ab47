"""Credential wrap keys

Revision ID: 0003
Revises: 0002
Create Date: 2025-03-13 17:01:32.019908

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "0003"
down_revision: str | None = "0002"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "credentialwrapkey",
        sa.Column("secret_key", postgresql.BYTEA(), nullable=False),
        sa.Column("credential_id", postgresql.BYTEA(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["credential_id"],
            ["credential.id"],
        ),
        sa.<PERSON>ey<PERSON>onstraint("id"),
    )
    op.create_index(
        op.f("ix_credentialwrapkey_credential_id"),
        "credentialwrapkey",
        ["credential_id"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_credentialwrapkey_credential_id"), table_name="credentialwrapkey")
    op.drop_table("credentialwrapkey")
    # ### end Alembic commands ###
