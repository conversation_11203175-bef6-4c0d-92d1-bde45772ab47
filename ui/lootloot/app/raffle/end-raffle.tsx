"use client";

import { AlertError } from "@/lib/components/alert";
import { <PERSON><PERSON> } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { Label, ValidationError } from "@/lib/components/form";
import { Input } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { useHeistEndRaffle } from "@/lib/heist";
import { superstructResolver } from "@hookform/resolvers/superstruct";
import { Minus, Ticket } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { nonempty, object, string, trimmed } from "superstruct";

/**
 * A button that opens a dialog to end an existing Raffle by secret word.
 */
export function EndRaffleButton(props: Dialog.DialogTriggerProps) {
  const [open, setOpen] = useState(false);
  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Trigger {...props}>
        <span className="relative mr-3 w-8" aria-hidden>
          <Ticket strokeWidth={1} size={20} />
          <Minus size={10} className="absolute top-0 right-0" color="red" />
        </span>
        End raffle
      </Dialog.Trigger>
      <Dialog.Content title="End raffle">
        <EndRaffleForm
          onSuccess={() => {
            setOpen(false);
          }}
        />
      </Dialog.Content>
    </Dialog.Root>
  );
}

const EndRaffleFormStruct = object({
  secretWord: nonempty(trimmed(string())),
});

type EndRaffleFormStruct = typeof EndRaffleFormStruct.TYPE;

function EndRaffleForm({ onSuccess }: { onSuccess: () => void }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<EndRaffleFormStruct>({
    resolver: superstructResolver(EndRaffleFormStruct, { coerce: true }),
  });

  const endRaffle = useHeistEndRaffle();

  const onSubmit = (event: React.FormEvent) => {
    void handleSubmit((data) => {
      endRaffle.mutate(data.secretWord, { onSuccess });
    })(event);
  };
  return (
    <form
      onSubmit={onSubmit}
      className="grid grid-cols-[auto_1fr] justify-items-end gap-x-2 gap-y-3"
    >
      {endRaffle.error && (
        <div className="col-span-full w-full">
          {<AlertError>{getErrorMessage(endRaffle.error)}</AlertError>}
        </div>
      )}

      <ValidationError className="col-start-2 -mt-1">
        {errors.secretWord?.message}
      </ValidationError>
      <Label className="mt-3">Secret Word</Label>
      <Input {...register("secretWord")} placeholder="Case insensitive but must be unique" />

      <div className="col-span-full flex gap-4">
        <Button type="submit" disabled={endRaffle.isPending}>
          End
        </Button>
      </div>
    </form>
  );
}
