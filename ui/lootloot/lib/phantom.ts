import { useWalletAuthMessage } from "@/lib/auth";
import { Connection, PublicKey, Transaction, TransactionInstruction } from "@solana/web3.js";
import { useQuery } from "@tanstack/react-query";
import bs58 from "bs58";

export function useBlockHash(connection: Connection) {
  return useQuery({
    queryKey: ["blockHash"],
    queryFn: async () => {
      return await connection.getLatestBlockhash();
    },
  });
}

/**
 * signMessage is still not supported on Ledger. As a workaround, use signTransaction
 * These utilities are required for the below adapted workaround to work
 *
 * https://github.com/solana-labs/solana/issues/21366#issuecomment-1194310677
 * Issue had updates, it was implement in solana-cli, Phantom is ready to support it
 * but they are waiting on Ledger to actually support it themselves, so still stuck
 */

const MEMO_PROGRAM_ID = new PublicKey("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr");

const buildAuthTx = (message: string): Transaction => {
  const tx = new Transaction();
  tx.add(
    new TransactionInstruction({
      programId: MEMO_PROGRAM_ID,
      keys: [],
      data: Buffer.from(message, "utf8"),
    }),
  );

  return tx;
};

interface PhantomSignature {
  connection: Connection;
  publicKey: PublicKey | null;
  signTransaction: ((transaction: Transaction) => Promise<Transaction>) | undefined;
  walletAuthMessage: ReturnType<typeof useWalletAuthMessage>;
}

export async function phantomSignature({
  connection,
  publicKey,
  signTransaction,
  walletAuthMessage,
}: PhantomSignature) {
  const blockHash = await connection.getLatestBlockhash();
  if (!signTransaction) throw new Error("No sign transaction method available");
  if (!publicKey) throw new Error("No public key available");
  if (!walletAuthMessage.isSuccess) throw new Error("No wallet auth message available");

  const message = walletAuthMessage.data.message;
  const tx = buildAuthTx(message);
  tx.feePayer = publicKey;
  tx.recentBlockhash = blockHash.blockhash;

  const signedTx = await signTransaction(tx);
  const signature = bs58.encode(signedTx.serialize());
  return { signature, message, address: publicKey.toString() };
}
