import * as Radix<PERSON><PERSON><PERSON> from "@radix-ui/react-label";
import { cva, cx, VariantProps } from "class-variance-authority";
import { forwardRef } from "react";

export const label = cva("font-medium", {
  variants: {
    size: {
      sm: "text-xs",
      md: "text-sm",
      lg: "text-base",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

export interface LabelProps extends RadixLabel.LabelProps, VariantProps<typeof label> {}

export const Label = forwardRef<React.ElementRef<typeof RadixLabel.Root>, LabelProps>(
  function Label(props, ref) {
    return (
      <RadixLabel.Root {...props} ref={ref} className={cx(label(props), props.className)} />
    );
  },
);

type ValidationErrorProps = Omit<React.ComponentPropsWithoutRef<"p">, "role">;

export function ValidationError(props: ValidationErrorProps) {
  return props.children ? (
    <p
      {...props}
      className={cx(
        "w-full rounded-md bg-red-50/80 px-1.5 py-0.5 text-xs dark:bg-red-900/40",
        props.className,
      )}
      role="alert"
    />
  ) : null;
}
