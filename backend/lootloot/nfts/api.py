from fastapi import APIRouter

from common.psql import DatabaseSession
from lootloot import schemas
from lootloot.auth.fastapi import CurrentUser
from lootloot.nfts.crud import NFTCollectionManager, NFTManager
from lootloot.wallets.crud import UserWalletManager

router = APIRouter()


@router.get("", response_model=schemas.ListNFTCollectionsResp)
def list_nft_collections(db: DatabaseSession, user: CurrentUser):
    wallets = UserWalletManager(db).all_from_user(user.id)
    address_uris = [wallet.address_uri for wallet in wallets]
    owned_collections = NFTCollectionManager(db).list_owned(address_uris)

    items = [
        schemas.NFTCollectionListItem(
            uid=collection.uid,
            name=collection.name,
            nft_count=nft_count,
            image_url=image_url,
        )
        for collection, nft_count, image_url in owned_collections
    ]

    return schemas.ListNFTCollectionsResp(items=items)


@router.get("/{collection_uid}", response_model=schemas.ListNFTResp)
def get_nft_collection(collection_uid: str, db: DatabaseSession, user: CurrentUser):
    wallets = UserWalletManager(db).all_from_user(user.id)
    nfts = NFTManager(db).get_from_collection_and_owner(
        collection_uid=collection_uid,
        owner_address_uris=[wallet.address_uri for wallet in wallets],
    )
    return schemas.ListNFTResp(
        items=[schemas.NFTListItem.model_validate(nft, from_attributes=True) for nft in nfts]
    )
