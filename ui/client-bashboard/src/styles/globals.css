@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

@custom-variant sahaba (&:where(.sahaba, .sahaba *));

@import "tw-animate-css";

@layer base {
  :root {
    --background: oklch(100% 0 0);
    --foreground: oklch(0 0 0);

    --muted: oklch(96.5% 0.007 248);
    --muted-foreground: oklch(55.5% 0.04 257);

    --accent: oklch(96.5% 0.007 248);
    --accent-foreground: oklch(21% 0.04 266);

    --popover: oklch(100% 0 0);
    --popover-foreground: oklch(0 0 0);

    --card: oklch(100% 0 0);
    --card-foreground: oklch(21% 0.04 266);

    --primary: #155dfc;
    --primary-foreground: oklch(100% 0 0);

    --secondary: oklch(96.5% 0.007 248);
    --secondary-foreground: oklch(21% 0.04 266);

    --destructive: oklch(62.66% 0.257 29.2);
    --destructive-foreground: oklch(98% 0.0042 248);

    --border: oklch(92.4% 0.0076 284);
    --ring: #155dfc;

    /* Text colors */
    --desert-text-dark: #3d3026;
  }

  :where(.sahaba, .sahaba *) {
    --primary: #c3761e;
    --primary-foreground: oklch(100% 0 0);

    --secondary: #4a90d9;
    --secondary-foreground: oklch(100% 0 0);

    --accent: oklch(97% 0.001 106.424);
    --accent-foreground: oklch(21% 0.04 266);

    --destructive: oklch(58.6% 0.253 17.585);
    --destructive-foreground: oklch(98.5% 0.001 106.423);

    --ring: #e09e51;

    outline-color: #e09e51;
  }
}

@theme inline {
  --color-border: var(--border);
  --color-ring: var(--ring);
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
}

@layer base {
  html,
  body {
    min-height: 100%;
  }

  #__next {
    height: 100%;
  }

  code {
    font-family:
      "Inter",
      sans-serif,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      "Roboto",
      "Oxygen",
      "Ubuntu",
      "Cantarell",
      "Fira Sans",
      "Droid Sans",
      "Helvetica Neue";
    padding: 0.1rem 0.4rem;
    font-size: 90%;
    color: #d400ff;
    border-radius: 0.1335rem;
  }
  code:not([class*="language-"]):before,
  code:not([class*="language-"]):after {
    content: "`";
  }
  code[class*="language-"] {
    padding: 0;
  }
}
