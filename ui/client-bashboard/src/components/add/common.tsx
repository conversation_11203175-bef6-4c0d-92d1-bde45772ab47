import { CancelButton } from "@/components/button/CancelButton";
import { getErrorDetail } from "@/store/services/helpers";
import { <PERSON><PERSON>, <PERSON><PERSON>, Stack, TextField, Typography } from "@mui/material";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import {
  BaseQueryFn,
  MutationActionCreatorResult,
  MutationDefinition,
} from "@reduxjs/toolkit/query/react";
import { Close } from "mdi-material-ui";
import React, { useState } from "react";

export interface AddProps {
  close: () => void;
  onSuccess: () => void;
}

interface BaseAddProps<T> extends AddProps {
  addMutation: (
    arg: T,
  ) => MutationActionCreatorResult<MutationDefinition<T, BaseQueryFn, string, unknown>>;
  disableAdd: boolean;
  fields: React.ReactNode;
  label: string;
  submitData: T;
}

export function BaseAdd<T>(props: BaseAddProps<T>) {
  const [errorAlert, setErrorAlert] = useState("");
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const submitData = props.submitData;

  return (
    submitData && (
      <Stack spacing={6}>
        <Grid container>
          <Grid item xs={11}>
            <Typography variant={"h6"} pt={1}>
              {props.label}
            </Typography>
          </Grid>
          <Grid item xs={1} justifyContent={"flex-end"}>
            <IconButton size={"small"} onClick={props.close}>
              <Close />
            </IconButton>
          </Grid>
        </Grid>

        {showErrorAlert && <Alert severity={"error"}>{errorAlert}</Alert>}

        {props.fields}
        <Stack direction={"row"} spacing={4}>
          <Button
            disabled={props.disableAdd}
            color={"success"}
            fullWidth
            variant={"outlined"}
            onClick={() => {
              props
                .addMutation(submitData)
                .unwrap()
                .then(() => {
                  props.onSuccess();
                  props.close();
                })
                .catch((error: unknown) => {
                  setErrorAlert(getErrorDetail(error, { fallback: "Unknown failure to add" }));
                  setShowErrorAlert(true);
                });
            }}
          >
            {" "}
            Add New{" "}
          </Button>
          <CancelButton
            onClick={() => {
              props.close();
            }}
          />
        </Stack>
      </Stack>
    )
  );
}

export const nameUrlFields = (
  nameValue: string,
  setNameValue: React.Dispatch<string>,
  urlValue: string,
  setUrlValue: React.Dispatch<string>,
) => {
  return (
    <>
      <TextField
        label={"Name"}
        onChange={(event) => {
          setNameValue(event.target.value);
        }}
        value={nameValue}
      />
      <TextField
        label={"URL"}
        onChange={(event) => {
          setUrlValue(event.target.value);
        }}
        value={urlValue}
      />
    </>
  );
};
