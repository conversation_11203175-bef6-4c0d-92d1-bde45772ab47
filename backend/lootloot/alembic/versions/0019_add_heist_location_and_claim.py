"""Add heist location and claim

Revision ID: 0019
Revises: 0018
Create Date: 2024-03-11 15:46:55.702335

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0019"
down_revision = "0018"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "heistlocation",
        sa.<PERSON>umn("game_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("name", sa.String(), nullable=False),
        sa.Column("location_type", sa.String(), nullable=False),
        sa.Column("risk", sa.String(), nullable=False),
        sa.Column("district_id", sa.Integer(), nullable=True),
        sa.Column("district_name", sa.String(), nullable=True),
        sa.Column("active_chimp_count", sa.Integer(), nullable=False),
        sa.Column("active_orangutan_count", sa.Integer(), nullable=False),
        sa.Column("active_gorilla_count", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "heistclaim",
        sa.Column("game_id", sa.Integer(), nullable=False),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("ended_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("total_amount_emitted", sa.Numeric(), nullable=False),
        sa.Column("total_amount_claimed", sa.Numeric(), nullable=False),
        sa.Column("nft_address", sa.String(), nullable=False),
        sa.Column("wallet_address", sa.String(), nullable=False),
        sa.Column("location_id", sa.Integer(), nullable=False),
        sa.Column("event_id", sa.Integer(), nullable=False),
        sa.Column("arresting_officer_address", sa.String(), nullable=True),
        sa.Column("is_bonus_roll", sa.Boolean(), nullable=False),
        sa.Column("secondary_event", sa.String(), nullable=True),
        sa.Column("opposing_nft_address", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["location_id"],
            ["heistlocation.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_heist_claim_ended_at_game_id",
        "heistclaim",
        [sa.text("ended_at DESC"), "game_id"],
        unique=True,
    )
    op.add_column("rawheistclaim", sa.Column("processed_id", sa.Integer(), nullable=True))
    op.add_column(
        "rawheistclaim",
        sa.Column("unprocessable", sa.Boolean(), server_default="false", nullable=False),
    )
    op.create_index(
        "ix_raw_heist_claim_unprocessed",
        "rawheistclaim",
        [sa.text("(processed_id IS NULL)"), sa.text("(unprocessable = false)")],
        unique=False,
    )
    op.create_foreign_key(
        "rawheistclaim_processed_id_fkey",
        "rawheistclaim",
        "heistclaim",
        ["processed_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("rawheistclaim_processed_id_fkey", "rawheistclaim", type_="foreignkey")
    op.drop_index("ix_raw_heist_claim_unprocessed", table_name="rawheistclaim")
    op.drop_column("rawheistclaim", "unprocessable")
    op.drop_column("rawheistclaim", "processed_id")
    op.drop_index("ix_heist_claim_ended_at_game_id", table_name="heistclaim")
    op.drop_table("heistclaim")
    op.drop_table("heistlocation")
    # ### end Alembic commands ###
