# syntax=docker/dockerfile:1
FROM node:22 AS subql-runner

RUN npm install -g pnpm@latest-10

WORKDIR /usr/src/app/backend/wallet/subql

COPY backend/wallet/subql/package.json ./
COPY backend/wallet/subql/pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

ENV PATH="/usr/src/app/backend/wallet/subql/node_modules/.bin:$PATH"

FROM subql-runner AS dev
CMD ["bash", "-c", "echo 'No command specified' && exit 1"]

FROM subql-runner AS prod

COPY backend/wallet/subql ./

ARG SENTRY_RELEASE
ENV SENTRY_RELEASE=${SENTRY_RELEASE}

CMD ["bash", "-c", "echo 'No command specified' && exit 1"]
