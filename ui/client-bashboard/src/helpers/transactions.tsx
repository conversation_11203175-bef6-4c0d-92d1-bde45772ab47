import { processCoinTotal } from "@/helpers/coin";
import { currencyFormatter } from "@/helpers/formatters";
import { Transaction, TransactionsOverview } from "@/types/schemas";
import { useMemo } from "react";

export function processTransactionsOverview(raw: TransactionsOverview) {
  return { ...raw, coinTotals: raw.coinTotals.map(processCoinTotal) };
}

/**
 * Calculate the average cost of transactions for a given coin.
 *
 * Don't take into consideration the direction of the transfers to define
 * the amounts sign. This should be used only for transactions/transfers
 * collections where all are going to the same direction.
 */
export function useSimpleAverageCost(
  coinId: number,
  transactions: readonly Transaction[],
): number | null {
  return useMemo(() => {
    if (coinId === 0) return null;
    let totalAmount = 0;
    let totalCost = 0;
    for (const tx of transactions) {
      for (const transfer of tx.transfers) {
        if (transfer.assetAccount.coin?.id !== coinId || transfer.assetPriceUsd == null)
          continue;
        const amount = Number.parseFloat(transfer.amount);
        totalAmount += amount;
        totalCost += Number.parseFloat(transfer.assetPriceUsd) * amount;
      }
    }
    if (totalAmount === 0) return null;
    return totalCost / totalAmount;
  }, [coinId, transactions]);
}

/**
 * Return cost basis for transaction transfers.
 *
 * Don't take into consideration the direction of the transfers to define
 * the amounts sign. This should be used only for transactions/transfers
 * collections where all are going to the same direction.
 *
 * @returns In case of a single coin, return the cost basis as a string.
 * Otherwise, return a string with the cost basis for each coin with the
 * format "costBasis1/COIN1 costBasis2/COIN2 ...".
 */
export function simpleCostBasis(transaction: Transaction): string {
  const amountCostByTicker: Record<string, [amount: number, cost: number]> = {};
  for (const transfer of transaction.transfers) {
    if (transfer.assetAccount.coin == null || transfer.assetPriceUsd == null) continue;
    const amount = Number.parseFloat(transfer.amount);
    const cost = Number.parseFloat(transfer.assetPriceUsd) * amount;
    const ticker = transfer.assetAccount.coin.ticker;
    const [prevAmount, prevCost] = amountCostByTicker[ticker] ?? [0, 0];
    amountCostByTicker[ticker] = [prevAmount + amount, prevCost + cost];
  }

  const costBasisList: [ticker: string, costBasis: number][] = [];
  for (const [ticker, [amount, cost]] of Object.entries(amountCostByTicker)) {
    costBasisList.push([ticker, cost / amount]);
  }

  if (costBasisList.length === 1) {
    const [, costBasis] = costBasisList[0];
    return currencyFormatter.format(costBasis);
  }

  return costBasisList
    .map(([ticker, costBasis]) => {
      return `${currencyFormatter.format(costBasis)}/${ticker.toUpperCase()}`;
    })
    .join(" ");
}
