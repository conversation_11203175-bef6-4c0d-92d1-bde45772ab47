import { Coin, CoinTotal } from "@/types/schemas";

export function getCoinDisplayName(coin: Pick<Coin, "uid" | "name" | "ticker">): string {
  return coin.uid == "dollar" ? coin.name : `${coin.name} (${coin.ticker.toUpperCase()})`;
}

export interface CoinProcessed extends Omit<Coin, "latestUsdPrice"> {
  latestUsdPrice: number;
}

export function processCoin(raw: Coin): CoinProcessed {
  return { ...raw, latestUsdPrice: Number.parseFloat(raw.latestUsdPrice) };
}

export interface CoinTotalProcessed extends Omit<CoinTotal, "coin" | "amount"> {
  coin: CoinProcessed;
  amount: number;
}

export function processCoinTotal(raw: CoinTotal): CoinTotalProcessed {
  return {
    ...raw,
    coin: processCoin(raw.coin),
    amount: Number.parseFloat(raw.amount),
  };
}
