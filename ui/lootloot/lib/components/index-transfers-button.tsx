"use client";

import { AlertError, AlertSuccess } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { InputClearable } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { useIndexTransfers } from "@/lib/transfers";
import { Search } from "lucide-react";
import { useState } from "react";

export function IndexTransfersButton() {
  const [address, setAddress] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const indexTransfers = useIndexTransfers();

  const isLoading = indexTransfers.isPending;

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    if (!address.trim()) return;

    setErrorMessage(null);
    indexTransfers.mutate(address.trim(), {
      onSuccess() {
        setAddress("");
        setShowSuccess(true);
        // Hide success message after 3 seconds
        setTimeout(() => {
          setShowSuccess(false);
        }, 3000);
      },
      onError(error) {
        const message = getErrorMessage(error);
        setErrorMessage(message);
        // Hide error message after 5 seconds
        setTimeout(() => {
          setErrorMessage(null);
        }, 5000);
      },
    });
  };

  return (
    <div>
      {showSuccess && (
        <AlertSuccess className="mb-4">Successfully indexed transfers</AlertSuccess>
      )}
      {errorMessage && <AlertError className="mb-4">{errorMessage}</AlertError>}
      <form onSubmit={handleSubmit} className="flex items-center gap-2">
        <InputClearable
          className="flex-1"
          placeholder="Enter wallet address to index transfers"
          value={address}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setAddress(e.target.value);
          }}
          onClear={() => {
            setAddress("");
          }}
          clearHidden={!address}
        />
        <Button
          type="submit"
          variant="ghost"
          size="icon"
          disabled={isLoading || !address.trim()}
        >
          <Search strokeWidth={1} className={isLoading ? "animate-pulse" : ""} />
        </Button>
      </form>
    </div>
  );
}
