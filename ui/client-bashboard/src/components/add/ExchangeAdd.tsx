import { AddProps, BaseAdd, nameUrlFields } from "@/components/add/common";
import { useIndexerExchangeAddMutation } from "@/store/services/api";
import { useState } from "react";

export const ExchangeAdd = (props: AddProps) => {
  const [addExchange] = useIndexerExchangeAddMutation();
  const [nameValue, setNameValue] = useState("");
  const [urlValue, setUrlValue] = useState("");

  return (
    <BaseAdd
      close={props.close}
      addMutation={addExchange}
      disableAdd={!nameValue || !urlValue}
      label={"Add an Exchange"}
      onSuccess={props.onSuccess}
      submitData={{ name: nameValue, url: urlValue }}
      fields={nameUrlFields(nameValue, setNameValue, urlValue, setUrlValue)}
    />
  );
};
