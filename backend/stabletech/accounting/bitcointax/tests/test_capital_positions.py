from datetime import date, datetime, timedelta
from decimal import Decimal
from unittest.mock import patch

from common.pricer import schemas
from stabletech.accounting.bitcointax.tests.conftest import (
    clear_capital_positions_and_transactions,
    create_capital_positions,
    create_transactions,
)
from stabletech.accounting.crud import CapitalPositionManager
from stabletech.indexer.models import TransactionAction
from stabletech.utils.pricer import pricer

BTC_PRICE = schemas.CoinHistory(
    id="bitcoin",
    symbol="BTC",
    name="Bitcoin",
    market_data=schemas.HistoricMarketData(
        current_price={"usd": 2000},
        market_cap={"usd": ************0},
        total_volume={"usd": ************},
    ),
)
USDC_PRICE = schemas.CoinHistory(
    id="usd-coin",
    symbol="USDC",
    name="USD Coin",
    market_data=schemas.HistoricMarketData(
        current_price={"usd": 1},
        market_cap={"usd": ************0},
        total_volume={"usd": ************},
    ),
)


def get_price_mock(coin_id: str, date: date):
    if coin_id == "BTC":
        return BTC_PRICE
    if coin_id == "USDC":
        return USDC_PRICE
    raise ValueError(f"Unknown coin {coin_id}")


def test_generate_capital_positions(client):
    response = client.post("/accounting/capital-positions/regenerate")
    assert response.status_code == 200


@patch.object(pricer, "get_coin_history", side_effect=get_price_mock)
def test_capital_positions_matching(_, client, session, transactions, capital_positions):
    capital_positions = CapitalPositionManager(session).get_all()
    # Assert unmatched
    for position in capital_positions:
        assert position.buy_transaction_id is None
        assert position.sell_transaction_id is None

    response = client.post("/accounting/capital-positions/regenerate")
    assert response.status_code == 200

    # Assert matched
    new_capital_positions = CapitalPositionManager(session).get_all()
    for position in capital_positions:
        for new_position in new_capital_positions:
            if position.id == new_position.id:
                assert new_position.buy_transaction_id is not None
                assert new_position.sell_transaction_id is not None

    # Assert new capital positions generated
    assert len(new_capital_positions) > len(capital_positions)


def test_capital_position_matching_exact_buy_amount(client, session):
    capital_positions_data = [
        {
            "amount": Decimal("1.1201034"),
            "bought_at": datetime(2021, 1, 2, 0, 0, 0),
            "bought_usd_amount": Decimal(1000),
            "currency": "BTC",
            "csv_row": 1,
            "sold_at": datetime(2021, 1, 3, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal("1"),
            "bought_at": datetime(2021, 1, 2, 0, 0, 0),
            "bought_usd_amount": Decimal(500),
            "currency": "BTC",
            "csv_row": 2,
            "sold_at": datetime(2021, 1, 3, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal("1"),
            "bought_at": datetime(2021, 1, 2, 0, 0, 0),
            "bought_usd_amount": Decimal(500),
            "currency": "BTC",
            "csv_row": 3,
            "sold_at": datetime(2021, 1, 3, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal(2000),
            "bought_at": datetime(2021, 1, 1, 0, 0, 0),
            "bought_usd_amount": Decimal(2000),
            "currency": "USDC",
            "csv_row": 4,
            "sold_at": datetime(2021, 1, 2, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal(6000),
            "bought_at": datetime(2021, 1, 3, 0, 0, 0),
            "bought_usd_amount": Decimal(6000),
            "currency": "USDC",
            "csv_row": 5,
            "sold_at": datetime(2021, 1, 4, 0, 0, 0),
            "sold_usd_amount": Decimal(6000),
        },
    ]
    transactions_data = [
        {
            "action": TransactionAction.loan.value,
            "confirmed_at": datetime(2021, 1, 1, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(2000),
                    "from": "Loan",
                    "to": "Wallet",
                }
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 2, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(1000),
                    "from": "Wallet",
                    "to": "Exchange",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(2),
                    "from": "Exchange",
                    "to": "Wallet",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 2, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(1000),
                    "from": "Wallet",
                    "to": "Exchange",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal("1.1201034"),
                    "from": "Exchange",
                    "to": "Wallet",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(2000),
                    "from": "Exchange",
                    "to": "Wallet",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal("1.1201034"),
                    "from": "Wallet",
                    "to": "Exchange",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(4000),
                    "from": "Exchange",
                    "to": "Wallet",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(2),
                    "from": "Wallet",
                    "to": "Exchange",
                },
            ],
        },
        {
            "action": TransactionAction.spend.value,
            "confirmed_at": datetime(2021, 1, 4, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(6000),
                    "from": "Wallet",
                    "to": "Spend",
                }
            ],
        },
    ]
    clear_capital_positions_and_transactions(session)

    capital_positions = CapitalPositionManager(session).get_all()
    assert len(capital_positions) == 0
    create_transactions(session, transactions_data)
    create_capital_positions(session, capital_positions_data)

    response = client.post("/accounting/capital-positions/regenerate")
    assert response.status_code == 200

    capital_positions = CapitalPositionManager(session).get_all()

    # No new capital positions generated
    assert len(capital_positions) == len(capital_positions_data)

    # Fully matched
    for capital_position in capital_positions:
        assert capital_position.buy_transaction_id is not None
        assert capital_position.sell_transaction_id is not None


def test_remove_loan_withdrawals_from_buys(client, session):
    """Tests that loan withdrawals are not matched but substracted from buys"""
    capital_positions_data = [
        {
            "amount": Decimal("1.1201034"),
            "bought_at": datetime(2021, 1, 2, 0, 0, 0),
            "bought_usd_amount": Decimal(1000),
            "currency": "BTC",
            "csv_row": 1,
            "sold_at": datetime(2021, 1, 3, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal("1"),
            "bought_at": datetime(2021, 1, 2, 0, 0, 0),
            "bought_usd_amount": Decimal(500),
            "currency": "BTC",
            "csv_row": 2,
            "sold_at": datetime(2021, 1, 3, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal("1"),
            "bought_at": datetime(2021, 1, 2, 0, 0, 0),
            "bought_usd_amount": Decimal(500),
            "currency": "BTC",
            "csv_row": 3,
            "sold_at": datetime(2021, 1, 3, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal(2000),
            "bought_at": datetime(2021, 1, 1, 0, 0, 0),
            "bought_usd_amount": Decimal(2000),
            "currency": "USDC",
            "csv_row": 4,
            "sold_at": datetime(2021, 1, 2, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
        {
            "amount": Decimal(6000),
            "bought_at": datetime(2021, 1, 3, 0, 0, 0),
            "bought_usd_amount": Decimal(6000),
            "currency": "USDC",
            "csv_row": 5,
            "sold_at": datetime(2021, 1, 4, 0, 0, 0),
            "sold_usd_amount": Decimal(6000),
        },
    ]
    transactions_data = [
        {
            "action": TransactionAction.loan.value,
            "confirmed_at": datetime(2021, 1, 1, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(4000),
                    "from": "Loan",
                    "to": "Wallet",
                }
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 2, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(1000),
                    "from": "Wallet",
                    "to": "Exchange",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(2),
                    "from": "Exchange",
                    "to": "Wallet",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 2, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(1000),
                    "from": "Wallet",
                    "to": "Exchange",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal("1.1201034"),
                    "from": "Exchange",
                    "to": "Wallet",
                },
            ],
        },
        {
            "action": TransactionAction.loan.value,
            "confirmed_at": datetime(2021, 1, 2, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(2000),
                    "from": "Wallet",
                    "to": "Loan",
                }
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(2000),
                    "from": "Exchange",
                    "to": "Wallet",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal("1.1201034"),
                    "from": "Wallet",
                    "to": "Exchange",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(4000),
                    "from": "Exchange",
                    "to": "Wallet",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(2),
                    "from": "Wallet",
                    "to": "Exchange",
                },
            ],
        },
        {
            "action": TransactionAction.spend.value,
            "confirmed_at": datetime(2021, 1, 4, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(6000),
                    "from": "Wallet",
                    "to": "Spend",
                }
            ],
        },
    ]
    clear_capital_positions_and_transactions(session)

    capital_positions = CapitalPositionManager(session).get_all()
    assert len(capital_positions) == 0
    create_transactions(session, transactions_data)
    create_capital_positions(session, capital_positions_data)

    response = client.post("/accounting/capital-positions/regenerate")
    assert response.status_code == 200

    capital_positions = CapitalPositionManager(session).get_all()

    # No new capital positions generated
    assert len(capital_positions) == len(capital_positions_data)

    # Fully matched
    for capital_position in capital_positions:
        assert capital_position.buy_transaction_id is not None
        assert capital_position.sell_transaction_id is not None


def test_fail_match_outside_of_tolerance(client, session):
    """Tests that loan withdrawals are not matched but substracted from buys"""
    capital_positions_data = [
        {
            "amount": Decimal("2000.02"),
            "bought_at": datetime(2021, 1, 1, 0, 0, 0),
            "bought_usd_amount": Decimal(2000),
            "currency": "USDC",
            "csv_row": 4,
            "sold_at": datetime(2021, 1, 4, 0, 0, 0),
            "sold_usd_amount": Decimal(2000),
        },
    ]
    transactions_data = [
        {
            "action": TransactionAction.loan.value,
            "confirmed_at": datetime(2021, 1, 1, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal("2000"),
                    "from": "Loan",
                    "to": "Wallet",
                }
            ],
        },
        {
            "action": TransactionAction.spend.value,
            "confirmed_at": datetime(2021, 1, 4, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal("2000"),
                    "from": "Wallet",
                    "to": "Spend",
                }
            ],
        },
    ]
    clear_capital_positions_and_transactions(session)

    capital_positions = CapitalPositionManager(session).get_all()
    assert len(capital_positions) == 0
    create_transactions(session, transactions_data)
    create_capital_positions(session, capital_positions_data)

    response = client.post("/accounting/capital-positions/regenerate")
    assert response.status_code == 400

    capital_positions = CapitalPositionManager(session).get_all()

    # No new capital positions generated
    assert len(capital_positions) == len(capital_positions_data)


@patch.object(pricer, "get_coin_history", side_effect=get_price_mock)
def test_generate_long_term_capital_position(_, client, session):
    transactions_data = [
        {
            "action": TransactionAction.loan.value,
            "confirmed_at": datetime(2021, 1, 1, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal("8240.2068"),
                    "from": "Loan",
                    "to": "Wallet",
                }
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2021, 1, 2, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(4000),
                    "from": "Wallet",
                    "to": "Exchange",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(2),
                    "from": "Exchange",
                    "to": "Wallet",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2022, 1, 2, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal("2240.2068"),
                    "from": "Wallet",
                    "to": "Exchange",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal("1.1201034"),
                    "from": "Exchange",
                    "to": "Wallet",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2022, 1, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(2000),
                    "from": "Wallet",
                    "to": "Exchange",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(1),
                    "from": "Exchange",
                    "to": "Wallet",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2022, 3, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal("2240.2068"),
                    "from": "Exchange",
                    "to": "Wallet",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal("1.1201034"),
                    "from": "Wallet",
                    "to": "Exchange",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2022, 3, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(4000),
                    "from": "Exchange",
                    "to": "Wallet",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(2),
                    "from": "Wallet",
                    "to": "Exchange",
                },
            ],
        },
        {
            "action": TransactionAction.swap.value,
            "confirmed_at": datetime(2022, 3, 3, 0, 0, 0),
            "transfers": [
                {
                    "currency": "USDC",
                    "amount": Decimal(2000),
                    "from": "Exchange",
                    "to": "Wallet",
                },
                {
                    "currency": "BTC",
                    "amount": Decimal(1),
                    "from": "Wallet",
                    "to": "Exchange",
                },
            ],
        },
    ]
    clear_capital_positions_and_transactions(session)
    create_transactions(session, transactions_data)

    response = client.post("/accounting/capital-positions/regenerate")
    assert response.status_code == 200

    capital_positions = CapitalPositionManager(session).get_all()

    assert len(capital_positions) == 6

    long_term_count = 0
    short_term_count = 0
    for capital_position in capital_positions:
        assert capital_position.buy_transaction_id is not None
        assert capital_position.sell_transaction_id is not None

        if capital_position.sold_at < capital_position.bought_at + timedelta(days=365):
            assert (
                capital_position.sell_transaction.confirmed_at
                < capital_position.buy_transaction.confirmed_at + timedelta(days=365)
            )
            short_term_count += 1
        else:
            assert (
                capital_position.sell_transaction.confirmed_at
                >= capital_position.buy_transaction.confirmed_at + timedelta(days=365)
            )
            long_term_count += 1

    assert long_term_count == 3  # 2 USDC Long term 1 BTC Long Term
    assert short_term_count == 3  # 2 BTC Short term 1 USDC Short Term
