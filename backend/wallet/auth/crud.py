import secrets
from collections.abc import Sequence
from typing import Any
from uuid import UUID

from sqlalchemy import ColumnExpressionArgument, and_, delete, func, insert, select, update

from wallet.base import BaseManager
from wallet.kinds import CredentialUse
from wallet.models import (
    Credential,
    CredentialWrapKey,
    OngoingCredentialAuthentication,
    OngoingCredentialRegistration,
    User,
    UserSession,
)


class UserManager(BaseManager):
    def get_by_email(self, email: str) -> User | None:
        return self.db.scalars(select(User).where(User.email == email)).one_or_none()

    def get_by_id(self, user_id: int) -> User | None:
        """Get a User by their ID."""
        return self.db.scalar(select(User).where(User.id == user_id))

    def create_from_jwt_payload(self, email: str, payload: dict[str, Any]) -> User:
        """Create a User from the payload of a decoded JWT."""
        user = User(
            email=email,
            first_name=payload.get("given_name"),
            last_name=payload.get("family_name"),
            picture_url=payload.get("picture"),
        )
        self.db.add(user)
        return user


class UserSessionManager(BaseManager):
    def create(self, *, user: User, user_agent: str, ip_address: str) -> UserSession:
        session = UserSession.create(user=user, user_agent=user_agent, ip_address=ip_address)
        self.db.add(session)
        return session

    def get_by_token(self, token: str) -> UserSession | None:
        return self.db.scalar(
            select(UserSession).where(
                UserSession.token == token, UserSession.expires_at > func.now()
            )
        )


class CredentialManager(BaseManager):
    def create(
        self,
        *,
        credential_id: bytes,
        name: str,
        public_key: bytes,
        backed_up: bool,
        device_type: str,
        transports: list[str],
        sign_count: int,
        user_credentials_id: UUID,
        use_for: CredentialUse,
    ) -> None:
        self.db.execute(
            insert(Credential),
            {
                "id": credential_id,
                "name": name,
                "public_key": public_key,
                "backed_up": backed_up,
                "device_type": device_type,
                "transports": transports,
                "sign_count": sign_count,
                "user_credentials_id": user_credentials_id,
                "use_for": use_for,
            },
        )

    def get_by_id(self, credential_id: bytes) -> Credential | None:
        return self.db.scalar(select(Credential).where(Credential.id == credential_id))

    def list_by_credentials_id(
        self,
        user_credentials_id: UUID,
        *,
        use_for: CredentialUse | None,
        order_by: ColumnExpressionArgument = Credential.created_at,
    ) -> Sequence[Credential]:
        stmt = (
            select(Credential)
            .where(Credential.user_credentials_id == user_credentials_id)
            .order_by(order_by)
        )
        if use_for is not None:
            stmt = stmt.where(Credential.use_for == use_for)
        return self.db.scalars(stmt).all()

    def update_on_auth(
        self, credential_id: bytes, *, backed_up: bool, device_type: str, sign_count: int
    ) -> None:
        self.db.execute(
            update(Credential)
            .where(Credential.id == credential_id)
            .values(latest_use_at=func.now()),
            {
                "backed_up": backed_up,
                "device_type": device_type,
                "sign_count": sign_count,
            },
        )

    def get_for_user(
        self, *, credential_id: bytes, user_credentials_id: UUID
    ) -> Credential | None:
        return self.db.scalar(
            select(Credential).where(
                Credential.id == credential_id,
                Credential.user_credentials_id == user_credentials_id,
            )
        )

    def delete_for_user(self, *, credential_id: bytes, user_credentials_id: UUID) -> None:
        """Delete a credential ensuring it belongs to the specified user."""
        criteria = and_(
            Credential.id == credential_id,
            Credential.user_credentials_id == user_credentials_id,
        )
        subquery = select(Credential.id).where(criteria).scalar_subquery()
        self.db.execute(
            delete(CredentialWrapKey).where(CredentialWrapKey.credential_id == subquery)
        )
        self.db.execute(delete(Credential).where(criteria))

    def update_for_user(
        self, *, credential_id: bytes, user_credentials_id: UUID, name: str
    ) -> None:
        """Update a credential's name ensuring it belongs to the specified user."""
        self.db.execute(
            update(Credential).where(
                Credential.id == credential_id,
                Credential.user_credentials_id == user_credentials_id,
            ),
            {"name": name},
        )


class OngoingCredentialRegistrationManager(BaseManager):
    def create(self, challenge: bytes, require_user_verification: bool, user_id: int) -> None:
        # Cancel any previous ongoing registration for the user
        self.db.execute(
            delete(OngoingCredentialRegistration).where(
                OngoingCredentialRegistration.user_id == user_id
            )
        )
        self.db.execute(
            insert(OngoingCredentialRegistration),
            {
                "challenge": challenge,
                "require_user_verification": require_user_verification,
                "user_id": user_id,
            },
        )

    def get_by_user_id(self, user_id: int) -> OngoingCredentialRegistration | None:
        return self.db.scalar(
            select(OngoingCredentialRegistration).where(
                OngoingCredentialRegistration.user_id == user_id
            )
        )

    def delete(self, user_id: int) -> None:
        self.db.execute(
            delete(OngoingCredentialRegistration).where(
                OngoingCredentialRegistration.user_id == user_id
            )
        )


class OngoingCredentialAuthenticationManager(BaseManager):
    def create(self, challenge: bytes, require_user_verification: bool, user_id: int) -> None:
        # Cancel any previous ongoing authentication for the user
        self.db.execute(
            delete(OngoingCredentialAuthentication).where(
                OngoingCredentialAuthentication.user_id == user_id
            )
        )
        self.db.execute(
            insert(OngoingCredentialAuthentication),
            {
                "challenge": challenge,
                "require_user_verification": require_user_verification,
                "user_id": user_id,
            },
        )

    def get_by_user_id(self, user_id: int) -> OngoingCredentialAuthentication | None:
        return self.db.scalar(
            select(OngoingCredentialAuthentication).where(
                OngoingCredentialAuthentication.user_id == user_id
            )
        )

    def delete(self, user_id: int) -> None:
        self.db.execute(
            delete(OngoingCredentialAuthentication).where(
                OngoingCredentialAuthentication.user_id == user_id
            )
        )


class CredentialWrapKeyManager(BaseManager):
    def get_or_create_secret_key(self, credential_id: bytes) -> bytes:
        secret_key = self.db.scalar(
            select(CredentialWrapKey.secret_key).where(
                CredentialWrapKey.credential_id == credential_id
            )
        )
        if secret_key:
            return secret_key

        return self.db.scalars(
            insert(CredentialWrapKey).returning(CredentialWrapKey.secret_key),
            {"credential_id": credential_id, "secret_key": secrets.token_bytes(32)},
        ).one()
