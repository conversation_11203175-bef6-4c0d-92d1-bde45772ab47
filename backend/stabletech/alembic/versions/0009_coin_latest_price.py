"""Coin Latest Price

Revision ID: 0009
Revises: 0008
Create Date: 2022-12-17 13:24:28.427868

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0009"
down_revision = "0008"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("coin", sa.<PERSON>umn("latest_price_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "coin", "price", ["latest_price_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("coin", "latest_price_id")
    # ### end Alembic commands ###
