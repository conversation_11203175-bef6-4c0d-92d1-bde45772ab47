"""Transaction Approval

Revision ID: 0018
Revises: 0017
Create Date: 2023-04-07 03:45:49.807635

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0018"
down_revision = "0017"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Transaction(Base):
    id = Column(Integer, primary_key=True)
    approval_asset_account_id: int = Column(Integer, index=True)
    has_approval: bool = Column(Bo<PERSON>an, nullable=False, server_default="false")
    __tablename__ = "transaction"


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "transaction",
        sa.Column("has_approval", sa.<PERSON>(), server_default="false", nullable=False),
    )

    session = Session(bind=op.get_bind())
    for transaction in session.query(Transaction).all():
        if transaction.approval_asset_account_id:
            transaction.has_approval = True
    session.commit()

    op.drop_index("ix_transaction_approval_asset_account_id", table_name="transaction")
    op.drop_constraint(
        "transaction_approval_asset_account_id_fkey", "transaction", type_="foreignkey"
    )
    op.drop_column("transaction", "approval_asset_account_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "transaction",
        sa.Column(
            "approval_asset_account_id", sa.INTEGER(), autoincrement=False, nullable=True
        ),
    )
    op.create_foreign_key(
        "transaction_approval_asset_account_id_fkey",
        "transaction",
        "account",
        ["approval_asset_account_id"],
        ["id"],
    )
    op.create_index(
        "ix_transaction_approval_asset_account_id",
        "transaction",
        ["approval_asset_account_id"],
        unique=False,
    )
    op.drop_column("transaction", "has_approval")
    # ### end Alembic commands ###
