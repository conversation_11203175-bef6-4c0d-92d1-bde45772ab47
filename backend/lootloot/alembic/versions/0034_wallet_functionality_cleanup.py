"""Wallet functionality cleanup

Revision ID: 0034
Revises: 0033
Create Date: 2025-02-07 17:44:06.860348

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0034"
down_revision = "0033"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_ongoingcredentialauthentication_user_id",
        table_name="ongoingcredentialauthentication",
    )
    op.drop_table("ongoingcredentialauthentication")
    op.drop_constraint("credential_user_credentials_id_fkey", "credential")
    op.drop_index("ix_walletuser_credentials_id", table_name="walletuser")
    op.drop_index("ix_walletuser_cubist_user_id", table_name="walletuser")
    op.drop_index("ix_walletuser_email", table_name="walletuser")
    op.drop_constraint("walletsession_user_id_fkey", "walletsession")
    op.drop_constraint(
        "ongoingcredentialregistration_user_id_fkey", "ongoingcredentialregistration"
    )
    op.drop_table("walletuser")
    op.drop_index("ix_walletsession_token", table_name="walletsession")
    op.drop_index("ix_walletsession_user_id", table_name="walletsession")
    op.drop_table("walletsession")
    op.drop_index(
        "ix_ongoingcredentialregistration_user_id", table_name="ongoingcredentialregistration"
    )
    op.drop_table("ongoingcredentialregistration")
    op.drop_table("cubesignersession")
    op.drop_index("ix_credential_user_credentials_id", table_name="credential")
    op.drop_table("credential")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "credential",
        sa.Column("id", postgresql.BYTEA(), autoincrement=False, nullable=False),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("public_key", postgresql.BYTEA(), autoincrement=False, nullable=False),
        sa.Column("backed_up", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column("device_type", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "transports", postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=False
        ),
        sa.Column("sign_count", sa.BIGINT(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "latest_use_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("user_credentials_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_credentials_id"],
            ["walletuser.credentials_id"],
            name="credential_user_credentials_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="credential_pkey"),
    )
    op.create_index(
        "ix_credential_user_credentials_id", "credential", ["user_credentials_id"], unique=False
    )
    op.create_table(
        "cubesignersession",
        sa.Column("session_name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("data", postgresql.BYTEA(), autoincrement=False, nullable=False),
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id", name="cubesignersession_pkey"),
        sa.UniqueConstraint("session_name", name="cubesignersession_session_name_key"),
    )
    op.create_table(
        "ongoingcredentialregistration",
        sa.Column("challenge", postgresql.BYTEA(), autoincrement=False, nullable=False),
        sa.Column(
            "require_user_verification", sa.BOOLEAN(), autoincrement=False, nullable=False
        ),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"], ["walletuser.id"], name="ongoingcredentialregistration_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="ongoingcredentialregistration_pkey"),
    )
    op.create_index(
        "ix_ongoingcredentialregistration_user_id",
        "ongoingcredentialregistration",
        ["user_id"],
        unique=True,
    )
    op.create_table(
        "walletsession",
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("user_agent", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("ip_address", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("token", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "expires_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"], ["walletuser.id"], name="walletsession_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="walletsession_pkey"),
    )
    op.create_index("ix_walletsession_user_id", "walletsession", ["user_id"], unique=False)
    op.create_index("ix_walletsession_token", "walletsession", ["token"], unique=True)
    op.create_table(
        "walletuser",
        sa.Column("email", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("first_name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("last_name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("picture_url", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('walletuser_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column(
            "credentials_id",
            sa.UUID(),
            server_default=sa.text("gen_random_uuid()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("cubist_user_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.PrimaryKeyConstraint("id", name="walletuser_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_walletuser_email", "walletuser", ["email"], unique=True)
    op.create_index(
        "ix_walletuser_cubist_user_id", "walletuser", ["cubist_user_id"], unique=True
    )
    op.create_index(
        "ix_walletuser_credentials_id", "walletuser", ["credentials_id"], unique=True
    )
    op.create_table(
        "ongoingcredentialauthentication",
        sa.Column("challenge", postgresql.BYTEA(), autoincrement=False, nullable=False),
        sa.Column(
            "require_user_verification", sa.BOOLEAN(), autoincrement=False, nullable=False
        ),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"], ["walletuser.id"], name="ongoingcredentialauthentication_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="ongoingcredentialauthentication_pkey"),
    )
    op.create_index(
        "ix_ongoingcredentialauthentication_user_id",
        "ongoingcredentialauthentication",
        ["user_id"],
        unique=True,
    )
    # ### end Alembic commands ###
