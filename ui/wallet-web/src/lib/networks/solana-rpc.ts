import {
  BlockheightBasedTransactionConfirmationStrategy,
  Cluster,
  clusterApiUrl,
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  SystemProgram,
  Transaction,
} from "@solana/web3.js";
import { Buffer } from "buffer";

const ENVIRONMENTS: Set<string> = new Set<string>(["devnet", "mainnet"]);

export type SolanaSignFn = (message: Uint8Array) => Promise<Uint8Array>;

export type PublicKeyInput = PublicKey | ArrayBuffer | Uint8Array | string;

export interface SolanaTransfer {
  readonly amount: number;
  readonly from: PublicKeyInput;
  readonly to: PublicKeyInput;
  readonly sign: SolanaSignFn;
}

export class SolanaRPC {
  private readonly connection: Connection;

  constructor(connection: Connection) {
    this.connection = connection;
  }

  static create(network: string): SolanaRPC {
    if (!ENVIRONMENTS.has(network)) {
      throw new Error(`Unsupported environment: ${network}`);
    }
    const connection = new Connection(clusterApiUrl(network as Cluster), "confirmed");
    return new SolanaRPC(connection);
  }

  async getBalance(publicKey: PublicKeyInput): Promise<number> {
    publicKey = this.normalizePublicKey(publicKey);
    const balanceResponse = await this.connection.getBalance(publicKey, "confirmed");
    return balanceResponse / LAMPORTS_PER_SOL;
  }

  private normalizePublicKey(publicKey: PublicKeyInput): PublicKey {
    if (publicKey instanceof PublicKey) {
      return publicKey;
    }
    if (publicKey instanceof ArrayBuffer) {
      publicKey = new Uint8Array(publicKey);
    }
    return new PublicKey(publicKey);
  }

  async transfer({ amount, from, to, sign }: SolanaTransfer) {
    const amountInLamports = amount * LAMPORTS_PER_SOL;
    if (!Number.isInteger(amountInLamports)) {
      throw new TypeError("Amount has too many decimal places");
    }

    from = this.normalizePublicKey(from);
    to = this.normalizePublicKey(to);

    const recentBlockhash = await this.connection.getLatestBlockhash("confirmed");
    const transaction = new Transaction({
      feePayer: from,
      blockhash: recentBlockhash.blockhash,
      lastValidBlockHeight: recentBlockhash.lastValidBlockHeight,
    }).add(
      SystemProgram.transfer({
        fromPubkey: from,
        toPubkey: new PublicKey(to),
        lamports: amountInLamports,
      }),
    );

    const messageSignature = await sign(transaction.serializeMessage());
    transaction.addSignature(from, Buffer.from(messageSignature));

    const signature = await this.connection.sendRawTransaction(transaction.serialize());

    const confirmStrategy: BlockheightBasedTransactionConfirmationStrategy = {
      blockhash: recentBlockhash.blockhash,
      lastValidBlockHeight: recentBlockhash.lastValidBlockHeight,
      signature: signature,
    };
    // TODO: Change confirmTransaction method it has been deprecated https://solana.com/docs/rpc/deprecated/confirmtransaction
    return await this.connection.confirmTransaction(confirmStrategy, "confirmed");
  }
}
