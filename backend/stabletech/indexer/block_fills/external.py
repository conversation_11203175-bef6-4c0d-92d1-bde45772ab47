import logging
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Any, Literal

from httpx import Client
from pydantic import Field, model_validator
from sqlalchemy import select
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import Session

from common.conf import settings
from stabletech.auth.models import Role, User
from stabletech.client.models import ClientTransaction
from stabletech.indexer.block_fills.models import BlockFillsInstrument
from stabletech.indexer.crud import (
    AccountManager,
    ExchangeManager,
    NetworkManager,
    TransactionManager,
    WalletManager,
)
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Account, Network, Transaction, Transfer
from stabletech.market.crud import CoinManager
from stabletech.utils.pydantic import BaseSchema

logger = logging.getLogger(__name__)


class Asset(BaseSchema):
    asset_id: int
    asset_class: str
    description: str
    ticker: str


class Execution(BaseSchema):
    id: str
    amount: Decimal = Field(..., alias="quantity")
    at: datetime
    instrument_id: int
    price: Decimal
    side: Literal["buy", "sell"]

    @model_validator(mode="before")
    @classmethod
    def combine_date(cls, data: Any):
        data["at"] = f"{data['date']} {data['time']}+00:00"
        return data


class Instrument(BaseSchema):
    base_asset_ticker: str
    contra_asset_ticker: str | None
    description: str
    instrument_id: int


def save_assets_and_instruments(db: Session, client: Client):
    """Fetch and save all the assets and instruments BlockFills supports."""
    assets: dict[int, Asset] = {}
    instruments: list[dict[str, Any]] = []
    logger.info("Fetching instruments.")
    assets_data = client.get("assets").json()
    for asset_data in assets_data:
        asset = Asset(**asset_data)
        assets[asset.asset_id] = asset

    instruments_data = client.get("instruments").json()

    for instrument_data in instruments_data:
        try:
            instrument = Instrument(
                **instrument_data,
                base_asset_ticker=assets[instrument_data["baseAssetId"]].ticker,
                contra_asset_ticker=assets[instrument_data["contraAssetId"]].ticker,
            )
            instruments.append(instrument.model_dump())
        except KeyError:
            # Some Assets don't exist anymore.
            pass

    # Add assets and instruments ignoring any previously saved duplicates
    db.execute(insert(BlockFillsInstrument).on_conflict_do_nothing(), instruments)
    db.commit()


def get_instruments(
    db: Session, client: Client, executions: list[Execution], retry: bool = True
) -> dict[int, BlockFillsInstrument]:
    """Returns the set of BlockFillsInstruments used across all `executions` indexed by `instrument_id`."""
    instrument_ids = set(execution.instrument_id for execution in executions)
    instruments = db.scalars(
        select(BlockFillsInstrument).where(
            BlockFillsInstrument.instrument_id.in_(instrument_ids)
        )
    ).all()
    if len(instrument_ids) != len(instruments):
        if retry:
            save_assets_and_instruments(db, client)
            return get_instruments(db, client, executions, False)
        else:
            raise Exception("Could not find all the executions' instruments.")
    return {instrument.instrument_id: instrument for instrument in instruments}


def sync_block_fills_executions(db: Session):
    """
    Saves the latest executions from the BlockFills API.
    Currently only supported on Apollo for a single client.
    """
    if not settings.block_fills_api_key:
        logger.info("No API key found.")
        return

    client: Client = Client(base_url="https://visiontrader.blockfills.com/api/v2/restapi/")
    network: Network = NetworkManager(db).get_by_name("BlockFills")
    try:
        # Currently expects a single client User.
        client_user = db.scalars(select(User).where(User.role != Role.admin.value)).one()
    except NoResultFound as error:
        if settings.is_local:
            # Use first User for debugging when running locally
            client_user = db.scalars(select(User)).first()
        else:
            raise error

    auth_token: str = client.post(
        "login", json={"API_login_key": settings.block_fills_api_key}
    ).json()["token"]

    logger.info("Fetching executions.")
    wallet = WalletManager(db).get_or_create_by_address(
        network.name, name=network.name, networks=[network], commit=False
    )
    latest_transaction: Transaction | None = (
        TransactionManager(db).get_latest(network, wallet) if wallet.id else None
    )
    from_at: date | datetime = (
        latest_transaction.confirmed_at
        if latest_transaction
        else date(year=2024, month=2, day=1)
    )
    client.headers = {"Authorization": auth_token}
    executions_data = client.get(
        f"executions?fromDateTime={from_at}&toDateTime={datetime.now().date() + timedelta(days=1)}",
    ).json()["items"]
    if latest_transaction and executions_data:
        # Remove latest transaction that's always returned as it's the starting time filter.
        executions_data.pop(0)
    executions = [Execution(**execution_data) for execution_data in executions_data]
    instruments: dict[int, BlockFillsInstrument] = get_instruments(db, client, executions)

    account_manager = AccountManager(db)
    accounts: dict[str, Account] = {}
    coin_manager = CoinManager(db)

    block_fills_exchange = ExchangeManager(db).get_or_create_by_name(
        network.name, url="https://visiontrader.blockfills.com", commit=False
    )
    block_fills_account = account_manager.get_or_create_with_network_id(
        f"{network.name}Exchange", network=network, exchange=block_fills_exchange, commit=False
    )
    wallet_account = account_manager.get_or_create_with_network_id(
        wallet.address, network.id, wallet=wallet, commit=False
    )

    for execution in executions:
        # Create the Transaction, ClientTransaction, and Transfers.
        instrument = instruments[execution.instrument_id]
        if not instrument.contra_asset_ticker:
            logger.error(f"Execution {execution.id} has no contra_asset_id - ignoring.")
            continue

        if execution.side == "buy":
            tx_hash = f"{instrument.base_asset_ticker}-{instrument.contra_asset_ticker}"
        else:
            tx_hash = f"{instrument.contra_asset_ticker}-{instrument.base_asset_ticker}"
        tx_hash += f"-{execution.at.timestamp()}"
        transaction = Transaction(
            action=str(TransactionAction.swap.value),
            confirmed_at=execution.at,
            json_data=execution.model_dump_json(),
            network=network,
            signer_account=wallet_account,
            tx_hash=tx_hash,
            wallet=wallet,
        )
        db.add(transaction)
        for ticker, is_base in (
            (instrument.base_asset_ticker, True),
            (instrument.contra_asset_ticker, False),
        ):
            asset_account = accounts.get(ticker)
            if not asset_account:
                coin = coin_manager.get_coin_by_ticker_or_fail(ticker)
                asset_account = accounts[ticker] = (
                    account_manager.get_or_create_with_network_id(
                        address=ticker, network=network, coin=coin, commit=False
                    )
                )
            if is_base:
                # Asset transfer being bought/sold
                amount = execution.amount
                price = execution.price
                db.add(
                    ClientTransaction(
                        amount=amount,
                        coin=asset_account.coin,
                        network=network,
                        reviewed_at=datetime.now(),
                        transaction=transaction,
                        user=client_user,
                    )
                )
            else:
                # USD transfer
                amount = execution.amount * execution.price
                price = 1

            if execution.side == "buy" and not is_base or execution.side == "sell" and is_base:
                from_account = wallet_account
                to_account = block_fills_account
            else:
                from_account = block_fills_account
                to_account = wallet_account
            db.add(
                Transfer(
                    amount=amount,
                    asset_account=asset_account,
                    asset_price_usd=price,
                    from_account=from_account,
                    to_account=to_account,
                    transaction=transaction,
                )
            )
    db.commit()
