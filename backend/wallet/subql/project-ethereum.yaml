# // Auto-generated , DO NOT EDIT
specVersion: 1.0.0
version: 0.0.1
name: ethereum-sepolia-starter
description: >-
  This project can be use as a starting point for developing your new Ethereum
  Sepolia SubQuery project
runner:
  node:
    name: '@subql/node-ethereum'
    version: '>=3.0.0'
  query:
    name: '@subql/query'
    version: '*'
schema:
  file: ./schema.graphql
network:
  chainId: '1'
  endpoint:
    - 'https://ethereum.rpc.subquery.network/public'
dataSources:
  - kind: ethereum/Runtime
    startBlock: 21782000
    endBlock: 21782001
    options:
      abi: erc20
    assets:
      erc20:
        file: ./abis/erc20.abi.json
    mapping:
      file: ./dist/index.js
      handlers:
        - kind: ethereum/LogHandler
          handler: handleEthereumLog
          filter:
            topics:
              - 'Transfer(address,address,uint256)'
repository: 'https://github.com/subquery/ethereum-subql-starter'
