variable "api_domain" {
  description = "Domain to point the ingress to"
}

variable "docker_repository" {
  description = "Fully qualified docker repository name where images are stored"
  type        = string
  default     = "us-west1-docker.pkg.dev/temet-359508/temet"
  nullable    = false
}

variable "env_name" {
  description = "Environment name"
}

variable "managed_zone_name" {
  description = "Manged Zone Name"
}

variable "namespace" {
  description = "Kubernetes namespace to use"
}

variable "postgresql_db" {
  description = "PostgreSQL database name"
  default     = "temet"
}

variable "release" {
  description = "Current version of the code, usually the git short commit SHA"
  type        = string
  nullable    = false
}

variable "secrets" {
  type = object({
    bitquery_api_key     = optional(string)
    block_fills_api_key  = optional(string)
    covalent_api_key     = string
    helius_api_key       = string
    internal_sync_secret = optional(string)
    mintscan_api_key     = string
    postgresql_host      = string
    postgresql_password  = string
    postgresql_user      = string
    slack_bot_token      = string
    slack_signing_secret = string
    sendgrid_api_key     = string
  })
  nullable  = false
  sensitive = true
}

variable "api_requests" {
  description = "CPU and Memory requests for the api containers"
  type = object({
    cpu    = string
    memory = string
  })
  default  = null
  nullable = true
}
