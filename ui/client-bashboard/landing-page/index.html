<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./assets/stable-tech-shield.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="./styles.css" rel="stylesheet" />
    <title>Stable Tech</title>
  </head>
  <body class="font-sans">
    <!-- Hero Section with gradient background -->
    <div class="relative bg-gradient-to-br from-blue-200 via-blue-100 to-cyan-50">
      <!-- Curvy background shapes -->
      <div class="absolute right-0 bottom-0 left-0 h-80 overflow-hidden">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
          class="wave-slow absolute bottom-0 w-[120%] text-blue-200 opacity-30"
        >
          <path
            fill="currentColor"
            d="M0,192L48,186.7C96,181,192,171,288,181.3C384,192,480,224,576,229.3C672,235,768,213,864,192C960,171,1056,149,1152,160C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
          class="wave-slower absolute bottom-0 w-[120%] text-blue-100 opacity-50"
        >
          <path
            fill="currentColor"
            d="M0,128L60,149.3C120,171,240,213,360,213.3C480,213,600,171,720,165.3C840,160,960,192,1080,197.3C1200,203,1320,181,1380,170.7L1440,160L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z"
          ></path>
        </svg>
      </div>

      <!-- Header with logo and sign up button -->
      <header class="relative z-10 bg-white shadow-xs">
        <div class="cx flex h-16 items-center justify-between bg-white py-2.5 xs:py-2">
          <a href="/" class="fade-in -my-3 flex items-center gap-1 xs:gap-1.5 sm:gap-2.5">
            <img
              src="./assets/stable-tech-shield.svg"
              alt=""
              class="h-9 w-10 xs:h-12 xs:w-13"
            />
            <h1 class="text-md font-semibold text-blue-600 xs:text-xl md:text-2xl">
              Stable Tech
            </h1>
          </a>
          <div class="fade-in flex items-center delay-100 sm:gap-2">
            <!-- Mobile menu button (visible on small screens) -->
            <div class="relative sm:hidden">
              <button id="mobile-menu-button" aria-expanded="false" aria-haspopup="true">
                <span class="sr-only">Open menu</span>
                <i data-lucide="menu" class="size-5"></i>
              </button>
              <!-- Dropdown menu - positioned absolutely -->
              <div
                id="mobile-dropdown"
                class="hidden"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="mobile-menu-button"
              >
                <a href="/" role="menuitem">Home</a>
                <a href="about.html" role="menuitem">About Us</a>
                <a href="/signin/" role="menuitem">Sign in</a>
                <a href="#getintouch" role="menuitem">Sign up</a>
              </div>
            </div>
            <!-- Desktop navigation (hidden on small screens) -->
            <a
              href="about.html"
              class="hidden rounded-full px-2 py-2 text-sm font-medium text-blue-600 transition hover:text-blue-700 sm:inline-block"
              >About Us</a
            >
            <a
              href="/signin/"
              class="hidden rounded-full bg-gray-100 px-5 py-2 text-sm font-medium transition hover:bg-gray-200 sm:inline-block"
              >Sign in</a
            >
            <a
              href="index.html#getintouch"
              class="hidden rounded-full bg-blue-600 px-5 py-2 text-sm font-medium text-white transition hover:bg-blue-700 sm:inline-block"
              >Sign up</a
            >
          </div>
        </div>
      </header>

      <!-- Hero content -->
      <div class="cx pt-10 pb-20">
        <!-- Tagline -->
        <div class="fade-in mb-6 text-center delay-100">
          <h2 class="font-medium text-gray-600 md:text-lg">Stable. Secure. Convenient.</h2>
        </div>

        <!-- Main headline -->
        <div class="fade-in mb-8 text-center delay-200 md:mb-10">
          <h2
            class="text-4xl leading-tight font-bold text-balance text-gray-900 md:text-5xl lg:text-6xl"
          >
            Get <span class="text-blue-600">7%</span> Annually on your USD
          </h2>
        </div>

        <!-- Subheadlines -->
        <div class="fade-in mb-10 space-y-4 text-center delay-300">
          <p class="text-gray-600 md:text-lg">Generate Yield from Stablecoin Trading Fees.</p>
          <p class="text-gray-600 md:text-lg">4+ Years of Operation and Client Payments</p>
          <p class="text-gray-600 md:text-lg">
            Available exclusively to
            <span class="font-medium">U.S. accredited investors</span>.
          </p>
        </div>

        <!-- CTA Button -->
        <div class="fade-in text-center delay-400">
          <a
            href="#getintouch"
            class="inline-block rounded-full bg-blue-600 px-10 py-3 text-lg font-medium text-white shadow-lg transition hover:bg-blue-700 md:px-12 md:py-4 md:text-xl"
          >
            Start Earning Today
          </a>
        </div>
      </div>
    </div>

    <!-- Main content with white background -->
    <!-- Security First Section -->
    <section class="fade-in cx my-16">
      <div class="mb-10 flex justify-center gap-3 sm:mb-12">
        <h2
          class="relative bg-gradient-to-r from-blue-600 from-5% to-blue-950 bg-clip-text text-2xl leading-tight font-semibold text-transparent sm:text-3xl"
        >
          Security First
          <i
            data-lucide="fingerprint"
            class="absolute -top-2.5 -left-15 size-12 text-blue-600 sm:-top-1.5 sm:-left-16 sm:size-13"
          ></i>
        </h2>
      </div>

      <div class="rounded-xl bg-blue-600/5 px-4 py-6 shadow-md sm:mx-auto sm:max-w-lg sm:p-8">
        <ul class="space-y-5 sm:space-y-7">
          <!-- Stablecoin-Only Strategy -->
          <li class="flex items-center">
            <div class="mr-3 sm:mr-5">
              <i data-lucide-thick="shield" class="size-5 text-blue-600 sm:size-7"></i>
            </div>
            <span class="text-gray-900 sm:text-lg">Stablecoin-Only Strategy</span>
          </li>

          <!-- No Volatile Crypto Assets -->
          <li class="flex items-center">
            <div class="mr-3 sm:mr-5">
              <i data-lucide-thick="ban" class="size-5 text-blue-600 sm:size-7"></i>
            </div>
            <span class="text-gray-900 sm:text-lg">No Volatile Crypto Assets</span>
          </li>

          <!-- 24/7 Market Monitoring -->
          <li class="flex items-center">
            <div class="mr-3 sm:mr-5">
              <i data-lucide-thick="monitor" class="size-5 text-blue-600 sm:size-7"></i>
            </div>
            <span class="text-gray-900 sm:text-lg">24/7 Market Monitoring</span>
          </li>

          <!-- Funds Secured in Hardware Wallets -->
          <li class="flex items-center">
            <div class="mr-3 sm:mr-5">
              <i data-lucide-thick="lock" class="size-5 text-blue-600 sm:size-7"></i>
            </div>
            <span class="text-gray-900 sm:text-lg">Funds Secured in Hardware Wallets</span>
          </li>
        </ul>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="fade-in cx my-16">
      <div class="mb-10 flex justify-center gap-3 pt-2 sm:mb-12">
        <h2
          class="relative bg-gradient-to-r from-blue-950 to-blue-600 to-95% bg-clip-text text-2xl leading-tight font-semibold text-transparent sm:text-3xl"
        >
          How It Works
          <img
            src="./assets/light-bulb-and-gears.svg"
            alt=""
            class="absolute -top-3.5 -left-16 size-14 sm:-top-4 sm:-left-17 sm:size-16"
          />
        </h2>
      </div>

      <div
        class="space-y-12 rounded-xl bg-blue-600/5 px-4 py-6 shadow-md sm:mx-auto sm:max-w-lg sm:p-8"
      >
        <!-- Step 1: Deposit USD -->
        <div class="flex">
          <div class="mr-4 sm:mr-6">
            <div class="flex h-12 w-12 items-center justify-center rounded-full">
              <i data-lucide="wallet" class="size-6 text-blue-600 sm:size-8"></i>
            </div>
          </div>
          <div>
            <h3 class="mb-2 text-lg leading-none font-medium text-gray-900 sm:text-xl">
              1. Deposit USD
            </h3>
            <p class="text-base text-pretty text-gray-600 sm:text-lg">
              Securely deposit your USD through our platform. Minimum deposit of $5,000. For
              U.S. accredited investors only.
            </p>
          </div>
        </div>

        <!-- Step 2: Market Making -->
        <div class="flex">
          <div class="mr-4 sm:mr-6">
            <div class="flex h-12 w-12 items-center justify-center rounded-full">
              <i data-lucide="refresh-cw" class="size-6 text-blue-600 sm:size-8"></i>
            </div>
          </div>
          <div>
            <h3 class="mb-2 text-lg leading-none font-medium text-gray-900 sm:text-xl">
              2. Market Making
            </h3>
            <p class="text-base text-pretty text-gray-600 sm:text-lg">
              Your funds are swapped into Stablecoins and earn trading fees while deposited.
            </p>
          </div>
        </div>

        <!-- Step 3: Market Neutral -->
        <div class="flex">
          <div class="mr-4 sm:mr-6">
            <div class="flex h-12 w-12 items-center justify-center rounded-full">
              <i data-lucide="scale" class="size-6 text-blue-600 sm:size-8"></i>
            </div>
          </div>
          <div>
            <h3 class="mb-2 text-lg leading-none font-medium text-gray-900 sm:text-xl">
              3. Market Neutral
            </h3>
            <p class="text-base text-pretty text-gray-600 sm:text-lg">
              Stablecoins retain value while generating higher returns than traditional savings
              accounts.
            </p>
          </div>
        </div>

        <!-- Step 4: Earn Returns -->
        <div class="flex">
          <div class="mr-4 sm:mr-6">
            <div class="flex h-12 w-12 items-center justify-center rounded-full">
              <i data-lucide="chart-line" class="size-6 text-blue-600 sm:size-8"></i>
            </div>
          </div>
          <div>
            <h3 class="mb-2 text-lg leading-none font-medium text-gray-900 sm:text-xl">
              4. Earn Returns
            </h3>
            <p class="text-base text-pretty text-gray-600 sm:text-lg">
              Receive <span class="font-bold text-blue-600">7%</span> annual returns, paid out
              as yearly interest payments or upon withdrawal.
            </p>
          </div>
        </div>

        <!-- Step 5: Flexible Withdrawals -->
        <div class="flex">
          <div class="mr-4 sm:mr-6">
            <div class="flex size-12 items-center justify-center rounded-full">
              <i data-lucide="banknote-arrow-down" class="size-6 text-blue-600 sm:size-8"></i>
            </div>
          </div>
          <div>
            <h3 class="mb-2 text-lg leading-none font-medium text-gray-900 sm:text-xl">
              5. Flexible Withdrawals
            </h3>
            <p class="text-base text-pretty text-gray-600 sm:text-lg">
              Withdraw your funds anytime with no lock-up periods. Complete flexibility and
              control over your investment.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Calculate Your Returns Section -->
    <section class="relative">
      <!-- Background image -->
      <img
        src="./assets/building-glass-2896w.jpg"
        alt=""
        srcset="
          ./assets/building-glass-640w.jpg   640w,
          ./assets/building-glass-1920w.jpg 1920w,
          ./assets/building-glass-2896w.jpg 2896w
        "
        class="absolute -z-1 h-full w-full object-cover"
      />

      <div class="cx py-16">
        <h2
          class="fade-in mb-8 text-center text-2xl leading-none font-semibold text-white text-shadow-lg sm:mb-10 sm:text-3xl"
        >
          Calculate Your Returns
        </h2>

        <!-- Calculator Form -->
        <div
          class="mx-auto max-w-3xl overflow-hidden rounded-xl bg-gray-50/85 shadow-md backdrop-blur-md"
        >
          <div class="grid grid-cols-1 gap-4 px-4 pt-6 sm:px-8 sm:pt-8 md:grid-cols-2">
            <!-- Investment Amount -->
            <div>
              <label
                for="investmentAmount"
                class="mb-2 block text-lg font-medium text-gray-800"
              >
                Initial Deposit
              </label>
              <div class="relative">
                <div
                  class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4"
                >
                  <span class="text-lg font-medium text-gray-500">$</span>
                </div>
                <input
                  type="text"
                  id="investmentAmount"
                  value="10,000"
                  class="block w-full rounded-lg border border-gray-300 bg-white py-3 pr-3 pl-10 text-lg transition focus:border-blue-600 focus:ring-2 focus:ring-blue-600 focus:outline-none"
                />
              </div>
            </div>

            <!-- Monthly Contribution -->
            <div>
              <label
                for="monthlyContribution"
                class="mb-2 block text-lg font-medium text-gray-800"
              >
                Monthly Contribution
              </label>
              <div class="relative">
                <div
                  class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4"
                >
                  <span class="text-lg font-medium text-gray-500">$</span>
                </div>
                <input
                  type="text"
                  id="monthlyContribution"
                  value="500"
                  class="block w-full rounded-lg border border-gray-300 bg-white py-3 pr-3 pl-10 text-lg transition focus:border-blue-600 focus:ring-2 focus:ring-blue-600 focus:outline-none"
                />
              </div>
            </div>

            <!-- Deposit Length -->
            <div>
              <label for="depositLength" class="mb-2 block text-lg font-medium text-gray-800">
                Deposit Length
              </label>
              <div class="relative">
                <select
                  id="depositLength"
                  class="block w-full appearance-none rounded-lg border border-gray-300 bg-white px-4 py-3 pr-10 text-lg transition focus:border-blue-600 focus:ring-2 focus:ring-blue-600 focus:outline-none"
                >
                  <option value="3">3 Years</option>
                  <option value="5" selected>5 Years</option>
                  <option value="10">10 Years</option>
                  <option value="15">15 Years</option>
                  <option value="20">20 Years</option>
                </select>
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-4"
                >
                  <i data-lucide="chevron-down" class="size-5 text-gray-500"></i>
                </div>
              </div>
            </div>

            <!-- Compound Interest Toggle -->
            <div class="flex flex-col">
              <div class="mb-2 flex items-center">
                <label
                  for="compoundInterest"
                  class="mr-2 block text-lg font-medium text-gray-800"
                >
                  Compound Interest
                </label>
                <div class="group relative">
                  <button
                    type="button"
                    class="peer flex items-center text-gray-600 focus:text-blue-600 focus:outline-none"
                    aria-label="Toggle tooltip"
                  >
                    <i data-lucide="info" class="size-5"></i>
                  </button>
                  <div
                    class="invisible absolute left-1/2 z-10 mt-2 w-40 -translate-x-1/2 transform rounded-md bg-white p-3 text-sm text-gray-700 opacity-0 shadow-lg transition group-hover:visible group-hover:opacity-100 peer-focus:visible peer-focus:opacity-100"
                  >
                    Reinvest the interest payments each year
                  </div>
                </div>
              </div>
              <div class="flex flex-auto items-center">
                <label class="relative inline-flex cursor-pointer items-center">
                  <input type="checkbox" id="compoundInterest" class="peer sr-only" />
                  <div
                    class="h-6 w-11 rounded-full bg-gray-300 transition-colors peer-checked:bg-blue-600 peer-focus:ring-2 peer-focus:ring-blue-600 peer-checked:peer-focus:ring-sky-400"
                  ></div>
                  <div
                    class="absolute inset-y-1 left-0.5 h-5 w-5 transform rounded-full bg-white shadow-md transition-transform peer-checked:translate-x-5"
                  ></div>
                  <span class="ml-2 text-lg text-gray-800 peer-checked:hidden">Off</span>
                  <span class="ml-2 hidden text-lg text-gray-800 peer-checked:inline">On</span>
                </label>
              </div>
            </div>

            <!-- Chart Container -->
            <div
              class="col-span-full -mx-4 mt-2 border-t border-gray-300 px-2 py-6 xs:px-4 sm:-mx-8 md:px-8"
            >
              <div class="mb-2 flex items-center justify-between">
                <div class="flex space-x-6">
                  <div class="flex items-center">
                    <span class="mr-2 inline-block h-4 w-4 rounded-sm bg-gray-400"></span>
                    <span class="text-sm text-gray-600">Principal + Contributions</span>
                  </div>
                  <div class="flex items-center">
                    <span class="mr-2 inline-block h-4 w-4 rounded-sm bg-blue-600"></span>
                    <span class="text-sm text-gray-600">Total with Returns</span>
                  </div>
                </div>
              </div>
              <div class="relative h-64 md:h-80">
                <canvas id="investmentChart"></canvas>
              </div>
            </div>
          </div>

          <!-- Results Summary -->
          <div class="border-t border-gray-300">
            <div
              class="grid grid-cols-1 divide-y divide-gray-300 md:grid-cols-3 md:divide-x md:divide-y-0"
            >
              <!-- Deposit without interest -->
              <div class="px-4 py-6 text-center sm:px-8 md:pb-8">
                <h3
                  class="flex items-center justify-center text-sm font-medium tracking-wider text-gray-600 uppercase md:h-10"
                >
                  Deposit without interest
                </h3>
                <p id="depositWithoutInterest" class="mt-2 text-3xl font-bold text-gray-900">
                  $50,000
                </p>
              </div>

              <!-- Interest income -->
              <div class="px-4 py-6 text-center sm:px-8 md:pb-8">
                <h3
                  class="flex items-center justify-center text-sm font-medium tracking-wider text-gray-600 uppercase md:h-10"
                >
                  Interest income
                </h3>
                <p id="interestIncome" class="mt-2 text-3xl font-bold text-blue-600">$13,692</p>
              </div>

              <!-- Total with interest -->
              <div class="px-4 py-6 text-center sm:px-8 sm:pb-8">
                <h3
                  class="flex items-center justify-center text-sm font-medium tracking-wider text-gray-600 uppercase md:h-10"
                >
                  Total with interest
                </h3>
                <p id="totalWithInterest" class="mt-2 text-3xl font-bold text-gray-900">
                  $63,692
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- U.S. Accredited Investor Section -->
    <div class="bg-blue-600/5">
      <section id="getintouch" class="fade-in cx pt-16 pb-25">
        <h2
          class="mb-10 text-center text-2xl leading-none font-semibold text-gray-900 sm:mb-14 sm:text-3xl"
        >
          U.S. Accredited Investor?
        </h2>

        <div class="mx-auto max-w-3xl space-y-4 text-center">
          <!-- Subtitle -->
          <h3 class="text-xl font-medium text-gray-700 sm:text-2xl">Get Started today!</h3>

          <!-- Description -->
          <p class="mb-8 text-lg text-gray-500 sm:text-xl">
            Our team will be in touch to outline your next steps.
          </p>

          <!-- Actual form with display toggle -->
          <form id="contact-form" style="display: var(--form-display, block)">
            <!-- Email Input -->
            <div class="w-full">
              <input
                type="email"
                name="investorEmail"
                required
                placeholder="Enter your email"
                class="w-full max-w-lg rounded-lg border border-gray-300 bg-white p-4 text-xl transition focus:border-blue-600 focus:ring-2 focus:ring-blue-600 focus:outline-none"
              />
            </div>

            <div class="my-8">
              <!-- Get in Touch Button -->
              <button
                type="submit"
                class="w-48 rounded-full bg-blue-600 px-10 py-3 text-lg font-medium text-white shadow-lg transition hover:cursor-pointer hover:bg-blue-700 sm:w-56 sm:px-12 sm:py-4 sm:text-xl"
              >
                Get in Touch
              </button>
            </div>
          </form>

          <!-- Success message -->
          <div
            id="success-message"
            class="mt-4 mb-8 text-center"
            style="display: var(--success-message-display, none)"
          >
            <h3 class="mb-4 text-xl font-medium text-green-600 sm:text-2xl">Thank you!</h3>
            <p class="mb-6 text-gray-600">
              We've received your request and our team will contact you shortly.
            </p>
            <button
              id="submit-another-button"
              class="rounded-full bg-blue-600 px-6 py-2 text-base font-medium text-white shadow-lg transition hover:cursor-pointer hover:bg-blue-700"
            >
              Submit another email
            </button>
          </div>

          <!-- Additional Info - remains visible in both states -->
          <p class="mb-4 text-lg text-gray-600 sm:mt-4 sm:mb-6 sm:text-xl">
            Need more information?
          </p>

          <a
            href="https://calendly.com/stable-tech/intro"
            target="_blank"
            rel="noopener"
            class="text-lg font-medium text-blue-600 hover:underline sm:text-xl"
          >
            Schedule a meeting with Calendly
          </a>
        </div>
      </section>
    </div>

    <script type="module" src="./main.mjs"></script>
    <script type="module" src="./investment-chart.mjs"></script>
  </body>
</html>
