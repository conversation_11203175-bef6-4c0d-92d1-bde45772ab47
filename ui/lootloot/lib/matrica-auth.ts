import { apiMutation } from "@/lib/api";
import { useLogIn } from "@/lib/auth-ctx";
import { LoginResponse, MatricaLogin } from "@/types/schemas";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useRef } from "react";
import { assert, nonempty, object, string } from "superstruct";

interface RouterLike {
  readonly push: (path: string) => void;
}

export function useLoginWithMatrica() {
  return useMutation({ mutationFn: loginWithMatrica });
}

export function useMatricaCallback() {
  const logIn = useLogIn();
  const logInRef = useRef(logIn);
  logInRef.current = logIn;

  // Uses a query because it is run on page load, but this will do a mutation
  // on the server (POST auth/matrica-login).
  return useQuery({
    queryKey: ["auth", "matrica-login"],
    queryFn: async () => {
      const resp = await matricaCallback();
      logInRef.current(resp);
      return null;
    },
    retry: false,
    staleTime: 5000, // prevents re-fetching
  });
}

export class MatricaError extends Error {
  readonly code: string;
  constructor(code: string, message: string, options?: ErrorOptions) {
    super(message, options);
    this.name = "MatricaError";
    this.code = code;
  }
}

const LoginState = object({ id: nonempty(string()) });
type LoginState = typeof LoginState.TYPE;

const StoredLogin = object({ id: nonempty(string()), codeVerifier: nonempty(string()) });
type StoredLogin = typeof StoredLogin.TYPE;

const MATRICA_LOGIN_KEY = "auth.matrica.login";

async function loginWithMatrica(router: RouterLike): Promise<void> {
  const codeVerifier = randomString(96);
  const codeChallenge = base64UrlEncodeBuffer(await sha256String(codeVerifier));

  const id = randomString(32);
  const state = JSON.stringify({ id } satisfies LoginState);

  const redirectUri = getRedirectUri();

  const url = new URL("https://matrica.io/oauth2");
  url.searchParams.set("client_id", process.env.MATRICA_CLIENT_ID);
  url.searchParams.set("response_type", "code");
  url.searchParams.set("redirect_uri", redirectUri);
  url.searchParams.set("scope", "profile wallets");
  url.searchParams.set("code_challenge", codeChallenge);
  url.searchParams.set("code_challenge_method", "S256");
  url.searchParams.set("state", state);

  localStorage.setItem(
    MATRICA_LOGIN_KEY,
    JSON.stringify({ id, codeVerifier } satisfies StoredLogin),
  );

  router.push(url.toString());
}

async function matricaCallback(): Promise<LoginResponse> {
  const searchParams = new URLSearchParams(location.search);
  const error = searchParams.get("error");
  if (error != null) {
    throw new MatricaError(error, `Authentication failed with an error: ${error}`);
  }

  const state = validateLoginState(searchParams.get("state"));
  const code = searchParams.get("code");
  if (!code) {
    throw new MatricaError("code_missing", "Missing code");
  }

  const stored = validateStoredLogin(localStorage.getItem(MATRICA_LOGIN_KEY));
  if (stored.id !== state.id) {
    throw new MatricaError("state_mismatch", "Unexpected state");
  }

  const redirectUri = getRedirectUri();

  try {
    return await matricaAuthentication({
      code,
      redirectUri,
      codeVerifier: stored.codeVerifier,
    });
  } finally {
    localStorage.removeItem(MATRICA_LOGIN_KEY);
  }
}

async function matricaAuthentication({
  code,
  redirectUri,
  codeVerifier,
}: {
  code: string;
  redirectUri: string;
  codeVerifier: string;
}) {
  return apiMutation<LoginResponse>({
    method: "POST",
    resource: "auth/matrica-login",
    body: {
      code,
      redirect_uri: redirectUri,
      code_verifier: codeVerifier,
    } satisfies MatricaLogin,
  });
}

// Note that all characters must be valid for the PKCE flow
const CHARSET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";

function randomString(length: number): string {
  const bytes = crypto.getRandomValues(new Uint8Array(length));
  let str = "";
  for (const b of bytes) {
    str += CHARSET[b % CHARSET.length]!; // eslint-disable-line @typescript-eslint/no-non-null-assertion
  }
  return str;
}

async function sha256String(str: string): Promise<ArrayBuffer> {
  return crypto.subtle.digest("SHA-256", new TextEncoder().encode(str));
}

function base64UrlEncodeBuffer(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  const base64Encoded = btoa(String.fromCodePoint(...bytes));
  const base64UrlEncoded = base64Encoded
    .replace(/=+$/u, "")
    .replaceAll("+", "-")
    .replaceAll("/", "_");
  return base64UrlEncoded;
}

function getRedirectUri() {
  return location.origin + "/auth/matrica-callback";
}

function validateLoginState(state: string | null): LoginState {
  if (!state) {
    throw new MatricaError("state_missing", "Missing state");
  }
  try {
    const parsed: unknown = JSON.parse(state);
    assert(parsed, LoginState);
    return parsed;
  } catch (error) {
    throw new MatricaError("state_invalid", "Invalid state", { cause: error });
  }
}

function validateStoredLogin(stored: string | null): StoredLogin {
  if (!stored) {
    throw new MatricaError("stored_missing", "Missing local state");
  }
  try {
    const parsed: unknown = JSON.parse(stored);
    assert(parsed, StoredLogin);
    return parsed;
  } catch (error) {
    throw new MatricaError("stored_invalid", "Invalid local state", { cause: error });
  }
}
