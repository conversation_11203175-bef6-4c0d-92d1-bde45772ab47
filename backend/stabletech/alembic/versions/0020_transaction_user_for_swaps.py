"""Transaction User for swaps

Revision ID: 0020
Revises: 0019
Create Date: 2023-04-12 12:05:15.058725

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0020"
down_revision = "0019"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("transaction", sa.Column("swap_user_id", sa.Integer(), nullable=True))
    op.drop_constraint("transaction_tx_hash_key", "transaction", type_="unique")
    op.create_index(
        op.f("ix_transaction_swap_user_id"), "transaction", ["swap_user_id"], unique=False
    )
    op.create_unique_constraint(None, "transaction", ["network_id", "tx_hash"])
    op.create_foreign_key(None, "transaction", "user", ["swap_user_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_transaction_swap_user_id"), table_name="transaction")
    op.create_unique_constraint("transaction_tx_hash_key", "transaction", ["tx_hash"])
    op.drop_column("transaction", "swap_user_id")
    # ### end Alembic commands ###
