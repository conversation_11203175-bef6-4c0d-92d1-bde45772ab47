"""Drop primary keys non unique indexes

Revision ID: 0027
Revises: 0026
Create Date: 2024-06-11 15:24:47.917715

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0027"
down_revision = "0026"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_fungibleinventory_id", table_name="fungibleinventory")
    op.drop_index("ix_fungibletransfer_id", table_name="fungibletransfer")
    op.drop_index("ix_nonfungibleinventory_id", table_name="nonfungibleinventory")
    op.drop_index("ix_nonfungibletransfer_id", table_name="nonfungibletransfer")
    op.drop_index("ix_raffle_id", table_name="raffle")
    op.drop_index("ix_raffleentry_id", table_name="raffleentry")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_raffleentry_id", "raffleentry", ["id"], unique=False)
    op.create_index("ix_raffle_id", "raffle", ["id"], unique=False)
    op.create_index("ix_nonfungibletransfer_id", "nonfungibletransfer", ["id"], unique=False)
    op.create_index("ix_nonfungibleinventory_id", "nonfungibleinventory", ["id"], unique=False)
    op.create_index("ix_fungibletransfer_id", "fungibletransfer", ["id"], unique=False)
    op.create_index("ix_fungibleinventory_id", "fungibleinventory", ["id"], unique=False)
    # ### end Alembic commands ###
