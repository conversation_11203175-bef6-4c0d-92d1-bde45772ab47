// Env flags
export const isApollo = process.env.SITE_URL === "https://apollo.stabletech.capital";
export const isDiwan = process.env.SITE_URL === "https://diwan.digital";
export const isLp = process.env.SITE_URL === "https://lp.stabletech.capital";
export const isSahaba = process.env.SITE_URL === "https://sahaba.me";
export const isLocal = process.env.NEXT_PUBLIC_ENV_NAME === "local";
export const isProduction = process.env.NEXT_PUBLIC_ENV_NAME === "production";
export const isStableTech = process.env.SITE_URL === "https://stabletech.capital";

export const newPagesEnabled = process.env.NEXT_PUBLIC_NEW_PAGES_ENABLED === "true";

export const disableUserAgreement = isApollo || isDiwan || isLp || isSahaba;
export const displayTxHash = !(isApollo || isDiwan || isLp || isSahaba);
export const enableDashboardAdminUser = isDiwan || isLp || isSahaba;
export const enableDummyAddTransactions = !isProduction;
export const enablePasswordLogin = !isProduction;
export const enablePooledFund = isDiwan || isLp;
export const hasEarnProduct = !(isApollo || isDiwan || isLp);

export const colors = getColors();

function getColors() {
  if (isApollo)
    return {
      bgDark: "#212829",
      bgLight: "#f5faf9",
      darkColor: "240, 252, 252",
      lightColor: "32, 38, 37",
      paperBgDark: "#212e2e",
      primaryContrastTextDark: "#FFF",
      primaryContrastTextLight: "#FFF",
      primaryDarkDark: "#2FB1A0",
      primaryDarkLight: "#2FB1A0",
      primaryGradientDark: "#255953",
      primaryGradientLight: "#49bfaf",
      primaryMainDark: "#41ccba",
      primaryMainLight: "#0e7568",
      secondaryMainDark: "#8A8D93",
      secondaryMainLight: "#8A8D93",
      tableHeaderBgDark: "#2d403d",
      textPrimaryAlpha: "0.95",
    };
  if (isDiwan)
    return {
      bgDark: "#1f0d0d",
      bgLight: "#FBFAFA",
      darkColor: "243, 239, 236",
      lightColor: "58, 53, 65",
      paperBgDark: "#231010",
      primaryContrastTextDark: "#000",
      primaryContrastTextLight: "#FFF",
      primaryDarkDark: "#541E1B",
      primaryDarkLight: "#541E1B",
      primaryGradientDark: "#541E1B",
      primaryGradientLight: "#E87E5A",
      primaryMainDark: "#E87E5A",
      primaryMainLight: "#541e1b",
      secondaryMainDark: "#B8B4B2",
      secondaryMainLight: "#756F6C",
      tableHeaderBgDark: "#291615",
      textPrimaryAlpha: "0.85",
    };
  if (isLp)
    return {
      bgDark: "#2B2826",
      bgLight: "#FBFAFA",
      darkColor: "243, 239, 236",
      lightColor: "58, 53, 65",
      paperBgDark: "#343332",
      primaryContrastTextDark: "#000",
      primaryContrastTextLight: "#FFF",
      primaryDarkDark: "#F8ECE2",
      primaryDarkLight: "#0C0A09",
      primaryGradientDark: "#3B3835",
      primaryGradientLight: "#594E40",
      primaryMainDark: "#FCF7F3",
      primaryMainLight: "#29241E",
      secondaryMainDark: "#B8B4B2",
      secondaryMainLight: "#756F6C",
      tableHeaderBgDark: "#3C3A39",
      textPrimaryAlpha: "0.85",
    };
  return {
    bgDark: "#28243D",
    bgLight: "#F4F5FA",
    darkColor: "231, 227, 252",
    lightColor: "70, 67, 63",
    paperBgDark: "#312D4B",
    primaryContrastTextDark: "#FFF",
    primaryContrastTextLight: "#FFF",
    primaryDarkDark: "#804BDF",
    primaryDarkLight: "#804BDF",
    primaryGradientDark: "#C6A7FE",
    primaryGradientLight: "#C6A7FE",
    primaryMainDark: "#9155FD",
    primaryMainLight: "#9155FD",
    secondaryMainDark: "#8A8D93",
    secondaryMainLight: "#8A8D93",
    tableHeaderBgDark: "#3D3759",
    textPrimaryAlpha: "0.87",
  };
}
