// ** MUI Imports
import { SvgIconProps } from "@mui/material";

interface UserIconProps {
  iconProps?: SvgIconProps;
  icon: React.ComponentType;
  componentType: "search" | "vertical-menu" | "horizontal-menu";
}

const UserIcon = (props: UserIconProps) => {
  // ** Props
  const { icon, iconProps, componentType } = props;

  const IconTag = icon;

  let styles;

  switch (componentType) {
    case "search":
      // Conditional Props based on component type, like have different font size or icon color
      /* styles = {
        color: 'blue',
        fontSize: '2rem'
      } */
      break;
    case "vertical-menu":
      // Conditional Props based on component type, like have different font size or icon color
      /* styles = {
        color: 'red',
        fontSize: '1.5rem'
      } */
      break;
    case "horizontal-menu":
      // Conditional Props based on component type, like have different font size or icon color
      /* styles = {
        color: 'green',
        fontSize: '1rem'
      } */
      break;
    default:
      return null;
  }

  // @ts-expect-error - types seem wrong
  return <IconTag {...iconProps} style={{ ...styles }} />;
};

export default UserIcon;
