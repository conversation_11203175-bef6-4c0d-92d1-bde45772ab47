"""CoinHistoricalPrice

Revision ID: 0001
Revises:
Create Date: 2025-05-22 18:10:52.548406

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0001"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "coin_historical_price",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("coingecko_id", sa.String(), nullable=False),
        sa.Column("timestamp", sa.DateTime(timezone=True), nullable=False),
        sa.Column("price_usd", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("volume_24h_usd", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("market_cap_usd", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_coin_historical_price_coin_timestamp_desc",
        "coin_historical_price",
        ["coingecko_id", sa.literal_column("timestamp DESC")],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_coin_historical_price_coin_timestamp_desc", table_name="coin_historical_price"
    )
    op.drop_table("coin_historical_price")
    # ### end Alembic commands ###
