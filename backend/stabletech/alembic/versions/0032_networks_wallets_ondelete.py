"""Networks Wallets ondelete

Revision ID: 0032
Revises: 0031
Create Date: 2023-08-11 21:56:49.013791

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0032"
down_revision = "0031"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "network_wallet_association_network_id_fkey",
        "network_wallet_association",
        type_="foreignkey",
    )
    op.drop_constraint(
        "network_wallet_association_wallet_id_fkey",
        "network_wallet_association",
        type_="foreignkey",
    )
    op.create_foreign_key(
        None,
        "network_wallet_association",
        "network",
        ["network_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        None, "network_wallet_association", "wallet", ["wallet_id"], ["id"], ondelete="CASCADE"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "network_wallet_association_network_id_fkey",
        "network_wallet_association",
        type_="foreignkey",
    )
    op.drop_constraint(
        "network_wallet_association_wallet_id_fkey",
        "network_wallet_association",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "network_wallet_association_wallet_id_fkey",
        "network_wallet_association",
        "wallet",
        ["wallet_id"],
        ["id"],
    )
    op.create_foreign_key(
        "network_wallet_association_network_id_fkey",
        "network_wallet_association",
        "network",
        ["network_id"],
        ["id"],
    )
    # ### end Alembic commands ###
