import Link from "@/components/Link";
import { currencyFormatter, formatPercentage } from "@/helpers/formatters";
import { useIndexerLoansQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AccountLoan } from "@/types/schemas";
import { HelpOutline } from "@mui/icons-material";
import {
  <PERSON>ert,
  Card,
  CardContent,
  Grid,
  Stack,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { useRouter } from "next/router";
import { useMemo } from "react";

interface ProcessedLoan
  extends Omit<AccountLoan, "totalCollateral" | "totalBorrowed" | "liqT" | "ltv" | "ptl"> {
  totalCollateral: number;
  totalBorrowed: number;
  ltv: number;
  liqT: number;
  ptl: number;
}

const INITIAL_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "name", sort: "asc" }] },
};

export default function LoansPage() {
  const { query, replace } = useRouter();
  const p2p = query.p2p === "true";
  const columns = useMemo(() => computeColumns(p2p), [p2p]);

  const { data: loansData, isFetching, isError, error } = useIndexerLoansQuery({ p2p });
  const loansRows = useMemo(() => {
    if (isFetching || isError || loansData == null) return [];
    return loansData.map(processLoan);
  }, [isFetching, isError, loansData]);
  const [totalCollateral, totalBorrowed] = useMemo(
    () => computeGlobalTotals(loansRows),
    [loansRows],
  );

  const setP2p = (value: boolean) => {
    const url = value ? "?p2p=true" : "";
    replace(url, undefined, { shallow: true });
  };

  const header = (
    <Stack direction="row" alignItems="baseline" flexWrap="nowrap">
      <Typography variant={"h4"}>Loans</Typography>
      <Stack direction="row" gap={4} ml="auto" flexWrap="wrap" justifyContent="flex-end">
        {!p2p && (
          <Typography color="primary" noWrap>
            Collateral{" "}
            <Typography component="span" ml={1}>
              $ {currencyFormatter.format(totalCollateral)}
            </Typography>
          </Typography>
        )}{" "}
        <Typography color="primary" noWrap>
          Borrowed{" "}
          <Typography component="span" ml={1}>
            $ {currencyFormatter.format(totalBorrowed)}
          </Typography>
        </Typography>
      </Stack>
    </Stack>
  );

  const table = (
    <DataGridPro
      columns={columns}
      rows={loansRows}
      initialState={INITIAL_STATE}
      loading={isFetching}
      autoHeight
      disableSelectionOnClick
      style={p2p ? { width: 450, flex: "unset" } : undefined}
    />
  );

  const errorAlert = error && (
    <Alert severity="error">
      Error: {getErrorDetail(error, { fallback: "Failed listing loans" })}
    </Alert>
  );

  const loanKindToggleGroup = (
    <ToggleButtonGroup
      value={p2p}
      exclusive
      onChange={(_event, value: boolean) => setP2p(value)}
    >
      <ToggleButton value={false} size="small">
        Protocols
      </ToggleButton>
      <ToggleButton value={true} size="small">
        P2P
      </ToggleButton>
    </ToggleButtonGroup>
  );

  return (
    <Card>
      <CardContent>
        <Grid container gap={4}>
          <Grid item xs={12}>
            {header}
          </Grid>
          <Grid item xs={12} display="flex" justifyContent="flex-end">
            {loanKindToggleGroup}
          </Grid>
          {errorAlert && (
            <Grid item xs={12}>
              {errorAlert}
            </Grid>
          )}
          <Grid item xs={12} display="flex" justifyContent="center">
            {table}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}

function processLoan(loan: AccountLoan): ProcessedLoan {
  const totalCollateral = Number.parseFloat(loan.totalCollateral);
  const totalBorrowed = Number.parseFloat(loan.totalBorrowed);
  const totalLiquidationThreshold = Number.parseFloat(loan.totalLiquidationThreshold);
  const liqT =
    totalLiquidationThreshold != 0 && totalCollateral != 0
      ? totalLiquidationThreshold / totalCollateral
      : 0;
  const ltv = totalBorrowed != 0 && totalCollateral != 0 ? totalBorrowed / totalCollateral : 0;
  const ptl = ltv != 0 && liqT != 0 ? liqT - ltv : 0;
  return {
    ...loan,
    totalCollateral,
    totalBorrowed,
    ltv,
    liqT,
    ptl,
  };
}

function computeGlobalTotals(loans: ProcessedLoan[]): [number, number] {
  let totalCollateral = 0;
  let totalBorrowed = 0;
  for (const loan of loans) {
    totalCollateral += loan.totalCollateral;
    totalBorrowed += loan.totalBorrowed;
  }
  return [totalCollateral, totalBorrowed];
}

const FILTER_OUT_P2P_COLS: ReadonlySet<string> = new Set([
  "network",
  "totalCollateral",
  "ltv",
  "liqT",
  "ptl",
]);

function computeColumns(p2p: boolean): GridColDef<ProcessedLoan, unknown>[] {
  const columns: GridColDef<ProcessedLoan, unknown>[] = [
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      renderCell: p2p
        ? undefined
        : ({ row: { id, name } }) => (
            <Link href={`/admin/loans/${id}`} fontWeight={500}>
              {name}
            </Link>
          ),
    },
    {
      field: "network",
      headerName: "Network",
      flex: 1,
      valueGetter: ({ row: { network } }) => network.name,
    },
    {
      field: "totalCollateral",
      headerName: "$ Collateral",
      flex: 1,
      maxWidth: 180,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row: { totalCollateral } }) => currencyFormatter.format(totalCollateral),
    },
    {
      field: "totalBorrowed",
      headerName: "$ Borrowed",
      flex: 1,
      maxWidth: 180,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row: { totalBorrowed } }) => currencyFormatter.format(totalBorrowed),
    },
    {
      field: "ltv",
      headerName: "LTV",
      renderHeader: () => (
        <Tooltip title="Loan to Value (Borrowed ÷ Collateral)">
          <Typography
            noWrap
            fontFamily="inherit"
            fontSize={12}
            fontWeight={600}
            fontStyle="inherit"
          >
            LTV
            <HelpOutline sx={{ fontSize: "0.75rem" }} />
          </Typography>
        </Tooltip>
      ),
      flex: 1,
      maxWidth: 120,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row: { ltv } }) => formatPercentage(ltv),
    },
    {
      field: "liqT",
      headerName: "LiqT",
      renderHeader: () => (
        <Tooltip title="Liquidation Threshold">
          <Typography
            noWrap
            fontFamily="inherit"
            fontSize={12}
            fontWeight={600}
            fontStyle="inherit"
          >
            LiqT
            <HelpOutline sx={{ fontSize: "0.75rem" }} />
          </Typography>
        </Tooltip>
      ),
      flex: 1,
      maxWidth: 120,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row: { liqT } }) => formatPercentage(liqT),
    },
    {
      field: "ptl",
      headerName: "PTL",
      renderHeader: () => (
        <Tooltip title="Percent to Liquidation">
          <Typography
            noWrap
            fontFamily="inherit"
            fontSize={12}
            fontWeight={600}
            fontStyle="inherit"
          >
            PTL
            <HelpOutline sx={{ fontSize: "0.75rem" }} />
          </Typography>
        </Tooltip>
      ),
      flex: 1,
      maxWidth: 120,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row: { ptl } }) => formatPercentage(ptl),
    },
  ];
  return p2p ? columns.filter((col) => !FILTER_OUT_P2P_COLS.has(col.field)) : columns;
}
