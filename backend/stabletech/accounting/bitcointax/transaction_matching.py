import logging
from copy import deepcopy
from datetime import datetime
from decimal import Decimal

from sqlalchemy.orm import Session

from common.conf import settings
from stabletech.accounting.bitcointax.generate_new_capital_positions import (
    generate_new_capital_positions,
)
from stabletech.accounting.bitcointax.kinds import (
    MatchError,
    OverMatchedBuyTransactionError,
    ProcessedTransaction,
    TransactionsPerCoin,
)
from stabletech.accounting.crud import (
    AccountedTransactionAction,
    AccountedTransactionManager,
    CapitalPositionManager,
)
from stabletech.accounting.models import CapitalPosition
from stabletech.indexer.crud import TransactionManager
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Transaction, Transfer

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def match_imported_capital_positions(db: Session, user_id: int | None = None):
    """
    Calculate new Capital Positions starting from a specific time.
    Used when a new Transaction is added or removed.
    """
    if not settings.capital_positions_enabled:
        logger.info("Capital positions are disabled")
        return

    logger.info("Matching imported capital positions")
    accounted_transaction_manager = AccountedTransactionManager(db)
    capital_position_manager = CapitalPositionManager(db)
    transaction_manager = TransactionManager(db)

    capital_position_manager.delete_open_positions(user_id)
    db.commit()

    # Get all imported capital positions
    capital_positions = capital_position_manager.get_all_for(user_id)
    if capital_positions:
        # Reset transaction ids in order to re-process.
        for capital_position in capital_positions:
            capital_position.accounted_buy_transaction_id = None
            capital_position.accounted_sell_transaction_id = None
        db.commit()
        accounted_transaction_manager.delete()
        db.commit()
    else:
        logger.info(f"No capital positions to process for user_id: {user_id}")
        capital_positions = []

    earliest_bought_at = (
        min(capital_positions, key=lambda x: x.bought_at).bought_at
        if capital_positions
        else datetime(2021, 1, 1)  # Same as saying get all transactions
    )
    transactions = transaction_manager.get_after(earliest_bought_at)
    if not transactions:
        if capital_positions:
            # We can't have Capital positions and no transactions.
            logger.error(
                f"No transactions to process for user_id: {user_id} capital position's earliest_bought_at: {earliest_bought_at} with capital positions: {len(capital_positions)}"
            )
        else:
            logger.info(
                f"No transactions to process for user_id: {user_id} and no capital positions"
            )
        return

    processed_transactions = process_transactions(db, transactions, capital_positions)
    if capital_positions:
        processed_capital_positions = process_capital_positions_by_ticker(capital_positions)
        (
            matched_capital_positions,
            unmatched_capital_positions,
            accounted_transactions_to_update,
        ) = match_capital_positions_to_transactions(
            processed_capital_positions, processed_transactions
        )
        if unmatched_capital_positions:
            logger.info(
                f"Failed matchings for user_id: {user_id} unmatched_capital_positions: {unmatched_capital_positions}"
            )
            # Continue with creating positions for now
            # TODO: add a separate page for the User to view unmatched positions
            # return unmatched_capital_positions

        accounted_transaction_manager.bulk_update(accounted_transactions_to_update)
        capital_position_manager.bulk_updated_matched_transactions(matched_capital_positions)
        db.commit()

    logger.info("Done Matching imported capital positions")
    generate_new_capital_positions(db)
    return


def get_index_of_transaction_by_date(
    timestamp: datetime, processed_transactions: list[ProcessedTransaction]
) -> int:
    for i, transaction in enumerate(processed_transactions):
        if transaction.confirmed_at.date() == timestamp.date():
            return i
    return -1


def get_index_of_transaction_by_datetime(
    timestamp: datetime, processed_transactions: list[ProcessedTransaction]
) -> int:
    for i, transaction in enumerate(processed_transactions):
        if transaction.confirmed_at.replace(microsecond=0) == timestamp.replace(microsecond=0):
            return i
    return -1


def match_capital_positions_to_transactions(
    capital_positions_dict: dict[str, list[CapitalPosition]],
    processed_transactions: dict[str, TransactionsPerCoin],
) -> tuple[list[CapitalPosition], list[MatchError], list[ProcessedTransaction]]:
    if not set(capital_positions_dict.keys()).issubset(processed_transactions.keys()):
        raise ValueError(
            "There are tickers for capital_positions that are not present in the transactions tickers\n"
            f"capital_positions tickers: {capital_positions_dict.keys()}\n"
            f"transactions tickers: {processed_transactions.keys()}\n"
            f"missing: {set(capital_positions_dict.keys()) - set(processed_transactions.keys())}"
        )

    all_tickers = set(capital_positions_dict.keys()) | set(processed_transactions.keys())
    unmatched_capital_positions: list[MatchError] = []
    matched_capital_positions: list[CapitalPosition] = []
    accounted_transactions_to_update = []
    for ticker in all_tickers:
        capital_positions = capital_positions_dict.get(ticker, [])
        (
            result_capital_positions,
            failed_buy_matchings,
            accounted_buys,
            accounted_loan_withdrawals,
        ) = match_transactions_to_capital_positions_buy(
            capital_positions,
            processed_transactions[ticker].buys,
            processed_transactions[ticker].loan_withdrawals,
        )
        unmatched_capital_positions.extend(failed_buy_matchings)
        result_capital_positions, failed_sell_matchings, accounted_sells = (
            match_transactions_to_capital_positions_sells(
                result_capital_positions,
                processed_transactions[ticker].sells,
            )
        )
        accounted_transactions_to_update.extend(
            accounted_buys + accounted_sells + accounted_loan_withdrawals
        )
        unmatched_capital_positions.extend(failed_sell_matchings)
        matched_capital_positions.extend(result_capital_positions)

    return (
        matched_capital_positions,
        unmatched_capital_positions,
        accounted_transactions_to_update,
    )


def get_sorted_buy_transactions_from_history(
    buy_transactions_history: dict[str, ProcessedTransaction],
) -> list[ProcessedTransaction]:
    return sorted(
        deepcopy(list(buy_transactions_history.values())),
        key=lambda x: x.confirmed_at,
        reverse=False,
    )


def get_tolerance(amount: Decimal) -> Decimal:
    return Decimal("0.01") if amount > 1 else Decimal("0.000001")


def match_transactions_to_capital_positions_buy(
    capital_positions: list[CapitalPosition],
    buy_transactions: list[ProcessedTransaction],
    loan_withdrawals: list[ProcessedTransaction],
) -> tuple[
    list[CapitalPosition],
    list[MatchError],
    list[ProcessedTransaction],
    list[ProcessedTransaction],
]:
    """
    Matches buy transactions to capital positions as long as buy_transaction `at` timestamp is not newer than capital position's `bought_at`
    with 3 strategies:
    1. Match by swap transactions which exact transaction.remaining_amount to capital_position.amount match (with a 0.0000001 decimal error tolerance)
    2. Match by swap transactions by transaction.remaining_amount >= capital_position.amount
    3. Match by income transactions by transaction.remaining_amount >= capital_position.amount
    """

    sorted_capital_positions = sorted(
        capital_positions, key=lambda x: x.bought_at, reverse=False
    )
    buy_transactions_history = {
        buy_transaction.id: buy_transaction for buy_transaction in buy_transactions
    }
    sorted_buy_transactions = get_sorted_buy_transactions_from_history(buy_transactions_history)
    # Match swap transactions by exact amount first
    for capital_position in sorted_capital_positions:
        settle_loan_withdrawals(
            sorted_buy_transactions, loan_withdrawals, timestamp=capital_position.bought_at
        )
        for buy_transaction in sorted_buy_transactions:
            if buy_transaction.action != TransactionAction.swap:
                continue
            if buy_transaction.is_full():
                continue
            if buy_transaction.confirmed_at.date() != capital_position.bought_at.date():
                continue
            if buy_transaction.confirmed_at > capital_position.bought_at:
                break
            if (
                buy_transaction.remaining_amount.quantize(Decimal("1.000000"))
                .normalize()
                .compare(capital_position.amount.quantize(Decimal("1.000000")).normalize())
                == 0
            ):
                buy_transaction.remaining_amount -= capital_position.amount
                buy_transactions_history[buy_transaction.id].remaining_amount = (
                    buy_transaction.remaining_amount
                )

                # Make sure we didn't over match more than margin of error
                if buy_transaction.remaining_amount < 0:
                    if buy_transaction.remaining_amount >= Decimal("-0.000001"):
                        logger.info(
                            "Over matched within marging of error of 0.000001\n"
                            f"buy transaction for ticker: {capital_position.coin.ticker}\n"
                            f"buy transaction remaining_amount: {buy_transaction.remaining_amount}\n"
                            f"capital position: amount {capital_position.amount} bought_at: {capital_position.bought_at}\n"
                            f"buy_transaction: {buy_transaction.amount} at: {buy_transaction.confirmed_at}"
                        )
                    else:
                        logger.error(
                            f"Over matched buy transaction for ticker: {capital_position.coin.ticker}\n"
                            f"capital position: amount {capital_position.amount} bought_at: {capital_position.bought_at}\n"
                            f"buy_transaction: {buy_transaction.amount} at: {buy_transaction.confirmed_at}\n"
                            f"remaining amount: {buy_transaction.remaining_amount}"
                        )
                        raise OverMatchedBuyTransactionError

                capital_position.accounted_buy_transaction_id = buy_transaction.id
                break

    sorted_buy_transactions = get_sorted_buy_transactions_from_history(buy_transactions_history)
    # Then match swap transactions by remaining amount
    for capital_position in sorted_capital_positions:
        settle_loan_withdrawals(
            sorted_buy_transactions, loan_withdrawals, timestamp=capital_position.bought_at
        )
        for buy_transaction in sorted_buy_transactions:
            tolerance = get_tolerance(buy_transaction.amount)
            if capital_position.accounted_buy_transaction_id is not None:
                break
            if buy_transaction.action != TransactionAction.swap:
                continue
            if buy_transaction.is_full():
                continue
            if buy_transaction.remaining_amount + tolerance < capital_position.amount:
                continue
            if buy_transaction.confirmed_at > capital_position.bought_at:
                break
            buy_transaction.remaining_amount -= capital_position.amount
            buy_transactions_history[buy_transaction.id].remaining_amount = (
                buy_transaction.remaining_amount
            )
            capital_position.accounted_buy_transaction_id = buy_transaction.id
            break

    sorted_buy_transactions = get_sorted_buy_transactions_from_history(buy_transactions_history)
    # Lastly match income transactions by remaining amount
    for capital_position in sorted_capital_positions:
        settle_loan_withdrawals(
            sorted_buy_transactions, loan_withdrawals, timestamp=capital_position.bought_at
        )
        for buy_transaction in sorted_buy_transactions:
            tolerance = get_tolerance(buy_transaction.amount)
            if capital_position.accounted_buy_transaction_id is not None:
                break
            if buy_transaction.is_full():
                continue
            if buy_transaction.remaining_amount + tolerance < capital_position.amount:
                continue
            if buy_transaction.confirmed_at > capital_position.bought_at:
                break
            buy_transaction.remaining_amount -= capital_position.amount
            buy_transactions_history[buy_transaction.id].remaining_amount = (
                buy_transaction.remaining_amount
            )
            capital_position.accounted_buy_transaction_id = buy_transaction.id
            break

    for capital_position in sorted_capital_positions:
        if capital_position.accounted_buy_transaction_id is None:
            logger.info(
                f"Missing buy transaction for ticker: {capital_position.coin.ticker}"
                f"capital position: amount {capital_position.amount} bought_at: {capital_position.bought_at}"
            )
            for transaction in sorted_buy_transactions:
                logger.info(
                    f"buy_transaction: {transaction.amount} remaining_amount: {transaction.remaining_amount} at: {transaction.confirmed_at} tx {transaction.tx_hash}"
                )
            return (
                [],
                [
                    MatchError(
                        "unmatched-buy",
                        capital_position.csv_row,
                    )
                ],
                [],
                [],
            )

    for buy_transaction in sorted_buy_transactions:
        tolerance = get_tolerance(buy_transaction.amount)
        tolerance = tolerance * -1
        if buy_transaction.remaining_amount < tolerance:
            raise ValueError(
                f"Over matched buy transaction for ticker: {capital_position.coin.ticker}\n"
                f"buy transaction remaining_amount: {buy_transaction.remaining_amount}\n"
                f"capital position: amount {capital_position.amount} bought_at: {capital_position.bought_at}\n"
                f"buy_transaction: {buy_transaction.amount} at: {buy_transaction.confirmed_at}"
            )

    resulting_loan_withdrawals = settle_loan_withdrawals(
        sorted_buy_transactions, loan_withdrawals, timestamp=None
    )

    return sorted_capital_positions, [], sorted_buy_transactions, resulting_loan_withdrawals


def match_transactions_to_capital_positions_sells(
    capital_positions: list[CapitalPosition],
    sell_transactions: list[ProcessedTransaction],
) -> tuple[
    list[CapitalPosition],
    list[MatchError],
    list[ProcessedTransaction],
]:
    sorted_capital_positions = sorted(
        capital_positions, key=lambda x: (x.sold_at, x.amount), reverse=False
    )
    sorted_sell_transactions = sorted(
        sell_transactions, key=lambda x: x.confirmed_at, reverse=False
    )
    for capital_position in sorted_capital_positions:
        for sell_transaction in sorted_sell_transactions:
            if sell_transaction.is_full():
                continue
            if sell_transaction.remaining_amount != capital_position.amount:
                continue
            if sell_transaction.confirmed_at < capital_position.sold_at:
                continue
            if sell_transaction.confirmed_at > capital_position.sold_at:
                break

            sell_transaction.remaining_amount -= capital_position.amount
            capital_position.accounted_sell_transaction_id = sell_transaction.id
            break

    for capital_position in sorted_capital_positions:
        if capital_position.accounted_sell_transaction_id is not None:
            continue

        for sell_transaction in sorted_sell_transactions:
            tolerance = get_tolerance(sell_transaction.amount)
            if sell_transaction.is_full():
                continue
            if sell_transaction.remaining_amount + tolerance < capital_position.amount:
                continue
            if sell_transaction.confirmed_at < capital_position.sold_at:
                continue
            if sell_transaction.confirmed_at > capital_position.sold_at:
                break

            sell_transaction.remaining_amount -= capital_position.amount
            capital_position.accounted_sell_transaction_id = sell_transaction.id
            break

        if capital_position.accounted_sell_transaction_id is None:
            logger.info(
                f"Missing sell transaction for ticker: {capital_position.coin.ticker}"
                f"capital position: amount {capital_position.amount} sold_at: {capital_position.sold_at}"
            )
            for transaction in sorted_sell_transactions:
                logger.info(
                    f"sell_transaction: {transaction.amount} remaining_amount: {transaction.remaining_amount} at: {transaction.confirmed_at}"
                )
            return (
                [],
                [
                    MatchError(
                        "unmatched-sell",
                        capital_position.csv_row,
                    )
                ],
                [],
            )

    return sorted_capital_positions, [], sorted_sell_transactions


def process_capital_positions_by_ticker(
    capital_positions: list[CapitalPosition],
) -> dict[str, CapitalPosition]:
    """Split capital positions by ticker"""
    capital_positions_dict = {}
    for capital_position in capital_positions:
        assert capital_position.sold_at is not None

        # Use the alias coin's ticker if it exists
        if capital_position.coin.alias_coin:
            ticker = capital_position.coin.alias_coin.coin.ticker.upper()
        else:
            ticker = capital_position.coin.ticker.upper()

        if capital_positions_dict.get(ticker, None) is None:
            capital_positions_dict[ticker] = []
        capital_positions_dict[ticker].insert(0, capital_position)

    return capital_positions_dict


def get_ticker(transfer: Transfer) -> str:
    if transfer.asset_account.coin:
        if transfer.asset_account.coin.alias_coin:
            return transfer.asset_account.coin.alias_coin.coin.ticker.upper()
        return transfer.asset_account.coin.ticker.upper()
    return transfer.asset_account.ticker_symbol.upper()


def process_transactions(
    db: Session, transactions: list[Transaction], capital_positions: list[CapitalPosition]
) -> dict[str, TransactionsPerCoin]:
    """
    Process transactions to calculate new Capital Positions.
    """
    processed_transactions: dict[str, TransactionsPerCoin] = dict()
    for transaction in transactions:
        assert (
            len(transaction.transfers) > 0
        ), f"Transaction {transaction} has no transfers confirmed_at: {transaction.confirmed_at}, action: {transaction.action}"

        match transaction.action:
            case TransactionAction.loan:
                transfer = None
                if len(transaction.transfers) == 1:
                    transfer = transaction.transfers[0]
                else:
                    transfers = None
                    if transaction.is_collateral:
                        transfers = [
                            tf
                            for tf in transaction.transfers
                            if (transaction.is_deposit and tf.is_withdrawal)
                            or ((not transaction.is_deposit) and tf.is_deposit)
                        ]

                    else:
                        transfers = [
                            tf
                            for tf in transaction.transfers
                            if (transaction.is_deposit and tf.is_deposit)
                            or ((not transaction.is_deposit) and tf.is_withdrawal)
                        ]
                    if transfers:
                        if len(transfers) > 1:
                            for tf in transfers:
                                if tf.asset_price_usd is not None:
                                    transfer = tf
                                    break
                        else:
                            transfer = transfers[0]

                    if transfer is None:
                        logger.info(
                            f"Couldn't get right transfer for loan transaction: {transaction} with {len(transaction.transfers)} transfers and transaction is_deposit: {transaction.is_deposit}"
                        )
                        continue

                ticker = get_ticker(transfer)
                if processed_transactions.get(ticker, None) is None:
                    processed_transactions[ticker] = TransactionsPerCoin()
                if transaction.is_deposit:
                    buys = processed_transactions[ticker].buys
                    if (
                        index := get_index_of_transaction_by_date(
                            transaction.confirmed_at, processed_transactions[ticker].buys
                        )
                    ) != -1:
                        buys[index].aggregate(transaction, transfer)
                    else:
                        buys.append(
                            ProcessedTransaction.from_transaction_transfer(
                                transaction, transfer
                            )
                        )
                    processed_transactions[ticker].buys = buys
                else:
                    # We can't process all the loan withdrawals and deduct from the buy transactions (buy loans),
                    # we may exhaust a buy transaction that should be the match of a capital position

                    # Validate if the loan_withdrawal confirmed_at has a matching capital_position sold_at
                    # If it does, we need to reclassify the loan_withdrawal as a sell transaction
                    confirmed_at = transaction.confirmed_at
                    if transaction.api_type == "Other Expense" and any(
                        capital_position.coin.ticker.lower() == ticker.lower()
                        and capital_position.sold_at == confirmed_at
                        for capital_position in capital_positions
                    ):
                        processed_transactions[ticker].sells.append(
                            ProcessedTransaction.from_transaction_transfer(
                                transaction, transfer
                            )
                        )
                    else:
                        processed_transactions[ticker].loan_withdrawals.append(
                            ProcessedTransaction.from_transaction_transfer(
                                transaction, transfer
                            )
                        )
            case TransactionAction.income:
                for transfer in transaction.transfers:
                    if not transfer.is_deposit:
                        continue

                    ticker = get_ticker(transfer)
                    if processed_transactions.get(ticker, None) is None:
                        processed_transactions[ticker] = TransactionsPerCoin()
                    buys = processed_transactions[ticker].buys
                    if (
                        index := get_index_of_transaction_by_date(
                            transaction.confirmed_at, processed_transactions[ticker].buys
                        )
                    ) != -1:
                        buys[index].aggregate(transaction, transfer)
                    else:
                        buys.append(
                            ProcessedTransaction.from_transaction_transfer(
                                transaction, transfer
                            )
                        )
                    processed_transactions[ticker].buys = buys

            case TransactionAction.spend:
                for transfer in transaction.transfers:
                    if not transfer.is_withdrawal:
                        continue
                    ticker = get_ticker(transfer)
                    if processed_transactions.get(ticker, None) is None:
                        processed_transactions[ticker] = TransactionsPerCoin()
                    sells = processed_transactions[ticker].sells
                    if (
                        index := get_index_of_transaction_by_datetime(
                            transaction.confirmed_at, sells
                        )
                    ) != -1:
                        sells[index].aggregate(transaction, transfer)
                    else:
                        sells.append(
                            ProcessedTransaction.from_transaction_transfer(
                                transaction, transfer
                            )
                        )
                    processed_transactions[ticker].sells = sells
            case TransactionAction.swap:
                assert (
                    len(transaction.transfers) > 1
                ), f"Transaction {transaction} has less than 2 transfers"
                assets = set()
                for transfer in transaction.transfers:
                    ticker = get_ticker(transfer)
                    if processed_transactions.get(ticker, None) is None:
                        processed_transactions[ticker] = TransactionsPerCoin()

                    assets.add(ticker)

                    """
                    Currently we can't accurately match transactions with capital positions in the exact same way
                    that the capital position was generate by BitcoinTax.
                    So we group the buy and sell transactions by date, issue with this is that we are not able
                    to tell which transaction got matched to which capital position.
                    We only get the first transaction id and assign it to the capital position.
                    """
                    if transfer.is_deposit:
                        buys = processed_transactions[ticker].buys
                        if (
                            index := get_index_of_transaction_by_date(
                                transaction.confirmed_at, buys
                            )
                        ) != -1:
                            buys[index].aggregate(transaction, transfer)
                        else:
                            buys.append(
                                ProcessedTransaction.from_transaction_transfer(
                                    transaction, transfer
                                )
                            )
                        processed_transactions[ticker].buys = buys
                    elif transfer.is_withdrawal:
                        sells = processed_transactions[ticker].sells
                        if (
                            index := get_index_of_transaction_by_datetime(
                                transaction.confirmed_at, sells
                            )
                        ) != -1:
                            sells[index].aggregate(transaction, transfer)
                        else:
                            sells.append(
                                ProcessedTransaction.from_transaction_transfer(
                                    transaction, transfer
                                )
                            )
                        processed_transactions[ticker].sells = sells
                    else:
                        raise ValueError(
                            f"Unexpected transfer is_deposit: {transfer.is_deposit} is_withdrawal: {transfer.is_withdrawal}"
                        )
                if len(assets) > 2:
                    logger.info(f"Transaction {transaction} has more than 2 assets {assets}")

    accounted_transaction_manager = AccountedTransactionManager(db)
    for ticker, transactions in processed_transactions.items():
        accounted_buys = (
            accounted_transaction_manager.bulk_create_with_action(
                AccountedTransactionAction.buy,
                transactions.buys,
            )
            if transactions.buys
            else []
        )
        processed_transactions[ticker].buys = [
            ProcessedTransaction.from_accounted_transaction(accounted_buy)
            for accounted_buy in accounted_buys
        ]

        accounted_sells = (
            accounted_transaction_manager.bulk_create_with_action(
                AccountedTransactionAction.sell,
                transactions.sells,
            )
            if transactions.sells
            else []
        )
        processed_transactions[ticker].sells = [
            ProcessedTransaction.from_accounted_transaction(accounted_sell)
            for accounted_sell in accounted_sells
        ]

        accounted_loan_withdrawals = (
            accounted_transaction_manager.bulk_create_with_action(
                AccountedTransactionAction.loan_withdrawal,
                transactions.loan_withdrawals,
            )
            if transactions.loan_withdrawals
            else []
        )
        processed_transactions[ticker].loan_withdrawals = [
            ProcessedTransaction.from_accounted_transaction(accounted_loan_withdrawal)
            for accounted_loan_withdrawal in accounted_loan_withdrawals
        ]

    db.commit()
    return processed_transactions


def settle_loan_withdrawals(
    buy_transactions: list[ProcessedTransaction],
    loan_withdrawals: list[ProcessedTransaction],
    *,
    timestamp: datetime | None = None,
) -> list[ProcessedTransaction]:
    """
    Settle loan withdrawals with buy transactions.
    we make a copy of the loan withdrawals so we can always re-apply the same loan withdrawals
    as the timestmap changes and buy_transactions are mutated
    """
    if not loan_withdrawals:
        return loan_withdrawals

    if timestamp and not any(
        loan_withdrawal
        for loan_withdrawal in loan_withdrawals
        if loan_withdrawal.confirmed_at < timestamp
    ):
        return loan_withdrawals

    if not buy_transactions and any(
        loan_withdrawal.confirmed_at < datetime(2024, 1, 1)
        for loan_withdrawal in loan_withdrawals
    ):
        raise ValueError(f"No buy transactions to settle loan withdrawals: {loan_withdrawals}")

    resulting_loan_withdrawals = list(loan_withdrawals[::])
    for loan_withdrawal in resulting_loan_withdrawals:
        if timestamp and loan_withdrawal.confirmed_at > timestamp:
            break
        if loan_withdrawal.is_full():
            continue

        remaining_sell_amount = loan_withdrawal.remaining_amount
        for buy_transaction in buy_transactions:
            if (
                buy_transaction.is_full()
                or buy_transaction.confirmed_at > loan_withdrawal.confirmed_at
            ):
                continue
            if buy_transaction.remaining_amount <= remaining_sell_amount:
                remaining_sell_amount -= buy_transaction.remaining_amount
                buy_transaction.remaining_amount = 0
                buy_transaction.consume_loan_withdrawal(loan_withdrawal)
            else:
                buy_transaction.remaining_amount -= remaining_sell_amount
                remaining_sell_amount = 0
                buy_transaction.consume_loan_withdrawal(loan_withdrawal)
                break
        tolerance = get_tolerance(loan_withdrawal.amount)

        # Account for a margin of error and we don't care about transactions after 2024, we are only matching
        # For 2022 and 2023
        if remaining_sell_amount > tolerance and loan_withdrawal.confirmed_at < datetime(
            2024, 1, 1
        ):
            for buy_transaction in buy_transactions:
                logger.info(
                    f"buy_transaction: {buy_transaction.amount} remaining_amount: {buy_transaction.remaining_amount} at: {buy_transaction.confirmed_at}"
                )
            raise ValueError(
                f"Loan withdrawal {loan_withdrawal} has remaining amount {remaining_sell_amount} after matching all buy transactions"
            )

        loan_withdrawal.remaining_amount = 0

    return resulting_loan_withdrawals
