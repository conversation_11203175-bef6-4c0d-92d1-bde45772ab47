// ** React Imports
import { <PERSON><PERSON>, <PERSON><PERSON>ield } from "@mui/material";
import Button from "@mui/material/Button";
import { useRouter } from "next/router";
import { ReactNode, useState } from "react";

// ** MUI Components
import Box, { BoxProps } from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import Typography, { TypographyProps } from "@mui/material/Typography";
import { styled, useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";

// ** Icons Imports
import { enablePasswordLogin } from "@/context/env";
import { useAppDispatch } from "@/hooks/store";
import Google from "mdi-material-ui/Google";

// ** Hooks
import { useSettings } from "@/@core/hooks/useSettings";
import { useAuth } from "@/hooks/useAuth";

// ** Configs
import themeConfig from "@/configs/themeConfig";
import { getErrorDetail } from "@/store/services/helpers";

// ** Layout Import
import BlankLayout from "@/@core/layouts/BlankLayout";
import { setAuthToken, setUser } from "@/store/apps/auth";
import { useAuthLoginWithPasswordMutation } from "@/store/services/api";

// ** Demo Imports
import { STSmallIconOrange } from "@/components/STIcons";
import FooterIllustrationsV2 from "@/views/pages/auth/FooterIllustrationsV2";

// ** Styled Components
const LoginIllustrationWrapper = styled(Box)<BoxProps>(({ theme }) => ({
  padding: theme.spacing(20),
  paddingRight: "0 !important",
  [theme.breakpoints.down("lg")]: {
    padding: theme.spacing(10),
  },
}));

const LoginIllustration = styled("img")(({ theme }) => ({
  maxWidth: "48rem",
  [theme.breakpoints.down("lg")]: {
    maxWidth: "35rem",
  },
}));

const RightWrapper = styled(Box)<BoxProps>(({ theme }) => ({
  width: "100%",
  [theme.breakpoints.up("md")]: {
    maxWidth: 450,
  },
}));

const BoxWrapper = styled(Box)<BoxProps>(({ theme }) => ({
  [theme.breakpoints.down("xl")]: {
    width: "100%",
  },
  [theme.breakpoints.down("md")]: {
    maxWidth: 400,
  },
}));

const TypographyStyled = styled(Typography)<TypographyProps>(({ theme }) => ({
  fontWeight: 600,
  marginBottom: theme.spacing(1.5),
  [theme.breakpoints.down("md")]: { mt: theme.spacing(8) },
}));

const LoginPage = () => {
  // ** Hooks
  const auth = useAuth();
  const theme = useTheme();
  const { settings } = useSettings();
  const hidden = useMediaQuery(theme.breakpoints.down("md"));

  // ** Vars
  const { skin } = settings;
  const imageSource =
    skin === "bordered" ? "auth-v2-login-illustration-bordered" : "auth-v2-login-illustration";

  const dispatch = useAppDispatch();
  const [error, setError] = useState<string>("");
  const [passwordLogin] = useAuthLoginWithPasswordMutation();
  const router = useRouter();

  const handleSignInWithGoogle = () => {
    setError("");
    auth.signInWithGoogle().catch((error: unknown) => {
      setError(getErrorDetail(error));
    });
  };

  return (
    <Box className="content-right">
      {!hidden ? (
        <Box
          sx={{
            flex: 1,
            display: "flex",
            position: "relative",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <LoginIllustrationWrapper>
            <LoginIllustration
              alt="login-illustration"
              src={`/images/pages/${imageSource}-${theme.palette.mode}.png`}
            />
          </LoginIllustrationWrapper>
          <FooterIllustrationsV2 />
        </Box>
      ) : null}
      <RightWrapper
        sx={
          skin === "bordered" && !hidden
            ? { borderLeft: `1px solid ${theme.palette.divider}` }
            : {}
        }
      >
        <Box
          sx={{
            p: 12,
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "background.paper",
          }}
        >
          <BoxWrapper>
            <Box
              sx={{
                top: 30,
                left: 40,
                display: "flex",
                position: "absolute",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <STSmallIconOrange />
              <Typography
                variant="h6"
                sx={{
                  ml: 3,
                  lineHeight: 1,
                  fontWeight: 600,
                  textTransform: "uppercase",
                  fontSize: "1.5rem !important",
                }}
              >
                {themeConfig.templateName}
              </Typography>
            </Box>
            <Box mb={2}>
              <TypographyStyled variant="h5">
                Welcome to {themeConfig.templateName}
              </TypographyStyled>
              <Typography variant="body2">Please sign-in with your Google account</Typography>
            </Box>

            {error && (
              <Box py={2}>
                <Alert severity={"error"}>{error}</Alert>
              </Box>
            )}

            {/* eslint-disable-next-line jsx-a11y/anchor-is-valid, jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions */}
            <IconButton
              aria-label="Sign in with Google"
              component="a"
              disabled={auth.loading}
              onClick={handleSignInWithGoogle}
            >
              <Google sx={{ color: "#db4437" }} />
            </IconButton>

            {enablePasswordLogin && (
              <Box
                component="form"
                noValidate
                sx={{ mt: 1 }}
                onSubmit={(event) => {
                  setError("");
                  event.preventDefault();
                  const data = new FormData(event.currentTarget);
                  const email = data.get("email");
                  const password = data.get("password");
                  if (email && password) {
                    passwordLogin({
                      email: email.toString(),
                      password: password.toString(),
                    })
                      .unwrap()
                      .then((userData) => {
                        if (userData) {
                          dispatch(setAuthToken(userData.authToken ?? null));
                          dispatch(setUser(userData));

                          router
                            .push(
                              router.asPath.startsWith("/login")
                                ? (router.query.returnUrl as string) || "/dashboard"
                                : router.asPath,
                            )
                            .then();
                        }
                      })
                      .catch((error) => setError(error.data.detail));
                  }
                }}
              >
                <Typography variant="body2">or with email address and password</Typography>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Email Address"
                  name="email"
                  autoComplete="email"
                  // eslint-disable-next-line jsx-a11y/no-autofocus
                  autoFocus
                />
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type="password"
                  id="password"
                  autoComplete="current-password"
                />
                <Button type="submit" fullWidth variant="contained" sx={{ mt: 3, mb: 2 }}>
                  Sign In
                </Button>
              </Box>
            )}
          </BoxWrapper>
        </Box>
      </RightWrapper>
    </Box>
  );
};

LoginPage.getLayout = (page: ReactNode) => <BlankLayout>{page}</BlankLayout>;

LoginPage.guestGuard = true;

export default LoginPage;
