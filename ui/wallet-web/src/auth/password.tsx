import { isPasswordRegistered, registerPassword, testPassword } from "@/auth/password-store";
import { getPasswordKey } from "@/lib/secret-store";
import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  use,
  useCallback,
  useEffect,
  useState,
} from "react";

interface PasswordContextValue {
  readonly password: CryptoKey | null;
  readonly until: number;
  readonly registered: boolean | null;
}

const INITIAL_VALUE: PasswordContextValue = { password: null, until: 0, registered: null };

export const PasswordContext = createContext(INITIAL_VALUE);
PasswordContext.displayName = "Password";

const PasswordPrivateContext = createContext<Dispatch<SetStateAction<PasswordContextValue>>>(
  () => {
    throw new Error("Not implemented");
  },
);
PasswordPrivateContext.displayName = "PasswordPrivate";

const MAX_TIMEOUT_DURATION = 3000;

export function PasswordProvider(props: { readonly children: ReactNode }) {
  const { children } = props;
  const [value, setValue] = useState(INITIAL_VALUE);

  // Initialize whether a password is registered or not
  useEffect(() => {
    void isPasswordRegistered().then((registered) => {
      setValue((state) => {
        if (state.registered === registered) return state;
        return { ...state, registered };
      });
    });
  }, []);

  // Remove the password after the desired duration
  useEffect(() => {
    let timeout: undefined | number;

    function passwordRemovalRoutine() {
      if (timeout != null) {
        window.clearTimeout(timeout);
        timeout = undefined;
      }
      if (value.until <= Date.now() + 100) {
        setValue(resetPasswordState);
        return;
      }
      const duration = Math.min(value.until - Date.now(), MAX_TIMEOUT_DURATION);
      /* If the event loop gets interrupted just before the previous operation,
       * e.g. the computer is suspended, and after a while resumes on the
       * calculation of duration, then we could have a negative duration.
       * This is also why we need a conservative maximum timeout duration. */
      if (duration < 0) {
        setValue(resetPasswordState);
        return;
      }
      timeout = window.setTimeout(passwordRemovalRoutine, duration);
    }
    passwordRemovalRoutine();

    return () => {
      if (timeout != null) {
        window.clearTimeout(timeout);
        timeout = undefined;
      }
    };
  }, [value.until]);

  return (
    <PasswordContext value={value}>
      <PasswordPrivateContext value={setValue}>{children}</PasswordPrivateContext>
    </PasswordContext>
  );
}

export function useClearPassword() {
  const setCtx = use(PasswordPrivateContext);
  return useCallback(
    function clearPassword() {
      setCtx(resetPasswordState);
    },
    [setCtx],
  );
}

export interface GetPasswordOptions {
  testPassword?: boolean | undefined;
}

export function useGetPassword() {
  const ctx = use(PasswordContext);
  const setCtx = use(PasswordPrivateContext);

  return async function getPassword(formData: FormData, options: GetPasswordOptions = {}) {
    if (ctx.password != null) {
      if (ctx.until <= Date.now() - 100) {
        setCtx(resetPasswordState);
        throw new Error("Password expired, please re-enter");
      }
      return ctx.password;
    }

    const [password, unlockedFor] = await parseFormData(formData);

    const registered = await isPasswordRegistered();
    if (!registered) {
      try {
        await registerPassword({ password });
      } catch (error) {
        // User might've registered a password in another tab
        const registered = await isPasswordRegistered();
        setCtx((prev) => (prev.registered === registered ? prev : { ...prev, registered }));
        throw error;
      }
    }

    if (!registered || !ctx.registered) {
      // Mark the password as registered after registration
      // or in case of an outdated state
      setCtx((prev) => (prev.registered ? prev : { ...prev, registered: true }));
    }

    const mustKeepUnlocked = unlockedFor > 0;
    const mustTestPassword = mustKeepUnlocked || (options.testPassword ?? false);

    if (mustTestPassword) {
      const isValid = await testPassword({ password });
      if (!isValid) {
        throw new Error("Incorrect password");
      }
    }

    if (mustKeepUnlocked) {
      const until = Date.now() + unlockedFor;
      setCtx({ password, until, registered: true });
    }

    return password;
  };
}

async function parseFormData(formData: FormData) {
  const rawPassword = formData.get("password");
  const rawUnlockedFor = formData.get("keepUnlockedFor");
  if (typeof rawPassword !== "string") throw new Error("'password' must be a string");
  if (typeof rawUnlockedFor !== "string") throw new Error("'keepUnlockedFor' must be a string");

  const unlockedFor = Number.parseInt(rawUnlockedFor, 10);
  if (Number.isNaN(unlockedFor)) throw new Error("'keepUnlockedFor' must be an integer");

  const password = await getPasswordKey(rawPassword);
  return [password, unlockedFor] as const;
}

function resetPasswordState(state: PasswordContextValue): PasswordContextValue {
  if (state.password == null) return state;
  return { ...state, password: null, until: 0 };
}
