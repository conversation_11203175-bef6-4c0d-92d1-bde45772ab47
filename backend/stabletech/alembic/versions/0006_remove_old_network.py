"""Remove Old Network

Revision ID: 0006
Revises: 0005
Create Date: 2022-12-15 02:48:24.541395

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0006"
down_revision = "0005"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("pool", "old_network")
    op.drop_column("wallet", "old_network")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "wallet", sa.Column("old_network", sa.VARCHAR(), autoincrement=False, nullable=True)
    )
    op.add_column(
        "pool", sa.Column("old_network", sa.VARCHAR(), autoincrement=False, nullable=True)
    )
    # ### end Alembic commands ###
