"""Add coin collection lock table

Revision ID: 0002
Revises: 0001
Create Date: 2025-05-26 16:39:48.857020

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0002"
down_revision: str | None = "0001"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "coin_collection_lock",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("coingecko_id", sa.String(), nullable=False),
        sa.Column("last_updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.<PERSON>umn("failure_count", sa.Integer(), nullable=False),
        sa.Column("last_failure_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.<PERSON>eyConstraint("id"),
        sa.UniqueConstraint("coingecko_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("coin_collection_lock")
    # ### end Alembic commands ###
