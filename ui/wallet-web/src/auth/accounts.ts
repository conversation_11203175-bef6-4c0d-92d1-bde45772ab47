import { useCubeSignerKeys } from "@/auth/cubesigner-keys";
import { useLocalKeyList } from "@/auth/local-keys";
import { Network } from "@/lib/mnemonic";
import type { Key as SecretStoreKey } from "@/lib/secret-store";
import { getAddress } from "@/lib/utils/keys";
import * as cs from "@cubist-labs/cubesigner-sdk";
import { useMemo } from "react";

interface BaseUnifiedAccount {
  readonly id: string;
  readonly network: Network;
  readonly address: string;
  readonly masterKeyId: string | undefined;
  readonly isMasterKey: boolean;
}

export interface CubeSignerAccount extends BaseUnifiedAccount {
  readonly provider: "cubesigner";
  readonly key: cs.Key;
}

interface LocalAccount extends BaseUnifiedAccount {
  readonly provider: "local";
  readonly key: SecretStoreKey;
}

export type UnifiedAccount = CubeSignerAccount | LocalAccount;

/**
 * Hook that retrieves and combines accounts from both CubeSigner and local storage.
 */
export function useAccounts({ skipMasterKeys = false } = {}) {
  const cubeSignerKeysQuery = useCubeSignerKeys();
  const localKeysQuery = useLocalKeyList();

  const accounts: UnifiedAccount[] = useMemo(() => {
    const cubeSignerAccounts: CubeSignerAccount[] = (cubeSignerKeysQuery.data ?? []).map(
      (key) => ({
        id: `cubesigner/${key.id}`,
        provider: "cubesigner",
        network: "solana", // CubeSigner keys are currently only Solana
        address: key.materialId,
        key,
        isMasterKey: false, // Assume CubeSigner keys are master keys
        masterKeyId: undefined,
      }),
    );

    const localAccounts: LocalAccount[] = [];

    // Process local keys
    for (const key of localKeysQuery.data ?? []) {
      if (skipMasterKeys && key.metadata.isMasterKey) continue;
      // Determine if this is an Ethereum or Solana key based on the network in metadata
      const address = getAddress(key);

      localAccounts.push({
        id: `local/${key.id}`,
        provider: "local",
        network: key.metadata.network,
        address: address,
        isMasterKey: key.metadata.isMasterKey,
        masterKeyId: key.metadata.masterKeyId,
        key,
      });
    }

    return [...localAccounts, ...cubeSignerAccounts];
  }, [skipMasterKeys, cubeSignerKeysQuery.data, localKeysQuery.data]);

  return {
    accounts,
    isLoading: localKeysQuery.isLoading || cubeSignerKeysQuery.isLoading,
    error: localKeysQuery.error ?? cubeSignerKeysQuery.error,
  };
}

/**
 * Hook that retrieves master keys only
 */
export function useMasterKeys() {
  const { accounts, isLoading, error } = useAccounts();

  // Filter accounts to only show master keys
  const masterAccounts = accounts.filter((account) => account.isMasterKey);

  return {
    masterAccounts,
    isLoading,
    error,
  };
}

/**
 * Hook that retrieves derived keys for a given master key
 */
export function useDerivedWallets(masterAccount?: UnifiedAccount) {
  const { accounts, isLoading, error } = useAccounts();

  // Filter accounts to only show derived keys for the given master key
  const derivedWallets = masterAccount?.isMasterKey
    ? accounts.filter((acc) => !acc.isMasterKey && acc.masterKeyId === masterAccount.key.id)
    : accounts.filter((acc) => !acc.isMasterKey);

  return {
    accounts: derivedWallets,
    isLoading,
    error,
  };
}
