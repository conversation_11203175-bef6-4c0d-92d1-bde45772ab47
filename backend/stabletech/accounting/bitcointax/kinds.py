from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal

from stabletech.accounting.models import AccountedTransaction
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Transaction, Transfer


@dataclass
class ProcessedTransaction:
    id: int
    amount: Decimal
    remaining_amount: Decimal
    confirmed_at: datetime
    transaction_id: int
    transfer_id: int
    action: TransactionAction
    transaction: Transaction
    transfer: Transfer
    coin_id: int
    tx_hash: str
    flattened_txns: list[str] = field(default_factory=list)
    loan_withdrawals: list[str] = field(default_factory=list)

    def __repr__(self) -> str:
        return f"{self.id} {self.coin_id} {self.action} {self.amount} {self.remaining_amount} {self.confirmed_at} {self.tx_hash}"

    @classmethod
    def from_transaction_transfer(
        cls, transaction: Transaction, transfer: Transfer
    ) -> "ProcessedTransaction":
        return ProcessedTransaction(
            id=0,
            amount=transfer.amount,
            remaining_amount=transfer.amount,
            confirmed_at=transaction.confirmed_at,
            transaction_id=transaction.id,
            transfer_id=transfer.id,
            transfer=transfer,
            action=transaction.action,
            transaction=transaction,
            coin_id=transfer.asset_account.coin_id,
            tx_hash=transaction.tx_hash,
            loan_withdrawals=[],
        )

    @classmethod
    def from_accounted_transaction(
        cls, accounted_transaction: AccountedTransaction
    ) -> "ProcessedTransaction":
        return ProcessedTransaction(
            id=accounted_transaction.id,
            amount=accounted_transaction.amount,
            remaining_amount=accounted_transaction.remaining_amount,
            confirmed_at=accounted_transaction.confirmed_at,
            transaction_id=accounted_transaction.transfer.transaction_id,
            transfer_id=accounted_transaction.transfer_id,
            action=TransactionAction(accounted_transaction.transaction_action),
            transaction=accounted_transaction.transfer.transaction,
            transfer=accounted_transaction.transfer,
            coin_id=accounted_transaction.coin_id,
            tx_hash=accounted_transaction.tx_hash,
            flattened_txns=accounted_transaction.flattened_transactions,
            loan_withdrawals=accounted_transaction.loan_withdrawals,
        )

    def is_full(self) -> bool:
        return self.remaining_amount <= Decimal("0")

    def aggregate(self, txn: Transaction, transfer: Transfer) -> None:
        self.amount += transfer.amount
        self.remaining_amount += transfer.amount
        self.flattened_txns.append((txn.tx_hash, transfer.id))

    def consume_loan_withdrawal(self, loan_withdrawal: "ProcessedTransaction") -> None:
        if self.loan_withdrawals is None:
            self.loan_withdrawals = []
        self.loan_withdrawals.append(loan_withdrawal.id)


@dataclass
class TransactionsPerCoin:
    buys: list[ProcessedTransaction] = field(default_factory=list)
    sells: list[ProcessedTransaction] = field(default_factory=list)
    loan_withdrawals: list[ProcessedTransaction] = field(default_factory=list)


class OverMatchedBuyTransactionError(Exception):
    pass


class MatchError:
    err: str
    csv_row: int

    def __init__(self, err: str, csv_row: int):
        self.err = err
        self.csv_row = csv_row

    def __repr__(self) -> str:
        return f"MatchError({self.err!r}, {self.csv_row!r})"
