// ** React Imports
import { ReactNode, createContext, useContext } from "react";

// ** Hooks Imports
import { AuthContext } from "@/context/AuthContext";
import { AuthValuesType } from "@/types/market";

const authUserContext = createContext<AuthValuesType>({
  user: null,
  loading: false,
  logout: async () => {
    // empty
  },
  signInWithGoogle: async () => {
    // empty
  },
  signInWithEmailAndPassword: async () => {
    throw new Error("Not implemented");
  },
  createUserWithEmailAndPassword: async () => {
    throw new Error("Not implemented");
  },
});

export const FirebaseAuthProvider = ({ children }: { children: ReactNode }) => {
  const auth = AuthContext();

  return <authUserContext.Provider value={auth}>{children}</authUserContext.Provider>;
};

// custom hook to use the authUserContext and access authUser and loading
export const useAuth = () => useContext(authUserContext);
