from fastapi import APIRouter
from sqlalchemy.orm import Session

from common.conf import settings
from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from stabletech import schemas
from stabletech.auth.crud import UserManager
from stabletech.client import crud
from stabletech.indexer.crud import <PERSON>Manager, TransactionManager, WalletManager
from stabletech.indexer.models import Coin
from stabletech.market.crud import CoinManager
from stabletech.utils.fastapi import CurrentUser
from stabletech.utils.slack import post_message, stable_tech_channel

router = APIRouter()


@router.post("/client-transaction/add", response_model=schemas.ClientTransaction)
def add_client_transaction(
    client_transaction_schema: schemas.AddClientTransaction,
    db: DatabaseSession,
    user: CurrentUser,
):
    coin = CoinManager(db).get_coin_by_id(client_transaction_schema.coin_id)
    if not coin.is_usable_by_clients:
        raise HTTP400Exception("Coin is not usable")

    client_transaction_manager = crud.ClientTransactionManager(db)
    is_deposit = client_transaction_schema.is_deposit
    if not is_deposit:
        current_balance = client_transaction_manager.get_user_current_balance_for_coin(
            user.id, coin.id
        )
        if not current_balance or current_balance < client_transaction_schema.amount:
            raise HTTP400Exception(
                f"Trying to withdraw more {coin.ticker.upper()} than deposited (excluding interest)"
            )
        if not WalletManager(db).exists_for_user_on_network(
            user.id, client_transaction_schema.network_id
        ):
            raise HTTP400Exception(
                "No wallet found - please add a wallet for this Network before proceeding"
            )

    client_transaction = client_transaction_manager.add(client_transaction_schema, user=user)

    message = (
        f"New {'deposit' if is_deposit else 'withdrawal'} request of {abs(client_transaction_schema.amount)}"
        f" {coin.ticker} from {user.first_name} {user.last_name}"
    )

    post_message(channel=stable_tech_channel(), text=message)
    return client_transaction


@router.get("/client-transactions/pending", response_model=list[schemas.ClientTransaction])
def get_client_transactions(
    db: DatabaseSession,
    user: CurrentUser,
):
    return crud.ClientTransactionManager(db).get_user_client_transactions(
        user.id, filter_option="only_pending", sort_by="created_at"
    )


@router.get("/dashboard", response_model=schemas.Dashboard | schemas.SahabaDashboard)
def get_dashboard_stats(
    db: DatabaseSession,
    user: CurrentUser,
    user_id: int | None = None,
) -> schemas.Dashboard | schemas.SahabaDashboard:
    # Only allow admins to query any User
    user_id = user_id if user_id and user.is_admin else user.id

    if settings.enable_pooled_fund:
        return get_lp_dashboard(
            db,
            user_id=None if user.is_admin and user.id == user_id else user_id,
        )

    if settings.is_sahaba or settings.is_local:
        return get_sahaba_dashboard(db, user_id)

    return get_client_transactions_dashboard(db, user_id)


def get_lp_dashboard(db: Session, user_id: int | None) -> schemas.Dashboard:
    # Only get transactions that have a linked ClientTransaction
    all_transactions = TransactionManager(db).get_pooled_fund_transactions()
    dashboard_transactions = [
        schemas.TransactionForClient.from_transaction_model(transaction)
        for transaction in all_transactions
    ]

    if user_id is not None:
        share = UserManager(db).get_user_share_by_id(user_id)
        if share <= 0:
            return schemas.Dashboard(coins=[], transactions=[])
        for transaction in dashboard_transactions:
            if transaction.fees_paid:
                transaction.fees_paid *= share
            for transfer in transaction.transfers:
                transfer.amount *= share

    coins: set[Coin] = {
        transfer.asset_account.alias_coin
        for tx in all_transactions
        for transfer in tx.transfers
        if transfer.asset_account.alias_coin
    }
    return schemas.Dashboard(
        coins=[schemas.Coin.model_validate(coin) for coin in coins],
        transactions=dashboard_transactions,
    )


def get_sahaba_dashboard(db: DatabaseSession, user_id: int) -> schemas.SahabaDashboard:
    """Get dashboard data for sahaba deployment using client transactions."""
    client_transactions = crud.ClientTransactionManager(db).get_user_client_transactions(
        user_id, filter_option=None  # Include all client transactions (pending and completed)
    )
    coins: set[Coin] = set()

    for client_transaction in client_transactions:
        if client_transaction.alias_coin:
            coins.add(client_transaction.alias_coin)

    return schemas.SahabaDashboard(
        coins=[schemas.Coin.model_validate(coin) for coin in coins],
        client_transactions=client_transactions
    )


def get_client_transactions_dashboard(db: DatabaseSession, user_id: int) -> schemas.Dashboard:
    client_transactions = crud.ClientTransactionManager(db).get_user_client_transactions(
        user_id, filter_option="exclude_pending"
    )
    transactions: list[schemas.TransactionForClient] = []
    coins: set[Coin] = set()

    for client_transaction in client_transactions:
        transactions.append(
            schemas.TransactionForClient.from_client_transaction_model(client_transaction)
        )
        if client_transaction.alias_coin:
            coins.add(client_transaction.alias_coin)

    return schemas.Dashboard(
        coins=[schemas.Coin.model_validate(coin) for coin in coins], transactions=transactions
    )


@router.get("/deposit-address", response_model=str)
def get_deposit_address_for_network(
    network_id: int,
    db: DatabaseSession,
):
    wallet = WalletManager(db).get_deposit_wallet_for_network(network_id)
    if not wallet:
        raise HTTP400Exception(
            "Deposits on this network are currently disabled - please contact support"
        )
    return wallet.address


@router.get("/networks", response_model=list[schemas.Network])
def get_networks(db: DatabaseSession):
    return NetworkManager(db).get_usable_by_clients()


@router.post("/wallet/add", response_model=schemas.Wallet)
def add_wallet(
    db: DatabaseSession,
    add_wallet_schema: schemas.AddWallet,
    user: CurrentUser,
):
    wallet = WalletManager(db).add_wallet(add_wallet_schema, user.id)
    return wallet


@router.post("/wallet/edit", response_model=schemas.Wallet)
def edit_wallet(
    db: DatabaseSession,
    edit_wallet_schema: schemas.EditWallet,
    user: CurrentUser,
):
    wallet = WalletManager(db).edit(edit_wallet_schema, user.id)
    return wallet


@router.post("/wallet/archive", response_model=schemas.Wallet)
def archive_wallet(
    db: DatabaseSession,
    wallet_id: int,
    user: CurrentUser,
):
    """
    Archive a wallet by setting is_archived to true.
    Archived wallets will not be returned in the wallet list.
    """
    wallet = WalletManager(db).archive_wallet(wallet_id, user.id)
    db.commit()
    return wallet


@router.get("/wallets", response_model=list[schemas.Wallet])
def get_wallets(
    db: DatabaseSession,
    user: CurrentUser,
    network_id: int | None = None,
):
    """
    Returns all Wallets owned by the User.
    """
    network = NetworkManager(db).get_by_id(network_id) if network_id else None
    return WalletManager(db).get_all_active(network, user_id=user.id, is_archived=False)
