"""Price Alerts

Revision ID: 0054
Revises: 0053
Create Date: 2024-06-08 11:22:45.896895

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0054"
down_revision = "0053"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "poolpricealert",
        sa.<PERSON>umn("account_id", sa.Integer(), nullable=False),
        sa.Column("coin_id", sa.Integer(), nullable=False),
        sa.Column("last_triggered_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("min_price_usd", sa.Numeric(), nullable=False),
        sa.Column("max_price_usd", sa.Numeric(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["account.id"],
        ),
        sa.ForeignKeyConstraint(
            ["coin_id"],
            ["coin.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_pool_price_alert_unique_key",
        "poolpricealert",
        ["account_id", "coin_id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_poolpricealert_account_id"), "poolpricealert", ["account_id"], unique=False
    )
    op.create_index(
        op.f("ix_poolpricealert_coin_id"), "poolpricealert", ["coin_id"], unique=False
    )
    op.create_index(op.f("ix_poolpricealert_id"), "poolpricealert", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_poolpricealert_id"), table_name="poolpricealert")
    op.drop_index(op.f("ix_poolpricealert_coin_id"), table_name="poolpricealert")
    op.drop_index(op.f("ix_poolpricealert_account_id"), table_name="poolpricealert")
    op.drop_index("ix_pool_price_alert_unique_key", table_name="poolpricealert")
    op.drop_table("poolpricealert")
    # ### end Alembic commands ###
