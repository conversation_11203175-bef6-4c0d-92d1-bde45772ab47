import Spinner from "@/@core/components/spinner";
import { ButtonLink } from "@/components/Link";
import { formatDateTime, parseDateTime, validDateFormat } from "@/helpers/formatters";
import { useDebounce } from "@/hooks/debounce";
import {
  getUrlSearch,
  useIndexerTransactionsQuery,
  useIndexerTransactionsUnclassifiedQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import {
  PaginatedResultTransaction,
  Transaction,
  TransactionUnclassified,
} from "@/types/schemas";
import { AccessTimeFilled } from "@mui/icons-material";
import { Alert, ButtonGroup, TextField, ToggleButton, Tooltip } from "@mui/material";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import Stack from "@mui/material/Stack";
import * as chrono from "chrono-node";
import { useRouter } from "next/router";
import { useEffect, useRef, useState } from "react";

interface TransactionSearchProps {
  displayTransactions: boolean;
  body?: React.ReactNode;
  renderEmptyResults?: (searchQuery: string, refetch: () => void) => React.ReactNode;
  searchBarExtra?: React.ReactNode;
  header?: React.ReactNode;
  renderTransaction: (
    transaction: Transaction | TransactionUnclassified,
    isFetching: boolean,
    refetch: () => void,
  ) => React.ReactNode;
  transactionHook:
    | typeof useIndexerTransactionsQuery
    | typeof useIndexerTransactionsUnclassifiedQuery;
  onSearchChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const TransactionSearch = ({
  displayTransactions,
  header,
  body,
  renderEmptyResults,
  searchBarExtra,
  onSearchChange,
  renderTransaction,
  transactionHook,
}: TransactionSearchProps) => {
  const [searchQuery, setSearchQuery] = useState<string | null>(null);
  const [timeQuery, setTimeQuery] = useState<string | null>(null);
  const searchTextRef = useRef<HTMLInputElement | null>(null);
  const timeTextRef = useRef<HTMLInputElement | null>(null);
  const query = useQuery(searchQuery, timeQuery);

  const [isUtc, setIsUtc] = useState(false);
  const [displayTimeSearch, setDisplayTimeSearch] = useState(false);

  useEffect(() => {
    if (query.searchQuery && searchQuery === null) {
      setSearchQuery(query.searchQuery);
      setDisplayTimeSearch(false);
    }
    if (query.timeQuery && timeQuery === null) {
      setTimeQuery(query.timeQuery);
      setDisplayTimeSearch(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      searchQuery === null &&
      query.searchQuery &&
      searchTextRef.current &&
      searchTextRef.current.value === ""
    ) {
      searchTextRef.current.value = query.searchQuery;
    }
    if (
      timeQuery === null &&
      query.timeQuery &&
      timeTextRef.current &&
      timeTextRef.current.value === ""
    ) {
      timeTextRef.current.value = query.timeQuery;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timeTextRef.current, searchTextRef.current]);

  const debouncedSearchQuery = useDebounce(500, searchQuery ?? "");
  const debouncedTimeQuery = useDebounce(500, timeQuery ?? "");
  const { data, error, isFetching, refetch } = transactionHook(
    getQueryParams(query, displayTimeSearch, debouncedSearchQuery, debouncedTimeQuery),
  );

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Error loading transactions" })}
      </Alert>
    );
  }

  if (data == null) {
    return <Spinner />;
  }

  const pagination = getPagination(query, data, debouncedSearchQuery, debouncedTimeQuery);
  const transactionsData = data.items;

  return (
    <Grid container spacing={4}>
      {header}
      <Grid item xs={12}>
        <Stack direction="row" alignItems="center" gap={1}>
          {displayTimeSearch ? (
            <TextField
              sx={{ flex: 2 }}
              // eslint-disable-next-line jsx-a11y/no-autofocus
              autoFocus
              fullWidth
              label={"Search by Date, Time or Timestamp"}
              type={"search"}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                let { value } = event.target;
                if (!value) {
                  setTimeQuery("");
                  return;
                }
                // Some explorers (e.g. Etherscan) add "+UTC" to the date which
                // causes chrono to fail parsing the date as UTC. Removing the
                // `+` fixes the issue.
                value = value.replace(/(?<=\s)\+(?=UTC(?:$|\W))/u, "");

                if (value.length > 4) {
                  const format = validDateFormat(value);
                  if (format) {
                    if (value.endsWith("Z")) {
                      setIsUtc(true);
                      setTimeQuery(value);
                      return;
                    }

                    if (isUtc) {
                      setTimeQuery(value);
                    } else {
                      setTimeQuery(parseDateTime(value, format, false).utc().format(format));
                    }
                  } else if (value.endsWith("UTC")) {
                    const parsedDate = chrono.parseDate(
                      value,
                      isUtc ? { timezone: "UTC" } : undefined,
                    );
                    if (parsedDate) {
                      setTimeQuery(parsedDate.toISOString());
                      if (timeTextRef.current)
                        timeTextRef.current.value = formatDateTime(parsedDate, { utc: isUtc });
                    }
                  }
                }
              }}
              inputRef={timeTextRef}
              InputProps={{
                startAdornment: (
                  <ToggleButton
                    selected={isUtc}
                    onChange={() => {
                      const format = validDateFormat(timeTextRef.current?.value ?? "");
                      if (format) {
                        // Was in UTC, convert to local
                        if (isUtc) {
                          setTimeQuery(
                            parseDateTime(timeQuery ?? "", format, false)
                              .utc()
                              .format(format),
                          );
                        } else {
                          setTimeQuery(timeTextRef.current?.value ?? "");
                        }
                      }
                      setIsUtc(!isUtc);
                    }}
                    value=""
                    sx={{ fontSize: "small", py: 0.5, px: 1.5, mr: 2 }}
                  >
                    UTC
                  </ToggleButton>
                ),
              }}
            />
          ) : (
            <TextField
              sx={{ flex: 2 }}
              // eslint-disable-next-line jsx-a11y/no-autofocus
              autoFocus
              fullWidth
              label={"Search by Transaction or Transfer data"}
              type={"search"}
              inputRef={searchTextRef}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                const value = event.target.value ?? "";
                setSearchQuery(value);
                if (onSearchChange) onSearchChange(event);
              }}
            />
          )}
          <Tooltip title="Search by Time" placement="top">
            <IconButton
              sx={{ flex: 0 }}
              size={"large"}
              onClick={() => {
                setDisplayTimeSearch(!displayTimeSearch);
                setTimeQuery("");
                if (timeTextRef.current) {
                  timeTextRef.current.select();
                  timeTextRef.current.value = "";
                }
                setSearchQuery("");
                if (searchTextRef.current) {
                  searchTextRef.current.select();
                  searchTextRef.current.value = "";
                }
              }}
            >
              <AccessTimeFilled />
            </IconButton>
          </Tooltip>
          {searchBarExtra}
        </Stack>
      </Grid>
      {body}

      <Grid item xs={12}>
        <Grid container spacing={4} justifyContent={"center"}>
          {displayTransactions &&
            transactionsData.map((transaction: Transaction) =>
              renderTransaction(transaction, isFetching, refetch),
            )}
          {searchQuery &&
            transactionsData.length === 0 &&
            renderEmptyResults &&
            renderEmptyResults(searchQuery, refetch)}
          {displayTransactions && (
            <Grid item xs={12} display="flex" justifyContent="center">
              <ButtonGroup variant="outlined" aria-label="pagination controls">
                <ButtonLink
                  sx={{ width: 110 }}
                  disabled={pagination.prevUrl === ""}
                  href={pagination.prevUrl}
                >
                  Newer
                </ButtonLink>
                <ButtonLink
                  sx={{ width: 110 }}
                  disabled={pagination.nextUrl === ""}
                  href={pagination.nextUrl}
                >
                  Older
                </ButtonLink>
              </ButtonGroup>
            </Grid>
          )}
        </Grid>
      </Grid>
    </Grid>
  );
};

export default TransactionSearch;

function useQuery(searchQuery: string | null, timeQuery: string | null): ParsedQuery {
  const { query, replace } = useRouter();
  const parsedQuery = parseQuery(query);

  // Keep url tidy, order and remove invalid query params
  useEffect(() => {
    if (!searchQuery && !timeQuery) {
      replace("", undefined, { shallow: true });
      return;
    }
    const search = getUrlSearch([
      ["fromId", parsedQuery.fromId],
      ["page", parsedQuery.page],
      ["limit", parsedQuery.limit],
      ["search_query", searchQuery ?? parsedQuery.searchQuery],
      ["time_query", timeQuery ?? parsedQuery.timeQuery],
    ]);
    replace(search, undefined, { shallow: true });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, timeQuery]);

  return parsedQuery;
}

type ParsedQuery = ReturnType<typeof parseQuery>;

function parseQuery(query: Record<string, string | string[] | undefined>): {
  fromId: number | undefined;
  page: "next" | "prev" | undefined;
  limit: number | undefined;
  searchQuery: string | undefined;
  timeQuery: string | undefined;
} {
  const fromId = Number(query.fromId);
  const isFromIdValid = Number.isInteger(fromId) && fromId > 0;

  const { page } = query;
  const isPageValid = page == "next" || page == "prev";

  const limit = Number(query.limit);
  const isLimitValid = Number.isInteger(limit) && limit > 0;

  const searchQuery = query.search_query;
  const isSearchQueryValid = typeof searchQuery === "string" && searchQuery !== "";

  const timeQuery = query.time_query;
  const isTimeQueryValid = typeof timeQuery === "string" && timeQuery !== "";

  return {
    fromId: isFromIdValid ? fromId : undefined,
    page: isFromIdValid && isPageValid ? page : undefined,
    limit: isLimitValid ? limit : undefined,
    searchQuery: isSearchQueryValid ? searchQuery : undefined,
    timeQuery: isTimeQueryValid ? timeQuery : undefined,
  };
}

function getPagination(
  query: ParsedQuery,
  data: PaginatedResultTransaction,
  searchQuery: string | null,
  timeQuery: string | null,
) {
  if (data.items.length === 0) {
    return { prevUrl: "", nextUrl: "" };
  }
  if (
    (searchQuery && query.searchQuery != searchQuery) ||
    (timeQuery && query.timeQuery != timeQuery)
  ) {
    return {
      prevUrl: data.prev
        ? getUrlSearch([
            ["fromId", data.items[0].id],
            ["page", "prev"],
            ["limit", query.limit],
            ["search_query", searchQuery],
            ["time_query", timeQuery],
          ])
        : "",
      nextUrl: data.next
        ? getUrlSearch([
            ["fromId", data.items.at(-1)?.id],
            ["page", "next"],
            ["limit", query.limit],
            ["search_query", searchQuery],
            ["time_query", timeQuery],
          ])
        : "",
    };
  }
  return {
    prevUrl: data.prev
      ? getUrlSearch([
          ["fromId", data.items[0].id],
          ["page", "prev"],
          ["limit", query.limit],
          ["search_query", query.searchQuery],
          ["time_query", query.timeQuery],
        ])
      : "",
    nextUrl: data.next
      ? getUrlSearch([
          ["fromId", data.items.at(-1)?.id],
          ["page", "next"],
          ["limit", query.limit],
          ["search_query", query.searchQuery],
          ["time_query", query.timeQuery],
        ])
      : "",
  };
}

function getQueryParams(
  query: ParsedQuery,
  displayTimeSearch: boolean,
  searchQuery: string,
  timeQuery: string,
) {
  if (!(searchQuery || timeQuery)) {
    return query;
  }
  if (
    (query.searchQuery && query.searchQuery === searchQuery) ||
    (query.timeQuery && query.timeQuery === timeQuery)
  ) {
    return query;
  }
  return {
    searchQuery: displayTimeSearch ? "" : searchQuery,
    timeQuery: displayTimeSearch ? timeQuery : "",
  };
}
