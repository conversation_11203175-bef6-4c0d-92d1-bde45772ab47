"use client";

import { AlertError } from "@/lib/components/alert";
import * as Select from "@/lib/components/select";
import * as ToggleGroup from "@/lib/components/toggle-group";
import { UserDisplay } from "@/lib/components/user-display";
import { getErrorMessage } from "@/lib/error";
import { currencyFormatter } from "@/lib/formatters";
import { HEIST_EVENTS, HeistSpecies, useGetClaimsHistoryQuery } from "@/lib/heist";
import { TopClaimItem } from "@/types/schemas";
import { ArrowDownIcon, ArrowUpIcon, Loader2Icon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useCallback, useEffect, useMemo, useState } from "react";

type SortableColumn =
  | "total_amount_claimed"
  | "duration_hours"
  | "base_multiplier"
  | "ended_at";
type SortDirection = "asc" | "desc";

interface SortConfig {
  by: SortableColumn;
  order: SortDirection;
}

// Comparator functions for each sort type
const comparators: Readonly<
  Record<SortableColumn, (a: TopClaimItem, b: TopClaimItem) => number>
> = {
  total_amount_claimed: (a, b) => {
    const aNum = Number.parseFloat(a.total_amount_claimed) || 0;
    const bNum = Number.parseFloat(b.total_amount_claimed) || 0;
    return aNum - bNum;
  },
  duration_hours: (a, b) => a.duration_hours - b.duration_hours,
  base_multiplier: (a, b) => a.base_multiplier - b.base_multiplier,
  ended_at: (a, b) => new Date(a.ended_at).getTime() - new Date(b.ended_at).getTime(),
};

function createSortingComparator(sort: SortConfig) {
  const comparator = comparators[sort.by];
  const directionMultiplier = sort.order === "asc" ? 1 : -1;
  return (a: TopClaimItem, b: TopClaimItem): number => comparator(a, b) * directionMultiplier;
}

function getDefaultSort(isOwn: boolean): SortConfig {
  return {
    by: isOwn ? "ended_at" : "total_amount_claimed",
    order: "desc",
  };
}

const timeframeOptions = [
  { value: "daily", label: "Today" },
  { value: "weekly", label: "This Week" },
  { value: "monthly", label: "This Month" },
  { value: "all-time", label: "All-Time" },
] as const;

type TimeframeOption = (typeof timeframeOptions)[number]["value"];

interface ClaimsHistoryTableProps {
  isOwn: boolean;
}

const PAGE_SIZE = 20;

const allEventIds = Object.keys(HEIST_EVENTS)
  .map((id) => Number.parseInt(id))
  .filter((id): id is number => !Number.isNaN(id));

const allLocationIds = [31, 32, 33] as const;
type LocationId = (typeof allLocationIds)[number];

function validateEventIds(values: string[]): number[] {
  const validNumbers = values
    .map((v) => Number.parseInt(v))
    .filter((num): num is number => !Number.isNaN(num) && allEventIds.includes(num));
  return validNumbers.length ? validNumbers : [];
}

function validateLocationIds(values: string[]): LocationId[] {
  const validNumbers = values
    .map((v) => Number.parseInt(v))
    .filter(
      (num): num is LocationId =>
        !Number.isNaN(num) && allLocationIds.includes(num as LocationId),
    );
  return validNumbers.length ? validNumbers : [];
}

export function ClaimsHistoryTable({ isOwn }: ClaimsHistoryTableProps) {
  const [timeframe, setTimeframe] = useState<TimeframeOption>("daily");
  const [showFilters, setShowFilters] = useState(false);
  const [sort, setSort] = useState<SortConfig>(() => getDefaultSort(isOwn));

  useEffect(() => {
    setSort(getDefaultSort(isOwn));
  }, [isOwn]);
  const [page, setPage] = useState(1);
  const allSpecies = ["Chimp", "Orangutan", "Gorilla"] as const;

  const [selectedSpecies, setSelectedSpecies] = useState<HeistSpecies[]>([]);
  const [selectedEventIds, setSelectedEventIds] = useState<number[]>([]);
  const [selectedLocationIds, setSelectedLocationIds] = useState<LocationId[]>([]);

  const { data, error, isLoading, isFetching } = useGetClaimsHistoryQuery({
    timeframe,
    isOwn,
    sortBy: sort.by,
    sortOrder: sort.order,
    species:
      selectedSpecies.length !== allSpecies.length && selectedSpecies.length > 0
        ? selectedSpecies
        : undefined,
    eventId:
      selectedEventIds.length !== allEventIds.length && selectedEventIds.length > 0
        ? selectedEventIds
        : undefined,
    locationId:
      selectedLocationIds.length !== allLocationIds.length && selectedLocationIds.length > 0
        ? selectedLocationIds
        : undefined,
  });

  const sortedItems = useMemo(() => {
    if (!data?.items) return [];
    return [...data.items].sort(createSortingComparator(sort));
  }, [data?.items, sort]);

  const totalPages = useMemo(() => {
    if (!data?.items.length) return 0;
    return Math.ceil(data.items.length / PAGE_SIZE);
  }, [data?.items.length]);

  const paginatedItems = useMemo(() => {
    if (!sortedItems.length) return [];
    const start = (page - 1) * PAGE_SIZE;
    return sortedItems.slice(start, start + PAGE_SIZE);
  }, [sortedItems, page]);

  const handlePrevPage = useCallback(() => {
    setPage((p) => Math.max(1, p - 1));
  }, []);

  const handleNextPage = useCallback(() => {
    setPage((p) => Math.min(totalPages, p + 1));
  }, [totalPages]);

  const handleTimeframeChange = useCallback((value: TimeframeOption) => {
    setTimeframe(value);
    setPage(1); // Reset to first page when timeframe changes
  }, []);

  const handleSort = useCallback((column: SortableColumn) => {
    setSort((prev) => ({
      by: column,
      order: prev.by === column && prev.order === "desc" ? "asc" : "desc",
    }));
  }, []);

  const SortIcon = useCallback(
    ({ column }: { column: SortableColumn }) => {
      if (sort.by !== column) return null;
      return sort.order === "asc" ? (
        <ArrowUpIcon className="inline h-4 w-4" />
      ) : (
        <ArrowDownIcon className="inline h-4 w-4" />
      );
    },
    [sort],
  );

  const SortableHeader = useCallback(
    ({ column, children }: { column: SortableColumn; children: React.ReactNode }) => {
      const handleClick = () => {
        handleSort(column);
      };
      return (
        <th
          className="cursor-pointer px-3 py-4 text-center font-medium transition-colors hover:bg-muted/40 sm:px-6"
          onClick={handleClick}
        >
          <div className="flex flex-col items-center gap-1">
            <div className="text-muted-foreground/90">{children}</div>
            <SortIcon column={column} />
          </div>
        </th>
      );
    },
    [handleSort, SortIcon],
  );

  if (error) {
    return <AlertError>{getErrorMessage(error.message)}</AlertError>;
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-6">
        {isOwn && (
          <div className="space-y-4">
            <div className="flex items-center justify-between gap-4">
              <button
                onClick={() => {
                  setShowFilters((prev) => !prev);
                }}
                className="flex items-center gap-2 rounded-lg bg-muted/30 px-4 py-2 text-sm font-medium text-muted-foreground transition-colors hover:bg-muted/50"
              >
                {showFilters ? "Hide" : "Show"} Filters
                {showFilters ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
              </button>
              <div className="text-sm text-muted-foreground/80 italic">
                Click on any NFT&apos;s image to view its most recent claims
              </div>
            </div>

            {showFilters && (
              <div className="space-y-6 rounded-lg border border-border/50 bg-card/50 p-6 shadow-xs backdrop-blur-xs lg:grid lg:grid-cols-2 lg:gap-6 lg:space-y-0">
                <div className="space-y-4 lg:pr-6">
                  <div className="relative flex items-center lg:justify-center">
                    <div className="text-base font-semibold text-foreground">Species</div>
                    <div className="absolute right-0 flex gap-2">
                      <button
                        onClick={() => {
                          setSelectedSpecies([...allSpecies]);
                        }}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        All
                      </button>
                      <span className="text-xs text-muted-foreground">/</span>
                      <button
                        onClick={() => {
                          setSelectedSpecies([]);
                        }}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        None
                      </button>
                    </div>
                  </div>
                  <ToggleGroup.Root
                    type="multiple"
                    value={selectedSpecies.map(String)}
                    onValueChange={(values: string[]) => {
                      const validValues = values.filter((v): v is HeistSpecies =>
                        allSpecies.includes(v as HeistSpecies),
                      );
                      setSelectedSpecies(validValues.length ? validValues : []);
                    }}
                    className="flex flex-wrap gap-2"
                  >
                    {allSpecies.map((species) => (
                      <ToggleGroup.Item key={species} value={species}>
                        {species}
                      </ToggleGroup.Item>
                    ))}
                  </ToggleGroup.Root>
                </div>

                <div className="space-y-4">
                  <div className="relative flex items-center lg:justify-center">
                    <div className="text-base font-semibold text-foreground">Locations</div>
                    <div className="absolute right-0 flex gap-2">
                      <button
                        onClick={() => {
                          setSelectedLocationIds([...allLocationIds]);
                        }}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        All
                      </button>
                      <span className="text-xs text-muted-foreground">/</span>
                      <button
                        onClick={() => {
                          setSelectedLocationIds([]);
                        }}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        None
                      </button>
                    </div>
                  </div>
                  <ToggleGroup.Root
                    type="multiple"
                    value={selectedLocationIds.map(String)}
                    onValueChange={(values: string[]) => {
                      setSelectedLocationIds(validateLocationIds(values));
                    }}
                    className="flex flex-wrap gap-2"
                  >
                    {[...allLocationIds].map((id) => (
                      <ToggleGroup.Item key={id} value={String(id)}>
                        {id === 33
                          ? "Federal Reserve"
                          : id === 31
                            ? "Downtown Bank"
                            : "West Coast Bank"}
                      </ToggleGroup.Item>
                    ))}
                  </ToggleGroup.Root>
                </div>

                <div className="space-y-4 lg:col-span-2">
                  <div className="relative flex items-center lg:justify-center">
                    <div className="text-base font-semibold text-foreground">Events</div>
                    <div className="absolute right-0 flex gap-2">
                      <button
                        onClick={() => {
                          setSelectedEventIds([...allEventIds]);
                        }}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        All
                      </button>
                      <span className="text-xs text-muted-foreground">/</span>
                      <button
                        onClick={() => {
                          setSelectedEventIds([]);
                        }}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        None
                      </button>
                    </div>
                  </div>
                  <ToggleGroup.Root
                    type="multiple"
                    value={selectedEventIds.map(String)}
                    onValueChange={(values: string[]) => {
                      setSelectedEventIds(validateEventIds(values));
                    }}
                    className="flex flex-wrap gap-2"
                  >
                    {Object.entries(HEIST_EVENTS)
                      .sort(([, a], [, b]) => a.order - b.order)
                      .map(([id, event]) => (
                        <ToggleGroup.Item key={id} value={id}>
                          {event.name}
                        </ToggleGroup.Item>
                      ))}
                  </ToggleGroup.Root>
                </div>
              </div>
            )}
          </div>
        )}
        <div className="flex items-center justify-between gap-4">
          <div className="max-w-xs flex-1">
            <Select.Root value={timeframe} onValueChange={handleTimeframeChange}>
              <Select.Trigger
                placeholder="Select timeframe"
                className="w-full border-border/50 bg-card/50"
              />
              <Select.Content>
                {timeframeOptions.map((option) => (
                  <Select.Item key={option.value} value={option.value}>
                    {option.label}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
          </div>
          <div className="flex items-center gap-6">
            <button
              onClick={handlePrevPage}
              disabled={page === 1 || !data?.items.length}
              className="rounded-lg px-4 py-2 text-sm font-medium transition-colors hover:bg-muted/50 disabled:opacity-50 disabled:hover:bg-transparent"
            >
              Previous
            </button>
            <div className="shrink-0 text-sm font-medium whitespace-nowrap">
              {data?.items.length ? (
                <span>
                  <span className="text-muted-foreground/70">
                    Page {page} of {totalPages}
                  </span>
                </span>
              ) : null}
            </div>
            <button
              onClick={handleNextPage}
              disabled={page === totalPages || !data?.items.length}
              className="rounded-lg px-4 py-2 text-sm font-medium transition-colors hover:bg-muted/50 disabled:opacity-50 disabled:hover:bg-transparent"
            >
              Next
            </button>
          </div>
        </div>
      </div>
      <div className="relative -mx-4 overflow-hidden rounded-xl border border-border/80 bg-card/95 shadow-xs backdrop-blur-xs sm:mx-0">
        {isFetching && data && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-background/60 backdrop-blur-xs">
            <div className="rounded-lg bg-card/95 px-6 py-4 shadow-lg">
              <Loader2Icon className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          </div>
        )}
        <div className="overflow-x-auto">
          <table className="w-full text-left text-sm">
            <thead className="bg-muted/30 text-muted-foreground/90">
              <tr>
                <th className="px-3 py-4 text-center font-medium sm:px-6">
                  <div className="flex flex-col items-center gap-1">
                    <div className="text-muted-foreground/90">
                      <span className="block">Event</span>
                    </div>
                  </div>
                </th>
                <th className="px-3 py-4 text-center font-medium sm:px-6">
                  <div className="flex flex-col items-center gap-1">
                    <div className="text-muted-foreground/90">
                      <span className="block">NFT</span>
                    </div>
                  </div>
                </th>
                <SortableHeader column="total_amount_claimed">
                  <span className="block">NANA</span>
                  <span className="block">Claimed</span>
                </SortableHeader>
                <SortableHeader column="duration_hours">
                  <span className="block">Duration</span>
                  <span className="block">(hours)</span>
                </SortableHeader>
                <th className="px-3 py-4 text-center font-medium sm:px-6">
                  <div className="flex flex-col items-center gap-1">
                    <div className="text-muted-foreground/90">
                      <span className="block">Base</span>
                      <span className="block">NANA</span>
                    </div>
                  </div>
                </th>
                <SortableHeader column="base_multiplier">
                  <span className="block">Multiplier</span>
                  <span className="block">to Base</span>
                </SortableHeader>
                {isOwn ? (
                  <SortableHeader column="ended_at">
                    <span className="block">At</span>
                  </SortableHeader>
                ) : (
                  <th className="hidden px-3 py-4 text-center sm:table-cell sm:px-6">
                    <div className="flex flex-col items-center gap-1">
                      <div className="text-muted-foreground/90">
                        <span className="block">At</span>
                      </div>
                    </div>
                  </th>
                )}
                {!isOwn && (
                  <th className="hidden px-3 py-4 text-center sm:px-6 xl:table-cell">
                    <div className="flex flex-col items-center gap-1">
                      <div className="text-muted-foreground/90">
                        <span className="block">User</span>
                      </div>
                    </div>
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="divide-y divide-border/60">
              {isLoading || !data ? (
                <TableLoadingRows />
              ) : paginatedItems.length ? (
                paginatedItems.map((item) => (
                  <TableRow
                    key={`${item.address}-${item.ended_at}`}
                    item={item}
                    isOwn={isOwn}
                  />
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-center text-muted-foreground">
                    No data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

function TableLoadingRows() {
  return (
    <>
      {Array.from({ length: 5 }).map((_, i) => (
        <tr key={i} className="animate-pulse">
          <td colSpan={8} className="px-6 py-4">
            <div className="flex items-center justify-center space-x-6">
              <div className="h-8 w-24 rounded-md bg-muted/30" />
              <div className="h-10 w-10 rounded-full bg-muted/30" />
              <div className="h-8 w-32 rounded-md bg-muted/30" />
              <div className="hidden h-8 w-20 rounded-md bg-muted/30 sm:block" />
              <div className="hidden h-8 w-24 rounded-md bg-muted/30 sm:block" />
              <div className="hidden h-8 w-20 rounded-md bg-muted/30 sm:block" />
            </div>
          </td>
        </tr>
      ))}
    </>
  );
}

const rtf = new Intl.RelativeTimeFormat("en", { style: "short" });

function getRelativeTimeString(date: Date): string {
  const now = new Date();
  const diffInSeconds = (now.getTime() - date.getTime()) / 1000;

  if (diffInSeconds < 60) return rtf.format(-Math.floor(diffInSeconds), "seconds");
  if (diffInSeconds < 3600) return rtf.format(-Math.floor(diffInSeconds / 60), "minutes");
  if (diffInSeconds < 86_400) return rtf.format(-Math.floor(diffInSeconds / 3600), "hours");
  if (diffInSeconds < 2_592_000) return rtf.format(-Math.floor(diffInSeconds / 86_400), "days");
  if (diffInSeconds < 31_536_000)
    return rtf.format(-Math.floor(diffInSeconds / 2_592_000), "months");
  return rtf.format(-Math.floor(diffInSeconds / 31_536_000), "years");
}

function TableRow({ item, isOwn }: { item: TopClaimItem; isOwn: boolean }) {
  const amount = Number(item.total_amount_claimed);

  return (
    <tr className="group transition-colors duration-200 hover:bg-muted/30">
      <td
        className={`px-3 py-4 text-center sm:px-6 ${
          HEIST_EVENTS[item.event_id]?.isBad ||
          amount < 0 ||
          (amount === 0 && !HEIST_EVENTS[item.event_id]?.isBad)
            ? "text-red-500/90"
            : "text-green-500/90"
        } font-medium`}
      >
        <div>{HEIST_EVENTS[item.event_id]?.name ?? "Unknown"}</div>
        {item.is_ambush ? (
          <div className="text-xs font-normal opacity-80">Ambushed</div>
        ) : amount === 0 && !HEIST_EVENTS[item.event_id]?.isBad ? (
          <div className="text-xs font-normal opacity-80">Captured</div>
        ) : null}
      </td>
      <td className="px-3 py-4 text-center sm:px-6">
        {item.nft_image_url &&
          (isOwn && !item.is_ambush ? (
            <Link href={`/heists/nft?id=${item.address}`} className="block">
              <div className="relative mx-auto h-12 w-12 overflow-hidden rounded-full transition-all duration-200 group-hover:scale-110 group-hover:shadow-lg">
                <Image
                  src={item.nft_image_url}
                  alt=""
                  fill
                  className="object-cover transition-opacity group-hover:opacity-90"
                  sizes="48px"
                  unoptimized
                />
              </div>
            </Link>
          ) : (
            <div className="relative mx-auto h-12 w-12 overflow-hidden rounded-full ring-1 ring-border/10">
              <Image
                src={item.nft_image_url}
                alt=""
                fill
                className="object-cover"
                sizes="48px"
                unoptimized
              />
            </div>
          ))}
      </td>
      <td className="px-3 py-4 text-center sm:px-6">
        <div className="font-medium tracking-tight">
          {amount === 0 ? "-" : Math.floor(amount)}
        </div>
        {amount > 0 && (
          <div className="text-xs text-muted-foreground/70">
            {currencyFormatter.format(amount / item.duration_hours)} / hr.
          </div>
        )}
      </td>
      <td className="px-3 py-4 text-center font-medium tracking-tight sm:px-6">
        {item.duration_hours.toFixed(2)}
      </td>
      <td className="px-3 py-4 text-center sm:px-6">
        <div className="font-medium tracking-tight">
          {currencyFormatter.format(Number(item.total_amount_emitted))}
        </div>
      </td>
      <td className="px-3 py-4 text-center font-medium tracking-tight sm:px-6">
        {item.base_multiplier === 0 ? "-" : item.base_multiplier.toFixed(2)}
      </td>
      <td className="hidden px-3 py-4 text-center sm:table-cell sm:px-6">
        <div className="font-medium tracking-tight">
          {getRelativeTimeString(new Date(item.ended_at))}
        </div>
        <div className="text-sm text-muted-foreground/70">@ {item.location_name}</div>
      </td>
      {!isOwn && (
        <td className="hidden px-3 py-4 text-center hover:bg-transparent sm:px-6 xl:table-cell">
          <UserDisplay kind={item.wallet_kind} value={item.wallet_value} showOwnLabel={false} />
        </td>
      )}
    </tr>
  );
}
