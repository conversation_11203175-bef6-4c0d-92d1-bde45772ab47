import { Alert } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import * as Dialog from "@/components/ui/dialog";
import { getErrorDetail } from "@/store/services/helpers";
import { useState } from "react";

interface State {
  readonly error: string;
  readonly isLoading: boolean;
  readonly open: boolean;
}

const INITIAL_STATE: State = { error: "", isLoading: false, open: false };
const OPEN_STATE: State = { error: "", isLoading: false, open: true };
const LOADING_STATE: State = { error: "", isLoading: true, open: true };

interface ConfirmationButtonProps extends Dialog.DialogTriggerProps {
  readonly action: (() => void) | (() => Promise<unknown>);
  readonly message: React.ReactNode;
  readonly title: string;
  readonly confirmLabel?: string | undefined;
}

export function ConfirmationButton(props: ConfirmationButtonProps) {
  const { action, message, title, confirmLabel = "Confirm", ...triggerProps } = props;
  const [state, setState] = useState(INITIAL_STATE);

  const close = () => {
    setState((prev) => ({ ...prev, open: false }));
  };

  async function runAction() {
    try {
      setState(LOADING_STATE);
      await action();
      close();
    } catch (error) {
      setState({
        error: getErrorDetail(error, { fallback: "Unexpected error" }),
        isLoading: false,
        open: true,
      });
    }
  }

  return (
    <Dialog.Root
      open={state.open}
      onOpenChange={(isOpen) => {
        if (isOpen) setState(OPEN_STATE);
        else close();
      }}
    >
      <Dialog.Trigger {...triggerProps} />
      <Dialog.Content title={title} aria-describedby="">
        {message}
        <div className="mt-6">
          {state.error && (
            <Alert variant="error" className="mb-3">
              {state.error}
            </Alert>
          )}
          <div className="flex gap-4">
            <Button variant="secondary" disabled={state.isLoading} onClick={close}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              disabled={state.isLoading}
              onClick={() => {
                if (state.isLoading) return;
                void runAction();
              }}
            >
              {confirmLabel}
            </Button>
          </div>
        </div>
      </Dialog.Content>
    </Dialog.Root>
  );
}
