import psycopg.errors
from alembic import config as alembic_config
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError

from common.psql import engine

# Run Alembic migrations for PostgreSQL
alembic_config.main(
    argv=["--raiseerr", "-c", "stabletech/alembic/alembic.ini", "upgrade", "head"]
)

# Create database for Metabase
with engine.connect().execution_options(isolation_level="AUTOCOMMIT") as connection:
    try:
        connection.execute(text("CREATE DATABASE metabase"))
    except ProgrammingError as e:
        if not isinstance(e.orig, psycopg.errors.DuplicateDatabase):
            raise
