import spinner from "@/lib/components/assets/spinner.svg";
import Image from "next/image";

export function Spinner({
  className,
  priority = false,
  size = 20,
}: {
  className?: string;
  priority?: boolean;
  size?: number;
}) {
  return (
    <div role="status" className={className}>
      <span className="sr-only">Loading…</span>
      <Image
        className="animate-spin"
        src={spinner}
        alt=""
        width={size}
        height={size}
        priority={priority}
      />
    </div>
  );
}
