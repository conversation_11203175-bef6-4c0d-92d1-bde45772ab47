import * as <PERSON>di<PERSON><PERSON><PERSON><PERSON> from "@radix-ui/react-label";
import { cva, cx, VariantProps } from "class-variance-authority";

export const label = cva("font-medium", {
  variants: {
    size: {
      sm: "text-xs",
      md: "text-sm",
      lg: "text-base",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

export interface LabelProps
  extends React.ComponentProps<typeof RadixLabel.Root>,
    VariantProps<typeof label> {}

export function Label(props: LabelProps) {
  return <RadixLabel.Root {...props} className={label(props)} />;
}

type ValidationErrorProps = Omit<React.ComponentProps<"p">, "role">;

export function ValidationError(props: ValidationErrorProps) {
  return props.children ? (
    <p
      {...props}
      className={cx(
        "w-full rounded-md bg-red-50/80 px-1.5 py-0.5 text-xs dark:bg-red-900/40",
        props.className,
      )}
      role="alert"
    />
  ) : null;
}
