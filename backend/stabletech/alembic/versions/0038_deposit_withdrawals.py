"""Deposit Withdrawals

Revision ID: 0038
Revises: 0037
Create Date: 2023-09-28 06:33:29.839940

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0038"
down_revision = "0037"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("clienttransaction", sa.<PERSON>umn("is_deposit", sa.<PERSON>(), nullable=True))
    op.add_column("clienttransaction", sa.Column("network_id", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_clienttransaction_network_id"),
        "clienttransaction",
        ["network_id"],
        unique=False,
    )
    op.create_foreign_key(None, "clienttransaction", "network", ["network_id"], ["id"])
    op.add_column("wallet", sa.Column("is_usable_by_clients", sa.<PERSON>(), nullable=True))
    op.create_index(
        op.f("ix_wallet_is_usable_by_clients"), "wallet", ["is_usable_by_clients"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_wallet_is_usable_by_clients"), table_name="wallet")
    op.drop_column("wallet", "is_usable_by_clients")
    op.drop_constraint(None, "clienttransaction", type_="foreignkey")
    op.drop_index(op.f("ix_clienttransaction_network_id"), table_name="clienttransaction")
    op.drop_column("clienttransaction", "network_id")
    op.drop_column("clienttransaction", "is_deposit")
    # ### end Alembic commands ###
