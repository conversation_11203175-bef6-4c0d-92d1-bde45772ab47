from sendgrid.helpers.mail import From, Mail, To

from stabletech import schemas
from stabletech.auth.models import User
from stabletech.client.models import ClientTransaction
from stabletech.mail import client


def classified_client_transaction_emails(client_transaction: ClientTransaction) -> None:
    """Handle emails when a ClientTransaction is classified."""
    transaction = schemas.TransactionForClient.from_client_transaction_model(client_transaction)
    match transaction.client_action:
        case "Deposit":
            send_client_deposit_email(client_transaction)
        case "Withdrawal":
            send_client_withdrawal_email(client_transaction)
        case "Interest Payment":
            send_client_interest_payment_email(client_transaction)
        case _:
            pass


default_from_address = From(email="<EMAIL>", name="Stable Tech")


def get_user_address(user: User) -> To:
    """Get the email address for a user."""
    name = " ".join(n for n in (user.first_name, user.last_name) if n).strip()
    if not name:
        name = None
    return To(email=user.email, name=name)


def get_transaction_template_data(client_transaction):
    return {
        "coin": client_transaction.coin.name,
        "amount": f"{client_transaction.amount:.12f}".rstrip("0").rstrip("."),
        "ticker": client_transaction.coin.ticker.upper(),
        "hash": client_transaction.transaction.tx_hash,
        "network": client_transaction.transaction.network.name,
    }


def send_client_deposit_email(client_transaction: ClientTransaction) -> None:
    """Send an email to notify the user about a deposit."""
    user_address = get_user_address(client_transaction.user)
    message = Mail(from_email=default_from_address, to_emails=user_address)
    message.dynamic_template_data = {
        "name": user_address.name,
        "transaction": get_transaction_template_data(client_transaction),
    }
    message.template_id = "d-b7eefba2fd214bf79cb7e29705b95d17"
    client.send_email(message)


def send_client_withdrawal_email(client_transaction: ClientTransaction) -> None:
    """Send an email to notify the user about a withdrawal."""
    user_address = get_user_address(client_transaction.user)
    message = Mail(from_email=default_from_address, to_emails=user_address)
    message.dynamic_template_data = {
        "name": user_address.name,
        "transaction": get_transaction_template_data(client_transaction),
    }
    message.template_id = "d-efd7d66ef741497da6dbfa989fed472b"
    client.send_email(message)


def send_client_interest_payment_email(client_transaction: ClientTransaction) -> None:
    """Send an email to notify the user about an interest payment."""
    user_address = get_user_address(client_transaction.user)
    message = Mail(from_email=default_from_address, to_emails=user_address)
    message.dynamic_template_data = {
        "name": user_address.name,
        "transactions": [get_transaction_template_data(client_transaction)],
    }
    message.template_id = "d-d67ab7c2c90345a280e453dba008871c"
    client.send_email(message)
