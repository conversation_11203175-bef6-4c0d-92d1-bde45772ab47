@import "tailwindcss";

@theme {
  --breakpoint-xs: 375px;
}

/* Custom styles to enhance the landing page */

html {
  scroll-behavior: smooth;
}

.cx {
  @apply mx-auto w-full max-w-[540px] px-4 sm:max-w-xl sm:px-6 md:max-w-3xl md:px-8 lg:max-w-5xl lg:px-10;
}

/* Smooth fade-in animation for main content */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

/* Wave animation for the background */
@keyframes wave {
  0% {
    transform: translateX(0) translateY(0);
  }
  50% {
    transform: translateX(-25px) translateY(10px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}

.wave-slow {
  animation: wave 18s ease-in-out infinite;
}

.wave-slower {
  animation: wave 24s ease-in-out infinite;
}

/* Mobile menu styles */
#mobile-menu-button {
  @apply flex h-9 w-9 items-center justify-center rounded-full bg-blue-100/30 text-sm font-medium text-blue-600 transition;
}

#mobile-menu-button:hover {
  @apply bg-blue-100/50 text-blue-700;
}

#mobile-dropdown {
  @apply absolute top-full right-0 z-50 mt-1 min-w-32 origin-top-right flex-col rounded-md border-1 border-gray-100 bg-white shadow-lg transition-all duration-200 focus:outline-none;
}

#mobile-dropdown a {
  @apply block px-5 py-3 text-sm text-gray-700 first:rounded-t-md last:rounded-b-md hover:bg-gray-100;
}
