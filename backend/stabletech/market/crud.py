from typing import Sequence

from sqlalchemy import Select, delete, func, or_, select, update

from common.exceptions import HTTP400Exception
from stabletech.indexer.models import <PERSON>ccount, <PERSON><PERSON><PERSON><PERSON><PERSON>, Coin
from stabletech.market.models import Alert
from stabletech.utils.models import BaseManager


class CoinManager(BaseManager[Coin]):
    model = Coin

    def add_coins(self, coins: list[Coin]):
        if not coins:
            return

        # Create a mapping of uid to coin object for easy lookup
        coins_by_uid = {coin.uid: coin for coin in coins}

        # Get existing coins
        existing_coins = self.db.scalars(
            select(Coin).where(Coin.uid.in_(coins_by_uid.keys()))
        ).all()

        # Update existing coins
        for existing_coin in existing_coins:
            new_coin = coins_by_uid[existing_coin.uid]
            existing_coin.name = new_coin.name
            existing_coin.ticker = new_coin.ticker
            existing_coin.market_cap_usd = new_coin.market_cap_usd
            # Remove from mapping so we know what's left are new coins
            del coins_by_uid[existing_coin.uid]

        # Add new coins
        if coins_by_uid:
            self.db.bulk_save_objects(coins_by_uid.values())

        self.db.commit()

    def delete(self, coin_id: int) -> None:
        # Delete any associated Accounts and free up their addresses to be re-used if necessary
        self.db.execute(delete(Account).where(Account.coin_id == coin_id))
        self.db.execute(delete(Coin).where(Coin.id == coin_id))

    def get_coin_by_id(self, coin_id) -> Coin:
        return self.db.scalars(select(Coin).where(Coin.id == coin_id)).one()

    def get_coin_by_ticker(self, coin_ticker: str, priced: bool = False) -> Coin | None:
        query = select(Coin).where(func.lower(Coin.ticker) == coin_ticker.lower())
        if priced:
            query = query.where(or_(Coin.has_prices == True, Coin.alias_coin_id.is_not(None)))
        return self.db.scalars(query.order_by(Coin.has_prices.desc()).limit(1)).first()

    def get_or_create_by_ticker(self, coin_ticker: str) -> Coin:
        if coin := self.get_coin_by_ticker(coin_ticker):
            return coin
        coin_ticker = coin_ticker.lower()
        if coin := self.db.scalars(select(Coin).where(Coin.uid == coin_ticker)).one_or_none():
            return coin

        coin = Coin(name=coin_ticker, ticker=coin_ticker, uid=coin_ticker)
        self.db.add(coin)
        self.db.commit()
        return coin

    def get_priced_coin_by_ticker(self, coin_ticker: str) -> Coin | None:
        return self.get_coin_by_ticker(coin_ticker, priced=True)

    def get_coin_by_ticker_or_fail(self, coin_ticker) -> Coin:
        coin = self.get_coin_by_ticker(coin_ticker)
        if not coin:
            raise HTTP400Exception(f"Could not find {coin_ticker}")
        return coin

    def get_coin_by_uid(self, coin_uid: str) -> Coin:
        return self.db.scalars(select(Coin).where(Coin.uid == coin_uid)).one()

    def get_coins_usable_by_clients(self) -> Sequence[Coin]:
        return self.db.scalars(
            select(Coin).where(
                Coin.is_usable_by_clients == True,
                Coin.networks != None,
            )
        ).all()

    def get_coins_with_prices(
        self,
        *,
        exclude_alias_coins=False,
        exclude_coins_ids: Sequence[int] = (),
        include_without_prices=False,
        limit: int = 0,
        search: str = "",
    ) -> Sequence[Coin]:
        query = select(Coin)
        if not include_without_prices:
            query = query.where(Coin.has_prices)
        if exclude_alias_coins:
            query = query.where(Coin.id.not_in(select(AliasCoin.coin_id)))
        if exclude_coins_ids:
            query = query.where(Coin.id.not_in(exclude_coins_ids))
        if limit:
            query = query.limit(limit)
        if search:
            if len(search) > 3:
                query = query.where((Coin.ticker.ilike(search)) | (Coin.name.ilike(search)))
            else:
                query = query.where(func.lower(Coin.ticker).startswith(search.lower()))
        return self.db.scalars(query).all()

    def _search_coin(
        self,
        search: str,
        *,
        network_id: int = None,
        with_asset_address: bool = False,
        with_prices: bool = False,
    ) -> Select[tuple[Coin, str, int]] | Select[Coin]:
        filter_args = ((Coin.has_prices == True), (Coin.ticker.startswith(search)))
        if len(search) > 2:
            filter_args += (Coin.name.ilike(search), Coin.ticker.ilike(search))

        if with_asset_address:
            query = select(Coin, Account.address, Account.network_id).join(
                Account, Coin.id == Account.coin_id
            )
            if network_id:
                query = query.where(Account.network_id == network_id)
        else:
            query = select(Coin)

        query = query.where(Coin.has_prices == with_prices)

        query = (
            query.where(or_(*filter_args))
            .outerjoin(Coin.networks)
            .order_by(Coin.market_cap_usd.desc(), Coin.has_prices.desc(), Coin.uid)
            .distinct(Coin.market_cap_usd, Coin.has_prices, Coin.uid)
        )

        return query

    def search_coin(self, search: str, *, with_prices: bool = False) -> Sequence[Coin]:
        return self.db.scalars(self._search_coin(search, with_prices=with_prices)).all()

    def search_coin_with_asset_address(
        self, search: str, *, network_id: int = None
    ) -> Sequence[tuple[Coin, str, int]]:
        return self.db.execute(
            self._search_coin(
                search, network_id=network_id, with_asset_address=True, with_prices=True
            )
        )

    def update_coins_has_prices(self, coin_ids):
        self.db.execute(update(Coin).where(Coin.id.in_(coin_ids)).values(has_prices=True))
        self.db.commit()

    def update_coin_has_prices(self, coin_id):
        self.update_coins_has_prices([coin_id])


class AliasCoinManager(BaseManager[AliasCoin]):
    model = AliasCoin

    def delete(self, alias_coin_id: int) -> None:
        self.db.execute(
            update(Coin).where(Coin.alias_coin_id == alias_coin_id), {"alias_coin_id": None}
        )
        self.db.execute(delete(AliasCoin).where(AliasCoin.id == alias_coin_id))

    def get_by_aliased_coin_id(self, aliased_coin_id) -> AliasCoin | None:
        return self.db.scalars(
            select(AliasCoin).where(AliasCoin.aliased_coins.any(Coin.id == aliased_coin_id))
        ).one_or_none()

    def update_aliased_coins(self, alias_coin_id: int, aliased_coins_ids: list[int]):
        alias_coin = self.db.scalar(select(AliasCoin).where(AliasCoin.id == alias_coin_id))
        if not alias_coin:
            raise HTTP400Exception(f"Alias coin with id {alias_coin_id} not found")

        if not aliased_coins_ids:
            alias_coin.aliased_coins = []
        else:
            existing_alias_coins = self.db.scalars(
                select(AliasCoin).where(
                    AliasCoin.id != alias_coin_id,
                    or_(
                        AliasCoin.aliased_coins.any(Coin.id.in_(aliased_coins_ids)),
                        AliasCoin.coin_id.in_(aliased_coins_ids),
                    ),
                )
            ).all()
            if existing_alias_coins:
                error_coins = []
                error_alias_coins = [
                    f"{existing_alias_coin.coin.name} ({existing_alias_coin.coin.ticker.upper()})"
                    for existing_alias_coin in existing_alias_coins
                ]
                for existing_alias_coin in existing_alias_coins:
                    for alias_coin in existing_alias_coin.aliased_coins:
                        if alias_coin.id in aliased_coins_ids:
                            error_coins.append(
                                f"{alias_coin.name} ({alias_coin.ticker.upper()})"
                            )

                error_message = "Aliased coins are already used"
                if error_coins:
                    error_message += f" by alias coin aliased coins {error_coins}"
                if error_alias_coins:
                    error_message += f" for alias coin {error_alias_coins}"

                raise HTTP400Exception(error_message)

            self.db.execute(
                update(Coin).where(Coin.id.in_(aliased_coins_ids)).values(has_prices=False)
            )
            alias_coin.aliased_coins = self.db.scalars(
                select(Coin).where(Coin.id.in_(aliased_coins_ids))
            ).all()

        return alias_coin


class AlertManager(BaseManager[Alert]):
    model = Alert

    def get_active_by_account_id(self, account_id) -> Alert | None:
        return self.db.scalars(
            select(Alert).where(Alert.account_id == account_id, Alert.is_active == True)
        ).one_or_none()
