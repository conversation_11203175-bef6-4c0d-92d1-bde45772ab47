from enum import StrEnum, auto
from typing import Literal


class TransactionAction(StrEnum):
    bridge = auto()  # could be a deposit or withdrawal
    income = auto()  # is a deposit
    loan = auto()  # could be a deposit or withdrawal
    pool = auto()  # could be a deposit or withdrawal
    spend = auto()  # is a withdrawal
    stake = auto()  # could be a deposit or withdrawal
    swap = auto()  # is neither


JobKind = Literal["import_cointracking_csv", "import_bitcointax_csv", "sync_wallet"]
JobStatus = Literal["pending", "failed", "completed"]

WalletSyncProvider = Literal["covalent", "mintscan", "helius"]
"""Solana (helius) is just supported for the initial sync of a single newly created wallet."""
