import Spinner from "@/@core/components/spinner";
import Link from "@/components/Link";
import { getAccountedTransactionCoinDisplayName, getNumber } from "@/helpers/accounting";
import { currencyFormatter, formatDateTime } from "@/helpers/formatters";
import { useCoinId } from "@/helpers/routing";
import { useAccountingOpenPositionSummaryQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { coinFmt } from "@/types/market";
import { AccountedTransaction } from "@/types/schemas";
import { ArrowBack } from "@mui/icons-material";
import {
  Alert,
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Stack,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { skipToken } from "@reduxjs/toolkit/query";
import { useMemo } from "react";

const INITIAL_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "confirmedAt", sort: "desc" }] },
};

export default function OpenPositionCoinSummary() {
  const coinId = useCoinId();
  const { data, error } = useAccountingOpenPositionSummaryQuery(
    coinId ? { coinId } : skipToken,
  );

  const columns = useMemo(computeColumns, []);

  if (coinId == null) {
    return <Alert severity="error">Invalid coin id</Alert>;
  }

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed loading open position" })}
      </Alert>
    );
  }

  if (data == null) {
    return <Spinner />;
  }

  const {
    coin,
    openConfirmedAt,
    amount,
    cost,
    avgCost,
    unrealizedPnl,
    value,
    latestAccountedTransactions,
  } = data;

  return (
    <Grid container gap={6}>
      <Grid item xs={12}>
        <Grid container gap={6}>
          <Grid item xs={1}>
            <Stack direction="row" gap={4} alignItems="baseline">
              <Link href="/admin/open-positions" lineHeight={1} alignSelf="center">
                <ArrowBack fontSize="large" />
              </Link>
            </Stack>
          </Grid>
          <Grid item xs={10}>
            <Stack
              direction="row"
              columnGap={10}
              rowGap={4}
              justifyContent="center"
              flexWrap="wrap"
            >
              <Box textAlign="center">
                <Typography noWrap>{coinFmt(coin)}</Typography>
                <Typography noWrap variant="body2">
                  Asset
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>{formatDateTime(openConfirmedAt)}</Typography>
                <Typography noWrap variant="body2">
                  Open At
                </Typography>
              </Box>

              <Box textAlign="center">
                <Typography noWrap>
                  {currencyFormatter.format(Number.parseFloat(amount))}
                </Typography>
                <Typography noWrap variant="body2">
                  Amount
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(cost))}
                </Typography>
                <Typography noWrap variant="body2">
                  Cost
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(value))}
                </Typography>
                <Typography noWrap variant="body2">
                  Value
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(unrealizedPnl))}
                </Typography>
                <Typography noWrap variant="body2">
                  Unrealized PnL
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(avgCost))}
                </Typography>
                <Typography noWrap variant="body2">
                  Avg Cost
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(coin.latestUsdPrice))}
                </Typography>
                <Typography noWrap variant="body2">
                  Latest Price
                </Typography>
              </Box>
            </Stack>
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12}>
        <Card>
          <CardHeader
            title={
              <Stack direction="row" gap={4} alignItems="flex-end">
                <span>Accounted Transactions</span>
              </Stack>
            }
          />
          <CardContent>
            <DataGridPro
              columns={columns}
              rows={latestAccountedTransactions || []}
              initialState={INITIAL_STATE}
              autoHeight
              disableSelectionOnClick
              getRowId={getRowId}
            />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

function computeColumns(): GridColDef<AccountedTransaction, string | number>[] {
  return [
    {
      field: "asset",
      headerName: "Asset",
      flex: 1,
      valueGetter: ({ row }) => getAccountedTransactionCoinDisplayName(row),
    },
    {
      field: "action",
      headerName: "Action",
      flex: 1,
      valueGetter: ({ row }) => row.action,
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.amount),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.amount)),
    },
    {
      field: "remainingAmount",
      headerName: "Remaining Amount",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.remainingAmount),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.remainingAmount)),
    },
    {
      field: "assetPriceUsd",
      headerName: "$ Price",
      flex: 1,
      valueGetter: ({ row }) => getNumber(row.assetPriceUsd),
      renderCell: ({ row }) => currencyFormatter.format(getNumber(row.assetPriceUsd)),
    },
    {
      field: "confirmedAt",
      headerName: "Confirmed At",
      flex: 1,
      renderCell: ({ row }) => formatDateTime(row.confirmedAt),
    },
    {
      field: "matchedCapitalPositionsCount",
      headerName: "Matches",
      flex: 1,
      valueGetter: ({ row }) => getNumber(row.matchedCapitalPositionsCount),
      renderCell: ({ row }) =>
        currencyFormatter.format(getNumber(row.matchedCapitalPositionsCount)),
    },
    {
      field: "flattenedTransactionsCount",
      headerName: "Txns",
      flex: 1,
      valueGetter: ({ row }) => getNumber(row.flattenedTransactionsCount),
      renderCell: ({ row }) =>
        currencyFormatter.format(getNumber(row.flattenedTransactionsCount)),
    },
    {
      field: "loanWithdrawalsConsumedCount",
      headerName: "Loan Withdrawals",
      flex: 1,
      valueGetter: ({ row }) => getNumber(row.loanWithdrawalsConsumedCount),
      renderCell: ({ row }) =>
        currencyFormatter.format(getNumber(row.loanWithdrawalsConsumedCount)),
    },
  ];
}

function getRowId(row: AccountedTransaction) {
  return row.id;
}
