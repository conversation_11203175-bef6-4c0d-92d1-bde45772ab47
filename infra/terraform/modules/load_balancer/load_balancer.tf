locals {
  domain_name = trimsuffix(var.dns_managed_zone.dns_name, ".")
}

# Load balancer's public IPv4 address
resource "google_compute_global_address" "ipv4" {
  name       = "${var.namespace}-ipv4"
  ip_version = "IPV4"
}

# Load balancer's public IPv6 address
resource "google_compute_global_address" "ipv6" {
  name       = "${var.namespace}-ipv6"
  ip_version = "IPV6"
}

# Compute instances deny all ingress traffic by default, so we need to allow
# traffic from the load balancer health checkers (managed by GCP).
resource "google_compute_firewall" "rules" {
  name          = "${var.namespace}-allow-health-check"
  network       = var.network
  description   = "Allow load balancers to access GKE endpoints to perform health checks"
  direction     = "INGRESS"
  target_tags   = var.network_tags
  source_ranges = ["***********/22", "**********/16"] # GCP health checkers
  allow {
    protocol = "tcp"
    ports    = ["80"]
  }
}

# ---------------------------------------------------------------------
# Backends
# ---------------------------------------------------------------------

resource "google_compute_health_check" "default" {
  name                = "${var.namespace}-health-check"
  unhealthy_threshold = 1
  http_health_check {
    request_path = "/"
    port         = 80
  }
}

# Backend service with all the api NEGs
resource "google_compute_backend_service" "api" {
  name                  = "${var.namespace}-api"
  protocol              = "HTTP"
  health_checks         = [google_compute_health_check.default.id]
  load_balancing_scheme = "EXTERNAL_MANAGED"
  dynamic "backend" {
    for_each = var.api_negs
    content {
      group                 = backend.value
      balancing_mode        = "RATE"
      max_rate_per_endpoint = 5
    }
  }
}

# Backend bucket for serving the website
resource "google_compute_backend_bucket" "website" {
  name             = "${var.namespace}-website"
  bucket_name      = var.bucket_name
  enable_cdn       = true
  compression_mode = "AUTOMATIC"
  cdn_policy {
    cache_mode         = "CACHE_ALL_STATIC"
    client_ttl         = ********
    default_ttl        = 3600
    max_ttl            = ********
    request_coalescing = true
  }
}

# ---------------------------------------------------------------------
# URL maps for routing traffic to the backends
# ---------------------------------------------------------------------

# Serve the website and the api, redirecting www.{domain} to naked domain.
# For use with HTTPS only.
resource "google_compute_url_map" "default" {
  provider        = google-beta
  name            = var.namespace
  default_service = google_compute_backend_bucket.website.id

  host_rule {
    hosts        = ["www.${local.domain_name}"]
    path_matcher = "www"
  }

  host_rule {
    hosts        = [local.domain_name]
    path_matcher = "naked"
  }

  path_matcher {
    name = "www"
    default_url_redirect {
      host_redirect = local.domain_name
      strip_query   = false
    }
  }

  path_matcher {
    name            = "naked"
    default_service = google_compute_backend_bucket.website.id

    # API routing
    path_rule {
      paths   = ["/api/*"]
      service = google_compute_backend_service.api.id
      route_action {
        url_rewrite {
          path_prefix_rewrite = "/"
        }
      }
    }

    # SPA routing only when website_is_spa = true
    dynamic "path_rule" {
      for_each = var.website_is_spa ? [1] : []
      content {
        paths   = ["/*"]
        service = google_compute_backend_bucket.website.id
        custom_error_response_policy {
          error_response_rule {
            match_response_codes   = [404]
            path                   = "/index.html"
            override_response_code = 200
          }
          error_service = google_compute_backend_bucket.website.id
        }
      }
    }
  }
}

# Redirect HTTP to HTTPS and www.{domain} to naked domain.
# For use with HTTP only.
resource "google_compute_url_map" "https_redirect" {
  name = "${var.namespace}-https-redirect"

  default_url_redirect {
    https_redirect = true
    strip_query    = false
  }

  host_rule {
    hosts        = ["www.${local.domain_name}"]
    path_matcher = "www"
  }

  path_matcher {
    name = "www"
    default_url_redirect {
      host_redirect  = local.domain_name
      https_redirect = true
      strip_query    = false
    }
  }
}

# ---------------------------------------------------------------------
# Target proxies for the URL maps
# ---------------------------------------------------------------------

# Target proxy for HTTP (redirects to HTTPS)
resource "google_compute_target_http_proxy" "default" {
  name    = "${var.namespace}-http"
  url_map = google_compute_url_map.https_redirect.id
}

resource "google_compute_managed_ssl_certificate" "naked_and_www" {
  name = "${var.namespace}-naked-and-www"
  managed {
    domains = [local.domain_name, "www.${local.domain_name}"]
  }
}

# Target proxy for HTTPS (serves website and api)
resource "google_compute_target_https_proxy" "default" {
  name             = "${var.namespace}-https"
  url_map          = google_compute_url_map.default.id
  ssl_certificates = [google_compute_managed_ssl_certificate.naked_and_www.id]
}

# ---------------------------------------------------------------------
# Forwarding rules linking IP addresses to the target proxies
# ---------------------------------------------------------------------

# Forward IPv4 traffic to HTTP proxy (redirects to HTTPS)
resource "google_compute_global_forwarding_rule" "ipv4_http" {
  name                  = "${var.namespace}-ipv4-http"
  load_balancing_scheme = "EXTERNAL_MANAGED"
  port_range            = "80-80"
  ip_address            = google_compute_global_address.ipv4.id
  target                = google_compute_target_http_proxy.default.id
}

# Forward IPv4 traffic to HTTPS proxy (serves website and api)
resource "google_compute_global_forwarding_rule" "ipv4_https" {
  name                  = "${var.namespace}-ipv4-https"
  load_balancing_scheme = "EXTERNAL_MANAGED"
  port_range            = "443-443"
  ip_address            = google_compute_global_address.ipv4.id
  target                = google_compute_target_https_proxy.default.id
}

# Forward IPv6 traffic to HTTP proxy (redirects to HTTPS)
resource "google_compute_global_forwarding_rule" "ipv6_http" {
  name                  = "${var.namespace}-ipv6-http"
  load_balancing_scheme = "EXTERNAL_MANAGED"
  port_range            = "80-80"
  ip_address            = google_compute_global_address.ipv6.id
  target                = google_compute_target_http_proxy.default.id
}

# Forward IPv6 traffic to HTTPS proxy (serves website and api)
resource "google_compute_global_forwarding_rule" "ipv6_https" {
  name                  = "${var.namespace}-ipv6-https"
  load_balancing_scheme = "EXTERNAL_MANAGED"
  port_range            = "443-443"
  ip_address            = google_compute_global_address.ipv6.id
  target                = google_compute_target_https_proxy.default.id
}

# ---------------------------------------------------------------------
# DNS records linking the domains to the IP addresses
# ---------------------------------------------------------------------

resource "google_dns_record_set" "naked_ipv4" {
  name         = var.dns_managed_zone.dns_name
  managed_zone = var.dns_managed_zone.name
  type         = "A"
  ttl          = 86400
  rrdatas      = [google_compute_global_address.ipv4.address]
}

resource "google_dns_record_set" "naked_ipv6" {
  name         = var.dns_managed_zone.dns_name
  managed_zone = var.dns_managed_zone.name
  type         = "AAAA"
  ttl          = 86400
  rrdatas      = [google_compute_global_address.ipv6.address]
}

resource "google_dns_record_set" "www_ipv4" {
  name         = "www.${var.dns_managed_zone.dns_name}"
  managed_zone = var.dns_managed_zone.name
  type         = "A"
  ttl          = 86400
  rrdatas      = [google_compute_global_address.ipv4.address]
}

resource "google_dns_record_set" "www_ipv6" {
  name         = "www.${var.dns_managed_zone.dns_name}"
  managed_zone = var.dns_managed_zone.name
  type         = "AAAA"
  ttl          = 86400
  rrdatas      = [google_compute_global_address.ipv6.address]
}

# ---------------------------------------------------------------------
# Outputs
# ---------------------------------------------------------------------

output "url_map" {
  value = google_compute_url_map.default
}
