from datetime import datetime
from decimal import Decimal
from enum import StrEnum, auto

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, Numeric
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.mutable import MutableList
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship

from common.models import BaseModel
from stabletech.indexer.models import Coin, Transfer, User


class Base(DeclarativeBase, BaseModel):
    pass


class AccountedTransactionAction(StrEnum):
    buy = auto()
    sell = auto()
    loan_withdrawal = auto()


class AccountedTransaction(Base):
    tx_hash: Mapped[str]
    confirmed_at: Mapped[datetime]
    transfer_id: Mapped[int] = mapped_column(ForeignKey(Transfer.id))
    transfer: Mapped[Transfer] = relationship(
        Transfer, single_parent=True, cascade="all, delete-orphan"
    )
    coin: Mapped[Coin | None] = relationship(lazy="selectin")
    coin_id: Mapped[int | None] = mapped_column(ForeignKey(Coin.id))
    ticker_symbol: Mapped[str | None]

    # Aggregated transactions that were used to create this accounted transaction on processing
    # tuple[tx_hash, transfer_id]
    flattened_transactions: Mapped[list[tuple[str, int]] | None] = mapped_column(
        MutableList.as_mutable(JSONB())
    )
    amount: Mapped[Decimal] = mapped_column(Numeric(precision=48, scale=18))
    # Remaining amount after matching/generating capital positions
    remaining_amount: Mapped[Decimal] = mapped_column(Numeric(precision=48, scale=18))
    action: Mapped[str]  # AccountedTransactionAction
    transaction_action: Mapped[str]  # TransactionAction
    # Loan withdrawals that consumed this transaction
    # tuple[tx_hash, transfer_id]
    loan_withdrawals: Mapped[list[int] | None] = mapped_column(MutableList.as_mutable(JSONB()))
    asset_price_usd: Mapped[Decimal | None] = mapped_column(Numeric(precision=32, scale=18))

    def __repr__(self):
        return f"{self.id} {self.coin.ticker if self.coin else self.ticker_symbol} {self.action} {self.amount} {self.remaining_amount} {self.confirmed_at} {self.tx_hash}"

    def is_full(self) -> bool:
        return self.remaining_amount <= Decimal("0")

    @property
    def alias_coin(self):
        if self.coin:
            return self.coin.alias_coin.coin if self.coin.alias_coin else self.coin
        return None


class CapitalPosition(Base):
    """
    A Capital Position that tracks the cost-basis and proceeds when buying (opening) and selling (closing) a Coin.
    A position is considered fully closed when `sold_at` is set.
    """

    # The amount of Coin being closed in this position
    amount: Mapped[Decimal] = mapped_column(Numeric(precision=48, scale=18))
    coin: Mapped[Coin] = relationship(lazy="selectin")
    coin_id: Mapped[int] = mapped_column(ForeignKey(Coin.id))

    # Null User positions are the Fund's
    user_id: Mapped[int | None] = mapped_column(ForeignKey(User.id), index=True)
    user: Mapped[User | None] = relationship(User)

    bought_at: Mapped[datetime]
    bought_usd_amount: Mapped[Decimal] = mapped_column(Numeric(precision=32, scale=18))
    external_buy_transaction_id: Mapped[str | None]
    sold_at: Mapped[datetime | None]
    sold_usd_amount: Mapped[Decimal] = mapped_column(Numeric(precision=32, scale=18))
    external_sell_transaction_id: Mapped[str | None]

    accounted_buy_transaction_id: Mapped[int | None] = mapped_column(
        Integer, ForeignKey("accountedtransaction.id")
    )
    accounted_buy_transaction: Mapped[AccountedTransaction | None] = relationship(
        AccountedTransaction, foreign_keys=[accounted_buy_transaction_id]
    )
    accounted_sell_transaction_id: Mapped[int | None] = mapped_column(
        Integer, ForeignKey("accountedtransaction.id")
    )
    accounted_sell_transaction: Mapped[AccountedTransaction | None] = relationship(
        AccountedTransaction, foreign_keys=[accounted_sell_transaction_id]
    )

    # If the capital position has been filed as part of a tax report (immutable now)
    filed = Mapped[bool]

    # Imported positions are not editable when optimizing for tax purposes
    csv_row: Mapped[int | None]

    def __repr__(self):
        return (
            f"{self.coin_id} {self.amount} Bought @ {self.bought_usd_amount} USD {self.bought_at} txn {self.accounted_buy_transaction_id}"
            " - "
            f"Sold @ {self.sold_usd_amount} USD {self.sold_at} by {self.user or 'No User'} {self.is_imported and "Imported"} txn {self.accounted_sell_transaction_id} id {self.id}"
        )

    @property
    def profit(self) -> Decimal:
        return self.sold_usd_amount - self.bought_usd_amount

    @property
    def is_imported(self) -> bool:
        return self.csv_row is not None
