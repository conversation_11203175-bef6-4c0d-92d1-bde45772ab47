name: Docker
on:
  push:
    branches: [main, stage]
jobs:
  runners:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    env:
      REGISTRY: us-west1-docker.pkg.dev
      PYTHON_RUNNER_IMAGE_NAME: temet-359508/temet/python-runner
      SENTRY_RELEASE: ${{ github.sha }}

    steps:
      - uses: actions/checkout@v4

      - uses: docker/setup-buildx-action@v3

      - uses: google-github-actions/auth@v2
        id: auth
        with:
          access_token_lifetime: 1200s
          create_credentials_file: false
          project_id: temet-359508
          service_account: <EMAIL>
          token_format: access_token
          workload_identity_provider: projects/************/locations/global/workloadIdentityPools/github-actions/providers/temet-gh-actions

      - uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}

      - uses: docker/metadata-action@v5
        id: meta-python
        with:
          images: ${{ env.REGISTRY }}/${{ env.PYTHON_RUNNER_IMAGE_NAME }}
          flavor: latest=false
          tags: type=sha,prefix=

      - uses: docker/build-push-action@v6
        id: push-python
        with:
          context: .
          file: infra/docker/python-runner.dockerfile
          build-args: SENTRY_RELEASE=${{ env.SENTRY_RELEASE }}
          push: true
          tags: ${{ steps.meta-python.outputs.tags }}
          labels: ${{ steps.meta-python.outputs.labels }}
          cache-from: type=registry,ref=${{ env.REGISTRY }}/${{ env.PYTHON_RUNNER_IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.REGISTRY }}/${{ env.PYTHON_RUNNER_IMAGE_NAME }}:buildcache,mode=max,compression=zstd

      # We cannot do an attestation without upgrading our billing plan (as of May 14th 2024).
      # It is not important as we're not distributing this image to third parties.
      # - uses: actions/attest-build-provenance@v1
      #   with:
      #     subject-name: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}
      #     subject-digest: ${{ steps.push.outputs.digest }}
      #     push-to-registry: true

      - name: Create wallet-backend Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_RELEASE_INTEGRATION_TOKEN }}
          SENTRY_ORG: ${{ vars.SENTRY_ORG }}
          SENTRY_PROJECT: "4508800445841408"
        with:
          ignore_empty: true
          version: ${{ env.SENTRY_RELEASE }}

      - name: Create stabletech-backend Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_RELEASE_INTEGRATION_TOKEN }}
          SENTRY_ORG: ${{ vars.SENTRY_ORG }}
          SENTRY_PROJECT: "4504199259422720"
        with:
          ignore_empty: true
          version: ${{ env.SENTRY_RELEASE }}

      - name: Create lootloot-backend Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_RELEASE_INTEGRATION_TOKEN }}
          SENTRY_ORG: ${{ vars.SENTRY_ORG }}
          SENTRY_PROJECT: "4508440261033984"
        with:
          ignore_empty: true
          version: ${{ env.SENTRY_RELEASE }}
