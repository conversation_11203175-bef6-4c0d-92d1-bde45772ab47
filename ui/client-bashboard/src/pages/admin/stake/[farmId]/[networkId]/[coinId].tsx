import Spinner from "@/@core/components/spinner";
import Link from "@/components/Link";
import TransactionsTable, {
  useTransfersMapperTickers,
} from "@/components/admin/TransactionsTable";
import { currencyFormatter } from "@/helpers/formatters";
import { useStakeIdNetworkIdCoinId } from "@/helpers/routing";
import { useIndexerStakeCoinQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { ArrowBack } from "@mui/icons-material";
import { Alert, Box, Card, CardContent, Tooltip, Typography } from "@mui/material";
import Stack from "@mui/material/Stack";
import { skipToken } from "@reduxjs/toolkit/query";

export default function StakeDetailPage() {
  const [stakeId, networkId, coinId] = useStakeIdNetworkIdCoinId();

  const {
    data: stake,
    isLoading,
    error,
  } = useIndexerStakeCoinQuery(
    stakeId && networkId && coinId ? { stakeId, networkId, coinId } : skipToken,
  );

  const tickersMapper = useTransfersMapperTickers(
    stake?.asset.coin ? [stake.asset.coin.ticker] : [],
  );

  if (stakeId == null || coinId == null) {
    return <Alert severity="error">Invalid Farm id or coin id</Alert>;
  }
  if (stake == null || isLoading) {
    return <Spinner />;
  }
  const filteredStakeTransactions = stake.transactions.map((transaction) => ({
    ...transaction,
    transfers: transaction.transfers.filter((transfer) => {
      if (transaction.isDeposit) {
        if (transfer.isWithdrawal) return transfer;
        return;
      }
      if (transfer.isDeposit) return transfer;
      return;
    }),
  }));

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed to load stake coin" })}
      </Alert>
    );
  }

  const header = (
    <Stack direction="row" gap={4} alignItems="baseline">
      <Link href="/admin/stake" lineHeight={1} alignSelf="center">
        <ArrowBack fontSize="large" />
      </Link>
      <Typography variant="h4">{stake.farm.name}</Typography>
    </Stack>
  );

  const amount = Number.parseFloat(stake.amount);
  const latestUsdPrice =
    stake.asset.coin?.latestUsdPriceUpdatedAt != null
      ? Number.parseFloat(stake.asset.coin.latestUsdPrice)
      : null;
  const currentValue = latestUsdPrice ? latestUsdPrice * amount : null;
  const stakeStats = (
    <Stack direction="row" columnGap={12} rowGap={4} justifyContent="center" flexWrap="wrap">
      <Box textAlign="center">
        {amount >= 0 ? (
          <Typography noWrap>
            {currencyFormatter.format(amount)} {stake.asset.coin?.ticker.toUpperCase()}
          </Typography>
        ) : (
          <Tooltip title="Profit">
            <Typography
              fontFamily="inherit"
              fontSize="inherit"
              fontStyle="inherit"
              fontWeight="inherit"
              color="success.main"
            >
              {currencyFormatter.format(amount)} {stake.asset.coin?.ticker}
            </Typography>
          </Tooltip>
        )}
        <Typography noWrap variant="body2">
          Staked Amount
        </Typography>
      </Box>
      <Box textAlign="center">
        <Typography noWrap>
          {typeof currentValue === "number"
            ? `$ ${currencyFormatter.format(currentValue)}`
            : "—"}
        </Typography>
        <Typography noWrap variant="body2">
          Curent $ Value
        </Typography>
      </Box>
    </Stack>
  );

  return (
    <Card>
      <CardContent>
        <Stack gap={6}>
          {header}
          {stakeStats}
          <Typography variant="h6">Transactions</Typography>
          <TransactionsTable rows={filteredStakeTransactions} transfersMapper={tickersMapper} />
        </Stack>
      </CardContent>
    </Card>
  );
}
