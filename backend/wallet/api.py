import logging

from fastapi import <PERSON><PERSON><PERSON>, Response

import wallet.auth.api
import wallet.cubist.api
from wallet.auth.fastapi import depends_user_session

app = FastAPI()

# Auth router - all authentication related endpoints
app.include_router(
    wallet.auth.api.router,
    prefix="/auth",
    tags=["auth"],
)

# Cubist router
app.include_router(
    wallet.cubist.api.router,
    prefix="/cubist",
    tags=["cubist"],
    dependencies=[depends_user_session],
)


# Health check endpoint
@app.get("/")
async def health_check():
    return Response(status_code=200)


# Access logging filter
class AccessLoggerFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        method = record.args[1]  # type: ignore
        route = record.args[2]  # type: ignore
        return not (method == "GET" and route == "/")


logging.getLogger("uvicorn.access").addFilter(AccessLoggerFilter())

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("wallet.api:app", host="localhost", port=8080, reload=True)
