// We're directly using importWalletFromMnemonic rather than apiMutation
import { LOCAL_KEY_LIST_KEY } from "@/auth/local-keys";
import {} from "@/lib/api";
import { importWalletFromMnemonic } from "@/lib/mnemonic";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface WalletParams {
  readonly mnemonic: string;
  readonly password: CryptoKey;
}

interface CreateWalletMutationProps {
  readonly onSuccess?: () => void;
  readonly onSettled?: () => void;
  readonly validation?: () => void;
}

/**
 * Hook to create a wallet from a mnemonic phrase
 * This handles both generating a new wallet and importing an existing one
 */
export function useCreateWalletMutation({
  onSuccess,
  onSettled,
  validation,
}: CreateWalletMutationProps = {}) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ mnemonic, password }: WalletParams) => {
      validation?.();
      // Create wallet from mnemonic
      const { keyId, derivedKeyIds } = await importWalletFromMnemonic(mnemonic, password);
      return { keyId, derivedKeyIds };
    },
    onSettled: () => {
      // Invalidate wallet-related queries
      void queryClient.invalidateQueries({ queryKey: [LOCAL_KEY_LIST_KEY] });
      onSettled?.();
    },
    onSuccess: () => {
      onSuccess?.();
    },
  });
}
