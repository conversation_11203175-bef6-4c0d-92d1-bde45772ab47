data "google_client_config" "default" {}

data "google_container_cluster" "this" {
  name     = "${data.google_client_config.default.project}-gke"
  location = data.google_client_config.default.zone
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.this.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.this.master_auth[0].cluster_ca_certificate)
}

locals {
  gke_nodes_zones        = data.google_container_cluster.this.node_pool[0].node_locations
  gke_nodes_network_tags = data.google_container_cluster.this.node_pool[0].node_config[0].tags
  gke_network            = data.google_container_cluster.this.network
  project_id             = data.google_client_config.default.project
}

resource "kubernetes_namespace" "this" {
  metadata {
    name = var.namespace
  }
}

locals {
  namespace           = kubernetes_namespace.this.metadata[0].name
  python_runner_image = "${var.docker_repository}/python-runner:${var.release}"
  website_domain_name = trimsuffix(var.dns_managed_zone.dns_name, ".")
}

module "webapp" {
  source = "../../../../modules/webapp"

  website_domain_name = local.website_domain_name
  not_found_page      = "index.html" # because it's a SPA
}

module "common_config_map" {
  source = "../../../../modules/immutable_config_map"

  prefix    = "common"
  namespace = local.namespace
  data = {
    ENV_NAME                   = var.env_name
    NAMESPACE                  = local.namespace
    ALLOWED_ISSUERS            = jsonencode(var.auth_allowed_issuers)
    WEBAPP_ORIGIN_DOMAIN       = local.website_domain_name
    SENTRY_DSN                 = "https://<EMAIL>/4508800445841408"
    CUBIST_SESSION_SECRET_NAME = "${local.namespace}-cubist-session-secret-box-key"
    GCP_PROJECT_ID             = data.google_client_config.default.project
  }
}

module "postgresql" {
  source = "../../../../modules/postgres"

  name             = "${local.namespace}-psql"
  database_version = "POSTGRES_17"
  disk_size        = var.pg_disk_size
  private_network  = local.gke_network
  tier             = var.pg_tier
}

module "postgresql_secret" {
  source = "../../../../modules/immutable_secret"

  prefix    = "postgresql"
  namespace = local.namespace
  data = {
    POSTGRESQL_DB       = module.postgresql.db
    POSTGRESQL_HOST     = module.postgresql.host
    POSTGRESQL_USER     = module.postgresql.user
    POSTGRESQL_PASSWORD = module.postgresql.password
  }
}

module "init" {
  source = "../init"

  namespace           = local.namespace
  python_runner_image = local.python_runner_image

  config_maps = [module.common_config_map.name]
  secrets = [
    module.postgresql_secret.name,
  ]
}

module "helius_secret" {
  source = "../../../../modules/immutable_secret"

  prefix    = "helius"
  namespace = local.namespace
  data = {
    HELIUS_API_KEY = var.secrets.helius_api_key
  }
}

data "google_service_account" "api_pods" {
  account_id = "${local.namespace}-api-pods"
}

module "api" {
  source     = "../api"
  depends_on = [module.init]

  namespace           = local.namespace
  replicas            = var.api_replicas
  python_runner_image = local.python_runner_image
  create_neg          = true

  pods_service_account = data.google_service_account.api_pods.email

  config_maps = [module.common_config_map.name]
  secrets = [
    module.helius_secret.name,
    module.postgresql_secret.name,
  ]
}

locals {
  api_negs = [
    for zone in local.gke_nodes_zones :
    "projects/${data.google_client_config.default.project}/zones/${zone}/networkEndpointGroups/${module.api.neg_name}"
  ]
}

module "load_balancer" {
  source = "../../../../modules/load_balancer"

  namespace        = local.namespace
  network          = local.gke_network
  network_tags     = local.gke_nodes_network_tags
  api_negs         = local.api_negs
  bucket_name      = module.webapp.bucket.name
  dns_managed_zone = var.dns_managed_zone
  website_is_spa   = true
}
