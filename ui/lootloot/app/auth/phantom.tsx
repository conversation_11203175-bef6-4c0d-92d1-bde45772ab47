"use client";
import { usePhantomLogin, useWalletAuthMessage } from "@/lib/auth";
import { useLogIn } from "@/lib/auth-ctx";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { phantomSignature } from "@/lib/phantom";
import { useConnection, useWallet } from "@solana/wallet-adapter-react";
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui";
import "@solana/wallet-adapter-react-ui/styles.css";
import { PublicKey } from "@solana/web3.js";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";

interface AuthenticateProps {
  publicKey: PublicKey;
  autoLogin: boolean;
}

const Authenticate = ({ publicKey, autoLogin }: AuthenticateProps) => {
  const logIn = useLogIn();
  const { connection } = useConnection();
  const { connected, signTransaction } = useWallet();

  const phantomLogin = usePhantomLogin();
  const walletAuthMessage = useWalletAuthMessage(publicKey.toString());

  const canSignIn = connected && walletAuthMessage.isSuccess;

  const signIn = useQuery({
    // eslint-disable-next-line @tanstack/query/exhaustive-deps
    queryKey: ["phantomSignature"],
    enabled: autoLogin && canSignIn,
    retry: false,
    retryOnMount: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    gcTime: 0,
    queryFn: async () => {
      const { signature, address, message } = await phantomSignature({
        connection,
        publicKey,
        signTransaction,
        walletAuthMessage,
      });
      const resp = await phantomLogin.mutateAsync({ signature, address, message });
      logIn(resp);
      return null;
    },
  });

  const onClick = () => {
    void signIn.refetch({ cancelRefetch: false });
  };

  const error = signIn.error ?? walletAuthMessage.error;
  const isLoading = signIn.isLoading || walletAuthMessage.isLoading;
  return (
    <>
      <Button className="relative" onClick={onClick} disabled={isLoading}>
        Sign to Confirm Ownership
        {isLoading && (
          <div className="absolute inset-0 grid place-items-center">
            <Spinner size={24} />
          </div>
        )}
      </Button>
      {error && <AlertError className="w-auto!">{getErrorMessage(error)}</AlertError>}
    </>
  );
};

const Login = () => {
  const { publicKey, connected, connecting } = useWallet();
  const disconnected = !(connecting || connected);
  const [autoLogin, setAutoLogin] = useState(disconnected);

  useEffect(() => {
    if (disconnected) {
      // When disconnected, we want to auto-login on next wallet connect.
      setAutoLogin(true);
    }
  }, [disconnected]);

  if (!publicKey) return null;

  return <Authenticate publicKey={publicKey} autoLogin={autoLogin} />;
};

export function SignInWithPhantom() {
  return (
    <>
      <WalletMultiButton />
      <Login />
    </>
  );
}
