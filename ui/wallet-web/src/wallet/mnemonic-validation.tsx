import { useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { Input } from "@/lib/components/input";
import { useEffect, useState } from "react";

// Utility function to get n random unique indices from an array
function getRandomIndices(length: number, count: number): number[] {
  // Safety check to make sure we don't request more indices than available
  const n = Math.min(count, length);

  const indices: number[] = [];
  while (indices.length < n) {
    const index = Math.floor(Math.random() * length);
    if (!indices.includes(index)) {
      indices.push(index);
    }
  }

  // Sort the indices in ascending order for better UX
  return indices.sort((a, b) => a - b);
}

interface MnemonicValidationProps {
  expectedWords: readonly string[];
  onSuccess: (words: string, password: CryptoKey) => string | null | undefined;
  onCancel: () => void;
  importMode?: boolean;
}

export function MnemonicValidation({
  expectedWords,
  onSuccess,
  onCancel,
  importMode = false,
}: MnemonicValidationProps) {
  const [passphrase, setPassphrase] = useState("");
  const [mnemonicStrength, setMnemonicStrength] = useState<128 | 256>(256);

  // For verification mode, we'll adjust the number of words based on mnemonic length
  const wordCount = expectedWords.length > 12 ? 8 : 4;

  // Generate random word indices to verify (4 words)
  const [wordIndices, setWordIndices] = useState<number[]>([]);

  // Instead of storing all words, we'll just store the 4 verification words
  const [inputWords, setInputWords] = useState<Record<number, string>>({});
  const [skipValidation, setSkipValidation] = useState<boolean>(false);
  const [error, setError] = useState("");
  const [isVerified, setIsVerified] = useState(false);

  // Generate random indices whenever expectedWords change
  useEffect(() => {
    if (!importMode && expectedWords.length > 0) {
      const indices = getRandomIndices(expectedWords.length, wordCount);
      setWordIndices(indices);
      // Initialize the input words object
      const initialInputs: Record<number, string> = {};
      for (const index of indices) {
        initialInputs[index] = "";
      }
      setInputWords(initialInputs);
    } else if (importMode) {
      // In import mode, we need all words
      setInputWords({});
    }
  }, [expectedWords, importMode, wordCount]);

  const handleInputChange = (index: number, value: string) => {
    setInputWords((prev) => ({
      ...prev,
      [index]: value.toLowerCase().trim(),
    }));
    setError("");
  };

  const getPassword = useGetPassword();

  const handlePaste = (index: number, e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text").toLowerCase().trim();

    // For pasting, we'll just update the current word
    setInputWords((prev) => ({
      ...prev,
      [index]: pastedText,
    }));

    setError("");
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const password = await getPassword(formData);

    // Reset previous states
    setError("");
    setIsVerified(false);

    if (skipValidation) {
      const errorMessage = onSuccess(expectedWords.join(" "), password);
      if (errorMessage) {
        setError(errorMessage);
      } else {
        // Successfully skipped validation
        setIsVerified(true);
        // Trigger onSuccess with a delay to show the success message
        setTimeout(() => {
          onSuccess(expectedWords.join(" "), password);
        }, 1500);
      }
      setSkipValidation(false);
      return;
    }

    // In import mode (not currently supported with 4-word validation)
    if (importMode) {
      setError("Import mode is not supported with partial word validation");
      return;
    }

    // Check if all requested words are filled
    const emptyWords = Object.values(inputWords).some((word) => !word.trim());
    if (emptyWords) {
      setError("Please enter all the requested words");
      return;
    }

    // In verify mode, check if the entered words match the expected words at their indices
    let isValid = true;

    for (const [indexStr, word] of Object.entries(inputWords)) {
      const index = Number.parseInt(indexStr, 10);
      if (word !== expectedWords[index]) {
        isValid = false;
        break;
      }
    }

    if (isValid) {
      // All words match, use the full original mnemonic
      const fullMnemonic = expectedWords.join(" ");
      const finalMnemonic = passphrase ? `${fullMnemonic} ${passphrase}` : fullMnemonic;

      // Set verification success state
      setIsVerified(true);

      // Trigger onSuccess with a delay to show the success message
      setTimeout(() => {
        const errorMessage = onSuccess(finalMnemonic, password);
        if (errorMessage) {
          setIsVerified(false);
          setError(errorMessage);
        }
      }, 1500);
    } else {
      setError("The recovery phrase does not match. Please check your input and try again.");
    }
  };

  return (
    <div className="grid gap-4">
      <div className="mb-4 text-sm text-gray-500">
        {importMode
          ? "Please enter your recovery phrase to import your wallet."
          : "Please verify your recovery phrase by entering the requested words."}
      </div>

      {importMode && (
        <div className="mb-4 rounded bg-gray-200/80 p-4 select-none dark:bg-slate-800/80">
          <div className="mb-2 flex items-center justify-between">
            <h3 className="text-base font-medium">Recovery Phrase Length</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant={mnemonicStrength === 128 ? "primary" : "outline"}
              onClick={() => {
                setMnemonicStrength(128);
                setInputWords({});
                setError("");
              }}
              className="py-2"
            >
              12 Words
            </Button>
            <Button
              variant={mnemonicStrength === 256 ? "primary" : "outline"}
              onClick={() => {
                setMnemonicStrength(256);
                setInputWords({});
                setError("");
              }}
              className="py-2"
            >
              24 Words
            </Button>
          </div>
        </div>
      )}

      <form
        className="grid gap-2"
        onSubmit={(e) => {
          void handleSubmit(e);
        }}
      >
        <div className="user-select-none mb-4 rounded bg-gray-200/80 p-4 select-none dark:bg-slate-800/80">
          <div className="mb-2 flex items-center justify-between">
            <h3 className="text-base font-medium">Verify Your Recovery Phrase</h3>
            {!importMode && (
              <p className="text-xs text-gray-500">
                Fill in the highlighted words (#{wordIndices.map((i) => i + 1).join(", ")})
              </p>
            )}
          </div>

          {importMode ? (
            // Import mode UI (not implemented for 4-word validation)
            <div className="rounded bg-red-100 p-4 text-red-700 dark:bg-red-900/20 dark:text-red-400">
              Import mode with partial word validation is not supported.
            </div>
          ) : (
            // Verification mode UI
            <div className="grid grid-cols-3 gap-x-4 gap-y-4 md:grid-cols-4">
              {Array.from({ length: expectedWords.length }).map((_, index) => {
                const isEditable = wordIndices.includes(index);
                return (
                  <div key={index} className={`relative ${isEditable ? "" : "opacity-60"}`}>
                    {isEditable ? (
                      <Input
                        type="text"
                        value={inputWords[index] ?? ""}
                        onChange={(e) => {
                          handleInputChange(index, e.target.value);
                        }}
                        onPaste={(e) => {
                          handlePaste(index, e);
                        }}
                        className="min-w-[110px] border-0 bg-gray-300/50 p-2 pl-8 font-mono text-sm focus-visible:ring-primary/50 dark:bg-slate-700/50"
                        placeholder="word"
                        autoComplete="off"
                        spellCheck={false}
                      />
                    ) : (
                      <div className="flex h-[40px] min-w-[110px] items-center rounded border-0 bg-gray-300/30 p-2 pl-8 font-mono text-sm dark:bg-slate-700/30">
                        •••••
                      </div>
                    )}
                    <span
                      className={`absolute top-1/2 left-3 -translate-y-1/2 transform text-xs ${isEditable ? "font-medium text-primary" : "text-gray-500"}`}
                    >
                      {index + 1}.
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        <div className="rounded bg-gray-200/80 p-4 select-none dark:bg-slate-800/80">
          <div className="mb-2 flex items-center justify-between">
            <h3 className="text-base font-medium">Passphrase (optional)</h3>
          </div>
          <div className="relative">
            <Input
              id="passphrase"
              type="password"
              value={passphrase}
              onChange={(e) => {
                setPassphrase(e.target.value);
                setError("");
              }}
              className="w-full border-0 bg-gray-300/50 p-2 font-mono text-sm focus-visible:ring-primary/50 dark:bg-slate-700/50"
              placeholder="Leave empty for no passphrase"
            />
          </div>
          <p className="mt-2 text-xs text-gray-500">
            A passphrase adds extra security to your recovery phrase.
          </p>
        </div>

        {error && <AlertError>{error}</AlertError>}

        {isVerified && (
          <div className="flex animate-pulse items-start gap-2 rounded border border-green-500/20 bg-green-500/10 p-4 text-green-600 dark:text-green-400">
            <svg className="h-5 w-5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            <div>
              <p className="font-medium">Recovery Phrase Verified Successfully!</p>
              <p className="text-sm">Creating your wallet now...</p>
            </div>
          </div>
        )}

        <div className="rounded bg-gray-200/80 p-4 select-none dark:bg-slate-800/80">
          <div className="mb-2 flex items-center justify-between">
            <h3 className="text-base font-medium">Wallet Password</h3>
          </div>
          <PasswordInput />
        </div>

        <div className="flex flex-col items-start gap-2 rounded border border-amber-500/20 bg-amber-500/10 p-3 text-amber-600 dark:text-amber-400">
          <p className="flex items-center gap-2 text-xs">
            <span className="flex-shrink-0">⚠️</span> Make sure your recovery phrase is correct.
            You won&apos;t be able to access your wallet otherwise.
          </p>
          <p className="flex items-center gap-2 text-xs">
            <span className="flex-shrink-0">⚠️</span> We&apos;re asking you to verify only{" "}
            {wordCount} highlighted words from your recovery phrase.
          </p>
        </div>

        <div className="mt-4 grid gap-4">
          {!importMode && !isVerified && (
            <Button
              variant="secondary"
              type="submit"
              onClick={() => {
                setSkipValidation(true);
              }}
            >
              I know what I&apos;m doing
            </Button>
          )}
          <div className="grid grid-cols-2 gap-4">
            {!isVerified && (
              <>
                <Button type="submit" variant="primary" size="lg">
                  {importMode ? "Import Wallet" : "Verify Recovery Phrase"}
                </Button>
                <Button variant="outline" size="lg" onClick={onCancel}>
                  Cancel
                </Button>
              </>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
