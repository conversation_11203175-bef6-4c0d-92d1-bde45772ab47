"""CovalentSync table

Revision ID: 0049
Revises: 0048
Create Date: 2024-03-31 12:05:05.119072

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0049"
down_revision = "0048"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "covalentsync",
        sa.Column("chain_name", sa.String(), nullable=False),
        sa.Column("address", sa.String(), nullable=False),
        sa.Column("latest_page", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.<PERSON>KeyConstraint("id"),
    )
    op.create_index(
        "ix_covalent_sync_chain_name_address",
        "covalentsync",
        ["chain_name", "address"],
        unique=True,
    )
    op.create_index(op.f("ix_covalentsync_id"), "covalentsync", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_covalentsync_id"), table_name="covalentsync")
    op.drop_index("ix_covalent_sync_chain_name_address", table_name="covalentsync")
    op.drop_table("covalentsync")
    # ### end Alembic commands ###
