from datetime import date
from decimal import Decimal


def get_prices_for_lost_coingecko_history(coin_uid: str, date_timestamp: date) -> Decimal:
    """Returns default price for coins that are missing in CoinGecko history."""
    match coin_uid:
        case "beam-bridged-usdc-beam":
            if date_timestamp < date(2023, 11, 26):
                return Decimal("1")
        case "synthetic-usd" | "usd-dollar":
            return Decimal("1")
    return None
