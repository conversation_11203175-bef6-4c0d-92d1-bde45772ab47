"use client";

import { AlertError } from "@/lib/components/alert";
import { button } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { useMatricaCallback } from "@/lib/matrica-auth";
import Link from "next/link";

export default function MatricaCallback() {
  const matricaCallback = useMatricaCallback();

  return (
    <main className="flex flex-col gap-6 p-8">
      {matricaCallback.isError ? (
        <>
          <AlertError>{getErrorMessage(matricaCallback.error)}</AlertError>
          <Link className={button({ variant: "link", size: "lg" })} href="/auth">
            Go back to sign in
          </Link>
        </>
      ) : (
        <div className="grid place-items-center">
          <Spinner priority size={42} />
        </div>
      )}
    </main>
  );
}
