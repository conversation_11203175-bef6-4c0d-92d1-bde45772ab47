import { PasswordContext } from "@/auth/password";
import { <PERSON><PERSON> } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { Label } from "@/lib/components/form";
import { Input } from "@/lib/components/input";
import * as Slider from "@radix-ui/react-slider";
import { cx } from "class-variance-authority";
import { Lock, LockOpen } from "lucide-react";
import { use, useId, useState } from "react";

interface PasswordInputProps {
  className?: string | undefined;
  readonly disabled?: boolean | undefined;
  onReturnToAuthenticator?: (() => void) | undefined;
}

export function PasswordInput(props: PasswordInputProps) {
  const id = useId();
  const { password, registered } = use(PasswordContext);
  const [unlockDuration, setUnlockDuration] = useState(loadLastDuration);
  const [dialogOpen, setDialogOpen] = useState(false);

  if (password != null) {
    return null;
  }

  const { className, disabled } = props;
  const passwordId = `password${id}`;

  // Convert minutes to milliseconds for the hidden input
  const unlockDurationMs = unlockDuration * 60 * 1000;

  return (
    <div className={cx("grid gap-2 rounded border p-4", className)}>
      <Label htmlFor={passwordId}>
        {registered ? "Enter your password" : "Create a password"}
      </Label>
      <Input
        id={passwordId}
        name="password"
        type="password"
        minLength={6}
        pattern="^(?=[^\s])(.*)(?<=[^\s])$" // no leading or trailing spaces
        disabled={(disabled ?? false) || registered == null}
        required
      />
      <input type="hidden" name="keepUnlockedFor" value={unlockDurationMs.toString()} />
      <div className="grid w-full grid-cols-[auto_1fr] items-center gap-2">
        <Dialog.Root open={dialogOpen} onOpenChange={setDialogOpen}>
          <Dialog.Trigger asChild>
            <Button variant="outline" size="icon">
              {unlockDuration > 0 ? <LockOpen /> : <Lock className="min-w-6" />}
            </Button>
          </Dialog.Trigger>
          <Dialog.Content title="Wallet Unlock Duration" aria-describedby="">
            <div className="grid gap-4">
              <p>
                Set how long your wallet should remain unlocked on this session only. For better
                security, choose a shorter duration.
              </p>
              <div className="grid gap-2">
                <div className="flex justify-between">
                  <span>
                    Duration: {unlockDuration} {unlockDuration === 1 ? "minute" : "minutes"}
                  </span>
                </div>
                <div className="py-4">
                  <Slider.Root
                    className="relative flex h-5 w-full touch-none items-center select-none"
                    value={[unlockDuration]}
                    max={60}
                    step={1}
                    onValueChange={([value]) => {
                      if (value != null) {
                        setUnlockDuration(value);
                        persistDuration(value);
                      }
                    }}
                  >
                    <Slider.Track className="relative h-1 w-full grow rounded-full bg-muted">
                      <Slider.Range className="absolute h-full rounded-full bg-primary" />
                    </Slider.Track>
                    <Slider.Thumb
                      className="block h-5 w-5 rounded-full border border-primary/50 bg-background shadow-sm hover:border-primary focus-visible:ring-1 focus-visible:ring-ring focus-visible:outline-none"
                      aria-label="Unlock duration"
                    />
                  </Slider.Root>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0 min</span>
                  <span>15 min</span>
                  <span>30 min</span>
                  <span>45 min</span>
                  <span>60 min</span>
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Dialog.Close asChild>
                  <Button variant="primary">Save</Button>
                </Dialog.Close>
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Root>
        <p className="max-w-min min-w-full text-sm">
          {unlockDuration > 0
            ? `Keep unlocked for ${unlockDuration} ${unlockDuration === 1 ? "minute" : "minutes"}.`
            : "Unlock only for this operation. Your password will be required for every sensitive operation."}
        </p>
      </div>

      {/* Return to authenticator button */}
      {props.onReturnToAuthenticator && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2 justify-self-start"
          onClick={props.onReturnToAuthenticator}
        >
          Use authenticator
        </Button>
      )}
    </div>
  );
}

const DURATION_KEY = "auth.unlock-duration";

function persistDuration(value: number) {
  localStorage.setItem(DURATION_KEY, value.toString());
}

function loadLastDuration() {
  const storedValue = localStorage.getItem(DURATION_KEY);
  const parsedValue = storedValue ? Number.parseInt(storedValue, 10) : 0;
  return Number.isNaN(parsedValue) ? 0 : parsedValue;
}
