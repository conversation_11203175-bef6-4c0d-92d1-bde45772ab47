import logging
from typing import Set

from sqlalchemy import select
from sqlalchemy.orm import Session

from common.conf import Environment
from stabletech.indexer.crud import TransactionManager
from stabletech.indexer.internal_sync.client import InternalSyncClient
from stabletech.indexer.models import Transaction

logger = logging.getLogger(__name__)


def sync_missing_transactions(company: Environment, db: Session) -> int:
    """Sync any missing transactions from a subsidiary to the main company."""
    client = InternalSyncClient(company)

    # Get all transaction IDs from the subsidiary (excluding ClientTransactions)
    all_ids = client.get_transaction_ids()

    # Check which IDs we don't have yet
    stmt = select(Transaction.internal_id).where(
        Transaction.internal_id.in_([f"{company}:{id_}" for id_ in all_ids])
    )
    result = db.execute(stmt)
    existing_internal_ids: Set[str] = {row[0] for row in result.fetchall()}

    missing_ids = []
    for id_ in all_ids:
        if f"{company}:{id_}" not in existing_internal_ids:
            missing_ids.append(id_)

    if not missing_ids:
        return 0

    # Get and save missing transactions
    transactions = client.get_transactions(missing_ids)
    transaction_manager = TransactionManager(db)
    for transaction in transactions:
        transaction_manager.add_internal(transaction)
    return len(transactions)
