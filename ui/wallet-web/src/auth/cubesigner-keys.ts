import { useCubeSignerClient } from "@/auth/cubesigner-auth-store";
import { useQuery } from "@tanstack/react-query";

export const CUBESIGNER_KEYS_QUERY_KEY = ["CubeSigner", "keys"] as const;

export function useCubeSignerKeys() {
  const cubeSignerClient = useCubeSignerClient();
  return useQuery({
    enabled: cubeSignerClient != null,
    queryKey: CUBESIGNER_KEYS_QUERY_KEY,
    queryFn: async () => {
      return (await cubeSignerClient?.sessionKeys()) ?? null;
    },
  });
}
