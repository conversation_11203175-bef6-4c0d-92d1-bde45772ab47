from fastapi import APIRouter, Query

from common.auth import SecretToken<PERSON><PERSON>
from common.conf import settings
from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from stabletech.indexer.crud import TransactionManager
from stabletech.indexer.internal_sync.client import INTERNAL_SYNC_ACTIONS, SUBSIDIARY_ENVS
from stabletech.indexer.internal_sync.schemas import InternalTransaction
from stabletech.schemas import Transaction as TransactionSchema

auth = SecretTokenAuth(settings.internal_sync_secret)
router = APIRouter(
    dependencies=[auth.depends_authentication],
)


if settings.env_name in SUBSIDIARY_ENVS:

    @router.get("/transactions/ids")
    async def get_transaction_ids(
        db: DatabaseSession,
    ) -> list[str]:
        """Get all transaction IDs for income and spend transactions, excluding ClientTransactions."""
        return TransactionManager(db).get_internal_sync_transaction_ids()

    @router.get("/transactions")
    async def get_transactions(
        db: DatabaseSession,
        ids: str = Query(..., description="Comma-separated list of transaction IDs"),
    ) -> list[InternalTransaction]:
        """Get full transaction data for the specified IDs."""
        transaction_ids = [int(id_) for id_ in ids.split(",")]
        transactions = TransactionManager(db).get_internal_sync_transactions(transaction_ids)
        return [
            InternalTransaction.from_transaction(transaction) for transaction in transactions
        ]


if settings.is_stabletech or settings.is_local:

    @router.post("/transactions/add")
    async def add_internal_transaction(
        internal_transaction: InternalTransaction,
        db: DatabaseSession,
    ) -> TransactionSchema:
        """Add a transaction from a subsidiary company."""
        if internal_transaction.action not in INTERNAL_SYNC_ACTIONS:
            raise HTTP400Exception("Transaction has invalid action for syncing")
        db_transaction = TransactionManager(db).add_internal(internal_transaction)
        return TransactionSchema.model_validate(db_transaction)

    @router.delete("/transactions/delete")
    async def delete_internal_transaction(
        db: DatabaseSession,
        internal_id: str = Query(..., description="Internal ID of the transaction to delete"),
    ):
        """Delete a transaction from a subsidiary company."""
        transaction_manager = TransactionManager(db)
        transaction = transaction_manager.get_by_internal_id(internal_id)
        if not transaction:
            raise HTTP400Exception(f"Transaction with internal_id {internal_id} not found")
        if transaction.action not in INTERNAL_SYNC_ACTIONS:
            raise HTTP400Exception("Transaction has invalid action for syncing")
        transaction_manager.delete(transaction.id)
