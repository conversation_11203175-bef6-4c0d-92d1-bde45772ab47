import { initializeApp } from "firebase/app";
import { GoogleAuthProvider, getAuth } from "firebase/auth";

const firebaseConfig = {
  apiKey: "AIzaSyC0UG3vzWZrfMD_a95_egulH12ThMGfJUY",
  authDomain: "temet-359508.firebaseapp.com",
  projectId: "temet-359508",
  storageBucket: "temet-359508.appspot.com",
  messagingSenderId: "639860733581",
  appId: "1:639860733581:web:f6d1cdf9330a156fc854c4",
  measurementId: "G-3RGTBW7183",
};
initializeApp(firebaseConfig);

//init services
const auth = getAuth();
const googleAuthProvider = new GoogleAuthProvider();

export { auth, googleAuthProvider };

export {
  createUserWithEmailAndPassword,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
  updateProfile,
} from "firebase/auth";
