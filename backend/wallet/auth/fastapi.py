from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from common.psql import DatabaseSession
from wallet.auth.crud import UserSessionManager
from wallet.models import User, UserSession

security = HTTPBearer()

Credentials = Annotated[HTTPAuthorizationCredentials, Depends(security)]


def user_session(credentials: Credentials, db: DatabaseSession) -> UserSession:
    """Get user session from authorization credentials."""
    session = UserSessionManager(db).get_by_token(credentials.credentials)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"},
            detail="Invalid Token",
        )
    return session


depends_user_session = Depends(user_session)
DependsUserSession = Annotated[UserSession, depends_user_session]


def current_user(session: DependsUserSession) -> User:
    """Get current user from session."""
    return session.user


depends_current_user = Depends(current_user)
CurrentUser = Annotated[User, depends_current_user]
