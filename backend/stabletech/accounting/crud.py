from datetime import date, datetime
from decimal import Decimal
from typing import Sequence

from sqlalchemy import case, delete, func, insert, or_, select, update
from sqlalchemy.sql.selectable import Select

from stabletech.accounting.bitcointax.kinds import ProcessedTransaction
from stabletech.accounting.kinds import (
    CapitalPositionsSummary,
    CapitalPositionSummaryListTotals,
    OpenPositionSummary,
)
from stabletech.accounting.models import (
    AccountedTransaction,
    AccountedTransactionAction,
    CapitalPosition,
)
from stabletech.indexer.models import AliasCoin, Coin
from stabletech.utils.models import BaseManager


class AccountedTransactionManager(BaseManager[AccountedTransaction]):
    model = AccountedTransaction

    def bulk_create_with_action(
        self, action: AccountedTransactionAction, transactions: list[ProcessedTransaction]
    ) -> list[AccountedTransaction]:
        return self.db.scalars(
            insert(AccountedTransaction)
            .values(
                [
                    {
                        "tx_hash": transaction.tx_hash,
                        "transfer_id": transaction.transfer_id,
                        "action": action.value,
                        "amount": transaction.amount,
                        "remaining_amount": transaction.remaining_amount,
                        "confirmed_at": transaction.confirmed_at,
                        "flattened_transactions": transaction.flattened_txns,
                        "transaction_action": transaction.action,
                        "ticker_symbol": transaction.transfer.asset_account.ticker_symbol,
                        "coin_id": transaction.transfer.asset_account.coin_id,
                        "asset_price_usd": transaction.transfer.asset_price_usd,
                    }
                    for transaction in transactions
                ]
            )
            .returning(AccountedTransaction)
        ).all()

    def bulk_update(self, accounted_transactions: list[ProcessedTransaction]):
        self.db.execute(
            update(AccountedTransaction),
            [
                {
                    "id": accounted_transaction.id,
                    "remaining_amount": accounted_transaction.remaining_amount,
                    "loan_withdrawals": accounted_transaction.loan_withdrawals,
                }
                for accounted_transaction in accounted_transactions
            ],
        )

    def delete(self, after: datetime | None = None):
        query = delete(AccountedTransaction)
        if after:
            query = query.where(AccountedTransaction.confirmed_at >= after)
        self.db.execute(query)

    def get_open_positions_summary(
        self, coin_ids: list[int] | None = None
    ) -> tuple[list[OpenPositionSummary], Sequence[AccountedTransaction]]:
        summary_query = select(
            AccountedTransaction.coin_id,
            func.min(
                case(
                    (
                        AccountedTransaction.action == AccountedTransactionAction.buy.value,
                        AccountedTransaction.confirmed_at,
                    ),
                )
            ).label("open_confirmed_at"),
            func.sum(
                case(
                    (
                        AccountedTransaction.action == AccountedTransactionAction.buy.value,
                        AccountedTransaction.remaining_amount,
                    ),
                    (
                        AccountedTransaction.action == AccountedTransactionAction.sell.value,
                        -1 * AccountedTransaction.remaining_amount,
                    ),
                )
            ).label("amount"),
            func.avg(
                case(
                    (
                        AccountedTransaction.action == AccountedTransactionAction.buy.value,
                        AccountedTransaction.asset_price_usd,
                    ),
                )
            ).label("avg_cost"),
        ).where(
            AccountedTransaction.remaining_amount > Decimal("0"),
            AccountedTransaction.asset_price_usd.is_not(None),
        )
        latest_query = (
            select(AccountedTransaction)
            .limit(50)
            .order_by(AccountedTransaction.confirmed_at.desc())
        )

        if coin_ids:
            summary_query = summary_query.where(AccountedTransaction.coin_id.in_(coin_ids))
            latest_query = latest_query.where(AccountedTransaction.coin_id.in_(coin_ids))

        sub_query_summary = summary_query.group_by(AccountedTransaction.coin_id).subquery(
            "summary"
        )
        # We could further aggregate by Alias Coin
        query = (
            select(
                Coin,
                sub_query_summary.c.open_confirmed_at,
                sub_query_summary.c.amount,
                sub_query_summary.c.avg_cost,
            )
            .where(sub_query_summary.c.amount > Decimal("0"))
            .join(Coin, Coin.id == sub_query_summary.c.coin_id)
        )
        summary = self.db.execute(query).all()
        latest = self.db.scalars(latest_query).all()

        if summary:
            for summ in summary:
                assert summ[2] is not None, f"Open positions summary with amount none: {summ}"
                assert summ[1] is not None, f"Open positions summary with date none: {summ}"
            summary = [
                OpenPositionSummary(
                    coin=coin,
                    open_confirmed_at=open_confirmed_at,
                    amount=amount,
                    avg_cost=avg_cost,
                )
                for coin, open_confirmed_at, amount, avg_cost in summary
            ]
        return summary, latest

    def get_open_positions_by_coin(
        self, coin_id: int
    ) -> tuple[list[OpenPositionSummary], Sequence[AccountedTransaction]]:
        alias_coin = self.db.scalar(
            select(AliasCoin).where(
                or_(
                    (AliasCoin.aliased_coins.any(Coin.id == coin_id)),
                    (AliasCoin.coin_id == coin_id),
                )
            )
        )
        coin_ids = (
            [coin_id]
            if not alias_coin
            else [coin_id] + [coin.id for coin in alias_coin.aliased_coins]
        )
        return self.get_open_positions_summary(coin_ids=coin_ids)

    def get_open_transactions(self) -> list[AccountedTransaction]:
        return self.db.scalars(
            select(AccountedTransaction)
            .where(
                AccountedTransaction.remaining_amount > Decimal("0"),
                AccountedTransaction.coin_id.is_not(None),
            )
            .order_by(AccountedTransaction.confirmed_at.asc())
        ).all()


class CapitalPositionManager(BaseManager[CapitalPosition]):
    model = CapitalPosition

    def delete_imported_by_year(self, year: int):
        """Delete all capital positions sold on a given year."""
        self.db.execute(
            delete(CapitalPosition).where(
                CapitalPosition.csv_row.is_not(None),
                CapitalPosition.sold_at >= date(year, 1, 1),
                CapitalPosition.sold_at < date(year + 1, 1, 1),
            )
        )

    def delete(self, after: datetime | None, is_imported: bool, user_id: int | None):
        """Delete CapitalPositions."""
        if user_id is None:
            query = delete(CapitalPosition).where(
                CapitalPosition.user_id.is_(None),
            )
        else:
            query = delete(CapitalPosition).where(
                CapitalPosition.user_id == user_id,
            )
        if is_imported:
            query = query.where(CapitalPosition.csv_row.is_not(None))
        else:
            query = query.where(CapitalPosition.csv_row.is_(None))

        if after:
            query = query.where(CapitalPosition.sold_at >= after)
        self.db.execute(query)

    def delete_after(self, after: datetime, is_imported: bool, user_id: int | None):
        return self.delete(after, is_imported, user_id)

    def delete_open_positions(self, user_id: int | None):
        return self.delete(None, False, user_id)

    def get_all_for(self, user_id: int | None) -> Sequence[CapitalPosition]:
        """Get all capital positions for user_id if not user_id get all the fund's capital positions."""
        return self.db.scalars(
            select(CapitalPosition)
            .where(
                CapitalPosition.user_id == user_id,
            )
            .order_by(CapitalPosition.sold_at.asc())
        ).all()

    def get_by_year(self, year: int) -> tuple[list[CapitalPosition], list[CapitalPosition]]:
        """Get capital positions for a given year, separated into short-term and long-term.

        Short-term: Buy and sell dates within the same year
        Long-term: Buy and sell dates at least 1 year apart
        """
        base_query = (
            select(CapitalPosition)
            .where(
                CapitalPosition.sold_at >= date(int(year), 1, 1),
                CapitalPosition.sold_at < date(int(year) + 1, 1, 1),
            )
            .join(Coin, Coin.id == CapitalPosition.coin_id)
            .order_by(CapitalPosition.sold_at.asc())
        )

        # Short-term: Buy date in same year as sell date
        short_term = self.db.scalars(
            base_query.where(
                CapitalPosition.bought_at >= date(int(year), 1, 1),
            )
        ).all()

        # Long-term: Positions held for at least 365 days
        long_term = self.db.scalars(
            base_query.where(
                func.date_part("day", CapitalPosition.sold_at - CapitalPosition.bought_at)
                >= 365
            )
        ).all()

        return short_term, long_term

    def bulk_updated_matched_transactions(self, capital_positions: list[CapitalPosition]):
        self.db.execute(
            update(CapitalPosition),
            [
                {
                    "id": capital_position.id,
                    "accounted_buy_transaction_id": capital_position.accounted_buy_transaction_id,
                    "accounted_sell_transaction_id": capital_position.accounted_sell_transaction_id,
                }
                for capital_position in capital_positions
            ],
        )

    def bulk_insert(self, capital_positions: list[dict]):
        self.db.execute(
            insert(CapitalPosition),
            capital_positions,
        )

    def get_summary(
        self,
        generated_only: bool,
        coin_id: int | None = None,
        user_id: int | None = None,
        limit: int | None = None,
        year: int | None = None,
    ) -> tuple[Select, Select]:
        summary_query = select(
            CapitalPosition.coin_id,
            func.sum(CapitalPosition.bought_usd_amount).label("cost"),
            func.sum(CapitalPosition.sold_usd_amount - CapitalPosition.bought_usd_amount).label(
                "pnl"
            ),
            func.sum(CapitalPosition.sold_usd_amount).label("proceeds"),
            func.sum(CapitalPosition.amount).label("volume"),
        )
        latest_capital_positions_query = select(CapitalPosition).join(
            Coin, Coin.id == CapitalPosition.coin_id
        )

        if generated_only:
            summary_query = summary_query.where(
                CapitalPosition.csv_row.is_(None),
            )
            latest_capital_positions_query = latest_capital_positions_query.where(
                CapitalPosition.csv_row.is_(None),
            )

        if coin_id:
            summary_query = summary_query.where(
                CapitalPosition.coin_id == coin_id,
            )
            latest_capital_positions_query = latest_capital_positions_query.where(
                CapitalPosition.coin_id == coin_id,
            )
        if limit:
            latest_capital_positions_query = latest_capital_positions_query.limit(limit)

        if user_id:
            summary_query = summary_query.where(
                CapitalPosition.user_id == user_id,
            )
            latest_capital_positions_query = latest_capital_positions_query.where(
                CapitalPosition.user_id == user_id,
            )
        if year:
            summary_query = summary_query.where(
                CapitalPosition.sold_at >= date(int(year), 1, 1),
                CapitalPosition.sold_at < date(int(year) + 1, 1, 1),
            )
            latest_capital_positions_query = latest_capital_positions_query.where(
                CapitalPosition.sold_at >= date(int(year), 1, 1),
                CapitalPosition.sold_at < date(int(year) + 1, 1, 1),
            )

        summary_query = summary_query.group_by(CapitalPosition.coin_id).subquery("summary")
        query = select(
            Coin,
            summary_query.c.cost,
            summary_query.c.pnl,
            summary_query.c.proceeds,
            summary_query.c.volume,
        ).join(Coin, Coin.id == summary_query.c.coin_id)
        latest_capital_positions_query = latest_capital_positions_query.order_by(
            CapitalPosition.sold_at
        )

        return query, latest_capital_positions_query

    def get_capital_positions_summary(
        self, generated_only: bool, user_id: int | None = None, year: int | None = None
    ) -> tuple[
        list[CapitalPositionsSummary], list[CapitalPosition], CapitalPositionSummaryListTotals
    ]:
        summary, latest_capital_positions = self.get_summary(
            generated_only=generated_only, user_id=user_id, year=year
        )
        totals = self.db.execute(
            select(
                func.sum(summary.c.cost).label("total_cost"),
                func.sum(summary.c.pnl).label("total_pnl"),
                func.sum(summary.c.proceeds).label("total_proceeds"),
                func.sum(summary.c.volume).label("total_volume"),
            )
        ).one()
        summary = self.db.execute(summary).all()
        latest_capital_positions = self.db.scalars(latest_capital_positions).all()
        return summary, latest_capital_positions, totals

    def get_summary_by_coin(
        self,
        coin_id: int,
        generated_only: bool,
        user_id: int | None = None,
        year: int | None = None,
    ) -> tuple[list[CapitalPositionsSummary], list[CapitalPosition]]:
        summary, latest_capital_positions = self.get_summary(
            generated_only,
            coin_id=coin_id,
            user_id=user_id,
            limit=100,
            year=year,
        )
        summary = self.db.execute(summary).all()
        latest_capital_positions = self.db.scalars(latest_capital_positions).all()
        return summary, latest_capital_positions
