from datetime import datetime
from decimal import Decimal
from typing import NamedTuple

from stabletech.indexer.models import Coin


class CapitalPositionsSummary(NamedTuple):
    coin: Coin
    cost: Decimal
    pnl: Decimal
    proceeds: Decimal
    volume: Decimal


class CapitalPositionSummaryListTotals(NamedTuple):
    total_cost: Decimal
    total_pnl: Decimal
    total_proceeds: Decimal
    total_volume: Decimal


class OpenPositionSummary(NamedTuple):
    coin: Coin
    open_confirmed_at: datetime
    amount: Decimal
    avg_cost: Decimal
