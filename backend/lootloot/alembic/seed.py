import logging
from decimal import Decimal

from sqlalchemy import Engine
from sqlalchemy.dialects.postgresql import insert

from lootloot.models import NFTCollection

logger = logging.getLogger(__name__)


def seed_data(engine: Engine) -> None:
    """Seed the database with initial data."""
    with engine.begin() as db:
        # Upsert default NFT collections
        stmt = insert(NFTCollection)
        stmt = stmt.on_conflict_do_update(
            index_elements=[NFTCollection.uid],
            set_={
                NFTCollection.name: stmt.excluded.name,
                NFTCollection.address_uri: stmt.excluded.address_uri,
                NFTCollection.royalty_percent: stmt.excluded.royalty_percent,
                NFTCollection.description: stmt.excluded.description,
            },
        )
        db.execute(
            stmt,
            [
                {
                    "name": "The Heist",
                    "uid": "theheist",
                    "address_uri": "solana:6d9pvGuM6iG9GVuxRzSVHEQCdy44arm6oyqu6aUzrzLo",
                    "royalty_percent": Decimal("0.05"),
                    "description": "A high-stakes, risk-based game of crime, corruption...and bananas.",
                },
                {
                    "name": "The Heist Land",
                    "uid": "the_heist_land",
                    "address_uri": "solana:FiYyx2DXEsQtK6iVzQyFzJSistUa56xnKFagotrd2Mzf",
                    "royalty_percent": Decimal("0.05"),
                    "description": "Land plays an integral role in the Heist ecosystem. A player's Land will be their home base to farm in-game assets, upgrade infrastructure, and earn tokens.",
                },
                {
                    "name": "The Heist: Generations",
                    "uid": "the_heist_generations",
                    "address_uri": "solana:9P47xMr4Z9jETNt8okSbr4VLmiUfaGWgE2vy3ECpeVN8",
                    "royalty_percent": Decimal("0.05"),
                    "description": "A high-stakes, risk-based game of crime, corruption...and bananas.",
                },
            ],
        )
