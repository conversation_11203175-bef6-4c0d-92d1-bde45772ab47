import json
import urllib.parse
from decimal import Decimal
from typing import Any, TypedDict

import httpx
from pydantic import BaseModel, RootModel


class CollectionAttribute(TypedDict):
    traitType: str
    value: str


class CollectionListing(BaseModel):
    pdaAddress: str
    auctionHouse: str
    tokenAddress: str
    tokenMint: str
    seller: str
    sellerReferral: str | None = None
    tokenSize: float
    price: Decimal
    priceInfo: dict[str, Any] | None = None
    rarity: dict[str, Any]
    extra: dict[str, Any] | None = None
    expiry: float
    token: dict[str, Any]
    listingSource: str


CollectionListingsList = RootModel[list[CollectionListing]]


class MagicEdenClient:
    """Magic Eden API Client

    API Docs: https://docs.magiceden.io/reference/solana-overview
    """

    def __init__(self, *, timeout: float = 5) -> None:
        self._client = httpx.Client(
            base_url="https://api-mainnet.magiceden.dev/v2/", timeout=timeout
        )

    def collection_listings(
        self,
        symbol: str,
        *,
        attributes: list[list[CollectionAttribute]] | None = None,
        limit: int | None = None,
    ) -> list[CollectionListing]:
        """https://docs.magiceden.io/reference/get_collections-symbol-listings"""
        symbol_encoded = urllib.parse.quote(symbol, safe="")
        params = _prepare_params([("attributes", _json_param(attributes)), ("limit", limit)])
        resp = self._client.get(f"collections/{symbol_encoded}/listings", params=params)
        resp.raise_for_status()
        return CollectionListingsList.model_validate_json(resp.content).root


def _json_param(data: Any) -> str:
    if data is None:
        return None
    return json.dumps(data, separators=(",", ":"))


def _prepare_params(params: list[tuple[str, Any]]) -> list[tuple[str, Any]]:
    return [(k, v) for k, v in params if v is not None]
