import uuid

import httpx

from common import conf
from common.conf import logger, settings

from .models import (
    CollectionResponse,
    TokenMetadataResponse,
    TokenMetadataResult,
    Transaction,
    TransactionsResponse,
)


class HeliusRPCClient:
    def __init__(
        self,
        api_key: str | None = None,
        domain: str | None = None,
        das_domain: str | None = None,
    ):
        self.api_key = api_key or settings.helius_api_key
        self.domain = domain or settings.helius_domain
        self.das_domain = das_domain or settings.helius_das_domain

        if not self.api_key or not self.domain:
            if settings.is_local:
                logger.warning("Missing Helius api key, skipping client initialization.")
                return
            raise Exception("Missing api key and/or domain")

        self._client = httpx.Client(
            base_url=self.domain,
            params=[("api-key", self.api_key)],
            headers=[("Accept", "application/json")],
            timeout=60.0,
        )

        self._das_client = httpx.Client(
            base_url=self.das_domain,
            params=[("api-key", self.api_key)],
            headers=[("Accept", "application/json")],
            timeout=60.0,
        )

    @classmethod
    def from_env(cls) -> "HeliusRPCClient":
        return HeliusRPCClient(
            conf.settings.helius_api_key,
            conf.settings.helius_domain,
            conf.settings.helius_das_domain,
        )

    def get_address_transactions(
        self, wallet_address: str, last_fetched_tx_hash: str | None = None
    ) -> list[Transaction]:
        """Get transactions for a wallet address."""
        endpoint = f"/v0/addresses/{wallet_address}/transactions"

        resp = self._client.get(
            endpoint, params={"before": last_fetched_tx_hash} if last_fetched_tx_hash else None
        )
        resp.raise_for_status()
        return TransactionsResponse.model_validate_json(resp.content).root

    def get_assets_by_group(self, group: str, *, page: int = 1) -> CollectionResponse:
        logger.info(f"Fetching NFT collections from Helius collection={group} page={page}")
        query_data = {
            "jsonrpc": "2.0",
            "id": "temet",
            "method": "getAssetsByGroup",
            "params": {
                "after": "",
                "before": "",
                "groupKey": "collection",
                "groupValue": group,
                "limit": 1000,
                "page": page,
                "displayOptions": {
                    "showUnverifiedCollections": False,
                    "showGrandTotal": True,
                },
                "sortBy": {
                    "sortBy": "created",
                    "sortDirection": "asc",
                },
            },
        }
        resp = self._das_client.post("", json=query_data, timeout=60)
        resp.raise_for_status()
        return CollectionResponse.model_validate_json(resp.content)

    def get_tokens_metadata(self, mint_accounts: list[str]) -> list[TokenMetadataResult]:
        """Get metadata for token mint accounts."""
        resp = self._das_client.post(
            url="",
            json={
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "getAssetBatch",
                "params": {"ids": mint_accounts},
            },
        )
        resp.raise_for_status()
        result = TokenMetadataResponse.model_validate_json(resp.content).result
        return [data for data in result if data is not None]


helius_client = HeliusRPCClient.from_env()
