import { isLocal } from "@/context/env";
import { RootState } from "@/store";
import { selectAuthToken, setLogOut } from "@/store/apps/auth";
import { ImportRequest } from "@/types/indexer";
import {
  AccountLoan,
  AccountLoanFull,
  AccountPool,
  AccountPoolFull,
  AddAccount,
  AddClientTransaction,
  AddCoin,
  AddExchange,
  AddFarm,
  AddLendingProtocol,
  AddNetwork,
  AddTransaction,
  AddWallet,
  AdminClientTransaction,
  AliasCoin,
  AssetHoldingsResponse,
  Bridge,
  CapitalPositionSummary,
  CapitalPositionSummaryList,
  ClientTransaction,
  ClientTransactionWithUser,
  CoinWithAssetAddress,
  CoinWithNetworks,
  Dashboard,
  EditWallet,
  Exchange,
  ExchangeWithAccountAddress,
  Farm,
  FarmWithAccountAddress,
  JobResponse,
  LendingProtocol,
  LendingProtocolLiquidationThreshold,
  LendingProtocolWithAccountAddress,
  LoginPassword,
  Network,
  NetworkWithNativeCoin,
  OpenPositionSummary,
  OpenPositionSummaryList,
  PaginatedResultTransaction,
  PaginatedResultTransactionUnclassified,
  PoolPriceAlert,
  ReplaceTransaction,
  Stake,
  StakeDetails,
  Transaction,
  TransactionAction,
  TransactionClassify,
  TransactionsOverview,
  UpdateAliasCoin,
  UpdateCoin,
  User,
  UserLiability,
  Wallet,
} from "@/types/schemas";
import type {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
  FetchBaseQueryMeta,
} from "@reduxjs/toolkit/query";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

type UrlSearchValue = string | number | boolean;

type NameValuePair = readonly [
  string,
  UrlSearchValue | readonly UrlSearchValue[] | null | undefined,
];

/**
 * @param params Parameters names and their respective values. The
 * parameters with values of `null` or `undefined` are omitted.
 * @returns URL search string with a leading `?` or empty string
 */
export function getUrlSearch(params: readonly NameValuePair[]): string {
  const searchParams = new URLSearchParams();
  for (const [name, value] of params) {
    if (value != null) {
      if (typeof value === "object") {
        for (const item of value) {
          searchParams.append(name, `${item}`);
        }
      } else {
        searchParams.append(name, `${value}`);
      }
    }
  }

  const search = searchParams.toString();
  if (search.length === 0) return "";

  return `?${search}`;
}

const baseQuery = fetchBaseQuery({
  baseUrl: process.env.NEXT_PUBLIC_API_URL,

  prepareHeaders: (headers, { getState }) => {
    // Use any value for token locally to bypass Google Auth
    const token = selectAuthToken(getState() as RootState) ?? (isLocal ? "token" : null);

    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }

    // Causes the request to 401 if no token is set and the login flow to take over
    return headers;
  },
});

const baseQueryWithReAuth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError,
  object,
  FetchBaseQueryMeta
> = async (args, api, extraOptions: object) => {
  const result = await baseQuery(args, api, extraOptions);

  if (
    result.error &&
    result.error.status === 401 &&
    (typeof args === "string" || !args.url.startsWith("auth/login"))
  ) {
    // Log User out on a 401 if not already trying to log in
    api.dispatch(setLogOut(true));
  }

  return result;
};

// TODO: invalidate by ID instead of all objects of a type
export const api = createApi({
  baseQuery: baseQueryWithReAuth,
  refetchOnMountOrArgChange: true,
  tagTypes: [
    // Accounting
    "CapitalPositionSummary",
    "CapitalPositionSummaryList",
    "OpenPositionSummary",
    "OpenPositionSummaryList",

    // Auth
    "User",

    // Indexer
    "Account",
    "Bridge",
    "Exchange",
    "Farm",
    "LendingProtocol",
    "Network",
    "Pool",
    "AccountPool",
    "AccountPoolFull",
    "AccountLoan",
    "AccountLoanFull",
    "PoolPriceAlert",
    "StakeDetails",
    "Transaction",
    "TransactionUnclassified",
    "Wallet",

    // Market
    "AliasCoin",
    "ClientTransaction",
    "Coin",
  ],
  endpoints: (builder) => ({
    // Accounting
    accountingCapitalPositionListSummary: builder.query<
      CapitalPositionSummaryList,
      { generatedOnly?: boolean; year?: number }
    >({
      query: ({ generatedOnly, year }) =>
        `accounting/capital-positions${getUrlSearch([
          ["generated_only", generatedOnly],
          ["year", year],
        ])}`,
      providesTags: ["CapitalPositionSummaryList"],
    }),
    accountingCapitalPositionSummary: builder.query<
      CapitalPositionSummary,
      { coinId: number; generatedOnly?: boolean; year?: number }
    >({
      query: ({ coinId, generatedOnly, year }) =>
        `accounting/capital-positions/coin/${coinId}${getUrlSearch([
          ["generated_only", generatedOnly],
          ["year", year],
        ])}`,
      providesTags: ["CapitalPositionSummary"],
    }),
    accountingDeleteCapitalPositions: builder.mutation<null, { year: number }>({
      query: ({ year }) => {
        const search = getUrlSearch([["year", year]]);
        return {
          url: `accounting/capital-positions${search}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["CapitalPositionSummaryList"],
    }),
    accountingRegenerateCapitalPositions: builder.mutation<null, void>({
      query: () => {
        return {
          url: `accounting/capital-positions/regenerate`,
          method: "POST",
        };
      },
      invalidatesTags: ["CapitalPositionSummaryList"],
    }),
    accountingDownloadForm8949: builder.mutation<Blob, { year: number }>({
      query: ({ year }) => ({
        url: `accounting/capital-positions/generate-tax-form-8949/${year}`,
        method: "GET",
        responseHandler: (response) => response.blob(),
      }),
    }),
    accountingOpenPositionListSummary: builder.query<OpenPositionSummaryList, void>({
      query: () => `accounting/open-positions`,
      providesTags: ["CapitalPositionSummaryList"],
    }),
    accountingOpenPositionSummary: builder.query<OpenPositionSummary, { coinId: number }>({
      query: ({ coinId }) => `accounting/open-positions/coin/${coinId}`,
      providesTags: ["OpenPositionSummary"],
    }),

    // Auth
    authUserAcceptAgreement: builder.mutation<User, void>({
      query: () => {
        return {
          url: "auth/user/accept-agreement",
          method: "POST",
        };
      },
      invalidatesTags: ["User"],
    }),
    authUserAdd: builder.mutation<User, string>({
      query: (email) => {
        return {
          url: `auth/user/add?email=${email}`,
          method: "POST",
        };
      },
      invalidatesTags: ["User"],
    }),
    authUserAddWithPassword: builder.mutation<User, LoginPassword>({
      query: (login_password) => {
        return {
          url: `auth/user/add-with-password`,
          method: "POST",
          body: login_password,
        };
      },
      invalidatesTags: ["User"],
    }),
    authUserDelete: builder.mutation<void, number>({
      query: (user_id) => {
        return {
          url: `auth/user?user_id=${user_id}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["User"],
    }),
    authUserUpdate: builder.mutation<User, User>({
      query: (user) => {
        return {
          url: `auth/user/update`,
          method: "PATCH",
          body: user,
        };
      },
      invalidatesTags: ["User"],
    }),
    authUsers: builder.query<User[], boolean>({
      query: (includeAdmins: boolean) =>
        `auth/users${getUrlSearch([["include_admins", includeAdmins]])}`,
      providesTags: ["User"],
    }),
    authLogin: builder.mutation<User, string>({
      query: (google_auth_token) => {
        return {
          url: "auth/login",
          method: "GET",
          headers: { Authorization: `Bearer ${google_auth_token}` },
        };
      },
    }),
    authLoginWithPassword: builder.mutation<User, LoginPassword>({
      query: (loginPassword) => {
        return {
          url: "auth/login-with-password",
          method: "POST",
          body: loginPassword,
        };
      },
    }),
    authMe: builder.query<User, void>({
      query: () => "auth/me",
      providesTags: ["User"],
    }),

    // Client
    clientDepositAddress: builder.query<string, number>({
      query: (networkId) => `client/deposit-address?network_id=${networkId}`,
    }),
    clientNetworks: builder.query<Network[], void>({
      query: () => `client/networks`,
      providesTags: ["Network"],
    }),
    clientWalletAdd: builder.mutation<Wallet, AddWallet>({
      query: (addWallet) => {
        return {
          url: `client/wallet/add`,
          method: "POST",
          body: addWallet,
        };
      },
      invalidatesTags: ["Wallet"],
    }),
    clientWalletEdit: builder.mutation<Wallet, EditWallet>({
      query: (editWallet) => {
        return {
          url: `client/wallet/edit`,
          method: "POST",
          body: editWallet,
        };
      },
      invalidatesTags: ["Wallet"],
    }),
    clientWallets: builder.query<Wallet[], Network | undefined>({
      query: (network: Network) => {
        let url = `client/wallets?`;
        if (network) {
          url += `network_id=${network.id}&`;
        }

        return url;
      },
      providesTags: ["Wallet"],
    }),

    //Client Transactions
    clientDashboard: builder.query<Dashboard, User | null>({
      query: (user) => `client/dashboard${(user && `?user_id=${user.id}`) || ""}`,
      providesTags: ["ClientTransaction"],
    }),
    clientTransactionAdd: builder.mutation<ClientTransaction, AddClientTransaction>({
      query: (clientTransaction) => {
        return {
          url: `client/client-transaction/add`,
          method: "POST",
          body: clientTransaction,
        };
      },
      invalidatesTags: ["ClientTransaction"],
    }),
    clientTransactionsPending: builder.query<ClientTransaction[], void>({
      query: () => `client/client-transactions/pending`,
      providesTags: ["ClientTransaction"],
    }),

    // Indexer
    indexerAccount: builder.query<AddAccount, { address: string; networkId: number }>({
      query: ({ address, networkId }) =>
        `indexer/admin/account?address=${address}&network_id=${networkId}`,
      providesTags: ["Account"],
    }),
    indexerBridgeAdd: builder.mutation<Bridge, Bridge>({
      query: (bridge) => {
        return {
          url: `indexer/admin/bridge/add`,
          method: "POST",
          body: bridge,
        };
      },
      invalidatesTags: ["Bridge"],
    }),
    indexerBridges: builder.query<Bridge[], void>({
      query: () => `indexer/admin/bridges`,
      providesTags: ["Bridge"],
    }),
    indexerClientTransactionDelete: builder.mutation<void, number>({
      query: (clientTransactionId) => {
        return {
          url: `indexer/admin/client-transaction/delete?client_transaction_id=${clientTransactionId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["ClientTransaction"],
    }),
    indexerClientTransactions: builder.query<AdminClientTransaction[], [User, boolean]>({
      query: ([user, onlyPending]) =>
        `indexer/admin/client-transactions${getUrlSearch([
          ["only_pending", onlyPending],
          ["user_id", user.id],
        ])}`,
      providesTags: ["ClientTransaction"],
    }),
    indexerExchangeAdd: builder.mutation<Exchange, AddExchange>({
      query: (addExchange) => {
        return {
          url: `indexer/admin/exchange/add`,
          method: "POST",
          body: addExchange,
        };
      },
      invalidatesTags: ["Exchange"],
    }),
    indexerExchanges: builder.query<Exchange[], void>({
      query: () => `indexer/admin/exchanges`,
      providesTags: ["Exchange"],
    }),
    indexerExchangesWithAccountAddress: builder.query<ExchangeWithAccountAddress[], void>({
      query: () => `indexer/admin/exchanges/with-account-address`,
      providesTags: ["Exchange"],
    }),
    indexerLendingProtocols: builder.query<LendingProtocolWithAccountAddress[], void>({
      query: () => `indexer/admin/lending-protocols/with-account-address`,
      providesTags: ["LendingProtocol"],
    }),
    indexerFarmAdd: builder.mutation<Farm, AddFarm>({
      query: (addFarm) => {
        return {
          url: `indexer/admin/farm/add`,
          method: "POST",
          body: addFarm,
        };
      },
      invalidatesTags: ["Farm"],
    }),
    indexerFarms: builder.query<Farm[], void>({
      query: () => `indexer/admin/farms`,
      providesTags: ["Farm"],
    }),
    indexerFarmsWithAccountAddress: builder.query<FarmWithAccountAddress[], void>({
      query: () => `indexer/admin/farms/with-account-address`,
      providesTags: ["Farm"],
    }),
    indexerLiabilities: builder.query<UserLiability[], void>({
      query: () => "indexer/admin/liabilities",
    }),

    indexerNetworkAdd: builder.mutation<NetworkWithNativeCoin, AddNetwork>({
      query: (addNetwork) => {
        return {
          url: `indexer/admin/network/add`,
          method: "POST",
          body: addNetwork,
        };
      },
      invalidatesTags: ["Network"],
    }),
    indexerNetworkUpdate: builder.mutation<NetworkWithNativeCoin, Network>({
      query: (network) => {
        return {
          url: `indexer/admin/network/update`,
          method: "POST",
          body: network,
        };
      },
      invalidatesTags: ["Network"],
    }),
    indexerNetworks: builder.query<NetworkWithNativeCoin[], void>({
      query: () => `indexer/admin/networks`,
      providesTags: ["Network"],
    }),
    indexerNetworksWithAdminWallets: builder.query<NetworkWithNativeCoin[], void>({
      query: () => `indexer/admin/networks/with-admin-wallets`,
      providesTags: ["Network"],
    }),

    indexerLendingProtocolAdd: builder.mutation<LendingProtocol, AddLendingProtocol>({
      query: (lendingProtocol) => {
        return {
          url: `indexer/admin/lending-protocol/add`,
          method: "POST",
          body: lendingProtocol,
        };
      },
      invalidatesTags: ["LendingProtocol"],
    }),
    indexerLendingProtocolLiqTUpdate: builder.mutation<
      void,
      [number, LendingProtocolLiquidationThreshold]
    >({
      query: ([loanId, lendingProtocolLiquidationThreshold]) => {
        return {
          url: `indexer/admin/loans/${loanId}`,
          method: "PUT",
          body: lendingProtocolLiquidationThreshold,
        };
      },
      invalidatesTags: ["AccountLoanFull"],
    }),
    indexerPools: builder.query<AccountPool[], void>({
      query: () => `indexer/admin/pools`,
      providesTags: ["AccountPool"],
    }),
    indexerPool: builder.query<AccountPoolFull, number>({
      query: (id) => `indexer/admin/pools/${id}`,
      providesTags: ["AccountPoolFull"],
    }),
    indexerPoolPriceAlerts: builder.query<PoolPriceAlert[], number>({
      query: (accountId) => `indexer/admin/pools/${accountId}/price-alerts`,
      providesTags: ["PoolPriceAlert"],
    }),
    indexerPoolPriceAlertDelete: builder.mutation<void, { accountId: number; coinId: number }>({
      query: ({ accountId, coinId }) => {
        return {
          url: `indexer/admin/pools/${accountId}/price-alerts/${coinId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["PoolPriceAlert"],
    }),
    indexerPoolPriceAlertUpsert: builder.mutation<void, PoolPriceAlert>({
      query: ({ accountId, coinId, maxPriceUsd, minPriceUsd }) => {
        return {
          url: `indexer/admin/pools/${accountId}/price-alerts/${coinId}`,
          method: "PUT",
          body: { minPriceUsd, maxPriceUsd },
        };
      },
      invalidatesTags: ["PoolPriceAlert"],
    }),
    indexerLoans: builder.query<AccountLoan[], { p2p?: boolean } | void>({
      query: ({ p2p } = {}) => `indexer/admin/loans${getUrlSearch([["p2p", p2p]])}`,
      providesTags: ["AccountLoan"],
    }),
    indexerLoan: builder.query<AccountLoanFull, number>({
      query: (id) => `indexer/admin/loans/${id}`,
      providesTags: ["AccountLoanFull"],
    }),
    indexerTransactionClassify: builder.mutation<void, [TransactionClassify, number | null]>({
      query: ([transactionClassify, clientTransactionId]) => {
        const params = getUrlSearch([["client_transaction_id", clientTransactionId]]);
        return {
          url: `indexer/admin/transaction/classify${params}`,
          method: "PUT",
          body: transactionClassify,
        };
      },
      invalidatesTags: ["TransactionUnclassified"],
    }),
    indexerTransactionAdd: builder.mutation<Transaction, [AddTransaction, number | null]>({
      query: ([transaction, clientTransactionId]) => {
        let url = `indexer/admin/transaction/add`;
        if (clientTransactionId) {
          url += `?client_transaction_id=${clientTransactionId}`;
        }

        return {
          url: url,
          method: "POST",
          body: transaction,
        };
      },
      invalidatesTags: ["ClientTransaction", "Transaction"],
    }),
    indexerTransactionDelete: builder.mutation<void, number>({
      query: (transactionId) => {
        return {
          url: `indexer/admin/transaction?transaction_id=${transactionId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["TransactionUnclassified"],
    }),
    indexerTransactionQueryCovalent: builder.query<void, [number, number, string]>({
      query: ([networkId, walletId, txHash]) =>
        `indexer/admin/transaction/query-covalent?network_id=${networkId}&wallet_id=${walletId}&tx_hash=${txHash}`,
    }),
    indexerTransactionReplace: builder.mutation<Transaction, ReplaceTransaction>({
      query: (replaceTransaction) => {
        return {
          url: `indexer/admin/transaction/replace`,
          method: "POST",
          body: replaceTransaction,
        };
      },
      invalidatesTags: ["ClientTransaction", "Transaction", "TransactionUnclassified"],
    }),
    indexerTransactionReset: builder.mutation<void, number>({
      query: (transactionId) => {
        return {
          url: `indexer/admin/transaction/reset?transaction_id=${transactionId}`,
          method: "POST",
        };
      },
      invalidatesTags: ["Transaction", "AccountPoolFull", "AccountLoanFull"],
    }),
    indexerTransaction: builder.query<Transaction, number>({
      query: (id) => `indexer/admin/transactions/${id}`,
      providesTags: ["Transaction"],
    }),
    indexerTransactionClientTransaction: builder.query<
      ClientTransactionWithUser | null,
      number
    >({
      query: (id) => `indexer/admin/transactions/${id}/client-transaction`,
    }),
    indexerTransactionPossibleActions: builder.query<TransactionAction[], number>({
      query: (id) => `indexer/admin/transactions/${id}/possible-actions`,
    }),
    indexerTransactions: builder.query<
      PaginatedResultTransaction,
      {
        fromId?: number;
        page?: "next" | "prev";
        limit?: number;
        searchQuery?: string;
        timeQuery?: string;
      }
    >({
      query: ({ fromId, page, searchQuery, timeQuery, limit } = {}) =>
        `indexer/admin/transactions${getUrlSearch([
          ["from_id", fromId],
          ["page", page],
          ["limit", limit],
          timeQuery ? ["time_query", timeQuery] : ["search_query", searchQuery],
        ])}`,
      providesTags: ["Transaction"],
    }),
    indexerTransactionsUnclassified: builder.query<
      PaginatedResultTransactionUnclassified,
      {
        fromId?: number;
        page?: "next" | "prev";
        limit?: number;
        searchQuery?: string;
        timeQuery?: string;
      }
    >({
      query: ({ fromId, page, searchQuery, timeQuery, limit } = {}) =>
        `indexer/admin/transactions/unclassified${getUrlSearch([
          ["from_id", fromId],
          ["page", page],
          ["limit", limit],
          timeQuery ? ["time_query", timeQuery] : ["search_query", searchQuery],
        ])}`,
      providesTags: ["TransactionUnclassified"],
    }),
    indexerTransferDelete: builder.mutation<void, number>({
      query: (transferId) => {
        return {
          url: `indexer/admin/transfers/${transferId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["TransactionUnclassified"],
    }),
    indexerWalletAdd: builder.mutation<Wallet, AddWallet>({
      query: (addWallet) => {
        return {
          url: `indexer/admin/wallet/add`,
          method: "POST",
          body: addWallet,
        };
      },
      invalidatesTags: ["Wallet"],
    }),
    indexerWalletSync: builder.mutation<JobResponse, number>({
      query: (walletId) => {
        return {
          url: `indexer/admin/wallet/sync?wallet_id=${walletId}`,
          method: "POST",
        };
      },
    }),
    indexerWalletEdit: builder.mutation<Wallet, EditWallet>({
      query: (editWallet) => {
        return {
          url: `indexer/admin/wallet/edit`,
          method: "POST",
          body: editWallet,
        };
      },
      invalidatesTags: ["Wallet"],
    }),
    indexerWallets: builder.query<
      Wallet[],
      [Network | null | undefined, User | null | undefined]
    >({
      query: ([network, user]) => {
        let url = `indexer/admin/wallets?`;
        if (network) {
          url += `network_id=${network.id}&`;
        }
        if (user) {
          url += `user_id=${user.id}&`;
        }

        return url;
      },
      providesTags: ["Wallet"],
    }),
    indexerImportCointracking: builder.mutation<JobResponse, ImportRequest>({
      query: ({ provider, file, skipAlreadyInDb, mergeInFileDuplicates }) => {
        const search = getUrlSearch([
          ["skip_already_in_db", skipAlreadyInDb],
          ["merge_in_file_duplicates", mergeInFileDuplicates],
        ]);
        const formData = new FormData();
        formData.append("file", file);

        return {
          url: `indexer/admin/import/${provider}${search}`,
          method: "POST",
          body: formData,
        };
      },
    }),
    indexerJob: builder.query<JobResponse, number>({
      query: (jobId) => `indexer/admin/jobs/${jobId}`,
    }),
    indexerExpenses: builder.query<TransactionsOverview, void>({
      query: () => "indexer/admin/expenses",
      providesTags: ["Transaction"],
    }),
    indexerIncome: builder.query<TransactionsOverview, void>({
      query: () => "indexer/admin/income",
      providesTags: ["Transaction"],
    }),
    indexerStake: builder.query<Stake[], void>({
      query: () => "indexer/admin/stake",
      providesTags: ["Transaction"],
    }),
    indexerStakeCoin: builder.query<
      StakeDetails,
      { stakeId: number; networkId: number; coinId: number }
    >({
      query: ({ stakeId, networkId, coinId }) =>
        `indexer/admin/stake/${stakeId}${getUrlSearch([
          ["network_id", networkId],
          ["coin_id", coinId],
        ])}`,
      providesTags: ["StakeDetails"],
    }),
    indexerSyncBlockFills: builder.query<void, void>({
      query: () => `indexer/admin/sync/block-fills`,
    }),

    indexerSyncInternalTransactions: builder.mutation<number, string>({
      query: (company) => ({
        url: `indexer/admin/internal-sync/transactions/sync/${company}`,
        method: "POST",
      }),
    }),

    // Market
    marketAliasCoins: builder.query<AliasCoin[], void>({
      query: () => "market/alias-coins",
      providesTags: ["AliasCoin"],
    }),
    marketAliasCoinAdd: builder.mutation<AliasCoin, number>({
      query: (id: number) => {
        return {
          url: "market/alias-coins",
          method: "POST",
          body: { coinId: id },
        };
      },
      invalidatesTags: ["AliasCoin"],
    }),
    marketAliasCoinDelete: builder.mutation<void, number>({
      query: (aliasCoinId) => {
        return {
          url: `market/alias-coins/${aliasCoinId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["AliasCoin"],
    }),
    marketAliasCoinUpdate: builder.mutation<
      UpdateAliasCoin,
      { aliasCoinId: number; aliasedCoinsIds: number[] }
    >({
      query: ({
        aliasCoinId,
        aliasedCoinsIds,
      }: {
        aliasCoinId: number;
        aliasedCoinsIds: number[];
      }) => {
        return {
          url: `market/alias-coins/${aliasCoinId}`,
          method: "PATCH",
          body: { aliasedCoinsIds },
        };
      },
      invalidatesTags: ["AliasCoin"],
    }),
    marketCoins: builder.query<CoinWithNetworks[], { coin?: string; withPrices?: boolean }>({
      query: ({ coin, withPrices }) =>
        `market/coins${getUrlSearch([
          ["search", coin],
          ["with_prices", withPrices],
        ])}`,
      providesTags: ["Coin"],
    }),
    marketCoinsByNetworkWithAssetAddress: builder.query<
      CoinWithAssetAddress[],
      { coin: string; networkId: number | undefined }
    >({
      query: ({ coin, networkId }) =>
        `market/coins/asset-address${getUrlSearch([
          ["search", coin],
          ["network_id", networkId],
        ])}`,
      providesTags: ["Coin"],
    }),
    marketCoinsUsableByClients: builder.query<CoinWithNetworks[], void>({
      query: () => "market/coins/usable-by-clients",
      providesTags: ["Coin"],
    }),
    marketCoinsWithPrices: builder.query<
      CoinWithNetworks[],
      {
        excludeAliasCoins?: boolean;
        excludeCoinsIds?: number[];
        includeWithoutPrices?: boolean;
        search?: string;
      }
    >({
      query: ({ excludeAliasCoins, excludeCoinsIds, includeWithoutPrices, search }) =>
        `market/coins/with-prices${getUrlSearch([
          ["exclude_alias_coins", excludeAliasCoins],
          ["exclude_coins_ids", excludeCoinsIds],
          ["include_without_prices", includeWithoutPrices],
          ["search", search],
        ])}`,
      providesTags: ["Coin"],
    }),
    marketCoinAdd: builder.mutation<CoinWithNetworks, AddCoin>({
      query: (addCoin) => {
        return {
          url: "market/coin/add",
          method: "POST",
          body: addCoin,
        };
      },
      invalidatesTags: ["Coin"],
    }),
    marketCoinDelete: builder.mutation<void, number>({
      query: (coinId) => {
        return {
          url: `market/coins/${coinId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["Coin"],
    }),
    marketCoinUpdateHasPrices: builder.mutation<CoinWithNetworks, UpdateCoin>({
      query: (updateCoin) => {
        return {
          url: `market/coin/update`,
          method: "POST",
          body: updateCoin,
        };
      },
      invalidatesTags: ["Coin"],
    }),

    // Asset Holdings
    assetHoldings: builder.query<AssetHoldingsResponse, void>({
      query: () => "indexer/admin/asset-holdings",
    }),
    assetTransactions: builder.query<Transaction[], number>({
      query: (coinId) => `indexer/admin/asset-holdings/${coinId}/transactions`,
    }),
  }),
});

export const {
  useAssetHoldingsQuery,
  useAssetTransactionsQuery,
  useAccountingCapitalPositionSummaryQuery,
  useAccountingCapitalPositionListSummaryQuery,
  useAccountingDeleteCapitalPositionsMutation,
  useAccountingRegenerateCapitalPositionsMutation,
  useAccountingDownloadForm8949Mutation,
  useAccountingOpenPositionSummaryQuery,
  useAccountingOpenPositionListSummaryQuery,

  useAuthLoginMutation,
  useAuthLoginWithPasswordMutation,
  useLazyAuthMeQuery,
  useAuthUserAcceptAgreementMutation,
  useAuthUserAddMutation,
  useAuthUserAddWithPasswordMutation,
  useAuthUserDeleteMutation,
  useAuthUserUpdateMutation,
  useAuthUsersQuery,

  useClientDashboardQuery,
  useLazyClientDepositAddressQuery,
  useClientNetworksQuery,
  useClientTransactionsPendingQuery,
  useClientWalletAddMutation,
  useClientWalletEditMutation,
  useClientWalletsQuery,

  useIndexerLiabilitiesQuery,

  useClientTransactionAddMutation,
  useIndexerClientTransactionDeleteMutation,

  useLazyIndexerAccountQuery,
  useIndexerBridgeAddMutation,
  useIndexerBridgesQuery,
  useIndexerClientTransactionsQuery,
  useIndexerExchangeAddMutation,
  useIndexerExchangesQuery,
  useIndexerExchangesWithAccountAddressQuery,
  useIndexerFarmAddMutation,
  useIndexerFarmsQuery,
  useIndexerFarmsWithAccountAddressQuery,
  useIndexerLendingProtocolAddMutation,
  useIndexerLendingProtocolsQuery,
  useIndexerLendingProtocolLiqTUpdateMutation,

  useIndexerNetworkAddMutation,
  useIndexerNetworkUpdateMutation,
  useIndexerNetworksQuery,
  useIndexerNetworksWithAdminWalletsQuery,

  useIndexerPoolsQuery,
  useIndexerPoolQuery,
  useIndexerPoolPriceAlertsQuery,
  useIndexerPoolPriceAlertDeleteMutation,
  useIndexerPoolPriceAlertUpsertMutation,
  useIndexerLoansQuery,
  useIndexerLoanQuery,
  useIndexerTransactionAddMutation,
  useIndexerTransactionDeleteMutation,
  useIndexerTransactionClassifyMutation,
  useLazyIndexerTransactionQueryCovalentQuery,
  useIndexerTransactionReplaceMutation,
  useIndexerTransactionResetMutation,
  useIndexerTransactionsQuery,
  useIndexerTransactionQuery,
  useIndexerTransactionClientTransactionQuery,
  useIndexerTransactionPossibleActionsQuery,
  useIndexerTransactionsUnclassifiedQuery,
  useIndexerTransferDeleteMutation,
  useIndexerWalletAddMutation,
  useIndexerWalletSyncMutation,
  useIndexerWalletEditMutation,
  useIndexerWalletsQuery,
  useIndexerImportCointrackingMutation,
  useLazyIndexerJobQuery,
  useIndexerExpensesQuery,
  useIndexerIncomeQuery,
  useIndexerStakeQuery,
  useIndexerStakeCoinQuery,
  useLazyIndexerSyncBlockFillsQuery,
  useIndexerSyncInternalTransactionsMutation,
  useMarketAliasCoinsQuery,
  useMarketAliasCoinAddMutation,
  useMarketAliasCoinDeleteMutation,
  useMarketAliasCoinUpdateMutation,
  useMarketCoinsQuery,
  useMarketCoinsByNetworkWithAssetAddressQuery,
  useMarketCoinsUsableByClientsQuery,
  useMarketCoinsWithPricesQuery,
  useMarketCoinAddMutation,
  useMarketCoinDeleteMutation,
  useMarketCoinUpdateHasPricesMutation,
} = api;
