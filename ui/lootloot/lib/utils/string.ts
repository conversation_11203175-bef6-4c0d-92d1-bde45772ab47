import base58 from "bs58";

export function capitalize<T extends string>(str: T): Capitalize<T> {
  return (str.charAt(0).toUpperCase() + str.slice(1)) as Capitalize<T>;
}

export function shortenAddress(address: string): string {
  if (address.length < 10) {
    return address;
  }
  return `${address.slice(0, 4)}…${address.slice(-4)}`;
}

export function shortenSolanaAddress(address: string | ArrayBuffer | Uint8Array): string {
  if (typeof address === "object") {
    if (address instanceof ArrayBuffer) {
      address = new Uint8Array(address);
    }
    address = base58.encode(address);
  }
  return shortenAddress(address);
}
