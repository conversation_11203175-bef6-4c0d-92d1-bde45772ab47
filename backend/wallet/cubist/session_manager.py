import asyncio
import base64
import logging
import time
from enum import Enum
from typing import Any
from urllib.parse import quote_plus

import httpx
import nacl.secret
from google.cloud import secretmanager_v1
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception,
    stop_after_delay,
    wait_exponential_jitter,
)

from common.conf import settings
from common.psql import async_session
from wallet.models import CubeSignerSession

logger = logging.getLogger(__name__)

TOKEN_EXP_ACCEPTABLE = 30
TOKEN_EXP_STALE = 3
DEFAULT_SESSION_NAME = "default"


class SessionOrRefreshTokenExpired(ValueError):
    """Raised when the session or refresh token has expired"""

    pass


def is_5xx_error(e: BaseException) -> bool:
    """Check if an exception is an HTTPStatusError with a 5XX status code"""
    if not isinstance(e, httpx.HTTPStatusError):
        return False
    return 500 <= e.response.status_code < 600


class TokenState(Enum):
    """State of the authentication token"""

    FRESH = 1  # Token is fresh
    ACCEPTABLE = 2  # Token will expire soon but can still be used
    STALE = 3  # Token needs immediate refresh


class ClientSessionInfo(BaseModel):
    """Session information from the CubeSigner API"""

    session_id: str
    epoch: int
    epoch_token: str
    auth_token: str
    refresh_token: str
    auth_token_exp: int
    refresh_token_exp: int


class SessionData(BaseModel):
    """JSON representation of the CubeSigner session"""

    org_id: str
    role_id: str | None = None
    purpose: str | None = None
    token: str
    refresh_token: str
    session_info: ClientSessionInfo
    session_exp: int | None = None
    env: dict[str, Any]


class CubeSignerSessionManager:
    """Manages CubeSigner API session state and token refresh"""

    _client: httpx.AsyncClient
    _name: str
    _refresh_task: asyncio.Task | None
    _secret_box: nacl.secret.SecretBox
    _session: SessionData

    def __init__(
        self,
        client: httpx.AsyncClient,
        secret_box: nacl.secret.SecretBox,
        initial_session: SessionData,
        name: str = DEFAULT_SESSION_NAME,
    ) -> None:
        self._client = client
        self._name = name
        self._refresh_task = None
        self._secret_box = secret_box
        self._session = initial_session

    @staticmethod
    async def from_db(
        client: httpx.AsyncClient, name: str = DEFAULT_SESSION_NAME
    ) -> "CubeSignerSessionManager":
        """Create a new session manager from data in the database."""
        secret_box = await _init_secret_box()
        async with async_session.begin() as db:
            session_record = await _load_session(db, name)
            session = _parse_session_data(secret_box, session_record.data)
        return CubeSignerSessionManager(client, secret_box, session, name)

    async def get_session(self) -> SessionData:
        """Get the current session data, handling token refresh based on state."""
        token_state = self._get_token_state()
        if token_state == TokenState.STALE:
            await self._refresh_token()
        elif token_state == TokenState.ACCEPTABLE:
            # Start background refresh if not already running
            if not self._refresh_task or self._refresh_task.done():
                self._refresh_task = asyncio.create_task(
                    self._refresh_token(), name="cubist_token_refresh"
                )
        return self._session

    def _get_token_state(self) -> TokenState:
        """Check the current token state"""
        current_time = int(time.time())
        expiration_time = self._session.session_info.auth_token_exp - current_time

        if expiration_time > TOKEN_EXP_ACCEPTABLE:
            return TokenState.FRESH
        elif expiration_time > TOKEN_EXP_STALE:
            return TokenState.ACCEPTABLE
        return TokenState.STALE

    def _can_refresh(self) -> bool:
        """Check if the session can be refreshed"""
        current_time = int(time.time())

        # Check if session has expired or is too close to expire (if session_exp is set)
        if self._session.session_exp is not None:
            if self._session.session_exp < current_time + TOKEN_EXP_ACCEPTABLE:
                return False

        # Check if refresh token has expired
        return self._session.session_info.refresh_token_exp > current_time

    @retry(
        retry=retry_if_exception(is_5xx_error),
        wait=wait_exponential_jitter(initial=0.2, max=5),
        stop=stop_after_delay(20),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )
    async def _refresh_token(self) -> None:
        """Refresh the session token"""
        async with async_session.begin() as db:
            await self._refresh_token_inner(db)

    async def _refresh_token_inner(self, db: AsyncSession) -> None:
        """Refresh session token operations that must be run within a transaction"""
        # Ensure we have latest session data and lock it for update
        session_record = await _load_session(db, self._name, for_update=True)
        self._session = _parse_session_data(self._secret_box, session_record.data)

        if self._get_token_state() == TokenState.FRESH:
            # Latest session load got us a fresh token, no need to refresh
            return

        if not self._can_refresh():
            raise SessionOrRefreshTokenExpired(
                "Session cannot be refreshed - session or refresh token has expired"
            )

        logger.info("Refreshing CubeSigner token")
        refresh_data = await self._fetch_refresh_data()
        self._session = SessionData.model_validate(
            {**self._session.model_dump(), **refresh_data}
        )
        _save_session(self._secret_box, session_record, self._session)

    async def _fetch_refresh_data(self) -> dict[str, Any]:
        response = await self._client.patch(
            f"/v1/org/{quote_plus(self._session.org_id)}/token/refresh",
            json={
                "epoch_num": self._session.session_info.epoch,
                "epoch_token": self._session.session_info.epoch_token,
                "other_token": self._session.session_info.refresh_token,
            },
            headers={"Authorization": self._session.token},
        )
        response.raise_for_status()
        return response.json()


async def _load_session(
    db: AsyncSession, name: str, *, for_update: bool = False
) -> CubeSignerSession:
    """Load session data from database.

    Use for_update=True to lock the session record for update.
    """
    stmt = select(CubeSignerSession).where(CubeSignerSession.session_name == name)
    if for_update:
        stmt = stmt.with_for_update()
    session_record = await db.scalar(stmt)
    if not session_record:
        raise ValueError(f"No session data found in database for {name}")
    return session_record


def _parse_session_data(secret_box: nacl.secret.SecretBox, data: bytes) -> SessionData:
    """Parse and decrypt session data from bytes."""
    decrypted_data = secret_box.decrypt(data)
    return SessionData.model_validate_json(decrypted_data)


def _save_session(
    secret_box: nacl.secret.SecretBox,
    session_record: CubeSignerSession,
    session: SessionData,
) -> None:
    """Save session data to database record.

    Must be called within a transaction, and the session_record must have
    been acquired with a "FOR UPDATE" lock (for_update=True on load).
    """
    session_json = session.model_dump_json(round_trip=True)
    encrypted_data = secret_box.encrypt(session_json.encode("utf-8"))
    session_record.data = encrypted_data


async def _init_secret_box() -> nacl.secret.SecretBox:
    """Initialize the SecretBox using GCP Secret Manager or dev key."""

    if settings.is_local:
        key_base64 = "JEyjItrpjXDDUmGmiyC2mKuAHbI7CO0dhejP7UwCdW8="
    else:
        if not settings.cubist_session_secret_name:
            raise ValueError("CUBIST_SESSION_SECRET_NAME must be set for non-local envs")
        client = secretmanager_v1.SecretManagerServiceAsyncClient()
        request = secretmanager_v1.AccessSecretVersionRequest(
            name=f"projects/{settings.gcp_project_id}/secrets/{settings.cubist_session_secret_name}/versions/latest"
        )
        response = await client.access_secret_version(request)
        key_base64 = response.payload.data.decode("utf-8")

    key = base64.b64decode(key_base64)
    return nacl.secret.SecretBox(key)
