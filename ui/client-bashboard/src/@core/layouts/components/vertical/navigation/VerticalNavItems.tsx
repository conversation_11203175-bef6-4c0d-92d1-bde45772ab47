// ** Types Import
import { Settings } from "@/@core/context/settingsContext";
import {
  NavGroup,
  NavLink,
  NavSectionTitle,
  VerticalNavItemsType,
} from "@/@core/layouts/types";

// ** Custom Menu Components
import VerticalNavGroup from "@/@core/layouts/components/vertical/navigation/VerticalNavGroup";
import VerticalNavLink from "@/@core/layouts/components/vertical/navigation/VerticalNavLink";
import VerticalNavSectionTitle from "@/@core/layouts/components/vertical/navigation/VerticalNavSectionTitle";

interface Props {
  parent?: NavGroup;
  navHover?: boolean;
  settings: Settings;
  navVisible?: boolean;
  groupActive: string[];
  isSubToSub?: NavGroup;
  currentActiveGroup: string[];
  navigationBorderWidth: number;
  verticalNavItems?: VerticalNavItemsType;
  saveSettings: (values: Settings) => void;
  setGroupActive: (value: string[]) => void;
  setCurrentActiveGroup: (item: string[]) => void;
}

const resolveNavItemComponent = (item: NavGroup | NavLink | NavSectionTitle) => {
  if ((item as NavSectionTitle).sectionTitle) return VerticalNavSectionTitle;
  if ((item as NavGroup).children) return VerticalNavGroup;

  return VerticalNavLink;
};

const VerticalNavItems = (props: Props) => {
  // ** Props
  const { verticalNavItems } = props;

  const RenderMenuItems = verticalNavItems?.map(
    (item: NavGroup | NavLink | NavSectionTitle, index: number) => {
      const TagName = resolveNavItemComponent(item);

      // @ts-expect-error - item is the intersect of all possible types because how this was written
      return <TagName {...props} key={index} item={item} />;
    },
  );

  return <>{RenderMenuItems}</>;
};

export default VerticalNavItems;
