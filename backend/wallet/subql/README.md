# SubQuery Multi Chain Project

## Add more blockchains to the SubQuery project.

1. Add new project-<blockchain>.ts file. Can find examples at
   "[ethereum-subql-starter](https://github.com/subquery/ethereum-subql-starter/tree/main)
2. Add @subql/types-<blockchain> dependency and @subql/node-<blockchain> devDependency to the
   package.json file.
3. Add src/mappings/mappingHandles.ts to include the new blockchain's mapping handlers using the
   new types to store the values.
4. Execute subql codegen -f project-<blockchain>.ts to generate the required files for the
   blockchain
5. Add resulting .yaml manifest to the subquery-multichain.yaml
6. Add new infrastructure module on the infra/terraform/environments/wallet/local/main.tf file
   to run the new subql-node-<blockchain> pod for local development.
7. Add new infrastructure module on the infra/terraform/environments/wallet/modules/wallet.tf
   file to run the new subql-node-<blockchain> pod for staging and production.
8. Terraform init once the new modules have been added.
