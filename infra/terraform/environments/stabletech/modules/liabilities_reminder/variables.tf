variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "python_runner_image" {
  description = "python-runner fully qualified image name"
  type        = string
  nullable    = false
}

variable "config_maps" {
  description = "ConfigMap names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "secrets" {
  description = "Secret names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "host_volumes" {
  description = "Host paths to mount into containers, for development only"
  type = list(object({
    name       = string
    host_path  = string
    mount_path = string
  }))
  default  = []
  nullable = false
}
