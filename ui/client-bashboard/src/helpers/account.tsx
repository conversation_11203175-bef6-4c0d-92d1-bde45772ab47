import { CoinProcessed, getCoinDisplayName, processCoin } from "@/helpers/coin";
import { Account, Network, Stake } from "@/types/schemas";

export interface AccountProcessed extends Omit<Account, "coin"> {
  coin: CoinProcessed | null;
}

export function processAccount(raw: Account): AccountProcessed {
  return {
    ...raw,
    coin: raw.coin != null ? processCoin(raw.coin) : null,
  };
}

export interface StakeProcessed extends Omit<Stake, "amount" | "asset"> {
  asset: AccountProcessed;
  amount: number;
  network: Network;
}

export function processStake(raw: Stake): StakeProcessed {
  return {
    ...raw,
    asset: processAccount(raw.asset),
    amount: Number.parseFloat(raw.amount),
    network: raw.network,
  };
}

export function getAssetDisplayName(asset: Account | AccountProcessed): string {
  if (asset.coin != null) {
    return getCoinDisplayName(asset.coin);
  }
  let name;
  if (asset.name != null) {
    name = asset.name;
  } else {
    name = asset.address;
    if (name.length > 20) {
      name = `${name.slice(0, 7)}…${name.slice(-7)}`;
    }
  }
  if (asset.tickerSymbol != null) {
    name = `${name} (${asset.tickerSymbol})`;
  }
  return name;
}
