{"compilerOptions": {"emitDecoratorMetadata": true, "experimentalDecorators": true, "esModuleInterop": true, "declaration": true, "importHelpers": true, "resolveJsonModule": true, "module": "commonjs", "outDir": "dist", "rootDir": "src", "target": "es2020", "strict": true, "lib": ["es2020"], "types": ["node", "jest"]}, "include": ["src/**/*", "node_modules/@subql/types-core/dist/global.d.ts", "node_modules/@subql/types-ethereum/dist/global.d.ts"]}