"""Remove old Pool

Revision ID: 0051
Revises: 0050
Create Date: 2024-05-31 23:10:21.899883

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0051"
down_revision = "0050"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_poolleg_coin_id", table_name="poolleg")
    op.drop_index("ix_poolleg_id", table_name="poolleg")
    op.drop_index("ix_poolleg_transaction_id", table_name="poolleg")
    op.drop_table("poolleg")
    op.drop_index("ix_pooltransaction_id", table_name="pooltransaction")
    op.drop_index("ix_pooltransaction_pool_id", table_name="pooltransaction")
    op.drop_index("ix_pooltransaction_wallet_id", table_name="pooltransaction")
    op.drop_table("pooltransaction")
    op.drop_index("ix_position_created_at", table_name="position")
    op.drop_index("ix_position_id", table_name="position")
    op.drop_index("ix_position_is_active", table_name="position")
    op.drop_index("ix_position_swap_position_id", table_name="position")
    op.drop_index("ix_position_swap_transaction_id", table_name="position")
    op.drop_index("ix_position_user_id", table_name="position")
    op.drop_table("position")
    op.drop_index("ix_account_pool_id", table_name="account")
    op.drop_constraint("account_pool_id_fkey", "account", type_="foreignkey")
    op.drop_column("account", "pool_id")
    op.drop_index("ix_pool_farm_id", table_name="pool")
    op.drop_index("ix_pool_id", table_name="pool")
    op.drop_index("ix_pool_network_id", table_name="pool")
    op.drop_table("pool")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "pool",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('pool_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("exchange_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("farm_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("url", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("network_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "is_closed",
            sa.BOOLEAN(),
            server_default=sa.text("false"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["exchange_id"], ["exchange.id"], name="pool_exchange_id_fkey"),
        sa.ForeignKeyConstraint(["farm_id"], ["farm.id"], name="pool_farm_id_fkey"),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"], name="pool_network_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="pool_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_pool_network_id", "pool", ["network_id"], unique=False)
    op.create_index("ix_pool_id", "pool", ["id"], unique=False)
    op.create_index("ix_pool_farm_id", "pool", ["farm_id"], unique=False)
    op.add_column(
        "account", sa.Column("pool_id", sa.INTEGER(), autoincrement=False, nullable=True)
    )
    op.create_foreign_key("account_pool_id_fkey", "account", "pool", ["pool_id"], ["id"])
    op.create_index("ix_account_pool_id", "account", ["pool_id"], unique=False)
    op.create_table(
        "pooltransaction",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('pooltransaction_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("pool_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("tx_hash", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("wallet_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["pool_id"], ["pool.id"], name="transaction_pool_id_fkey"),
        sa.ForeignKeyConstraint(
            ["wallet_id"], ["wallet.id"], name="transaction_wallet_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="transaction_pkey"),
        sa.UniqueConstraint("tx_hash", "wallet_id", name="transaction_tx_hash_wallet_id_key"),
        postgresql_ignore_search_path=False,
    )
    op.create_index(
        "ix_pooltransaction_wallet_id", "pooltransaction", ["wallet_id"], unique=False
    )
    op.create_index("ix_pooltransaction_pool_id", "pooltransaction", ["pool_id"], unique=False)
    op.create_index("ix_pooltransaction_id", "pooltransaction", ["id"], unique=False)
    op.create_table(
        "poolleg",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("coin_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "coin_amount",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("transaction_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"], name="leg_coin_id_fkey"),
        sa.ForeignKeyConstraint(
            ["transaction_id"], ["pooltransaction.id"], name="leg_transaction_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="leg_pkey"),
        sa.UniqueConstraint("transaction_id", "coin_id", name="leg_transaction_id_coin_id_key"),
    )
    op.create_index("ix_poolleg_transaction_id", "poolleg", ["transaction_id"], unique=False)
    op.create_index("ix_poolleg_id", "poolleg", ["id"], unique=False)
    op.create_index("ix_poolleg_coin_id", "poolleg", ["coin_id"], unique=False)
    op.create_table(
        "position",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "amount", sa.NUMERIC(precision=32, scale=18), autoincrement=False, nullable=False
        ),
        sa.Column("is_active", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column("started_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("coin_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "cost_basis_usd",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("swap_position_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("swap_transaction_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("ended_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"], name="position_coin_id_fkey"),
        sa.ForeignKeyConstraint(
            ["swap_position_id"], ["position.id"], name="position_swap_position_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["swap_transaction_id"],
            ["transaction.id"],
            name="position_swap_transaction_id_fkey",
        ),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], name="position_user_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="position_pkey"),
    )
    op.create_index("ix_position_user_id", "position", ["user_id"], unique=False)
    op.create_index(
        "ix_position_swap_transaction_id", "position", ["swap_transaction_id"], unique=False
    )
    op.create_index(
        "ix_position_swap_position_id", "position", ["swap_position_id"], unique=False
    )
    op.create_index("ix_position_is_active", "position", ["is_active"], unique=False)
    op.create_index("ix_position_id", "position", ["id"], unique=False)
    op.create_index("ix_position_created_at", "position", ["created_at"], unique=False)
    # ### end Alembic commands ###
