"use client";

import { useAuth } from "@/lib/auth-ctx";
import { AlertError } from "@/lib/components/alert";
import { Button, button } from "@/lib/components/button";
import { Label, ValidationError, label } from "@/lib/components/form";
import { InputClearable } from "@/lib/components/input";
import * as Popover from "@/lib/components/popover";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { useUpdateProfile } from "@/lib/profile";
import { User } from "@/types/schemas";
import { superstructResolver } from "@hookform/resolvers/superstruct";
import { HelpCircle } from "lucide-react";
import { useId } from "react";
import { useForm } from "react-hook-form";
import { object, string, trimmed } from "superstruct";

const ProfileFormStruct = object({
  displayName: trimmed(string()),
});

type ProfileFormStruct = typeof ProfileFormStruct.TYPE;

export default function ProfilePage() {
  const { user, initialized } = useAuth();
  const isLoading = !initialized || user == null;
  return (
    <main className="flex flex-col gap-6 p-4">
      <h1 className="text-2xl">Profile</h1>
      {isLoading ? (
        <div className="mt-8 grid max-w-2xl place-items-center">
          <Spinner size={42} />
        </div>
      ) : (
        <Form user={user} />
      )}
    </main>
  );
}

function Form({ user }: { user: User }) {
  const updateProfile = useUpdateProfile();

  const {
    reset,
    setValue,
    getValues,
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<ProfileFormStruct>({
    defaultValues: getDefaultValues(user),
    resolver: superstructResolver(ProfileFormStruct, { coerce: true }),
  });

  const onSubmit = (event: React.FormEvent) => {
    void handleSubmit((data) => {
      if (!isDirty) return;
      updateProfile.mutate(data, {
        onSuccess(user) {
          reset(getDefaultValues(user));
        },
      });
    })(event);
  };

  const id = useId();
  const displayNameId = `${id}displayName`;
  return (
    <form onSubmit={onSubmit} className="flex max-w-2xl flex-col gap-4">
      <dl className="grid grid-cols-[auto_1fr] items-center gap-4">
        {user.email && <Item term="Email" description={user.email} />}

        {user.matrica_username && (
          <Item term="Matrica Username" description={user.matrica_username} />
        )}

        <Item
          term={
            <span className="inline-flex items-center gap-1">
              <Label htmlFor={displayNameId}>Display Name</Label>
              <Popover.Root>
                <Popover.Trigger
                  className={button({
                    variant: "link",
                    className: "h-5! w-5! rounded-full! p-0.5!",
                  })}
                >
                  <HelpCircle size={16} />
                </Popover.Trigger>
                <Popover.Content sideOffset={1}>
                  <Popover.Arrow />
                  <p className="text-sm">
                    Publicly displayed — leave empty to remain anonymous on the leaderboards
                  </p>
                </Popover.Content>
              </Popover.Root>
            </span>
          }
          description={
            <InputClearable
              {...register("displayName")}
              id={displayNameId}
              placeholder="Anonymous"
              aria-invalid={Boolean(errors.displayName)}
              clearHidden={!getValues("displayName")}
              onClear={() => {
                setValue("displayName", "", { shouldDirty: true, shouldTouch: true });
              }}
            />
          }
        />
        <ValidationError className="col-start-2 -mt-2">
          {errors.displayName?.message}
        </ValidationError>
      </dl>

      <div className="flex justify-end gap-4">
        <div className="relative">
          <Button
            type="submit"
            disabled={updateProfile.isPending || !isDirty}
            variant={isDirty ? "primary" : "secondary"}
            className="w-24"
          >
            Save
          </Button>
          {updateProfile.isPending && (
            <div className="absolute inset-0 grid place-items-center">
              <Spinner size={24} />
            </div>
          )}
        </div>
        <Button
          disabled={updateProfile.isPending || !isDirty}
          variant={isDirty ? "destructive" : "secondary"}
          className="w-24"
          onClick={() => {
            reset(getDefaultValues(user));
          }}
        >
          Reset
        </Button>
      </div>

      {updateProfile.error && <AlertError>{getErrorMessage(updateProfile.error)}</AlertError>}
    </form>
  );
}

function Item({ term, description }: { term: React.ReactNode; description: React.ReactNode }) {
  return (
    <>
      <dt className={label({ className: "justify-self-end" })}>{term}</dt>
      <dd className="grid">{description}</dd>
    </>
  );
}

function getDefaultValues(user: User) {
  return {
    displayName: user.display_name ?? "",
  };
}
