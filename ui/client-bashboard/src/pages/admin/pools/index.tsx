import Spinner from "@/@core/components/spinner";
import { DataGridTable, TableColumns } from "@/components/DataGridTable";
import { currencyFormatter } from "@/helpers/formatters";
import { useIndexerPoolsQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AccountPool } from "@/types/schemas";
import {
  Alert,
  Box,
  Button,
  Card,
  CardContent,
  Fade,
  Grid,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import ToggleButton from "@mui/material/ToggleButton";
import Link from "next/link";
import { useMemo, useState } from "react";

type AccountPoolProcessed = Omit<AccountPool, "tvl" | "impermanentLossPercent"> & {
  tvl: number;
  impermanentLossPercent: number | null;
};

function processPool(pool: AccountPool): AccountPoolProcessed {
  return {
    ...pool,
    tvl: Number.parseFloat(pool.tvl),
    impermanentLossPercent:
      pool.impermanentLossPercent != null
        ? Number.parseFloat(pool.impermanentLossPercent)
        : null,
  };
}

const closedTableColumns: TableColumns<AccountPoolProcessed>[] = [
  { field: "id", header: "ID" },
  {
    field: "name",
    header: "Name",
    renderCell: ({ row: { id, name } }) => (
      <Link href={`/admin/pools/${id}`} passHref legacyBehavior>
        <Button>{name}</Button>
      </Link>
    ),
  },
  {
    field: "exchange",
    header: "Exchange",
    renderCell: ({ row: { exchange } }) =>
      exchange != null && (
        <Button href={exchange.url} target="_blank">
          {exchange.name}
        </Button>
      ),
    sortByField: "name",
  },
  {
    field: "network",
    header: "Network",
    renderCell: ({ row: { network } }) => network.name,
    sortByField: "name",
  },
];

const openTableColumns: TableColumns<AccountPoolProcessed>[] = [
  ...closedTableColumns,
  {
    field: "tvl",
    header: "$ TVL",
    renderCell: ({ row: { tvl } }) => currencyFormatter.format(tvl),
  },
  {
    field: "impermanentLossPercent",
    header: "Imp. Loss",
    renderCell: ({ row: { impermanentLossPercent } }) =>
      impermanentLossPercent != null ? `${impermanentLossPercent.toFixed(2)} %` : null,
  },
];

const Pools = () => {
  const { data: poolsData, error } = useIndexerPoolsQuery();

  const [showPoolState, setShowPoolState] = useState<AccountPool["state"]>("has-funds");
  const [tableColumns] = useState(openTableColumns);

  const [poolRows, totalTvl] = useMemo(() => {
    if (poolsData == null) return [[], 0] as const;
    const poolRows = poolsData.filter((pool) => pool.state === showPoolState).map(processPool);
    const totalTvl = poolRows.reduce((acc, pool) => acc + pool.tvl, 0);
    return [poolRows, totalTvl] as const;
  }, [poolsData, showPoolState]);

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed to load pools" })}
      </Alert>
    );
  }

  if (!poolsData) {
    return <Spinner />;
  }

  return (
    <Grid container spacing={6}>
      <Grid item xs>
        <Card>
          <CardContent>
            <Grid container spacing={6}>
              <Grid container item xs={12} alignItems="baseline" rowGap={4}>
                <Grid item xs={6} md={4}>
                  <Typography variant={"h4"}>Liquidity Pools</Typography>
                </Grid>
                <Grid item xs={6} md={4} textAlign={{ xs: "right", md: "center" }}>
                  <Fade in={showPoolState === "has-funds"}>
                    <Box component="span">
                      <Typography component="span" color={"primary"} pr={2}>
                        TVL
                      </Typography>{" "}
                      <Typography component="span">
                        $ {currencyFormatter.format(totalTvl)}
                      </Typography>
                    </Box>
                  </Fade>
                </Grid>
                <Grid item xs={12} md={4} textAlign="right">
                  <ToggleButtonGroup
                    value={showPoolState}
                    exclusive
                    onChange={(_event, newPoolState) => setShowPoolState(newPoolState)}
                  >
                    <ToggleButton value={"has-funds"} size={"small"}>
                      Has Funds
                    </ToggleButton>
                    <ToggleButton value={"no-balance"} size={"small"}>
                      No Balance
                    </ToggleButton>
                    <ToggleButton value={"missing-transactions"} size={"small"}>
                      Missing Transactions
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <DataGridTable
                  hideIdColumn
                  rows={poolRows}
                  tableColumns={tableColumns}
                  sortByField="name"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

Pools.acl = {
  action: "manage",
  subject: "Pools",
};

export default Pools;
