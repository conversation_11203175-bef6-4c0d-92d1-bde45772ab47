from sqlalchemy.orm import Mapped, Session, declared_attr, mapped_column, object_session


class BaseModel:
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    @classmethod
    @declared_attr.directive
    def __tablename__(cls) -> str:
        # Name the table after the lowercase class name
        return cls.__name__.lower()

    @property
    def db(self) -> Session | None:
        return object_session(self)
