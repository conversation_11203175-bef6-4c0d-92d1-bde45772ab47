import asyncio
import logging
import random
import time
from datetime import datetime, timedelta, timezone
from decimal import Decimal

import sqlalchemy.exc
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from common.psql import async_session
from pricer.coingecko_client import (
    CoingeckoClient,
    CoingeckoError,
    CoingeckoUnavailableError,
    MarketChartData,
)
from pricer.historical.crud import CoinHistoricalPriceManager
from pricer.models import CoinCollectionLock

logger = logging.getLogger(__name__)

# Hard-coded list of active coins to collect prices for
ACTIVE_COINS = [
    "bitcoin",
    "ethereum",
    "binancecoin",
    "solana",
    "cardano",
    "avalanche-2",
    "polygon",
    "chainlink",
    "uniswap",
    "litecoin",
]

# Collection interval
COLLECTION_INTERVAL = timedelta(minutes=30)

# Failure count for alerting
ALERT_FAILURE_COUNT = 5

# Default lookback period when no historical data exists
DEFAULT_LOOKBACK = timedelta(days=1)


class PriceCollector:
    """Collects historical price data from CoinGecko and stores it in the database."""

    def __init__(self, coingecko: CoingeckoClient) -> None:
        self._coingecko = coingecko

    async def collect_prices_for_coin(self, coin_id: str) -> bool:
        """Collect historical price data for a single coin.

        Args:
            coin_id: The CoinGecko ID of the coin

        Returns:
            True if collection was successful, False otherwise
        """
        logger.info("Starting price collection for coin: %s", coin_id)

        async with async_session.begin() as db:
            # Acquire lock for this coin using SELECT FOR UPDATE
            lock_record = await self._acquire_coin_lock(db, coin_id)
            if not lock_record:
                logger.debug("Could not acquire lock for coin: %s", coin_id)
                return False

            try:
                # Check if we need to collect data for this coin
                if not self._should_collect_for_coin(lock_record):
                    logger.debug("Skipping collection for coin %s (too recent)", coin_id)
                    return False

                # Determine the date range to fetch
                from_timestamp, to_timestamp = await self._get_collection_range(db, coin_id)

                if from_timestamp >= to_timestamp:
                    logger.debug("No new data to collect for coin: %s", coin_id)
                    self._update_lock_success(lock_record)
                    return True

                # Fetch data from CoinGecko
                market_data = await self._fetch_market_data(
                    coin_id, from_timestamp, to_timestamp
                )
                if not market_data:
                    logger.warning("No market data returned for coin: %s", coin_id)
                    self._update_lock_success(lock_record)
                    return True

                # Store the price data
                stored_count = await self._store_price_data(db, coin_id, market_data)
                logger.info("Stored %d price points for coin: %s", stored_count, coin_id)

                # Update lock record with success
                self._update_lock_success(lock_record)
                return True

            except Exception as e:
                self._update_lock_failure(lock_record)

                # Send alert if failure count is a multiple of MAX_FAILURE_COUNT
                if (
                    lock_record.failure_count > 0
                    and lock_record.failure_count % ALERT_FAILURE_COUNT == 0
                ):
                    logger.error(
                        "Price collection failing repeatedly for coin %s (%d failures)",
                        coin_id,
                        lock_record.failure_count,
                        exc_info=e,
                    )

                if isinstance(e, CoingeckoUnavailableError):
                    sleep_duration = max(0.1, e.until - time.time())
                    logger.warning(
                        "CoinGecko unavailable, sleeping for %.1f seconds", sleep_duration
                    )
                    await asyncio.sleep(sleep_duration)

                return False

    async def _acquire_coin_lock(
        self, db: AsyncSession, coin_id: str
    ) -> CoinCollectionLock | None:
        """Acquire a lock for the given coin using SELECT FOR UPDATE.

        Args:
            db: Database session
            coin_id: The CoinGecko ID of the coin

        Returns:
            The lock record if acquired, None otherwise
        """
        stmt = (
            select(CoinCollectionLock)
            .where(CoinCollectionLock.coingecko_id == coin_id)
            .with_for_update(nowait=True)
        )
        try:
            result = await db.execute(stmt)
            lock_record = result.scalar_one_or_none()
        except sqlalchemy.exc.OperationalError as e:
            logger.debug("Lock already held for coin %s: %s", coin_id, e)
            return None

        if lock_record is None:
            # Create new lock record if it doesn't exist
            lock_record = CoinCollectionLock(coingecko_id=coin_id)
            db.add(lock_record)
            await db.flush()  # Get the ID without committing

        return lock_record

    def _should_collect_for_coin(self, lock_record: CoinCollectionLock) -> bool:
        """Check if we should collect data for this coin based on last update time.

        Args:
            lock_record: The lock record for the coin

        Returns:
            True if collection should proceed, False otherwise
        """
        if lock_record.last_updated_at is None:
            return True

        time_since_update = datetime.now(timezone.utc) - lock_record.last_updated_at
        return time_since_update >= COLLECTION_INTERVAL

    async def _get_collection_range(self, db: AsyncSession, coin_id: str) -> tuple[int, int]:
        """Determine the timestamp range to collect data for.

        Args:
            db: Database session
            coin_id: The CoinGecko ID of the coin

        Returns:
            Tuple of (from_timestamp, to_timestamp) in Unix seconds
        """
        manager = CoinHistoricalPriceManager(db)
        latest_price = await manager.get_latest_price(coin_id)

        now = datetime.now(timezone.utc)
        to_timestamp = int(now.timestamp())

        if latest_price:
            # Start from the latest price timestamp + 5 minutes to avoid duplicates
            from_timestamp = int((latest_price.timestamp + timedelta(minutes=5)).timestamp())
        else:
            # No existing data, start from default lookback period
            from_time = now - DEFAULT_LOOKBACK
            from_timestamp = int(from_time.timestamp())

        return from_timestamp, to_timestamp

    async def _fetch_market_data(
        self, coin_id: str, from_timestamp: int, to_timestamp: int
    ) -> MarketChartData | None:
        """Fetch market data from CoinGecko API.

        Args:
            coin_id: The CoinGecko ID of the coin
            from_timestamp: Start timestamp in Unix seconds
            to_timestamp: End timestamp in Unix seconds

        Returns:
            Market data dictionary or None if not found
        """
        try:
            return await self._coingecko.get_market_chart_range(
                coin_id, from_timestamp, to_timestamp
            )
        except (CoingeckoError, CoingeckoUnavailableError) as e:
            logger.warning("CoinGecko API error for coin %s: %s", coin_id, e)
            raise

    async def _store_price_data(
        self, db: AsyncSession, coin_id: str, market_data: MarketChartData
    ) -> int:
        """Store price data points in the database.

        Args:
            db: Database session
            coin_id: The CoinGecko ID of the coin
            market_data: Market data from CoinGecko API

        Returns:
            Number of price points stored
        """
        manager = CoinHistoricalPriceManager(db)
        stored_count = 0

        prices = market_data.get("prices", [])
        market_caps = market_data.get("market_caps", [])
        volumes = market_data.get("total_volumes", [])

        if not (len(prices) == len(market_caps) == len(volumes)):
            raise ValueError(
                f"Market data arrays have different lengths: "
                f"prices={len(prices)}, market_caps={len(market_caps)}, volumes={len(volumes)}"
            )

        for price_data, market_cap_data, volume_data in zip(prices, market_caps, volumes):
            timestamp_ms, price = price_data
            _, market_cap = market_cap_data
            _, volume = volume_data

            timestamp = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)

            try:
                # Use inner transaction for each price point to avoid rollback of entire batch
                async with db.begin_nested():
                    manager.record_price(
                        coingecko_id=coin_id,
                        price_usd=Decimal(str(price)),
                        volume_24h_usd=Decimal(str(volume)),
                        market_cap_usd=Decimal(str(market_cap)),
                        timestamp=timestamp,
                    )
            except sqlalchemy.exc.IntegrityError:
                # Price point already exists, skip it
                logger.debug("Price point already exists for %s at %s", coin_id, timestamp)
                continue

            stored_count += 1

        return stored_count

    def _update_lock_success(self, lock_record: CoinCollectionLock) -> None:
        """Update lock record after successful collection.

        Args:
            lock_record: The lock record to update
        """
        lock_record.last_updated_at = datetime.now(timezone.utc)
        lock_record.failure_count = 0
        lock_record.last_failure_at = None

    def _update_lock_failure(self, lock_record: CoinCollectionLock) -> None:
        """Update lock record after failed collection.

        Args:
            lock_record: The lock record to update
        """
        lock_record.failure_count += 1
        lock_record.last_failure_at = datetime.now(timezone.utc)

    def start_price_collection_task(self):
        """Start the background task for collecting historical price data.

        Returns:
            The asyncio task for price collection
        """
        loop_interval_seconds = 180

        log = logger.getChild("price_collection")

        async def collection_loop() -> None:
            while True:
                log.info("Starting price collection loop for %d coins", len(ACTIVE_COINS))

                # Process coins one at a time to avoid overwhelming the API
                for coin_id in ACTIVE_COINS:
                    try:
                        result = await self.collect_prices_for_coin(coin_id)
                        if result:
                            log.debug("Successfully collected prices for %s", coin_id)
                        else:
                            log.debug("Price collection failed for %s", coin_id)
                    except Exception as e:
                        log.error(
                            "Unexpected error collecting prices for %s: %s",
                            coin_id,
                            e,
                            exc_info=e,
                        )

                    # Small delay between coins to be respectful to the API
                    await asyncio.sleep(random.uniform(8, 16))

                log.info("Completed price collection loop")

                # Wait before starting the next loop
                await asyncio.sleep(loop_interval_seconds)

        return asyncio.create_task(collection_loop(), name="price_collection")
