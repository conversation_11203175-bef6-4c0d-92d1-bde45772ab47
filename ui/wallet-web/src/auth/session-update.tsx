import { MetadataIcon } from "@/auth/walletkit-metadata-icon";
import { getChainN<PERSON>, SOLANA_CHAINS } from "@/auth/walletkit-utils";
import { Label } from "@/lib/components/form";
import * as Select from "@/lib/components/select";
import { shortenAddress } from "@/lib/utils/string";
import WalletKitClient from "@reown/walletkit";
import { useMutation } from "@tanstack/react-query";
import type { SessionTypes } from "@walletconnect/types";
import { useId } from "react";

interface Props {
  getWalletKit: () => Promise<WalletKitClient>;
  sessions: readonly SessionTypes.Struct[];
  onChange: () => void;
}

export function SessionUpdate(props: Props) {
  const { getWalletKit, sessions, onChange } = props;

  const sessionEvent = useMutation({
    async mutationFn(event: { address: string; chainId: string; topic: string }) {
      const { address, chainId, topic } = event;
      const walletKit = await getWalletKit();
      const session = walletKit.getActiveSessions()[event.topic];
      if (!session) return;

      const account = `${chainId}:${address}`;
      const { namespaces } = session;
      const solanaNamespace = {
        ...namespaces.solana,
        methods: namespaces.solana?.methods ?? [],
        events: namespaces.solana?.events ?? [],
        accounts: [
          account,
          ...(namespaces.solana?.accounts ?? []).filter((acc) => acc !== account),
        ],
        chains: [
          chainId,
          ...(namespaces.solana?.chains ?? []).filter((chain) => chain !== chainId),
        ],
      };
      const { acknowledged } = await walletKit.updateSession({
        topic,
        namespaces: { ...namespaces, solana: solanaNamespace },
      });
      await Promise.race([acknowledged(), sleep(1000)]);

      if (solanaNamespace.events.includes("accountsChanged")) {
        await walletKit.emitSessionEvent({
          topic,
          chainId,
          event: {
            name: "chainChanged",
            data: chainId.split(":")[1] ?? chainId,
          },
        });
      }
      if (solanaNamespace.events.includes("chainChanged")) {
        await walletKit.emitSessionEvent({
          topic,
          chainId,
          event: {
            name: "accountsChanged",
            data: [address],
          },
        });
      }
      onChange();
    },
  });

  const chainChangeHandler = (address: string, topic: string) => (chainId: string) => {
    sessionEvent.mutate({ address, chainId, topic });
  };

  const accountChangeHandler = (chainId: string, topic: string) => (address: string) => {
    sessionEvent.mutate({ address, chainId, topic });
  };

  const id = useId();

  return (
    <section className="mb-6">
      <h3 className="text-center text-lg">Update Session</h3>
      <ul className="mt-4 grid gap-4">
        {sessions.map((session) => {
          const namespace = session.namespaces.solana;
          const supportedChains = (namespace?.chains ?? SOLANA_CHAINS) as readonly string[];
          const chainId = supportedChains[0] ?? SOLANA_CHAINS[0];
          const allAddresses = getAddresses(namespace?.accounts ?? []);
          const currentAddress = allAddresses[0] ?? "";
          const chainLabelId = `session-${session.topic}-${id}`;
          const addressLabelId = `session-${session.topic}-${id}`;
          return (
            <li key={session.topic}>
              <div className="mb-2 flex justify-center gap-2">
                <MetadataIcon metadata={session.peer.metadata} />
                {session.peer.metadata.name}
              </div>
              <div className="grid grid-cols-[auto_1fr] items-center gap-2">
                <Label id={chainLabelId} className="text-right">
                  Chain
                </Label>
                <Select.Root
                  disabled={sessionEvent.isPending}
                  onValueChange={chainChangeHandler(currentAddress, session.topic)}
                  value={chainId}
                >
                  <Select.Trigger id={chainLabelId} />
                  <Select.Content>
                    {supportedChains.map((chain) => (
                      <Select.Item key={chain} value={chain}>
                        {getChainName(chain)}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Root>
                <Label id={addressLabelId} className="text-right">
                  Account
                </Label>
                <Select.Root
                  disabled={sessionEvent.isPending}
                  onValueChange={accountChangeHandler(chainId, session.topic)}
                  value={currentAddress}
                >
                  <Select.Trigger id={addressLabelId} />
                  <Select.Content>
                    {allAddresses.map((addr) => (
                      <Select.Item key={addr} value={addr}>
                        {shortenAddress(addr)}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Root>
              </div>
            </li>
          );
        })}
      </ul>
    </section>
  );
}

async function sleep(ms: number): Promise<unknown> {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

/**
 * Get unique addresses from a list of CAIP-10 accounts.
 *
 * Expected format per account: `<chainName>:<chainReference>:<address>`.
 * Example: `solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp:7EcDhSYGxXyscszYEp35KHN8vvw3svAuLKTzXwCFLtV`.
 * @see https://chainagnostic.org/CAIPs/caip-10
 */
function getAddresses(accounts: string[]): string[] {
  return [...new Set(accounts.map(getCaip10Address))];
}

/** @see https://chainagnostic.org/CAIPs/caip-10 */
function getCaip10Address(account: string): string {
  const address = account.split(":").at(-1);
  if (!address) {
    throw new Error(`Invalid CAIP-10 account: ${account}`);
  }
  return address;
}
