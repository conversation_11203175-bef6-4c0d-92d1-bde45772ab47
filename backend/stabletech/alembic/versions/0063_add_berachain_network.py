"""Add Berachain Network

Revision ID: 0063
Revises: 0062

"""

from alembic import op
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0063"
down_revision = "0062"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Network(Base):
    id = Column(Integer, primary_key=True)
    covalent_chain_id: int = Column(Integer)
    explorer_url: str = Column(String)
    name: str = Column(String, nullable=False, index=True, unique=True)
    __tablename__ = "network"


def upgrade() -> None:
    session = Session(bind=op.get_bind())
    name = "Berachain"
    if not session.query(Network).filter_by(name=name).first():
        session.add(Network(name=name, explorer_url="https://berascan.com"))
    session.commit()


def downgrade() -> None:
    session = Session(bind=op.get_bind())
    name = "<PERSON><PERSON>hain"
    berachain_network = session.query(Network).filter_by(name=name).first()
    if berachain_network:
        session.delete(berachain_network)
    session.commit()
