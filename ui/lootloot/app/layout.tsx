import "@/app/globals.css";
import { RootClientProviders } from "@/app/root-client-providers";
import { WithSidebar } from "@/lib/components/sidebar";
import { UrlNormalizer } from "@/lib/routes/url-normalizer";
import { GoogleAnalytics } from "@next/third-parties/google";
import type { Metadata } from "next";
import { ThemeProvider } from "next-themes";
import { Inter } from "next/font/google";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "LootLoot",
  description: "theheist.game statistics and tools",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`min-h-dvh ${inter.className}`}>
        <UrlNormalizer />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <RootClientProviders>
            <WithSidebar>{children}</WithSidebar>
          </RootClientProviders>
        </ThemeProvider>
      </body>
      <GoogleAnalytics gaId="G-Z28GZTYF7Z" />
    </html>
  );
}
