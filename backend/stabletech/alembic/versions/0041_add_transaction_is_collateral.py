"""Add Transaction is_collateral

Revision ID: 0041
Revises: 0040
Create Date: 2023-10-14 02:43:49.706125

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0041"
down_revision = "0039"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "transaction",
        sa.<PERSON>umn("is_collateral", sa.<PERSON>(), server_default="false", nullable=False),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("transaction", "is_collateral")
    # ### end Alembic commands ###
