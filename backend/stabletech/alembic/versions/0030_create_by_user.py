"""Create by User

Revision ID: 0030
Revises: 0029
Create Date: 2023-08-09 08:10:23.839354

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0030"
down_revision = "0029"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("bridge", sa.Column("created_by_user_id", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_bridge_created_by_user_id"), "bridge", ["created_by_user_id"], unique=False
    )
    op.create_foreign_key(None, "bridge", "user", ["created_by_user_id"], ["id"])
    op.add_column(
        "clienttransaction", sa.Column("created_by_user_id", sa.Integer(), nullable=True)
    )
    op.create_index(
        op.f("ix_clienttransaction_created_by_user_id"),
        "clienttransaction",
        ["created_by_user_id"],
        unique=False,
    )
    op.create_foreign_key(None, "clienttransaction", "user", ["created_by_user_id"], ["id"])
    op.add_column("exchange", sa.Column("created_by_user_id", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_exchange_created_by_user_id"), "exchange", ["created_by_user_id"], unique=False
    )
    op.create_foreign_key(None, "exchange", "user", ["created_by_user_id"], ["id"])
    op.add_column("farm", sa.Column("created_by_user_id", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_farm_created_by_user_id"), "farm", ["created_by_user_id"], unique=False
    )
    op.create_foreign_key(None, "farm", "user", ["created_by_user_id"], ["id"])
    op.add_column(
        "lendingprotocol", sa.Column("created_by_user_id", sa.Integer(), nullable=True)
    )
    op.create_index(
        op.f("ix_lendingprotocol_created_by_user_id"),
        "lendingprotocol",
        ["created_by_user_id"],
        unique=False,
    )
    op.create_foreign_key(None, "lendingprotocol", "user", ["created_by_user_id"], ["id"])
    op.add_column("network", sa.Column("created_by_user_id", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_network_created_by_user_id"), "network", ["created_by_user_id"], unique=False
    )
    op.create_foreign_key(None, "network", "user", ["created_by_user_id"], ["id"])
    op.add_column("transaction", sa.Column("created_by_user_id", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_transaction_created_by_user_id"),
        "transaction",
        ["created_by_user_id"],
        unique=False,
    )
    op.create_foreign_key(None, "transaction", "user", ["created_by_user_id"], ["id"])
    op.add_column("wallet", sa.Column("created_by_user_id", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_wallet_created_by_user_id"), "wallet", ["created_by_user_id"], unique=False
    )
    op.create_foreign_key(None, "wallet", "user", ["created_by_user_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_wallet_created_by_user_id"), table_name="wallet")
    op.drop_column("wallet", "created_by_user_id")
    op.drop_index(op.f("ix_transaction_created_by_user_id"), table_name="transaction")
    op.drop_column("transaction", "created_by_user_id")
    op.drop_index(op.f("ix_network_created_by_user_id"), table_name="network")
    op.drop_column("network", "created_by_user_id")
    op.drop_index(op.f("ix_lendingprotocol_created_by_user_id"), table_name="lendingprotocol")
    op.drop_column("lendingprotocol", "created_by_user_id")
    op.drop_index(op.f("ix_farm_created_by_user_id"), table_name="farm")
    op.drop_column("farm", "created_by_user_id")
    op.drop_index(op.f("ix_exchange_created_by_user_id"), table_name="exchange")
    op.drop_column("exchange", "created_by_user_id")
    op.drop_index(
        op.f("ix_clienttransaction_created_by_user_id"), table_name="clienttransaction"
    )
    op.drop_column("clienttransaction", "created_by_user_id")
    op.drop_index(op.f("ix_bridge_created_by_user_id"), table_name="bridge")
    op.drop_column("bridge", "created_by_user_id")
    # ### end Alembic commands ###
