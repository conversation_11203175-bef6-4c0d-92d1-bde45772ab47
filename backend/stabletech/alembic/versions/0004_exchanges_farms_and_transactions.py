"""Exchanges, Farms, and Transactions

Revision ID: 0004
Revises: 0003
Create Date: 2022-12-10 13:01:49.419809

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0004"
down_revision = "0003"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "exchange",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "farm",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.<PERSON>KeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "wallet",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("address", sa.String(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("network", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("address", "network"),
    )
    op.create_index(op.f("ix_wallet_address"), "wallet", ["address"], unique=True)
    op.create_index(op.f("ix_wallet_is_active"), "wallet", ["is_active"], unique=False)
    op.create_table(
        "pool",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("exchange_id", sa.Integer(), nullable=True),
        sa.Column("farm_id", sa.Integer(), nullable=False),
        sa.Column("network", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["exchange_id"], ["exchange.id"]),
        sa.ForeignKeyConstraint(["farm_id"], ["farm.id"]),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_index(op.f("ix_pool_farm_id"), "pool", ["farm_id"], unique=False)
    op.create_table(
        "transaction",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("at", sa.DateTime(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("pool_id", sa.Integer(), nullable=False),
        sa.Column("tx_hash", sa.String(), nullable=True),
        sa.Column("wallet_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["pool_id"], ["pool.id"]),
        sa.ForeignKeyConstraint(["wallet_id"], ["wallet.id"]),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("tx_hash", "wallet_id"),
    )
    op.create_index(op.f("ix_transaction_pool_id"), "transaction", ["pool_id"], unique=False)
    op.create_index(
        op.f("ix_transaction_wallet_id"), "transaction", ["wallet_id"], unique=False
    )
    op.create_table(
        "leg",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("coin_id", sa.Integer(), nullable=False),
        sa.Column("coin_amount", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("transaction_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"]),
        sa.ForeignKeyConstraint(["transaction_id"], ["transaction.id"]),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("transaction_id", "coin_id"),
    )
    op.create_index(op.f("ix_leg_coin_id"), "leg", ["coin_id"], unique=False)
    op.create_index(op.f("ix_leg_transaction_id"), "leg", ["transaction_id"], unique=False)
    op.drop_index("ix_coin_id", table_name="coin")
    op.drop_index("ix_pool_position_id", table_name="pool_position")
    op.drop_index("ix_price_id", table_name="price")
    op.drop_index("ix_user_id", table_name="user")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_user_id", "user", ["id"], unique=False)
    op.create_index("ix_price_id", "price", ["id"], unique=False)
    op.create_index("ix_pool_position_id", "pool_position", ["id"], unique=False)
    op.create_index("ix_coin_id", "coin", ["id"], unique=False)
    op.drop_index(op.f("ix_leg_transaction_id"), table_name="leg")
    op.drop_index(op.f("ix_leg_coin_id"), table_name="leg")
    op.drop_table("leg")
    op.drop_index(op.f("ix_transaction_wallet_id"), table_name="transaction")
    op.drop_index(op.f("ix_transaction_pool_id"), table_name="transaction")
    op.drop_table("transaction")
    op.drop_index(op.f("ix_pool_farm_id"), table_name="pool")
    op.drop_table("pool")
    op.drop_index(op.f("ix_wallet_is_active"), table_name="wallet")
    op.drop_index(op.f("ix_wallet_address"), table_name="wallet")
    op.drop_table("wallet")
    op.drop_table("farm")
    op.drop_table("exchange")
    # ### end Alembic commands ###
