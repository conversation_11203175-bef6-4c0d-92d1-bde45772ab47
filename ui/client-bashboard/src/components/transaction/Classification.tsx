import { Toast, useToast } from "@/components/Toast";
import { BridgeAdd } from "@/components/add/BridgeAdd";
import { ExchangeAdd } from "@/components/add/ExchangeAdd";
import { FarmAdd } from "@/components/add/FarmAdd";
import { LendingProtocolAdd } from "@/components/add/LendingProtocolAdd";
import { BridgeSearch } from "@/components/autocomplete/BridgeSearch";
import { ExchangeSearchWithAccountAddress } from "@/components/autocomplete/ExchangeSearch";
import { FarmSearchWithAccountAddress } from "@/components/autocomplete/FarmSearch";
import { LendingProtocolSearch } from "@/components/autocomplete/LendingProtocolSearch";
import { UserSearch } from "@/components/autocomplete/UserSearch";
import { enablePooledFund, isSahaba } from "@/context/env";
import { capitalizeWord, formatDecimal } from "@/helpers/formatters";
import { useIndexerClientTransactionsQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import {
  AdminClientTransaction,
  Bridge,
  ExchangeWithAccountAddress,
  FarmWithAccountAddress,
  LendingProtocolWithAccountAddress,
  TransactionAction,
  User,
} from "@/types/schemas";
import { Add } from "@mui/icons-material";
import {
  Box,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import { skipToken } from "@reduxjs/toolkit/query";
import { useState } from "react";

export interface State {
  action: TransactionAction | null;
  bridge: Bridge | null;
  clientTransactionId: number | null;
  exchange: ExchangeWithAccountAddress | null;
  farm: FarmWithAccountAddress | null;
  isClientLoan: boolean;
  isCollateral: boolean | null;
  isDeposit: boolean | null;
  isForPooledFund: boolean;
  isRevenueShare: boolean;
  lendingProtocol: LendingProtocolWithAccountAddress | null;
  user: User | null;
}

interface Props<S extends State> {
  children?: React.ReactNode;
  possibleActions: readonly TransactionAction[];

  state: State;
  onChange: <K extends keyof S>(field: K, value: S[K]) => void;

  hideUserSearch?: boolean;
  isClient?: boolean;
}

type AddPane = "bridge" | "exchange" | "farm" | "lendingProtocol";

export function Classification<S extends State>(props: Props<S>) {
  const {
    children,
    possibleActions,
    state,
    onChange,
    hideUserSearch = false,
    isClient = false,
  } = props;
  const { isClientLoan } = state;

  const [addPaneToShow, setAddPaneToShow] = useState<AddPane | null>(null);

  const pendingTransactionsQuery = useIndexerClientTransactionsQuery(
    state.user ? [state.user, true] : skipToken,
  );

  const [toastProps, toast] = useToast();
  const handleSuccessAdd = () => {
    toast({ message: "Successfully added", severity: "success", timeout: 5000 });
  };

  const AddPaneComponent = getAddPaneComponent(addPaneToShow);

  const depositWithdrawButtons = (
    <ToggleButtonGroup
      exclusive
      onChange={(event, newValue: boolean | null) => {
        onChange("isDeposit", newValue);
      }}
      size={"small"}
      value={state.isDeposit}
    >
      <ToggleButton color={"success"} value={true}>
        {state.action === "loan" && !(isClient || isClientLoan || state.isCollateral)
          ? "Borrow"
          : "Deposit"}
      </ToggleButton>
      <ToggleButton color={"error"} value={false}>
        {state.action === "loan" && !(isClient || isClientLoan || state.isCollateral)
          ? "Repay"
          : "Withdraw"}
      </ToggleButton>
    </ToggleButtonGroup>
  );

  const renderAddButton = (name: AddPane) => (
    <IconButton
      onClick={() => {
        setAddPaneToShow(name);
      }}
    >
      <Add />
    </IconButton>
  );

  const getUserSearch = (label: string) => (
    <UserSearch
      fullWidth
      label={label}
      selectedUser={state.user}
      setSelectedUser={(user) => onChange("user", user)}
    />
  );

  return (
    <Stack alignItems="center" gap={4} flex={2}>
      <ToggleButtonGroup
        exclusive
        value={state.action}
        onChange={(event, nextView: TransactionAction | null) => {
          onChange("action", nextView);
          setAddPaneToShow(null);
        }}
      >
        {possibleActions.map((action) => {
          return (
            <ToggleButton key={action} value={action}>
              {capitalizeWord(action)}
            </ToggleButton>
          );
        })}
      </ToggleButtonGroup>

      {state.action === "bridge" && (
        <SearchContainer>
          {depositWithdrawButtons}
          <BridgeSearch
            selectedBridge={state.bridge}
            setSelectedBridge={(bridge) => onChange("bridge", bridge)}
          />
          {renderAddButton("bridge")}
        </SearchContainer>
      )}

      {state.action === "loan" && (
        <SearchContainer>
          {!isClient && (
            <Stack gap={2}>
              <ToggleButton
                selected={isClientLoan}
                size="small"
                value="check"
                onChange={() => onChange("isClientLoan", !isClientLoan)}
              >
                Client
              </ToggleButton>
              {!isClientLoan && (
                <>
                  <ToggleButtonGroup
                    exclusive
                    onChange={(event, newValue: boolean | null) => {
                      onChange("isCollateral", newValue);
                    }}
                    size={"small"}
                    value={state.isCollateral}
                  >
                    <ToggleButton color={"success"} value={true}>
                      Collateral
                    </ToggleButton>
                    <ToggleButton color={"error"} value={false}>
                      Borrow
                    </ToggleButton>
                  </ToggleButtonGroup>
                  {depositWithdrawButtons}
                </>
              )}
            </Stack>
          )}
          {isClient || isClientLoan ? (
            <Stack gap={4} width="100%">
              {depositWithdrawButtons}
              {!hideUserSearch && getUserSearch("Client the Loan is for")}

              {!isClient &&
                state.user &&
                (pendingTransactionsQuery.isFetching ? (
                  <CircularProgress size={16} />
                ) : pendingTransactionsQuery.isError ? (
                  <Typography color="error.dark" pl={4}>
                    Error loading pending transactions:{" "}
                    {getErrorDetail(pendingTransactionsQuery.error)}
                  </Typography>
                ) : (
                  <PendingTransactionsSelect
                    clientTransactionId={state.clientTransactionId}
                    onChange={(value) => {
                      onChange("clientTransactionId", value);
                    }}
                    pendingTransactions={pendingTransactionsQuery.data ?? []}
                  />
                ))}
            </Stack>
          ) : (
            <LendingProtocolSearch
              selectedLendingProtocol={state.lendingProtocol}
              setSelectedLendingProtocol={(lendingProtocol) =>
                onChange("lendingProtocol", lendingProtocol)
              }
            />
          )}
          {!(isClient || isClientLoan) && renderAddButton("lendingProtocol")}
        </SearchContainer>
      )}

      {state.action === "pool" && (
        <SearchContainer>
          {depositWithdrawButtons}
          <ExchangeSearchWithAccountAddress
            selectedExchange={state.exchange}
            setSelectedExchange={(exchange: ExchangeWithAccountAddress | null) =>
              onChange("exchange", exchange)
            }
          />
          {renderAddButton("exchange")}
        </SearchContainer>
      )}

      {state.action === "spend" && getUserSearch("Spent on Client payment (optional)")}

      {state.action === "swap" && (
        <SearchContainer>
          <ExchangeSearchWithAccountAddress
            selectedExchange={state.exchange}
            setSelectedExchange={(exchange: ExchangeWithAccountAddress | null) =>
              onChange("exchange", exchange)
            }
          />
          {renderAddButton("exchange")}
          {enablePooledFund ? (
            <Box display="flex" justifyContent="center" width="100%">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={state.clientTransactionId !== null || state.isForPooledFund}
                    onChange={(event) => {
                      onChange("isForPooledFund", event.target.checked);
                      // Reset the ClientTransaction ID in favor of the isForPooledFund option when used
                      onChange("clientTransactionId", null);
                    }}
                  />
                }
                label="For Pooled Fund"
              />
            </Box>
          ) : (
            getUserSearch("Swapped for User (optional)")
          )}
        </SearchContainer>
      )}

      {state.action === "stake" && (
        <SearchContainer>
          {depositWithdrawButtons}
          <FarmSearchWithAccountAddress
            selectedFarm={state.farm}
            setSelectedFarm={(farm: FarmWithAccountAddress | null) => onChange("farm", farm)}
          />
          {renderAddButton("farm")}
        </SearchContainer>
      )}

      {state.action && isSahaba && (
        <Box display="flex" justifyContent="center" width="100%">
          <FormControlLabel
            control={
              <Checkbox
                checked={state.isRevenueShare}
                onChange={(event) => {
                  onChange("isRevenueShare", event.target.checked);
                }}
                sx={{ padding: 0 }}
              />
            }
            label="Revenue Share"
          />
        </Box>
      )}

      {AddPaneComponent ? (
        <Box alignSelf="stretch">
          <AddPaneComponent
            close={() => {
              setAddPaneToShow(null);
            }}
            onSuccess={handleSuccessAdd}
          />
        </Box>
      ) : (
        children
      )}

      <Toast {...toastProps} />
    </Stack>
  );
}

function getAddPaneComponent(name: AddPane | null) {
  switch (name) {
    case "bridge":
      return BridgeAdd;
    case "exchange":
      return ExchangeAdd;
    case "farm":
      return FarmAdd;
    case "lendingProtocol":
      return LendingProtocolAdd;
    default:
      return null;
  }
}

function SearchContainer({ children }: { children: React.ReactNode }) {
  return (
    <Stack direction="row" gap={2} alignItems="center" width="100%">
      {children}
    </Stack>
  );
}

function PendingTransactionsSelect({
  clientTransactionId,
  onChange,
  pendingTransactions,
}: {
  clientTransactionId: number | null;
  onChange: (clientTransactionId: number | null) => void;
  pendingTransactions: readonly AdminClientTransaction[];
}) {
  return pendingTransactions.length > 0 ? (
    <FormControl fullWidth>
      <InputLabel>Client Transaction (optional)</InputLabel>
      <Select
        onChange={(event) => {
          const { value } = event.target;
          onChange(typeof value === "number" ? value : null);
        }}
        value={clientTransactionId ?? ""}
      >
        {pendingTransactions.map((clientTransaction) => (
          <MenuItem key={clientTransaction.id} value={clientTransaction.id}>
            {formatDecimal(Number(clientTransaction.amount))}{" "}
            {clientTransaction.coin.ticker.toUpperCase()}
            {clientTransaction.isDeposit ? " Deposit" : " Withdrawal"}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  ) : (
    <Typography color="warning.dark" pl={4}>
      No Pending Transactions
    </Typography>
  );
}
