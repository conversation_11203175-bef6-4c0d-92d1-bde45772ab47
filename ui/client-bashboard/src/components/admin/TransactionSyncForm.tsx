import { isLocal, isStableTech } from "@/context/env";
import { useIndexerSyncInternalTransactionsMutation } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { LoadingButton } from "@mui/lab";
import { Card, CardActions, CardContent, Typography } from "@mui/material";
import { useState } from "react";

export default function TransactionSyncForm() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [syncTransactions, { isLoading, isError, error }] =
    useIndexerSyncInternalTransactionsMutation();

  const handleSync = async (company: string) => {
    try {
      const result = await syncTransactions(company).unwrap();
      setSuccessMessage(`${result} transactions synced`);
    } catch {
      setSuccessMessage(null);
    }
  };

  if (!(isLocal || isStableTech)) {
    return null;
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h4" gutterBottom>
          Sync Transactions
        </Typography>
        <Typography variant="body1">
          Sync any missing transactions from subsidiary companies.
        </Typography>
        {successMessage && (
          <Typography variant="body2" color="success.main" mt={4}>
            {successMessage}
          </Typography>
        )}
        {isError && (
          <Typography variant="body2" color="error" mt={4}>
            {getErrorDetail(error, { fallback: "Unexpected error" })}
          </Typography>
        )}
      </CardContent>
      <CardActions style={{ paddingLeft: 8 }}>
        {["apollo", "lp"].map((company) => (
          <LoadingButton key={company} loading={isLoading} onClick={() => handleSync(company)}>
            Sync {company}
          </LoadingButton>
        ))}
      </CardActions>
    </Card>
  );
}
