# Transaction Tracking and Accounting System

## Transaction Classification

### Transaction Types

- **Bridge**: Cross-chain transfers (deposits/withdrawals)
- **Income**: Deposit transactions
- **Loan**: Lending-related transactions (deposits/withdrawals)
- **Pool**: Liquidity pool transactions (deposits/withdrawals)
- **Spend**: Withdrawal transactions
- **Stake**: Staking transactions (deposits/withdrawals)
- **Swap**: Exchange transactions (neither deposit nor withdrawal)

### Classification Process

- Manual classification based on transaction type
- Types defined in TransactionAction enum
- Future consideration: Automated classification system

## Accounting System

### Capital Positions

#### Features

- Year-based position tracking
- Generated and imported position support
- Position regeneration capability
- Cost basis calculation
- Profit and Loss (PnL) tracking

#### Position Metrics

- Total cost
- Total PnL
- Total proceeds
- Total volume
- Average cost calculation

### Open Positions

#### Tracking

- Position amount
- Average cost
- Opening confirmation timestamp
- Latest accounted transactions

#### Aggregation

- Positions grouped by coin
- Alias coin support for equivalent assets
- Amount summation
- Weighted average cost calculation

## Tax Reporting

### Implementation

- In-house tax accounting system
- Cost basis tracking
- Capital gains/losses calculation
- Transaction matching system

### Integration Features

- BitcoinTax CSV import support
- CoinTracking CSV import support
- Position matching system
- Year-based record management

## Data Import System

### Job Types

- Import CoinTracking CSV
- Import BitcoinTax CSV
- Wallet synchronization

### Import Sources

- CSV file imports
- Wallet synchronization providers:
  - Covalent
  - Mintscan
  - Helius (Solana initial sync only)

## API Endpoints

### Capital Positions

- Get all positions
- Get positions by coin
- Delete positions by year
- Regenerate positions
- Position summary retrieval

### Open Positions

- Get all open positions
- Get open positions by coin
- Position aggregation
- Transaction history

## Technical Implementation

### Database Management

- PostgreSQL storage
- Transaction managers
- Position managers
- CRUD operations

### Data Processing

- Transaction matching
- Position calculation
- Cost basis computation
- PnL tracking

## Future Considerations

### Automation Opportunities

- Transaction classification
- Tax report generation
- Position matching
- Cost basis calculation

### Enhancement Areas

- Additional import sources
- Automated categorization
- Advanced reporting features
- Performance optimization

## Technical Debt

### Documentation Needs

- Import process flows
- Matching algorithms
- Classification rules
- Tax calculation methods

### System Improvements

- Automated classification
- Performance optimization
- Error handling
- Data validation
- Import process enhancement
