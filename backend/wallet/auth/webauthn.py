import secrets
from collections.abc import Sequence
from typing import Any

from webauthn import (
    generate_authentication_options,
    generate_registration_options,
    verify_authentication_response,
    verify_registration_response,
)
from webauthn.helpers import bytes_to_base64url, options_to_json
from webauthn.helpers.cose import COSEAlgorithmIdentifier
from webauthn.helpers.structs import (
    AttestationConveyancePreference,
    AuthenticatorAttachment,
    AuthenticatorSelectionCriteria,
    AuthenticatorTransport,
    PublicKeyCredentialDescriptor,
    PublicKeyCredentialHint,
    ResidentKeyRequirement,
    UserVerificationRequirement,
)

from common.conf import Environment, settings
from wallet import schemas
from wallet.auth.crud import OngoingCredentialAuthenticationManager
from wallet.kinds import CredentialUse
from wallet.models import Credential, User

RP_ID = settings.webapp_origin_domain

RP_NAME = "Temet"
if settings.env_name != Environment.production:
    RP_NAME = f"{settings.env_name} {RP_NAME}"

EXPECTED_ORIGIN = (
    f"http://{settings.webapp_origin_domain}:{settings.webapp_dev_port}"
    if settings.is_local
    else f"https://{settings.webapp_origin_domain}"
)

SUPPORTED_PUB_KEY_ALGS = [
    COSEAlgorithmIdentifier.ECDSA_SHA_256,
    COSEAlgorithmIdentifier.ECDSA_SHA_512,
]


def registration_options_for(
    use_for: CredentialUse, user: User, existing_credentials: Sequence[Credential]
):
    match (use_for):
        case "2fa":
            authenticator_selection = AuthenticatorSelectionCriteria(
                authenticator_attachment=AuthenticatorAttachment.CROSS_PLATFORM,
                resident_key=ResidentKeyRequirement.DISCOURAGED,
                user_verification=UserVerificationRequirement.DISCOURAGED,
            )
            hints = [PublicKeyCredentialHint.SECURITY_KEY, PublicKeyCredentialHint.HYBRID]
        case "wrap-key":
            authenticator_selection = AuthenticatorSelectionCriteria(
                resident_key=ResidentKeyRequirement.DISCOURAGED,
                user_verification=UserVerificationRequirement.REQUIRED,
            )
            hints = None
        case _:
            raise ValueError(f"Unsupported credential use: {use_for}")

    registration_options = generate_registration_options(
        rp_id=RP_ID,
        rp_name=RP_NAME,
        user_id=user.credentials_id.bytes,
        user_name=user.email,
        user_display_name=user.get_display_name(),
        attestation=AttestationConveyancePreference.NONE,
        authenticator_selection=authenticator_selection,
        challenge=secrets.token_bytes(32),
        exclude_credentials=generate_credential_descriptors(existing_credentials),
        supported_pub_key_algs=SUPPORTED_PUB_KEY_ALGS,
        hints=hints,
    )
    return registration_options.challenge, options_to_json(registration_options)


def generate_credential_descriptors(
    credentials: Sequence[Credential],
) -> list[PublicKeyCredentialDescriptor]:
    return [
        PublicKeyCredentialDescriptor(
            id=credential.id,
            transports=[
                AuthenticatorTransport(transport)
                for transport in credential.transports
                if transport in AuthenticatorTransport
            ],
        )
        for credential in credentials
    ]


def authentication_options_for(use_for: CredentialUse, allow_credentials: Sequence[Credential]):
    if use_for == "2fa":
        user_verification = UserVerificationRequirement.DISCOURAGED
    else:
        user_verification = UserVerificationRequirement.REQUIRED
    auth_options = generate_authentication_options(
        rp_id=RP_ID,
        challenge=secrets.token_bytes(32),
        user_verification=user_verification,
        allow_credentials=generate_credential_descriptors(allow_credentials),
    )

    return auth_options


def verify_registration(
    expected_challenge: bytes,
    require_user_verification: bool,
    registration_response: dict[str, Any],
):
    return verify_registration_response(
        credential=registration_response,
        expected_challenge=expected_challenge,
        expected_rp_id=RP_ID,
        expected_origin=EXPECTED_ORIGIN,
        require_user_verification=require_user_verification,
        supported_pub_key_algs=SUPPORTED_PUB_KEY_ALGS,
    )


def verify_authentication(
    expected_challenge: bytes,
    credential_current_sign_count: int,
    credential_public_key: bytes,
    require_user_verification: bool,
    authentication_response: dict[str, Any],
):
    return verify_authentication_response(
        credential=authentication_response,
        expected_challenge=expected_challenge,
        expected_rp_id=RP_ID,
        expected_origin=EXPECTED_ORIGIN,
        credential_current_sign_count=credential_current_sign_count,
        credential_public_key=credential_public_key,
        require_user_verification=require_user_verification,
    )


def credential_model_to_schema(credential: Credential) -> schemas.Credential:
    return schemas.Credential(
        id=bytes_to_base64url(credential.id),
        name=credential.name,
        created_at=credential.created_at,
        latest_use_at=credential.latest_use_at,
        use_for=credential.use_for,
    )


def create_credential_authentication_options_for(
    use_for: CredentialUse,
    ongoing_auth_manager: OngoingCredentialAuthenticationManager,
    user: User,
    credentials: Sequence[Credential],
):
    auth_options = authentication_options_for(use_for, credentials)
    require_user_verification = (
        auth_options.user_verification == UserVerificationRequirement.REQUIRED
    )
    ongoing_auth_manager.create(
        challenge=auth_options.challenge,
        require_user_verification=require_user_verification,
        user_id=user.id,
    )
    return options_to_json(auth_options)
