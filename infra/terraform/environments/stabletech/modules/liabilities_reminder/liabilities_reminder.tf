resource "kubernetes_cron_job_v1" "liabilities_reminder" {
  metadata {
    name      = "liabilities-reminder"
    namespace = var.namespace
  }

  spec {
    concurrency_policy            = "Forbid"
    schedule                      = "0 14 * * *"
    failed_jobs_history_limit     = 3
    successful_jobs_history_limit = 1
    job_template {
      metadata {}
      spec {
        template {
          metadata {}
          spec {
            container {
              name              = "liabilities-reminder"
              image             = var.python_runner_image
              image_pull_policy = "IfNotPresent"
              args              = ["python", "-m", "stabletech.client.liabilities_reminder"]

              dynamic "env_from" {
                for_each = var.config_maps
                iterator = config_map
                content {
                  config_map_ref {
                    name = config_map.value
                  }
                }
              }
              dynamic "env_from" {
                for_each = var.secrets
                iterator = secret
                content {
                  secret_ref {
                    name = secret.value
                  }
                }
              }

              resources {
                requests = {
                  cpu    = "60m"
                  memory = "128Mi"
                }
              }

              dynamic "volume_mount" {
                for_each = var.host_volumes
                content {
                  name       = volume_mount.value["name"]
                  mount_path = volume_mount.value["mount_path"]
                }
              }
            }
            dynamic "volume" {
              for_each = var.host_volumes
              content {
                name = volume.value["name"]
                host_path {
                  path = volume.value["host_path"]
                }
              }
            }
          }
        }
      }
    }
  }
}
