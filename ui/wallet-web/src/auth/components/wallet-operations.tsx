import { LOCAL_KEY_LIST_KEY } from "@/auth/local-keys";
import { useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import { UnifiedWallet } from "@/auth/unified-wallet/wallet";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { Label } from "@/lib/components/form";
import { TextArea } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { importKey } from "@/lib/secret-store";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import base58 from "bs58";
import { useId, useState } from "react";

interface ImportParams {
  readonly password: Promise<CryptoKey>;
  readonly encodedKey: string;
}

interface WalletOperationsProps {
  selectedAccountId: string;
  setSelectedAccountId: (accountId: string) => void;
}

export function WalletOperations({
  selectedAccountId,
  setSelectedAccountId,
}: WalletOperationsProps) {
  const [importOpen, setImportOpen] = useState(false);

  const queryClient = useQueryClient();

  const importMutation = useMutation({
    mutationFn: async (params: ImportParams) => {
      const password = await params.password;
      const decodedKey = base58.decode(params.encodedKey.trim());
      if (decodedKey.length !== 64) throw new Error("Invalid key length");

      // The first 32 bytes are the secret key, the last 32 bytes are the public key
      // This is the format used by the library @solana/web3.js (used in Phantom wallet and others)
      // See: https://github.com/solana-labs/solana-web3.js/blob/4392380d180cbda0faa58b85a4f9dc988a307854/src/keypair.ts#L51-L69
      const secretKey = decodedKey.slice(0, 32);
      const publicKey = decodedKey.slice(32, 64);

      return await importKey({ password, publicKey, secretKey });
    },
    onSettled() {
      void queryClient.invalidateQueries({ queryKey: LOCAL_KEY_LIST_KEY });
    },
  });
  const getPassword = useGetPassword();

  const handleImport = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const encodedKey = formData.get("encodedKey");
    if (typeof encodedKey !== "string") throw new Error("'encodedKey' must be a string");
    importMutation.mutate(
      { password: getPassword(formData), encodedKey },
      {
        onSuccess() {
          setImportOpen(false);
        },
      },
    );
  };

  const id = useId();
  const encodedKeyId = `key-${id}`;

  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-center text-lg font-medium">Wallet Operations</h3>
      <div className="grid gap-4">
        <Dialog.Root open={importOpen} onOpenChange={setImportOpen}>
          <UnifiedWallet
            importTrigger={
              <Dialog.Trigger asChild>
                <Button variant="outline">Import Key</Button>
              </Dialog.Trigger>
            }
            selectedAccountId={selectedAccountId}
            onSelectAccount={setSelectedAccountId}
          />
          <Dialog.Content title="Import key" aria-describedby="">
            <form className="grid gap-4" onSubmit={handleImport}>
              <div className="grid gap-2">
                <Label htmlFor={encodedKeyId}>Solana secret key</Label>
                <TextArea
                  id={encodedKeyId}
                  name="encodedKey"
                  className="h-20!"
                  disabled={importMutation.isPending}
                  required
                />
              </div>
              <PasswordInput disabled={importMutation.isPending} />
              {importMutation.isError && (
                <AlertError>{getErrorMessage(importMutation.error)}</AlertError>
              )}
              <div className="grid grid-cols-2 gap-4">
                <Button type="submit" disabled={importMutation.isPending}>
                  Import
                </Button>
                <Dialog.Close asChild>
                  <Button variant="secondary">Cancel</Button>
                </Dialog.Close>
              </div>
            </form>
          </Dialog.Content>
        </Dialog.Root>
      </div>
    </div>
  );
}
