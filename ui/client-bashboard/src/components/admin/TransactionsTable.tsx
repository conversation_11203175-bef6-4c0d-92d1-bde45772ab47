import { CloseOutlined } from "@mui/icons-material";
import {
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Fade,
  IconButton,
  Stack,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { useCallback, useMemo, useState } from "react";

import { ButtonCopy } from "@/components/ButtonCopy";
import { addressFormatter, diffCurrencyFormatter, formatDateTime } from "@/helpers/formatters";
import { useIndexerTransactionResetMutation } from "@/store/services/api";
import { Transaction, Transfer } from "@/types/schemas";

const INITIAL_STATE: GridInitialState = {
  // most recent transaction first
  sorting: { sortModel: [{ field: "confirmedAt", sort: "desc" }] },
};

type ColDef = GridColDef<Transaction, unknown, unknown>;

interface MappedTransfer {
  readonly ticker: string;
  readonly amount: number;
}

type TransfersMapper = (transfers: readonly Transfer[]) => MappedTransfer[];

interface Props {
  readonly rows: readonly Transaction[];
  readonly transfersMapper: TransfersMapper;
  readonly costBasisCalculator?: (transaction: Transaction) => string;
  readonly pagination?: {
    pageSize: number;
  };
}

export default function TransactionsTable(props: Props) {
  const { rows, transfersMapper, costBasisCalculator } = props;
  const [transactionToReset, setTransactionToReset] = useState<Transaction>();

  const columns = useMemo(
    () => computeColumns(transfersMapper, costBasisCalculator, setTransactionToReset),
    [transfersMapper, costBasisCalculator],
  );

  const handleClose = () => {
    setTransactionToReset(undefined);
  };

  return (
    <>
      <DataGridPro
        columns={columns}
        rows={rows}
        initialState={INITIAL_STATE}
        autoHeight
        disableSelectionOnClick
        pagination
        pageSize={props.pagination?.pageSize}
      />
      <ResetTransactionDialog transaction={transactionToReset} onClose={handleClose} />
    </>
  );
}

interface ResetTransactionDialogProps {
  readonly transaction: Transaction | undefined;
  readonly onClose: () => void;
}

function ResetTransactionDialog(props: ResetTransactionDialogProps) {
  const { transaction, onClose } = props;
  const [resetTransaction, { isLoading, error }] = useIndexerTransactionResetMutation();
  return (
    <Dialog open={transaction != null} onClose={onClose}>
      <DialogTitle>Reset transaction?</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Are you sure you want to reset transaction with hash{" "}
          <code>{transaction?.txHash}</code>?
        </DialogContentText>
        {error != null && <Typography color="error">Error: {JSON.stringify(error)}</Typography>}
      </DialogContent>
      <DialogActions>
        <Fade in={isLoading}>
          <CircularProgress size={16} sx={{ marginRight: 4 }} />
        </Fade>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={() => {
            if (transaction == null) return;
            resetTransaction(transaction.id).then((result) => {
              if ("error" in result) return;
              onClose();
            });
          }}
        >
          Reset
        </Button>
      </DialogActions>
    </Dialog>
  );
}

/** Show only these tickers on this pre-defined order */
export function useTransfersMapperTickers(tickers: readonly string[]): TransfersMapper {
  return useCallback(
    (transfers: readonly Transfer[]) => mapTransfersWithTickers(tickers, transfers),
    [tickers],
  );
}

/** Combine transfers amounts with same ticker */
export function transfersMapperCombined(transfers: readonly Transfer[]): MappedTransfer[] {
  const mapped = transfers.map(computeMappedTransfer).filter(isNonNullable);
  mapped.sort((a, b) => a.ticker.localeCompare(b.ticker));
  // eslint-disable-next-line unicorn/no-array-reduce
  return mapped.reduce<MappedTransfer[]>(combineTransfersReducer, []);
}

function computeMappedTransfer(transfer: Transfer): MappedTransfer | null {
  if (transfer.assetAccount.coin == null) return null;
  let amount = Number.parseFloat(transfer.amount);
  if (transfer.isWithdrawal) amount = -amount;
  return {
    amount,
    ticker: transfer.assetAccount.coin.ticker,
  };
}

/** Combine consecutive transfers amounts with same ticker */
function combineTransfersReducer(
  arr: MappedTransfer[],
  item: MappedTransfer,
): MappedTransfer[] {
  const last = arr.pop();
  if (last?.ticker === item.ticker) {
    arr.push({ ticker: last.ticker, amount: last.amount + item.amount });
  } else {
    if (last != null) arr.push(last);
    arr.push(item);
  }
  return arr;
}

function computeColumns(
  transfersMapper: TransfersMapper,
  costBasisCalculator: ((transaction: Transaction) => string) | undefined,
  setTransactionToReset: (tx: Transaction) => void,
): ColDef[] {
  const columns: (ColDef | null)[] = [
    {
      field: "confirmedAt",
      headerName: "At",
      minWidth: 200,
      renderCell: ({ row: { confirmedAt } }) => formatDateTime(confirmedAt),
    },
    {
      field: "action",
      headerName: "Action",
      minWidth: 120,
      renderCell: ({ row: { action } }) => (
        <Typography sx={{ textTransform: "capitalize" }}>{action}</Typography>
      ),
    },
    {
      field: "transfers",
      headerName: "Transfers",
      headerAlign: "center",
      align: "center",
      sortable: false,
      filterable: false,
      flex: 1,
      minWidth: 220,
      renderCell: ({ row: { transfers } }) => renderTransfers(transfersMapper, transfers),
    },
    {
      field: "comment",
      headerName: "Comment",
      headerAlign: "center",
      align: "center",
      sortable: false,
      filterable: false,
      flex: 1,
      minWidth: 120,
    },
    costBasisCalculator == null
      ? null
      : {
          field: "costBasis",
          headerName: "$ Cost Basis",
          headerAlign: "right",
          align: "right",
          sortable: false,
          filterable: false,
          minWidth: 120,
          valueGetter: ({ row }) => costBasisCalculator(row),
        },
    {
      field: "txHash",
      headerName: "Tx Hash",
      headerAlign: "center",
      align: "center",
      minWidth: 160,
      renderCell: ({ row: { txHash } }) => (
        <ButtonCopy textToCopy={txHash}>
          <Typography fontSize={14}>{addressFormatter(txHash)}</Typography>
        </ButtonCopy>
      ),
    },
    {
      field: "reset",
      headerName: "Reset",
      headerAlign: "center",
      align: "center",
      sortable: false,
      filterable: false,
      width: 110,
      renderCell: ({ row }) => (
        <IconButton
          aria-label="reset"
          color="error"
          onClick={() => {
            setTransactionToReset(row);
          }}
        >
          <CloseOutlined />
        </IconButton>
      ),
    },
  ];
  return columns.filter(isNonNullable);
}

function mapTransfersWithTickers(
  tickers: readonly string[],
  transfers: readonly Transfer[],
): MappedTransfer[] {
  const mapped = new Array<MappedTransfer | null>(tickers.length).fill(null);
  for (const transfer of transfers) {
    if (transfer.assetAccount.coin == null) continue;
    const ticker = transfer.assetAccount.coin.ticker;
    const index = tickers.indexOf(ticker);
    if (index === -1) continue;
    const amountAbsolute = Number.parseFloat(transfer.amount);
    const amount = transfer.isWithdrawal ? amountAbsolute : -amountAbsolute;
    mapped[index] = { amount, ticker };
  }
  return mapped.filter(isNonNullable);
}

function isNonNullable<T>(value: T | null | undefined): value is T {
  return value != null;
}

function renderTransfers(transfersMapper: TransfersMapper, transfers: readonly Transfer[]) {
  const mapped = transfersMapper(transfers);
  return (
    <Stack direction="row" gap={3}>
      {mapped.map(({ ticker, amount }) => {
        const color = amount < 0 ? "error.main" : "success.main";
        return (
          <Typography
            key={ticker}
            component="span"
            color={color}
            fontSize={13}
            textTransform="uppercase"
          >
            {ticker}{" "}
            <Typography component="span" color="inherit" fontSize={11}>
              {diffCurrencyFormatter.format(amount)}
            </Typography>
          </Typography>
        );
      })}
    </Stack>
  );
}
