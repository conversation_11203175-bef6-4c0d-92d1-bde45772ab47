from datetime import datetime, timezone

from fastapi import <PERSON>Router, Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from firebase_admin import auth, initialize_app
from pydantic import EmailStr
from sqlalchemy.exc import Integrity<PERSON>rror, NoResultFound

from common.conf import logger, settings
from common.psql import DatabaseSession
from stabletech import schemas
from stabletech.auth import crud
from stabletech.auth.models import User
from stabletech.utils.fastapi import CurrentUser, depends_admin_role, depends_authentication

# Init Firebase Auth
if not settings.is_local:
    initialize_app()
http_401_exception = HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    headers={"WWW-Authenticate": 'Bearer realm="auth_required"'},
    detail="Unauthorized",
)
router = APIRouter()


@router.get("/login", response_model=schemas.User)
def login(
    db: DatabaseSession,
    credential: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
):
    """
    Validate Google Auth and return our own AuthToken.
    """
    decoded_token = None
    error_detail = ""
    user = None

    if not settings.is_local:
        # Avoid validating the token when running locally with no Firebase Auth
        try:
            decoded_token = auth.verify_id_token(credential.credentials)
            email: str = decoded_token["email"]
            user = crud.UserManager(db).get_active_by_email(email)
            if not user:
                if settings.enable_stabletech_auto_signup and email.endswith(
                    "@stabletech.capital"
                ):
                    user = add_user(email, db)
                    user.is_active = True
                    user.set_admin_role()
                    db.commit()
                    db.refresh(user)
                else:
                    error_detail = "Not found"
        except auth.RevokedIdTokenError:
            error_detail = "Token is Revoked"
        except auth.ExpiredIdTokenError:
            error_detail = "Expired Token"
        except auth.InvalidIdTokenError:
            error_detail = "Invalid Token"
        except Exception:
            error_detail = "Honey"

        if error_detail or not user:
            logger.warning(f"Unauthorized: {error_detail}")
            raise http_401_exception

    else:
        # Allow using any value for the token when running locally for debug purposes
        # Authenticates using the configured ADMIN_EMAIL
        user = db.query(User).filter_by(email=settings.admin_email).first()

        if not user:
            logger.warning(f"Unauthorized: {error_detail}")
            raise http_401_exception

    if user.first_name is None:
        # Fill data from Google Auth for new users
        name_split = decoded_token["name"].split()
        user.first_name = name_split[0]
        user.last_name = name_split[-1] if len(name_split) > 1 else ""
        user.google_id = decoded_token["user_id"]
        user.picture_url = decoded_token["picture"]
        db.commit()
        db.refresh(user)

    user.auth_token = crud.AuthTokenManager(db).get_or_create(user.id).token
    return user


if settings.enable_password_login:

    @router.post("/login-with-password", response_model=schemas.User)
    def login_with_password(login_schema: schemas.LoginPassword, db: DatabaseSession):
        user_manager = crud.UserManager(db)
        user = user_manager.get_active_by_email(login_schema.email)
        if not user:
            if settings.enable_stabletech_auto_signup and login_schema.email.endswith(
                "@stabletech.capital"
            ):
                user = user_manager.add_with_password(login_schema)
                user.is_active = True
                user.set_admin_role()
                db.commit()
                db.refresh(user)
        if not user or not crud.pwd_context.verify(login_schema.password, user.password_hash):
            raise http_401_exception
        user.auth_token = crud.AuthTokenManager(db).get_or_create(user.id).token
        return user


@router.get("/me", dependencies=[depends_authentication], response_model=schemas.User)
def me(db: DatabaseSession, user: CurrentUser):
    user.auth_token = crud.AuthTokenManager(db).get_or_create(user.id).token
    return user


@router.post(
    "/user/accept-agreement", dependencies=[depends_authentication], response_model=schemas.User
)
def accept_agreement(db: DatabaseSession, user: CurrentUser):
    if user.agreement_accepted_at is None:
        user.agreement_accepted_at = datetime.now(timezone.utc)
        db.commit()
    return user


@router.post("/user/add", dependencies=[depends_admin_role], response_model=schemas.User)
def add_user(email: EmailStr, db: DatabaseSession):
    try:
        user = crud.UserManager(db).add(email)
    except IntegrityError:
        raise HTTPException(status_code=400, detail="Email already exists")
    return user


if settings.enable_password_login:

    @router.post(
        "/user/add-with-password",
        dependencies=[depends_admin_role],
        response_model=schemas.User,
    )
    def add_user_with_password(
        login_password_schema: schemas.LoginPassword, db: DatabaseSession
    ):
        try:
            user = crud.UserManager(db).add_with_password(login_password_schema)
        except IntegrityError:
            raise HTTPException(status_code=400, detail="Email already exists")
        return user


@router.delete("/user", dependencies=[depends_admin_role])
def delete_user(user_id: int, db: DatabaseSession):
    crud.UserManager(db).delete(user_id)


@router.get("/users", dependencies=[depends_admin_role], response_model=list[schemas.User])
def get_users(db: DatabaseSession, include_admins: bool = False):
    users = crud.UserManager(db).get_active_users(include_admins=include_admins)
    return users


@router.patch("/user/update", dependencies=[depends_admin_role], response_model=schemas.User)
def update_user(user_schema: schemas.User, db: DatabaseSession):
    try:
        return crud.UserManager(db).update(user_schema.model_dump(exclude_unset=True))
    except NoResultFound:
        raise HTTPException(status_code=404, detail="User not found")
