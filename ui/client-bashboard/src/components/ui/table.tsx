import { cx } from "cva";
import { ComponentPropsWithoutRef, forwardRef } from "react";

export function Table(props: ComponentPropsWithoutRef<"table">) {
  return (
    <div className="relative w-full overflow-auto">
      <table {...props} className={cx("w-full caption-bottom text-sm", props.className)} />
    </div>
  );
}

export function TableHeader(props: ComponentPropsWithoutRef<"thead">) {
  return <thead {...props} className={cx("[&_tr]:border-b", props.className)} />;
}

export function TableBody(props: ComponentPropsWithoutRef<"tbody">) {
  return <tbody {...props} className={cx("[&_tr:last-child]:border-0", props.className)} />;
}

export function TableFooter(props: ComponentPropsWithoutRef<"tfoot">) {
  return (
    <tfoot
      {...props}
      className={cx("border-t bg-muted/50 font-medium last:[&>tr]:border-b-0", props.className)}
    />
  );
}

export const TableRow = forwardRef<HTMLTableRowElement, ComponentPropsWithoutRef<"tr">>(
  function TableRow(props, ref) {
    return (
      <tr
        {...props}
        ref={ref}
        className={cx(
          "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
          props.className,
        )}
      />
    );
  },
);

export function TableHead(props: ComponentPropsWithoutRef<"th">) {
  return (
    <th
      {...props}
      className={cx(
        "h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        props.className,
      )}
    />
  );
}

export function TableCell(props: ComponentPropsWithoutRef<"td">) {
  return (
    <td
      {...props}
      className={cx(
        "min-h-5 p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        props.className,
      )}
    />
  );
}

export function TableCaption(props: ComponentPropsWithoutRef<"caption">) {
  return (
    <caption {...props} className={cx("mt-4 text-sm text-muted-foreground", props.className)} />
  );
}
