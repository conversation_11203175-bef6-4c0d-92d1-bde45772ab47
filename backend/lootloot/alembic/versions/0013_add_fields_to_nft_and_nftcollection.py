"""Add fields to NFT and NFTCollection

Revision ID: 0013
Revises: 0012
Create Date: 2024-02-02 00:47:57.118408

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0013"
down_revision = "0012"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "nft",
        sa.Column("attributes_data", postgresql.JSON(astext_type=sa.Text()), nullable=False),
    )
    op.add_column("nft", sa.Column("image_url", sa.String(), nullable=False))
    op.add_column("nft", sa.Column("uid", sa.String(), nullable=False))
    op.add_column("nft", sa.Column("is_burnt", sa.Boolean(), nullable=False))
    op.add_column("nft", sa.Column("is_frozen", sa.<PERSON>olean(), nullable=False))
    op.add_column("nft", sa.Column("owner_address_uri", sa.String(), nullable=False))
    op.add_column("nftcollection", sa.Column("address_uri", sa.String(), nullable=False))
    op.add_column("nftcollection", sa.Column("royalty_perecent", sa.Numeric(), nullable=False))
    op.add_column("nftcollection", sa.Column("description", sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("nftcollection", "description")
    op.drop_column("nftcollection", "royalty_perecent")
    op.drop_column("nftcollection", "address_uri")
    op.drop_column("nft", "owner_address_uri")
    op.drop_column("nft", "is_frozen")
    op.drop_column("nft", "is_burnt")
    op.drop_column("nft", "uid")
    op.drop_column("nft", "image_url")
    op.drop_column("nft", "attributes_data")
    # ### end Alembic commands ###
