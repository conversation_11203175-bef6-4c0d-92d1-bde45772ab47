import { deriveWrapping<PERSON><PERSON> } from "@/lib/secret-store";
import { xchacha20poly1305 } from "@noble/ciphers/chacha";
import { managedNonce } from "@noble/ciphers/webcrypto";
import { get, set } from "idb-keyval";

const PREFIX = "password-store";
const PASSWORD_TEST_KEY = `${PREFIX}:test`;

interface TestData {
  readonly salt: Uint8Array;
  readonly iterations: number;
  readonly ciphertext: Uint8Array;
}

const xchacha20poly1305WithManagedNonce = managedNonce(xchacha20poly1305);

export async function isPasswordRegistered() {
  return (await get<TestData>(PASSWORD_TEST_KEY)) != null;
}

export async function registerPassword({ password }: { password: CryptoKey }) {
  const { wrappingKey, salt, iterations } = await deriveWrappingKey(password);

  const wrappingKeyData = await crypto.subtle.exportKey("raw", wrappingKey);
  const cypher = xchacha20poly1305WithManagedNonce(new Uint8Array(wrappingKeyData));
  const data = crypto.getRandomValues(new Uint8Array(32));
  const ciphertext = cypher.encrypt(data);

  if (await isPasswordRegistered()) {
    if (await testPassword({ password })) {
      return; // same password is already registered
    }
    throw new Error("Another password is already registered");
  }
  await set(PASSWORD_TEST_KEY, { iterations, salt, ciphertext } satisfies TestData);
}

export async function testPassword({ password }: { password: CryptoKey }) {
  const testData = await get<TestData>(PASSWORD_TEST_KEY);
  if (!testData) {
    throw new Error("No password was registered");
  }

  const { wrappingKey } = await deriveWrappingKey(password, testData.salt, testData.iterations);
  const wrappingKeyData = await crypto.subtle.exportKey("raw", wrappingKey);
  const cypher = xchacha20poly1305WithManagedNonce(new Uint8Array(wrappingKeyData));

  try {
    cypher.decrypt(testData.ciphertext);
  } catch (error) {
    if (error instanceof Error && error.message === "invalid tag") {
      return false;
    }
    throw new Error("Failed to decrypt password test data", { cause: error });
  }
  return true;
}
