import { Card } from "@/components/ui/card";
import { currencyFormatter } from "@/helpers/formatters";

interface PortfolioSummarySahabaProps {
  portfolioDollars: number;
  interestEarnedDollars: number;
  showInterest: boolean;
}

export function PortfolioSummarySahaba({
  portfolioDollars,
  interestEarnedDollars,
  showInterest,
}: PortfolioSummarySahabaProps) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
      <Card>
        <div className="flex flex-col">
          <h2 className="relative mb-1 text-sm font-medium text-gray-500">
            Portfolio Value
            <span className="absolute bottom-0 left-0 h-[1px] w-4 bg-primary"></span>
          </h2>
          <p className="mt-2 text-4xl font-medium text-primary">
            ${currencyFormatter.format(portfolioDollars)}
          </p>
        </div>
      </Card>

      {showInterest && (
        <Card className="bg-white/85 p-5 shadow-sm">
          <div className="flex flex-col">
            <h2 className="relative mb-1 text-sm font-medium text-gray-500">
              Lifetime Earnings
              <span className="absolute bottom-0 left-0 h-[1px] w-4 bg-secondary"></span>
            </h2>
            <p className="mt-2 text-4xl font-medium text-secondary">
              ${currencyFormatter.format(interestEarnedDollars)}
            </p>
          </div>
        </Card>
      )}
    </div>
  );
}
