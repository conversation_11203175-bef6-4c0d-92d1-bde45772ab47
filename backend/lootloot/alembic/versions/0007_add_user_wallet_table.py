"""Add user wallet table

Revision ID: 0007
Revises: 0006
Create Date: 2023-12-22 18:51:24.287799

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0007"
down_revision = "0006"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "userwallet",
        sa.<PERSON>umn("user_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("address", sa.String(), nullable=False),
        sa.<PERSON>umn("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "address"),
        sa.UniqueConstraint("user_id", "name"),
    )
    op.drop_index("ix_user_id", table_name="user")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_user_id", "user", ["id"], unique=False)
    op.drop_table("userwallet")
    # ### end Alembic commands ###
