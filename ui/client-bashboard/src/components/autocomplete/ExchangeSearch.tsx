import { addressFormatter } from "@/helpers/formatters";
import {
  useIndexerExchangesQuery,
  useIndexerExchangesWithAccountAddressQuery,
} from "@/store/services/api";
import { Exchange, ExchangeWithAccountAddress } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { Dispatch, SyntheticEvent } from "react";

interface ExchangeSearchProps {
  disabled?: boolean;
  hidden?: boolean;
  label?: string;
  selectedExchange: Exchange | null;
  setSelectedExchange: Dispatch<Exchange | null>;
}

export const ExchangeSearch = (props: ExchangeSearchProps) => {
  const selectedExchange = props.selectedExchange;
  const setSelectedExchange = props.setSelectedExchange;
  const { data: exchangesData } = useIndexerExchangesQuery();

  return (
    <Autocomplete
      fullWidth
      hidden={props.hidden}
      disabled={props.disabled}
      getOptionLabel={(option) => `${option.name}`}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      options={exchangesData || []}
      onChange={(_: SyntheticEvent, newValue: Exchange | null) => {
        setSelectedExchange(newValue);
      }}
      renderInput={(params) => <TextField {...params} label={props.label || "Exchange"} />}
      value={selectedExchange}
    />
  );
};

interface ExchangeSearchWithAccountAddressProps {
  selectedExchange: ExchangeWithAccountAddress | null;
  setSelectedExchange: Dispatch<ExchangeWithAccountAddress | null>;
}

export const ExchangeSearchWithAccountAddress = (
  props: ExchangeSearchWithAccountAddressProps,
) => {
  const { data: exchangesData } = useIndexerExchangesWithAccountAddressQuery();
  return (
    <Autocomplete
      fullWidth
      getOptionLabel={(option) =>
        `${option.name} | ${option.network?.name ?? "—"} | ${option.accountAddress ? addressFormatter(option.accountAddress) : "—"}`
      }
      isOptionEqualToValue={(option, value) =>
        option.id === value.id &&
        option.accountAddress === value.accountAddress &&
        option.network?.id === value.network?.id
      }
      options={exchangesData || []}
      onChange={(_: SyntheticEvent, newValue: ExchangeWithAccountAddress | null) => {
        props.setSelectedExchange(newValue);
      }}
      renderInput={(params) => <TextField {...params} label="Exchange" />}
      value={props.selectedExchange}
    />
  );
};
