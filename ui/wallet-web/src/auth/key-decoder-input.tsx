import { GetPasswordOptions, PasswordContext, useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import { createWrapKeyAuthOptions, RequestOptions, verifyCredential } from "@/lib/auth";
import { <PERSON><PERSON> } from "@/lib/components/button";
import {
  getCredentialRegistered,
  isKeyCredentialWrapped,
  KeyDecoder,
} from "@/lib/secret-store";
import { base64URLStringToBuffer, startAuthentication } from "@simplewebauthn/browser";
import { useQuery } from "@tanstack/react-query";
import { cx } from "class-variance-authority";
import { Fingerprint } from "lucide-react";
import { use, useState } from "react";

/**
 * Hook to get a key decoder that can be used to sign transactions.
 * It will check if a credential should be used based on the form data,
 * otherwise falling back to password-based authentication.
 */
export function useGetKeyDecoder() {
  const getPassword = useGetPassword();

  return async function getKeyDecoder(
    formData: FormData,
    options: GetPasswordOptions = {},
  ): Promise<KeyDecoder> {
    // Check if form has useCredential field set to true
    const useCredential = formData.get("useCredential") === "true";

    if (useCredential) {
      // Get registered credential data
      const credential = await getCredentialRegistered();
      if (!credential) {
        throw new Error("No wallet authenticator is activated");
      }

      // Get secret key for unwrapping with the authenticator flow
      const optionsResp = await createWrapKeyAuthOptions(credential.id);
      const authResp = await startAuthentication({
        optionsJSON: optionsResp.auth_options as RequestOptions,
      });
      const verificationResp = await verifyCredential(authResp);
      if (verificationResp.kind !== "wrap-key") {
        throw new Error("Unexpected authenticator verification response");
      }
      const secretKeyBuffer = base64URLStringToBuffer(verificationResp.secret_key);

      // Convert buffer to a CryptoKey
      const cryptoKey = await crypto.subtle.importKey(
        "raw", // Format
        secretKeyBuffer, // Key material
        "AES-KW", // Algorithm identifier
        true, // Extractable - needed for exportKey later
        ["wrapKey", "unwrapKey"], // Key usages
      );

      // Return credential-based decoder with the proper CryptoKey
      return { kind: "credential", key: cryptoKey };
    }

    // Fall back to password-based decoder if not using credential
    const passwordKey = await getPassword(formData, {
      testPassword: options.testPassword,
    });

    return { kind: "password", password: passwordKey };
  };
}

interface KeyDecoderInputProps {
  keyId: string;
  disabled?: boolean;
  className?: string;
}

export function KeyDecoderInput({ keyId, disabled = false, className }: KeyDecoderInputProps) {
  const [forcePasswordMode, setForcePasswordMode] = useState(false);
  const { password } = use(PasswordContext);

  const isCredentialWrapped = useQuery({
    queryKey: ["isKeyCredentialWrapped", keyId],
    queryFn: async () => isKeyCredentialWrapped(keyId),
  });

  // Use authenticator only if:
  // 1. User hasn't chosen to force password
  // 2. Wallet is not already unlocked
  // 3. The key supports credential unwrapping
  const useAuthenticator =
    !forcePasswordMode &&
    !password &&
    isCredentialWrapped.isSuccess &&
    isCredentialWrapped.data;

  // Show spinner or return null while loading
  if (isCredentialWrapped.isPending) {
    return null;
  }

  // If we can use the authenticator, show that UI
  if (useAuthenticator) {
    return (
      <div className={cx("grid gap-4 rounded border p-4", className)}>
        <div className="flex items-center gap-2 text-sm">
          <Fingerprint className="h-5 w-5 flex-none text-primary" />
          <span className="w-min min-w-0 flex-auto">
            Your wallet authenticator will be used to sign this transaction.
          </span>
        </div>

        {/* Hidden input to indicate credential should be used */}
        <input type="hidden" name="useCredential" value="true" />

        <Button
          type="button"
          variant="outline"
          size="sm"
          className="justify-self-start"
          disabled={disabled}
          onClick={() => {
            setForcePasswordMode(true);
          }}
        >
          Use password
        </Button>
      </div>
    );
  }

  // Otherwise, fall back to password input
  return (
    <PasswordInput
      className={className}
      disabled={disabled}
      onReturnToAuthenticator={
        isCredentialWrapped.data
          ? () => {
              setForcePasswordMode(false);
            }
          : undefined
      }
    />
  );
}
