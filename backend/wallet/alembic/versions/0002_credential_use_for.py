"""Credential use for

Revision ID: 0002
Revises: 0001
Create Date: 2025-03-10 17:39:05.811338

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0002"
down_revision: str | None = "0001"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "credential",
        sa.Column("use_for", sa.Enum("2fa", "wrap-key", native_enum=False), nullable=True),
    )
    op.execute(sa.text("UPDATE credential SET use_for = '2fa' WHERE use_for IS NULL"))
    op.alter_column("credential", "use_for", nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("credential", "use_for")
    # ### end Alembic commands ###
