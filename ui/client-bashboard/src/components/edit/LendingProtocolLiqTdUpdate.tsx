import { LoanCoinTotalProcessed } from "@/pages/admin/loans/[id]";
import { useIndexerLendingProtocolLiqTUpdateMutation } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Alert, Button, DialogActions, DialogTitle, Input, Stack } from "@mui/material";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { useEffect, useState } from "react";

interface LendingProtocolLiqTdUpdateProps {
  coinTotal: LoanCoinTotalProcessed | null;
  loanId: number;
  onClose: () => void;
}

export const LendingProtocolLiqTdUpdate = (props: LendingProtocolLiqTdUpdateProps) => {
  const { loanId, onClose } = props;
  const [lendingProtocolLiqTUpdate] = useIndexerLendingProtocolLiqTUpdateMutation();
  const [liquidationThresholdValue, setLiquidationThresholdValue] = useState<string | null>(
    null,
  );
  const [errorAlert, setErrorAlert] = useState<string | null>(null);
  const display = props.coinTotal != null;

  useEffect(() => {
    if (props.coinTotal != null) {
      setLiquidationThresholdValue(props.coinTotal.liquidationThreshold);
      setErrorAlert(null);
    }
  }, [props.coinTotal]);

  const closeDialog = () => {
    onClose();
  };

  const submitUpdate = () => {
    if (liquidationThresholdValue) {
      lendingProtocolLiqTUpdate([
        loanId,
        {
          coinUid: props.coinTotal?.coin.uid ?? "",
          liquidationThreshold: liquidationThresholdValue,
        },
      ])
        .unwrap()
        .then(closeDialog)
        .catch((error) => {
          setErrorAlert(getErrorDetail(error, { fallback: "Unknown error" }));
        });
    } else {
      setErrorAlert("Please enter a liquidation threshold");
    }
  };
  return (
    <Dialog open={display}>
      <DialogTitle>{"Liquidation Threshold"}</DialogTitle>
      <DialogContent>
        <Stack spacing={6} pt={4} minWidth={"xs"}>
          {errorAlert != null && <Alert severity={"error"}>{errorAlert}</Alert>}
          <Input
            type="number"
            onChange={(event) => {
              setLiquidationThresholdValue(event.target.value);
            }}
            value={liquidationThresholdValue}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        {/* eslint-disable-next-line jsx-a11y/no-autofocus */}
        <Button autoFocus onClick={closeDialog}>
          Cancel
        </Button>
        <Button onClick={submitUpdate} variant={"outlined"}>
          Update
        </Button>
      </DialogActions>
    </Dialog>
  );
};
