#!/bin/bash
set -euo pipefail

echo "Ensuring pip requirements are up to date..."
pip install --quiet -r common/requirements.txt

if [ -f "../infra/tmp/load.dump.sql" ]; then
  # Delete and replace `temet` DB with the dump file content
  # Data producers must be disabled to avoid contamination while loading
  PGPASSWORD=$POSTGRESQL_PASSWORD psql -h $POSTGRESQL_HOST -U postgres -c "drop database temet with (force);"
  PGPASSWORD=$POSTGRESQL_PASSWORD psql -h $POSTGRESQL_HOST -U postgres -c "create database temet;"
  PGPASSWORD=$POSTGRESQL_PASSWORD psql -h $POSTGRESQL_HOST -U postgres -d temet < ../infra/tmp/load.dump.sql

  rm ../infra/tmp/load.dump.sql
fi

echo "Executing command: " "$@"
exec "$@"
