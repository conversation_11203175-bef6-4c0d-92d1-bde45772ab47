from dataclasses import dataclass
from datetime import datetime, timedelta
from decimal import Decima<PERSON>
from typing import Literal, Sequence

from sqlalchemy import case, func, or_, select, update

from common.conf import settings
from common.exceptions import HTTP400Exception
from stabletech import schemas
from stabletech.auth.models import User
from stabletech.client.models import ClientTransaction
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Account, Coin, Transfer, Wallet
from stabletech.market.crud import CoinManager
from stabletech.utils import eager_load
from stabletech.utils.models import BaseManager


def get_appreciation_amount(
    amount: Decimal, started_at: datetime, is_withdrawal: bool, interest_rate: Decimal
) -> Decimal:
    """
    Returns the appreciated amount based on the provided interest rate
    """
    return (
        amount
        * (
            interest_rate
            / 365
            / 24
            / 60
            / 60
            * Decimal(str((datetime.now() - started_at).total_seconds()))
        )
        * Decimal(-1 if is_withdrawal else 1)
    )


@dataclass
class LiabilityDraft:
    coin_liabilities: dict[Coin, schemas.Liability]
    earliest_deposit_at: datetime
    last_payment_at: datetime | None
    total_usd_debt: Decimal


class ClientTransactionManager(BaseManager[ClientTransaction]):
    model = ClientTransaction

    def add(
        self,
        schema: schemas.AddClientTransaction,
        user: User | None = None,
    ):
        if settings.enable_pooled_fund:
            if user is not None:
                raise HTTP400Exception("User should be not be set for pooled funds")
        else:
            if user is None:
                raise HTTP400Exception("User must be set for client transactions")
        return super().add(schema, user=user)

    def delete(self, client_transaction_id: int):
        self.db.query(ClientTransaction).filter(
            ClientTransaction.id == client_transaction_id
        ).delete()
        self.db.commit()

    def delete_by_transaction_id(self, transaction_id: int, *, commit=True):
        self.db.query(ClientTransaction).filter(
            or_(
                ClientTransaction.transaction_id == transaction_id,
                ClientTransaction.revenue_transaction_id == transaction_id,
            )
        ).delete()
        if commit:
            self.db.commit()

    def get_by_transaction_id(self, transaction_id: int) -> ClientTransaction | None:
        return self.db.scalar(
            select(ClientTransaction).where(ClientTransaction.transaction_id == transaction_id)
        )

    def get_by_revenue_transaction_id(self, transaction_id: int) -> ClientTransaction | None:
        return self.db.scalar(
            select(ClientTransaction).where(
                ClientTransaction.revenue_transaction_id == transaction_id
            )
        )

    def get_ids_by_transaction_ids(self, transaction_ids: list[int]) -> dict[int, int]:
        """Returns a dict mapping transaction IDs to client transaction IDs.

        Args:
            transaction_ids: List of transaction IDs to look up

        Returns:
            Dict with transaction_id as key and client_transaction.id as value
        """
        results = self.db.execute(
            select(ClientTransaction.transaction_id, ClientTransaction.id).where(
                ClientTransaction.transaction_id.in_(transaction_ids)
            )
        ).all()

        return {tx_id: client_tx_id for tx_id, client_tx_id in results}

    def get_user_id_by_transaction_id(self, transaction_id: int) -> int | None:
        return self.db.scalar(
            select(ClientTransaction.user_id).where(
                ClientTransaction.transaction_id == transaction_id
            )
        )

    def get_user_current_balance_for_coin(self, user_id: int, coin_id: int) -> Decimal:
        coin_transfer_ids = (
            select(Transfer.id)
            .join(Transfer.asset_account)
            .join(
                ClientTransaction, ClientTransaction.transaction_id == Transfer.transaction_id
            )
            .where(
                Account.coin_id == coin_id,
                ClientTransaction.user_id == user_id,
            )
            .scalar_subquery()
        )
        balance = self.db.scalar(
            select(
                func.sum(
                    case(
                        (
                            # Opposite of `Account.is_wallet`
                            or_(
                                Account.wallet_id == None,
                                Wallet.user_id != None,
                            ),
                            Transfer.amount,
                        ),
                        else_=Transfer.amount * -1,
                    )
                )
            )
            .select_from(Transfer)
            .join(Transfer.from_account)
            .join(Account.wallet, isouter=True)
            .where(Transfer.id.in_(coin_transfer_ids))
        )
        return balance if balance is not None else Decimal(0)

    def get_user_client_transactions(
        self,
        user_id: int,
        filter_option: Literal["only_pending", "exclude_pending", None] = None,
        sort_by: Literal["created_at", None] = None,
    ) -> Sequence[ClientTransaction]:
        query = (
            select(ClientTransaction)
            .filter(ClientTransaction.user_id == user_id)
            .options(*eager_load.CLIENT_TRANSACTION)
        )
        if filter_option == "only_pending":
            query = query.filter(ClientTransaction.transaction_id == None)
        elif filter_option == "exclude_pending":
            query = query.filter(ClientTransaction.transaction_id != None)
        if sort_by == "created_at":
            query = query.order_by(ClientTransaction.created_at.desc())
        return self.db.scalars(query).all()

    def get_liabilities(self) -> list[schemas.UserLiability]:
        """Returns the total debt for each User"""
        user_liabilities: dict[User, LiabilityDraft] = {}
        client_transactions = self.db.scalars(
            select(ClientTransaction)
            .join(User, ClientTransaction.user_id == User.id)
            .where(ClientTransaction.transaction_id != None, User.earns_interest)
        ).all()

        for client_transaction in client_transactions:
            transaction = client_transaction.transaction
            if client_transaction.user not in user_liabilities:
                user_liabilities[client_transaction.user] = LiabilityDraft(
                    coin_liabilities={},
                    earliest_deposit_at=transaction.confirmed_at,
                    last_payment_at=None,
                    total_usd_debt=Decimal(0),
                )

            user_liability = user_liabilities[client_transaction.user]
            if transaction.confirmed_at < user_liability.earliest_deposit_at:
                user_liability.earliest_deposit_at = transaction.confirmed_at
            if transaction.action == TransactionAction.spend and (
                not user_liability.last_payment_at
                or transaction.confirmed_at > user_liability.last_payment_at
            ):
                user_liability.last_payment_at = transaction.confirmed_at

            for transfer in transaction.transfers:
                coin = transfer.asset_account.alias_coin
                if not coin:
                    asset_account = transfer.asset_account
                    asset_account.coin = CoinManager(self.db).get_coin_by_ticker_or_fail(
                        transfer.asset_account.ticker_symbol
                    )
                    asset_account.coin_id = asset_account.coin.id
                    coin = asset_account.alias_coin
                    self.db.commit()
                if coin not in user_liability.coin_liabilities:
                    coin_liability = user_liability.coin_liabilities[coin] = schemas.Liability(
                        coin_latest_price_usd=coin.latest_usd_price,
                        coin_name=coin.name,
                        current_balance=0,
                        total_debt=0,
                    )
                else:
                    coin_liability = user_liability.coin_liabilities[coin]

                if client_transaction.is_interest_payment:
                    debt = transfer.amount * -1
                else:
                    if not client_transaction.user:
                        raise HTTP400Exception("User must be set for client transactions")
                    debt = get_appreciation_amount(
                        transfer.amount,
                        transaction.confirmed_at,
                        transfer.is_withdrawal,
                        client_transaction.user.interest_rate,
                    )
                    coin_liability.current_balance += transfer.amount * (
                        -1 if transfer.is_withdrawal else 1
                    )
                coin_liability.total_debt += debt
                user_liability.total_usd_debt += debt * coin_liability.coin_latest_price_usd

        return list(
            schemas.UserLiability(
                due_at=(liability_draft.last_payment_at or liability_draft.earliest_deposit_at)
                + timedelta(days=365),
                liabilities=liability_draft.coin_liabilities.values(),
                total_usd_debt=liability_draft.total_usd_debt,
                user=user,
            )
            for user, liability_draft in user_liabilities.items()
        )

    def reset_by_transaction_id(self, transaction_id: int):
        """Set transaction_id and reviewed_at to `None` for its ClientTransaction."""
        self.db.execute(
            update(ClientTransaction).where(ClientTransaction.transaction_id == transaction_id),
            {"transaction_id": None, "reviewed_at": None},
        )

    def review(self, client_transaction_id: int) -> ClientTransaction:
        client_transaction = self.get_by_id(client_transaction_id)
        client_transaction.reviewed_at = datetime.now()
        self.db.commit()
        return client_transaction
