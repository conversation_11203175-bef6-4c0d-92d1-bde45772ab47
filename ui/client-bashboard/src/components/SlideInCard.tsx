import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Slide } from "@mui/material";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import Stack from "@mui/material/Stack";

interface SlideInCardProps {
  children: React.ReactNode;
  errorAlert: string;
  show: boolean;
  title?: string;
}

export const SlideInCard = (props: SlideInCardProps) => {
  return (
    <Grid item xs={12} lg={6} hidden={!props.show}>
      <Grid container spacing={6}>
        <Slide direction={"left"} in={props.show} mountOnEnter unmountOnExit>
          <Grid item xs>
            <Card>
              <CardContent>
                <Stack spacing={6} pt={4}>
                  {props.errorAlert && <Alert severity={"error"}>{props.errorAlert}</Alert>}
                  {props.title && <CardHeader title={props.title} />}
                  {props.children}
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Slide>
      </Grid>
    </Grid>
  );
};
