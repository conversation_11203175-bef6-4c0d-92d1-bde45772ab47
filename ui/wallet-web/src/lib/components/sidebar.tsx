"use client";
import { AuthContext } from "@/auth/auth-ctx";
import { button } from "@/lib/components/button";
import { PasswordMonitor } from "@/lib/components/password-monitor";
import { ScrollArea } from "@/lib/components/scroll-area";
import { ModeToggle } from "@/lib/components/theme";
import { User } from "@/types/schemas";
import * as Dialog from "@radix-ui/react-dialog";
import { cx } from "class-variance-authority";
import {
  BarChartBig,
  CircleUser,
  LayoutGrid,
  LogIn,
  LogOut,
  Menu,
  Shirt,
  Trophy,
  X,
} from "lucide-react";
import { use, useCallback, useState } from "react";
import { NavLink, Outlet } from "react-router";

export function SidebarLayout() {
  const authCtx = use(AuthContext);
  const { user } = authCtx;

  const [modalOpen, setModalOpen] = useState(false);
  const handleCloseModal = useCallback(() => {
    setModalOpen(false);
  }, []);

  // Hide private elements from sidebar if not logged in.
  // To prevent flickering for logged-in users, we only assume logged out state after initialized.
  const isLoggedIn = user != null;

  const navItems = (
    <ul className="flex flex-col gap-2 py-4">
      <Item to="/auth" Icon={BarChartBig} onClick={handleCloseModal}>
        Auth
      </Item>
      <Item to="/test" Icon={Trophy} onClick={handleCloseModal}>
        Test
      </Item>
      <Item to="/other-test" Icon={Shirt} onClick={handleCloseModal}>
        Other Test
      </Item>
      {isLoggedIn && (
        <Item to="/profile" Icon={LayoutGrid} onClick={handleCloseModal}>
          Profile
        </Item>
      )}
    </ul>
  );

  const signInButtonClass = button({
    className: "gap-2 self-start justify-self-end !-ml-2 !px-2",
    variant: "ghost",
    size: "sm",
  });
  const userDetail = (
    <div className="grid grid-cols-[1fr_auto] gap-2">
      <PasswordMonitor className="col-span-full" />
      <div className="flex max-w-full min-w-0 flex-col gap-1">
        <div className="flex flex-initial items-center gap-1">
          <CircleUser size={16} className="shrink-0" />
          <span className="truncate text-xs" title="Signed in user">
            {getIdentifier(user)}
          </span>
        </div>
        {isLoggedIn ? (
          <button
            type="button"
            className={signInButtonClass}
            onClick={() => {
              handleCloseModal();
              authCtx.set(null);
            }}
          >
            <span>Sign out</span>
            <LogOut size={16} />
          </button>
        ) : (
          <NavLink to="/auth" className={signInButtonClass} onClick={handleCloseModal}>
            <span>Sign In</span>
            <LogIn size={16} />
          </NavLink>
        )}
      </div>
      <ModeToggle />
    </div>
  );

  const menuModal = (
    <Dialog.Root open={modalOpen} onOpenChange={setModalOpen}>
      <Dialog.Trigger className={ITEM_CLASS}>
        <Menu aria-label="Menu" />
      </Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-background/70 backdrop-blur-xs data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:animate-in data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed right-0 bottom-0 flex min-h-[50%] w-60 flex-col rounded-tl-md border-t border-l bg-background px-3 py-6 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:slide-out-to-bottom-4 data-[state=closed]:slide-out-to-right data-[state=closed]:zoom-out-95 data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:slide-in-from-bottom-4 data-[state=open]:slide-in-from-right data-[state=open]:zoom-in-95">
          <Dialog.Title className="sr-only">Menu</Dialog.Title>
          <Dialog.Close
            className={cx(button({ variant: "ghost", size: "icon" }), "absolute top-2 right-3")}
            aria-label="Close"
          >
            <X />
          </Dialog.Close>
          {navItems}
          <div className="mt-auto flex flex-col gap-1 px-1.5 pt-4">{userDetail}</div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );

  return (
    <div className="flex min-h-dvh">
      <nav className="sticky top-0 left-0 hidden h-dvh w-60 shrink-0 flex-col border-r lg:flex">
        <ScrollArea className="w-full">
          <div className="flex h-full flex-col px-3">{navItems}</div>
        </ScrollArea>
        <div className="mt-auto flex flex-col gap-1 border-t px-4 py-4">{userDetail}</div>
      </nav>
      <div className="flex w-full flex-col">
        <div className="w-full flex-1 overflow-x-auto">
          <Outlet />
        </div>
        <nav className="sticky bottom-0 left-0 h-12 w-full shrink-0 bg-background lg:hidden">
          <ul className="flex h-full w-full items-center justify-around border-t">
            <ItemBottom to="/auth" Icon={BarChartBig}>
              Auth
            </ItemBottom>
            <ItemBottom to="/test" Icon={Trophy}>
              Test
            </ItemBottom>
            <li>{menuModal}</li>
          </ul>
        </nav>
      </div>
    </div>
  );
}

const ITEM_CLASS = cx(
  button({ variant: "ghost" }),
  "w-full justify-start! [&.active]:bg-primary/20",
);

function Item({
  children,
  to,
  Icon,
  onClick,
}: {
  children: React.ReactNode;
  to: string;
  Icon: React.ComponentType;
  onClick: () => void;
}) {
  return (
    <li>
      <NavLink to={to} onClick={onClick} className={ITEM_CLASS}>
        <Icon />
        <span className="ms-3">{children}</span>
      </NavLink>
    </li>
  );
}

function ItemBottom({
  children,
  to,
  Icon,
}: {
  children: React.ReactNode;
  to: string;
  Icon: React.ComponentType;
}) {
  return (
    <li>
      <NavLink to={to} className={ITEM_CLASS}>
        <Icon />
        <span className="sr-only">{children}</span>
        <span aria-hidden className="ms-3 hidden sm:inline">
          {children}
        </span>
      </NavLink>
    </li>
  );
}

function getIdentifier(user: User | null | undefined): string {
  if (!user) return "Anonymous";
  const name = [user.first_name, user.last_name].filter(Boolean).join(" ");
  // Cannot use nullish coalescing, as we don't want to prematurely return an empty string
  return name || user.email || "Anonymous";
}
