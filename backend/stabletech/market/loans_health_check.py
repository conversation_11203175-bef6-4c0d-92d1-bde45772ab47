from datetime import datetime, <PERSON><PERSON><PERSON>

from sqlalchemy.orm import Session

from common.conf import logger
from stabletech import schemas
from stabletech.indexer.crud import AccountManager
from stabletech.market.crud import AlertManager
from stabletech.market.models import Alert, AlertStatus
from stabletech.utils.slack import post_message, stable_tech_channel

INITIAL_ALERT_THRESHOLD = 10
LIQUIDATED_ALERT_THRESHOLD = 0
TIME_BETWEEN_ALERTS = 60 * 60 * 24


def loans_health_check(db: Session):
    account_manager = AccountManager(db)
    loans: list[schemas.AccountLoanFull] = account_manager.get_loans(p2p=False)
    for loan in loans:
        alert = AlertManager(db).get_active_by_account_id(loan.id)
        if (
            loan.total_liquidation_threshold == 0
            or loan.total_borrowed == 0
            or loan.total_collateral == 0
        ):
            continue

        loan_to_value = loan.total_borrowed / loan.total_collateral
        liquidation_threshold = loan.total_liquidation_threshold / loan.total_collateral
        percent_to_liquidation = (liquidation_threshold - loan_to_value) * 100
        collateral_amount = sum(
            coint_total.collateral_amount for coint_total in loan.coin_totals
        )

        status = None
        message = None
        if percent_to_liquidation <= LIQUIDATED_ALERT_THRESHOLD:
            status = AlertStatus.liquidated
            message = f"Loan {loan.id} name {loan.name} network {loan.network.name} is in liquidated state"
        elif percent_to_liquidation <= INITIAL_ALERT_THRESHOLD:
            status = AlertStatus.initial_alert
            message = f"Loan {loan.id} name {loan.name} network {loan.network.name} is in initial alert state, less than 10% to liquidation"
        else:
            status = AlertStatus.healthy
            message = None

        logger.info(
            f"Loan {loan.id} status {status} message {message} alert {alert} percent_to_liquidation {percent_to_liquidation} collateral_amount {collateral_amount} total_collateral {loan.total_collateral} total_borrowed {loan.total_borrowed} loan_to_value {loan_to_value} liquidation_threshold {liquidation_threshold}"
        )

        if alert is None and status is not AlertStatus.healthy:
            AlertManager(db).add_model(
                Alert(
                    account_id=loan.id,
                    collateral_amount=collateral_amount,
                    status=status,
                    created_at=datetime.utcnow(),
                    sent_at=datetime.utcnow(),
                ),
            )
            post_message(channel=stable_tech_channel(), text=message)
            db.commit()
            continue

        if alert:
            # If the collateral amount has changed, the loan has been updated so we need to reset tracking
            if alert.collateral_amount != collateral_amount:
                alert.is_active = False
                alert.updated_at = datetime.utcnow()
                db.commit()

                if status is not AlertStatus.healthy:
                    AlertManager(db).add_model(
                        Alert(
                            account_id=loan.id,
                            collateral_amount=collateral_amount,
                            status=status,
                            created_at=datetime.utcnow(),
                            sent_at=datetime.utcnow(),
                        ),
                    )
                    post_message(channel=stable_tech_channel(), text=message)
                    db.commit()
                    continue

            if alert.status != status:
                match alert.status:
                    case AlertStatus.initial_alert:
                        if status == AlertStatus.final_alert:
                            process_alert(db, alert, status, message, force_send=True)
                        if status == AlertStatus.healthy:
                            process_alert(db, alert, status, message, send_slack=False)
                        if status == AlertStatus.liquidated:
                            process_alert(db, alert, status, message, force_send=True)
                    case AlertStatus.final_alert:
                        if status == AlertStatus.liquidated:
                            process_alert(db, alert, status, message, force_send=True)
                        elif status == AlertStatus.healthy:
                            process_alert(db, alert, status, message, send_slack=False)
                        else:
                            process_alert(db, alert, status, message)
                    case AlertStatus.liquidated:
                        process_alert(db, alert, status, message)
                    case AlertStatus.healthy:
                        process_alert(db, alert, status, message)
            elif status in (AlertStatus.initial_alert, AlertStatus.final_alert):
                process_alert(db, alert, status, message)
            else:
                process_alert(db, alert, status, message, send_slack=False)


def process_alert(
    db: Session,
    alert: Alert,
    status,
    message: str,
    *,
    send_slack: bool = True,
    force_send: bool = False,
):
    if (
        force_send
        or send_slack
        and (
            alert.sent_at is None
            or alert.sent_at + timedelta(seconds=TIME_BETWEEN_ALERTS) < datetime.utcnow()
        )
    ):
        alert.sent_at = datetime.utcnow()
        post_message(channel=stable_tech_channel(), text=message)

    alert.status = status
    alert.updated_at = datetime.utcnow()
    db.commit()
