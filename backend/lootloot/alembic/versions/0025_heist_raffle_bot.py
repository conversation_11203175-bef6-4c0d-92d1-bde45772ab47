"""Heist Raffle Bot

Revision ID: 0025
Revises: 0024
Create Date: 2024-05-09 00:46:27.401714

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0025"
down_revision = "0024"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "fungibleinventory",
        sa.Column("amount", sa.Integer(), nullable=False),
        sa.Column("asset_type", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.<PERSON>eyConstraint("id"),
    )
    op.create_index(
        op.f("ix_fungibleinventory_asset_type"),
        "fungibleinventory",
        ["asset_type"],
        unique=True,
    )
    op.create_index(op.f("ix_fungibleinventory_id"), "fungibleinventory", ["id"], unique=False)
    op.create_table(
        "fungibletransfer",
        sa.Column("amount", sa.Integer(), nullable=False),
        sa.Column("asset_metadata", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("asset_type", sa.String(), nullable=False),
        sa.Column(
            "at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False
        ),
        sa.Column("wallet_address", sa.String(), nullable=True),
        sa.Column("withdrawal_user_id", sa.Integer(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.CheckConstraint("NOT(wallet_address IS NULL AND withdrawal_user_id IS NULL)"),
        sa.ForeignKeyConstraint(
            ["withdrawal_user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_fungibletransfer_asset_type"), "fungibletransfer", ["asset_type"], unique=False
    )
    op.create_index(op.f("ix_fungibletransfer_id"), "fungibletransfer", ["id"], unique=False)
    op.create_index(
        op.f("ix_fungibletransfer_wallet_address"),
        "fungibletransfer",
        ["wallet_address"],
        unique=False,
    )
    op.create_table(
        "nonfungibleinventory",
        sa.Column(
            "asset_type",
            sa.Enum("consumable", "cosmetic", "item", native_enum=False),
            nullable=False,
        ),
        sa.Column("game_id", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_nonfungibleinventory_asset_type"),
        "nonfungibleinventory",
        ["asset_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_nonfungibleinventory_game_id"),
        "nonfungibleinventory",
        ["game_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_nonfungibleinventory_id"), "nonfungibleinventory", ["id"], unique=False
    )
    op.create_table(
        "raffle",
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("ended_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("entries_count", sa.Integer(), nullable=False),
        sa.Column("secret_word", sa.String(), nullable=False),
        sa.Column("winners_count", sa.Integer(), nullable=False),
        sa.Column("is_active", sa.Boolean(), server_default="true", nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("secret_word"),
    )
    op.create_index(op.f("ix_raffle_id"), "raffle", ["id"], unique=False)
    op.create_table(
        "nonfungibletransfer",
        sa.Column("asset_metadata", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("asset_name", sa.String(), nullable=False),
        sa.Column(
            "asset_type",
            sa.Enum("consumable", "cosmetic", "item", native_enum=False),
            nullable=False,
        ),
        sa.Column(
            "at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False
        ),
        sa.Column("game_id", sa.String(), nullable=False),
        sa.Column("is_deposit", sa.Boolean(), nullable=False),
        sa.Column(
            "rarity",
            sa.Enum("COMMON", "UNCOMMON", "RARE", "EPIC", "LEGENDARY", "MYTHIC", name="rarity"),
            nullable=False,
        ),
        sa.Column("wallet_address", sa.String(), nullable=True),
        sa.Column("withdrawal_user_id", sa.Integer(), nullable=True),
        sa.Column("prize_for_raffle_id", sa.Integer(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.CheckConstraint("NOT(wallet_address IS NULL AND withdrawal_user_id IS NULL)"),
        sa.ForeignKeyConstraint(
            ["prize_for_raffle_id"],
            ["raffle.id"],
        ),
        sa.ForeignKeyConstraint(
            ["withdrawal_user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_nonfungibletransfer_asset_name"),
        "nonfungibletransfer",
        ["asset_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_nonfungibletransfer_asset_type"),
        "nonfungibletransfer",
        ["asset_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_nonfungibletransfer_game_id"), "nonfungibletransfer", ["game_id"], unique=False
    )
    op.create_index(
        op.f("ix_nonfungibletransfer_id"), "nonfungibletransfer", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_nonfungibletransfer_wallet_address"),
        "nonfungibletransfer",
        ["wallet_address"],
        unique=False,
    )
    op.create_table(
        "raffleentry",
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("non_fungible_transfer_id", sa.Integer(), nullable=True),
        sa.Column("raffle_id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["non_fungible_transfer_id"],
            ["nonfungibletransfer.id"],
        ),
        sa.ForeignKeyConstraint(
            ["raffle_id"],
            ["raffle.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("raffle_id", "user_id"),
    )
    op.create_index(op.f("ix_raffleentry_id"), "raffleentry", ["id"], unique=False)
    op.add_column(
        "nft",
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    op.add_column(
        "user",
        sa.Column(
            "joined_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    op.add_column(
        "userwallet",
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("userwallet", "created_at")
    op.drop_column("user", "joined_at")
    op.drop_column("nft", "created_at")
    op.drop_index(op.f("ix_raffleentry_id"), table_name="raffleentry")
    op.drop_table("raffleentry")
    op.drop_index(
        op.f("ix_nonfungibletransfer_wallet_address"), table_name="nonfungibletransfer"
    )
    op.drop_index(op.f("ix_nonfungibletransfer_id"), table_name="nonfungibletransfer")
    op.drop_index(op.f("ix_nonfungibletransfer_game_id"), table_name="nonfungibletransfer")
    op.drop_index(op.f("ix_nonfungibletransfer_asset_type"), table_name="nonfungibletransfer")
    op.drop_index(op.f("ix_nonfungibletransfer_asset_name"), table_name="nonfungibletransfer")
    op.drop_table("nonfungibletransfer")
    op.drop_index(op.f("ix_raffle_id"), table_name="raffle")
    op.drop_table("raffle")
    op.drop_index(op.f("ix_nonfungibleinventory_id"), table_name="nonfungibleinventory")
    op.drop_index(op.f("ix_nonfungibleinventory_game_id"), table_name="nonfungibleinventory")
    op.drop_index(op.f("ix_nonfungibleinventory_asset_type"), table_name="nonfungibleinventory")
    op.drop_table("nonfungibleinventory")
    op.drop_index(op.f("ix_fungibletransfer_wallet_address"), table_name="fungibletransfer")
    op.drop_index(op.f("ix_fungibletransfer_id"), table_name="fungibletransfer")
    op.drop_index(op.f("ix_fungibletransfer_asset_type"), table_name="fungibletransfer")
    op.drop_table("fungibletransfer")
    op.drop_index(op.f("ix_fungibleinventory_id"), table_name="fungibleinventory")
    op.drop_index(op.f("ix_fungibleinventory_asset_type"), table_name="fungibleinventory")
    op.drop_table("fungibleinventory")
    # ### end Alembic commands ###

    # Remove Enum not handled by Alembic autogen
    sa.Enum("COMMON", "UNCOMMON", "RARE", "EPIC", "LEGENDARY", "MYTHIC", name="rarity").drop(
        op.get_bind()
    )
