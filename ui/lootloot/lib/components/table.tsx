import { cx } from "class-variance-authority";
import { ComponentPropsWithoutRef } from "react";

export function Table(props: ComponentPropsWithoutRef<"table">) {
  return (
    <div className="relative w-full overflow-auto">
      <table {...props} className={cx("w-full caption-bottom text-sm", props.className)} />
    </div>
  );
}

export function TableHeader(props: ComponentPropsWithoutRef<"thead">) {
  return <thead className="[&_tr]:border-b" {...props} />;
}

export function TableBody(props: ComponentPropsWithoutRef<"tbody">) {
  return <tbody className="[&_tr:last-child]:border-0" {...props} />;
}

export function TableFooter(props: ComponentPropsWithoutRef<"tfoot">) {
  return (
    <tfoot className="border-t bg-muted/50 font-medium last:[&>tr]:border-b-0" {...props} />
  );
}

export function TableRow(props: ComponentPropsWithoutRef<"tr">) {
  return (
    <tr
      {...props}
      className={cx(
        "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
        props.className,
      )}
    />
  );
}

export function TableHead(props: ComponentPropsWithoutRef<"th">) {
  return (
    <th
      className="h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]"
      {...props}
    />
  );
}

export function TableCell(props: ComponentPropsWithoutRef<"td">) {
  return (
    <td
      className="min-h-5 p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]"
      {...props}
    />
  );
}

export function TableCaption(props: ComponentPropsWithoutRef<"caption">) {
  return <caption className="mt-4 text-sm text-muted-foreground" {...props} />;
}
