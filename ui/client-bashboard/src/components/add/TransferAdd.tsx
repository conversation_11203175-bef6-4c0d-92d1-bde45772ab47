import {
  CoinSearch,
  CoinSearchWithAssetAddressBase,
} from "@/components/autocomplete/CoinSearch";
import type { TransactionToAdd } from "@/components/transaction/TransactionAdd";
import type { EditState } from "@/components/transaction/TransactionEdit";
import {
  useLazyIndexerAccountQuery,
  useMarketCoinsByNetworkWithAssetAddressQuery,
} from "@/store/services/api";
import { AddAccount, AddTransfer } from "@/types/schemas";
import { Delete, MoneyOff } from "@mui/icons-material";
import { IconButton, Stack, TextField } from "@mui/material";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import ToggleButton from "@mui/material/ToggleButton";
import ToggleButtonGroup from "@mui/material/ToggleButtonGroup";
import { BaseSyntheticEvent, useState } from "react";

interface Account extends AddAccount {
  address: string;
}

interface Transfer extends AddTransfer {
  fromAccount: Account;
  toAccount: Account;
}

interface TransferAddProps {
  index: number;
  onTransferChange: (index: number, transfer: AddTransfer | null) => void;
  transaction: TransactionToAdd | EditState;
  transfer: AddTransfer;
  variant?: "elevation" | "outlined";
}

export const TransferAdd = ({
  index,
  onTransferChange,
  transaction,
  transfer,
  variant,
}: TransferAddProps) => {
  const [coinSearch, setCoinSearch] = useState<string>("");

  const [fetchAccount] = useLazyIndexerAccountQuery();
  const { data: coinsData } = useMarketCoinsByNetworkWithAssetAddressQuery({
    coin: coinSearch,
    networkId: transaction.network?.id,
  });
  const isAssetAddressSet = transfer.assetAccount?.address;
  const isCoinUnset = !transfer.assetAccount?.coin;
  const useCoinSearch = isAssetAddressSet && isCoinUnset;
  const updateTransfer = (transfer: AddTransfer) => onTransferChange(index, transfer);

  return (
    <Card variant={variant}>
      <CardContent>
        <Stack alignItems="center" direction="row" gap={3}>
          <Grid container columnSpacing={2} rowSpacing={4.5}>
            <Grid item xs={2}>
              <TextField
                fullWidth
                label={"Amount"}
                type={"number"}
                value={transfer.amount || ""}
                onChange={(event) => {
                  onTransferChange(index, { ...transfer, amount: event.target.value });
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label={"Asset Address"}
                value={transfer.assetAccount?.address || ""}
                onChange={(event) => {
                  const newAddress = event.target.value.trim();
                  updateTransfer({
                    ...transfer,
                    assetAccount: { ...transfer.assetAccount, address: newAddress },
                  });

                  if (newAddress && transaction.network) {
                    fetchAccount({
                      address: newAddress,
                      networkId: transaction.network.id,
                    })
                      .unwrap()
                      .then((account) => {
                        if (account)
                          updateTransfer({ ...transfer, assetAccount: { ...account } });
                      });
                  }
                }}
              />
            </Grid>
            <Grid item xs={4}>
              {useCoinSearch ? (
                <CoinSearch
                  withPrices
                  disabled={
                    !transaction.network || (transfer.assetAccount.isPoolToken ?? false)
                  }
                  selectedCoin={transfer.assetAccount?.coin || null}
                  setSelectedCoin={(coin) => {
                    updateTransfer({
                      ...transfer,
                      assetAccount: { ...transfer.assetAccount, coin: coin, coinId: coin?.id },
                    });
                  }}
                />
              ) : (
                <CoinSearchWithAssetAddressBase
                  coinsData={coinsData}
                  disabled={
                    !transaction.network || (transfer.assetAccount.isPoolToken ?? false)
                  }
                  selectedCoin={transfer.assetAccount?.coin || null}
                  setCoinSearch={setCoinSearch}
                  setSelectedCoin={(coinWithAssetAddress) => {
                    if (!coinWithAssetAddress?.assetAddress) {
                      updateTransfer({
                        ...transfer,
                        assetAccount: {
                          address: transfer?.assetAccount?.address ?? "",
                          coin: coinWithAssetAddress,
                          coinId: coinWithAssetAddress?.id,
                        },
                      });
                    } else {
                      updateTransfer({
                        ...transfer,
                        assetAccount: {
                          ...transfer.assetAccount,
                          address: coinWithAssetAddress?.assetAddress ?? "",
                          coin: coinWithAssetAddress,
                          coinId: coinWithAssetAddress?.id,
                        },
                      });
                    }
                  }}
                />
              )}
            </Grid>
            <Grid item xs={6}>
              <Address
                updateTransfer={updateTransfer}
                storeField="fromAccount"
                counterpartField="toAccount"
                label="From Address"
                transaction={transaction}
                transfer={transfer}
                value={transfer.fromAccount?.address || ""}
              />
            </Grid>
            <Grid item xs={6}>
              <Address
                updateTransfer={updateTransfer}
                storeField="toAccount"
                counterpartField="fromAccount"
                label="To Address"
                transaction={transaction}
                value={transfer.toAccount?.address || ""}
                transfer={transfer}
              />
            </Grid>
          </Grid>
          <Stack alignItems="center" gap={6} mr={-3}>
            <ToggleButton
              aria-label="No coin (e.g LP token)"
              title="No coin (e.g LP token)"
              selected={transfer.assetAccount.isPoolToken ?? false}
              onChange={() => {
                const isNoCoin = !transfer.assetAccount.isPoolToken;
                const assetAccount = {
                  ...transfer.assetAccount,
                  isPoolToken: isNoCoin,
                  // Also reset Account.id
                  ...(isNoCoin && { coin: null, coinId: null, id: null }),
                };
                updateTransfer({ ...transfer, assetAccount });
              }}
              value=""
              sx={{ height: 32, width: 32, p: 0, border: 0, borderRadius: 999 }}
            >
              <MoneyOff />
            </ToggleButton>
            <IconButton
              aria-label="Delete transfer"
              title="Delete transfer"
              color="error"
              onClick={() => onTransferChange(index, null)}
              size="small"
              sx={{ p: 1 }}
            >
              <Delete />
            </IconButton>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
};

interface AddressProps {
  updateTransfer: (transfer: AddTransfer) => void;
  label: string;
  counterpartField: keyof AddTransfer;
  storeField: keyof AddTransfer;
  transaction: TransactionToAdd | EditState;
  transfer: Transfer | AddTransfer;
  value: string | undefined;
}

export const Address = ({
  updateTransfer,
  label,
  counterpartField,
  storeField,
  transaction,
  transfer,
  value,
}: AddressProps) => {
  const [displayAlert, setDisplayAlert] = useState(false);

  const displayAccountAddress = ["pool", "swap", "stake", "loan"].includes(
    transaction.action ?? "",
  );

  const accountAddress =
    (transaction.exchange?.accountAddress ||
      transaction.farm?.accountAddress ||
      transaction.lendingProtocol?.accountAddress) ??
    "";
  const walletAddress = transaction.wallet?.address ?? "";
  return (
    <Grid container direction="row" justifyContent="center" alignItems="center">
      <Grid item xs={4}>
        <ToggleButtonGroup
          orientation="vertical"
          size={"small"}
          onChange={(event: BaseSyntheticEvent) => {
            let address = event.target.value.trim();
            if ((transfer[counterpartField] as Account)?.address === address) {
              setDisplayAlert(true);
              return;
            }
            if (address === value) {
              // Reset address field
              address = "";
            }
            updateTransfer({
              ...transfer,
              [storeField]: { ...(transfer[storeField] as Account), address: address },
            });
          }}
          onBlur={() => {
            setDisplayAlert(false);
          }}
        >
          <ToggleButton
            value={walletAddress}
            disabled={!walletAddress}
            selected={!!value && value === walletAddress}
          >
            Use Wallet
          </ToggleButton>
          {displayAccountAddress && (
            <ToggleButton
              value={accountAddress}
              disabled={!accountAddress}
              selected={!!value && value === accountAddress}
            >
              Use Account
            </ToggleButton>
          )}
        </ToggleButtonGroup>
      </Grid>

      <Grid item xs={8}>
        <TextField
          fullWidth
          label={label}
          value={value}
          onChange={(event) => {
            const address = event.target.value.trim();
            if ((transfer[counterpartField] as Account)?.address === address) {
              setDisplayAlert(true);
            } else {
              setDisplayAlert(false);
            }
            updateTransfer({
              ...transfer,
              [storeField]: { address: event.target.value.trim() },
            });
          }}
          helperText={displayAlert ? "Address already in use!" : ""}
        />
      </Grid>
      <Grid item xs={12}></Grid>
    </Grid>
  );
};
