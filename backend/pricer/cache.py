from enum import Enum

import redis.asyncio
import redis.asyncio.lock
from pydantic import RootModel

from common.pricer import schemas

MaybePrice = RootModel[schemas.Price | None]
CoinsList = RootModel[list[schemas.Coin]]
MaybeCoinHistory = RootModel[schemas.CoinHistory | None]

# We currently auto-refresh the requested prices every minute. This gives us
# 20 seconds (PRICE_TTL - PRICE_TTL_FRESH - 1 minute) to refresh the prices
# before they get stale. If we change these values, we should also tune the
# values in module `pricer.price_refresher`.
PRICE_TTL = 5 * 60  # 5 minutes
PRICE_TTL_FRESH = 3 * 60 + 40  # 3 minutes 40 seconds

COINS_TTL = 60 * 60  # 1 hour
COINS_TTL_FRESH = 45 * 60  # 45 minutes

COIN_HISTORY_TTL = 24 * 60 * 60  # 1 day
# Consider coin history fresh until expiration

REQUESTED_PRICE_TTL = 20 * 60  # 20 minutes


class CacheState(Enum):
    MISSING = 1
    STALE = 2
    FRESH = 3


class Cache:
    def __init__(
        self,
        *,
        prefix: str = "",
        host: str = "localhost",
        port: int = 6379,
        db: str | int = 0,
        password: str | None = None,
    ) -> None:
        self._redis = redis.asyncio.Redis(host=host, port=port, db=db, password=password)
        if prefix:
            prefix += "."
        self._price_key = prefix + "prices.{id}"
        self._coins_key = prefix + "coins"
        self._coin_history_key = prefix + "coins.{id}.history.{date}"
        self._requested_price_key = prefix + "requested_prices.{id}"
        self._last_requested_prices_refresh_key = prefix + "last_requested_prices_refresh"
        self._requested_prices_refresh_lock_key = prefix + "locks.requested_prices_refresh"

    async def get_prices(
        self, ids: list[str]
    ) -> list[tuple[CacheState, str, schemas.Price | None]]:
        keys = [self._price_key.format(id=cid) for cid in ids]
        pipe = self._redis.pipeline(transaction=False)
        pipe.mget(keys)
        for key in keys:
            pipe.ttl(key)
        values, *ttls = await pipe.execute()
        return [
            (_price_state(value, ttl), cid, _deserialize_price(value))
            for cid, value, ttl in zip(ids, values, ttls)
        ]

    async def set_prices(self, prices: list[tuple[str, schemas.Price | None]]) -> None:
        pipe = self._redis.pipeline(transaction=False)
        for cid, price in prices:
            key = self._price_key.format(id=cid)
            data = MaybePrice(price).model_dump_json(round_trip=True)
            pipe.set(key, data, PRICE_TTL)
        await pipe.execute()

    async def get_coins(self) -> tuple[CacheState, list[schemas.Coin] | None]:
        pipe = self._redis.pipeline(transaction=False)
        pipe.ttl(self._coins_key)
        pipe.get(self._coins_key)
        ttl, data = await pipe.execute()
        if data:
            data = CoinsList.model_validate_json(data).root
            state = CacheState.FRESH if ttl > COINS_TTL_FRESH else CacheState.STALE
            return state, data
        return CacheState.MISSING, None

    async def set_coins(self, coins: list[schemas.Coin]) -> None:
        data = CoinsList(coins).model_dump_json(round_trip=True)
        await self._redis.set(self._coins_key, data, COINS_TTL)

    async def get_coin_history(
        self, coin_id: str, date: str
    ) -> tuple[CacheState, schemas.CoinHistory | None]:
        key = self._coin_history_key.format(id=coin_id, date=date)
        data = await self._redis.get(key)
        if data:
            return CacheState.FRESH, MaybeCoinHistory.model_validate_json(data).root
        return CacheState.MISSING, None

    async def set_coin_history(
        self, coin_id: str, date: str, history: schemas.CoinHistory | None
    ) -> None:
        key = self._coin_history_key.format(id=coin_id, date=date)
        data = MaybeCoinHistory(history).model_dump_json(round_trip=True)
        await self._redis.set(key, data, COIN_HISTORY_TTL)

    async def get_all_requested_prices(self) -> list[str]:
        keys = await self._redis.keys(self._requested_price_key.format(id="*"))
        return [key.decode().split(".")[-1] for key in keys]

    async def set_requested_prices(self, ids: list[str]) -> None:
        pipe = self._redis.pipeline(transaction=False)
        for coin_id in ids:
            key = self._requested_price_key.format(id=coin_id)
            pipe.set(key, "", REQUESTED_PRICE_TTL)
        await pipe.execute()

    async def get_last_requested_prices_refresh(self) -> int:
        data = await self._redis.get(self._last_requested_prices_refresh_key)
        return int(data) if data else 0

    async def set_last_requested_prices_refresh(self, timestamp: int) -> None:
        await self._redis.set(self._last_requested_prices_refresh_key, int(timestamp))

    def requested_prices_refresh_lock(self, timeout: float | None) -> redis.asyncio.lock.Lock:
        return self._redis.lock(self._requested_prices_refresh_lock_key, timeout=timeout)


def _price_state(value: bytes | None, ttl: int) -> CacheState:
    return (
        (CacheState.FRESH if ttl > PRICE_TTL_FRESH else CacheState.STALE)
        if value
        else CacheState.MISSING
    )


def _deserialize_price(value: bytes | None) -> schemas.Price | None:
    return MaybePrice.model_validate_json(value).root if value else None
