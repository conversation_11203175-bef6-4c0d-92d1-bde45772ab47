{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "module": "preserve", "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/app/*": ["./app/*"], "@/lib/*": ["./lib/*"], "@/types/*": ["./types/*"]}, "allowUnreachableCode": false, "allowUnusedLabels": false, "exactOptionalPropertyTypes": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUncheckedIndexedAccess": true, "noUncheckedSideEffectImports": true, "forceConsistentCasingInFileNames": true}, "include": [".next/types/**/*.ts", "*.ts", "app/", "config/", "lib/", "types/"], "exclude": ["node_modules"]}