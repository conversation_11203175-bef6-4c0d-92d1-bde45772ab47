// ** MUI Imports
import { Theme } from "@mui/material/styles";

// ** Type Import
import { Settings } from "@/@core/context/settingsContext";

// ** Overrides Imports
import Mu<PERSON><PERSON>ccordion from "@/@core/theme/overrides/accordion";
import Mu<PERSON><PERSON><PERSON><PERSON> from "@/@core/theme/overrides/alerts";
import MuiAutocomplete from "@/@core/theme/overrides/autocomplete";
import MuiAvatar from "@/@core/theme/overrides/avatars";
import MuiBackdrop from "@/@core/theme/overrides/backdrop";
import MuiButton from "@/@core/theme/overrides/button";
import MuiCard from "@/@core/theme/overrides/card";
import MuiChip from "@/@core/theme/overrides/chip";
import MuiDataGrid from "@/@core/theme/overrides/dataGrid";
import MuiDateTimePicker from "@/@core/theme/overrides/dateTimePicker";
import MuiDialog from "@/@core/theme/overrides/dialog";
import MuiDivider from "@/@core/theme/overrides/divider";
import MuiInput from "@/@core/theme/overrides/input";
import MuiLink from "@/@core/theme/overrides/link";
import MuiList from "@/@core/theme/overrides/list";
import MuiMenu from "@/@core/theme/overrides/menu";
import MuiPagination from "@/@core/theme/overrides/pagination";
import MuiPaper from "@/@core/theme/overrides/paper";
import MuiPopover from "@/@core/theme/overrides/popover";
import MuiRating from "@/@core/theme/overrides/rating";
import MuiSelect from "@/@core/theme/overrides/select";
import MuiSnackbar from "@/@core/theme/overrides/snackbar";
import MuiSwitches from "@/@core/theme/overrides/switches";
import MuiTable from "@/@core/theme/overrides/table";
import MuiTabs from "@/@core/theme/overrides/tabs";
import MuiTimeline from "@/@core/theme/overrides/timeline";
import MuiToggleButton from "@/@core/theme/overrides/toggleButton";
import MuiTooltip from "@/@core/theme/overrides/tooltip";
import MuiTypography from "@/@core/theme/overrides/typography";

const Overrides = (theme: Theme, settings: Settings) => {
  const { skin } = settings;

  const button = MuiButton(theme);
  const chip = MuiChip(theme);
  const list = MuiList(theme);
  const tabs = MuiTabs(theme);
  const input = MuiInput(theme);
  const tables = MuiTable(theme);
  const alerts = MuiAlerts(theme);
  const rating = MuiRating(theme);
  const avatars = MuiAvatar(theme);
  const divider = MuiDivider(theme);
  const menu = MuiMenu(theme, skin);
  const tooltip = MuiTooltip(theme);
  const cards = MuiCard(theme, skin);
  const backdrop = MuiBackdrop(theme);
  const dataGrid = MuiDataGrid(theme);
  const switches = MuiSwitches(theme);
  const timeline = MuiTimeline(theme);
  const accordion = MuiAccordion(theme);
  const dialog = MuiDialog(theme, skin);
  const pagination = MuiPagination(theme);
  const popover = MuiPopover(theme, skin);
  const snackbar = MuiSnackbar(theme, skin);
  const dateTimePicker = MuiDateTimePicker(theme);
  const autocomplete = MuiAutocomplete(theme, skin);

  return Object.assign(
    chip,
    list,
    menu,
    tabs,
    cards,
    input,
    alerts,
    button,
    dialog,
    rating,
    tables,
    avatars,
    divider,
    MuiLink,
    popover,
    tooltip,
    backdrop,
    dataGrid,
    MuiPaper,
    snackbar,
    switches,
    timeline,
    accordion,
    MuiSelect,
    pagination,
    autocomplete,
    MuiTypography,
    dateTimePicker,
    MuiToggleButton,
  );
};

export default Overrides;
