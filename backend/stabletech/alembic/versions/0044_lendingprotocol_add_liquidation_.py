"""LendingProtocol - Add liquidation_thresholds field

Revision ID: 0044
Revises: 0043
Create Date: 2023-12-15 01:14:06.318412

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0044"
down_revision = "0043"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "lendingprotocol",
        sa.Column(
            "liquidation_thresholds", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("lendingprotocol", "liquidation_thresholds")
    # ### end Alembic commands ###
