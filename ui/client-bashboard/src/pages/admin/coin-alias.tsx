import Spinner from "@/@core/components/spinner";
import { ConfirmationDialog, useConfirmationDialog } from "@/components/ConfirmationDialog";
import { DataGridTable, TableColumns } from "@/components/DataGridTable";
import { CoinSearch } from "@/components/autocomplete/CoinSearch";
import { useDebounce } from "@/hooks/debounce";
import {
  useMarketAliasCoinAddMutation,
  useMarketAliasCoinDeleteMutation,
  useMarketAliasCoinUpdateMutation,
  useMarketAliasCoinsQuery,
  useMarketCoinsWithPricesQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { coinFmt } from "@/types/market";
import { AliasCoin, Coin } from "@/types/schemas";
import { Delete, Remove } from "@mui/icons-material";
import { LoadingButton } from "@mui/lab";
import {
  <PERSON><PERSON>,
  <PERSON>,
  But<PERSON>,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Fade,
  Grid,
  IconButton,
  List,
  ListItemButton,
  ListItemSecondaryAction,
  ListItemText,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { GridCellParams } from "@mui/x-data-grid-pro";
import { useState } from "react";

const CoinAlias = () => {
  const tableColumns: TableColumns[] = [
    {
      field: "coin.name",
      header: "Alias Coin",
      renderCell: (params: GridCellParams) => coinFmt(params.row.coin),
    },
    {
      field: "aliasedCoins",
      header: "Alias",
      renderCell: (params: GridCellParams) =>
        params.value.map((coin: Coin) => (
          <Box key={coin.name} pl={2}>
            <Chip label={coin.name} />
          </Box>
        )),
    },
  ];
  const { data } = useMarketAliasCoinsQuery();
  const [add, setAdd] = useState(false);
  const [edit, setEdit] = useState<AliasCoin | null>(null);
  if (!data) return <Spinner />;
  return (
    <Grid container spacing={6}>
      <Grid item xs>
        <Card>
          <CardContent>
            <Grid container spacing={6}>
              <Grid item xs={4}>
                <Typography variant={"h4"}> Coin Alias </Typography>
              </Grid>
              <Grid item xs={8} display={"Grid"} justifyContent={"end"}>
                <Stack direction={"row"} justifyContent={"end"} spacing={4}>
                  <Button onClick={() => setAdd(!add)}>+ Add Alias Coin</Button>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <DataGridTable
                  hideIdColumn
                  rows={data}
                  sortByField={"coin.name"}
                  tableColumns={tableColumns}
                  onRowClick={(params: GridCellParams<unknown, AliasCoin>) =>
                    setEdit(params.row)
                  }
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
      {add && <AddAliasCoin isOpen={true} onClose={() => setAdd(false)} />}
      {edit && <EditAliasCoin isOpen={true} onClose={() => setEdit(null)} aliasCoin={edit} />}
    </Grid>
  );
};

CoinAlias.acl = {
  action: "manage",
  subject: "Coin Alias",
};

export default CoinAlias;

interface AddAliasCoinProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AddAliasCoin = ({ isOpen, onClose }: AddAliasCoinProps) => {
  const [errorAlert, setErrorAlert] = useState("");
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [aliasCoin, setAliasCoin] = useState<Coin | null>(null);
  const [addAliasCoin, mutationResult] = useMarketAliasCoinAddMutation();
  const { data } = useMarketCoinsWithPricesQuery({ excludeAliasCoins: true });
  return (
    <Dialog fullWidth maxWidth="sm" open={isOpen} onClose={onClose}>
      <DialogTitle>Add Alias Coin</DialogTitle>
      <DialogContent>
        <CoinSearch
          selectedCoin={aliasCoin}
          setSelectedCoin={setAliasCoin}
          coinsToSearch={data}
        />
        {showErrorAlert && <Alert severity={"error"}>{errorAlert}</Alert>}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <LoadingButton
          color="error"
          loading={mutationResult.isLoading}
          onClick={() => {
            if (aliasCoin) {
              addAliasCoin(aliasCoin.id)
                .unwrap()
                .then(onClose)
                .catch((error: unknown) => {
                  setErrorAlert(getErrorDetail(error, { fallback: "Unknown failure to add" }));
                  setShowErrorAlert(true);
                });
            }
          }}
        >
          Add
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

interface EditAliasCoinProps {
  isOpen: boolean;
  onClose: () => void;
  aliasCoin: AliasCoin;
}

export const EditAliasCoin = ({ isOpen, onClose, aliasCoin }: EditAliasCoinProps) => {
  const [aliases, setAliases] = useState<Coin[]>(aliasCoin.aliasedCoins || []);
  const [errorAlert, setErrorAlert] = useState("");
  const [search, setSearch] = useState("");

  const debouncedSearch = useDebounce(200, search.trim());

  const excludeCoinsIds =
    aliasCoin !== null ? [aliasCoin.coin.id, ...aliases.map((c) => c.id)] : [];

  const { data, isFetching } = useMarketCoinsWithPricesQuery({
    excludeAliasCoins: true,
    excludeCoinsIds,
    includeWithoutPrices: true,
    search: debouncedSearch,
  });
  const [updateAliasCoin, updateStatus] = useMarketAliasCoinUpdateMutation();
  const [deleteAliasCoin, deleteStatus] = useMarketAliasCoinDeleteMutation();

  const isLoading = updateStatus.isLoading || deleteStatus.isLoading;

  const [confirmProps, confirm] = useConfirmationDialog();

  const addAlias = (coin: Coin | null) => {
    if (errorAlert !== "") setErrorAlert("");
    if (!coin) return;
    if (aliases.includes(coin)) {
      setErrorAlert("Coin already added");
      return;
    }
    setAliases([...aliases, coin]);
  };
  const removeAlias = (coin: Coin) => {
    if (errorAlert !== "") setErrorAlert("");
    if (!coin) return;
    if (!aliases.includes(coin)) {
      setErrorAlert("Coin not found");
      return;
    }
    setAliases(aliases.filter((c) => c !== coin));
  };
  const clearOnClose = () => {
    setAliases([]);
    onClose();
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={isOpen} onClose={clearOnClose}>
      <DialogTitle>Edit Alias Coin</DialogTitle>
      <DialogContent>
        <Stack spacing={6}>
          <DialogContentText>
            <Typography component={"span"}>{coinFmt(aliasCoin.coin)}</Typography>
          </DialogContentText>
          <TextField label={"Alias Coin"} disabled value={coinFmt(aliasCoin.coin)} />
          <Box position="relative">
            <CoinSearch
              selectedCoin={null}
              setSelectedCoin={addAlias}
              coinsToSearch={data}
              onSearchChange={(search) => {
                setSearch(search);
              }}
            />
            <Fade in={isFetching}>
              <Box position="absolute" top={12} right={38} sx={{ pointerEvents: "none" }}>
                <CircularProgress size={30} color="secondary" />
              </Box>
            </Fade>
          </Box>
          {errorAlert && <Alert severity={"error"}>{errorAlert}</Alert>}
          <DialogContentText>Coin Aliases</DialogContentText>
          <Divider />
          {aliases.length === 0 && <Typography>No aliases</Typography>}
          <List sx={{ maxHeight: 300, overflow: "auto", position: "relative" }}>
            {aliases.map((coin) => (
              <ListItemButton key={coin.id}>
                <ListItemText primary={coinFmt(coin)} />
                <ListItemSecondaryAction>
                  <IconButton edge="end" onClick={() => removeAlias(coin)}>
                    <Remove />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItemButton>
            ))}
          </List>
        </Stack>
      </DialogContent>
      <DialogActions>
        <IconButton
          aria-label="Delete alias coin"
          title="Delete alias coin"
          disabled={isLoading}
          sx={{ mr: "auto" }}
          onClick={() => {
            confirm({
              title: "Delete Alias Coin",
              message: "Delete this Alias Coin and all the Coin Aliases that have been set?",
              action: () =>
                deleteAliasCoin(aliasCoin.id)
                  .unwrap()
                  .then(clearOnClose)
                  .catch((error: unknown) => {
                    setErrorAlert(
                      getErrorDetail(error, { fallback: "Unknown failure on delete" }),
                    );
                  }),
            });
          }}
        >
          <Delete color="error" />
        </IconButton>
        <Button
          color="secondary"
          disabled={isLoading}
          onClick={clearOnClose}
          variant="outlined"
        >
          Cancel
        </Button>
        <Button
          disabled={isLoading}
          variant="contained"
          onClick={() => {
            updateAliasCoin({
              aliasCoinId: aliasCoin.id,
              aliasedCoinsIds: aliases.map((c) => c.id),
            })
              .unwrap()
              .then(clearOnClose)
              .catch((error: unknown) => {
                setErrorAlert(getErrorDetail(error, { fallback: "Unknown failure to update" }));
              });
          }}
        >
          Save
        </Button>
      </DialogActions>
      <ConfirmationDialog {...confirmProps} />
    </Dialog>
  );
};
