from datetime import datetime
from decimal import Decimal
from typing import TYPE_CHECKING, Annotated, Generic, Literal, TypeVar

from pydantic import (
    BeforeV<PERSON>da<PERSON>,
    EmailStr,
    Field,
    StringConstraints,
    computed_field,
    field_validator,
)

from stabletech.indexer.enums import <PERSON><PERSON><PERSON>, JobStatus, TransactionAction, WalletSyncProvider
from stabletech.market.calculate_cost_basis import calculate_swap_cost_basis
from stabletech.utils.pydantic import BaseSchema

if TYPE_CHECKING:
    import stabletech.client.models
    import stabletech.indexer.models

NonEmptyString = Annotated[str, StringConstraints(strip_whitespace=True, min_length=1)]

# ------------------------------------------------------------------------------
# User
# ------------------------------------------------------------------------------


class User(BaseSchema):
    id: int
    agreement_accepted_at: datetime | None
    auth_token: str | None = None
    earns_interest: bool
    email: EmailStr
    first_name: str | None
    interest_rate: Decimal
    last_name: str | None
    picture_url: str | None
    role: str
    usd_invested: Decimal  # only relevant for LP and Diwan


class LoginPassword(BaseSchema):
    email: EmailStr
    password: str


# ------------------------------------------------------------------------------
# Indexer
# ------------------------------------------------------------------------------


class UpdateCoin(BaseSchema):
    id: int
    has_prices: bool | None = None
    is_usable_by_clients: bool | None = None
    name: NonEmptyString | None = None
    network_ids: list[int] | None = None
    ticker: NonEmptyString | None = None
    uid: NonEmptyString | None = None


class AddCoin(BaseSchema):
    name: str
    ticker: str
    uid: str


class Coin(AddCoin):
    id: int
    is_usable_by_clients: bool
    latest_usd_price: Decimal
    latest_usd_price_updated_at: datetime | None
    usd_24h_change: float | None


class AliasCoin(BaseSchema):
    id: int
    coin: Coin
    aliased_coins: list[Coin]


class AddAliasCoin(BaseSchema):
    coin_id: int


class UpdateAliasCoin(BaseSchema):
    aliased_coins_ids: list[int]


class AddNetwork(BaseSchema):
    explorer_url: str | None = None
    name: str
    # Required for now as Networks without native Coins should be created programmatically
    native_coin_id: int


class Network(BaseSchema):
    id: int
    explorer_url: str | None = None
    name: str
    is_usable_by_clients: bool


class NetworkWithNativeCoin(Network):
    native_coin: Coin | None


class CoinWithNetworks(Coin):
    networks: list[Network]
    has_prices: Annotated[bool, BeforeValidator(lambda x: False if x is None else x)]


class CoinWithAssetAddress(Coin):
    asset_address: str
    network_id: int


class AddExchange(BaseSchema):
    name: str
    # TODO: fix pydantic HttpUrl bug when saving the schema to the DB and use that for all `url` types
    url: str


class Exchange(AddExchange):
    id: int


class ExchangeWithAccountAddress(Exchange):
    account_address: str | None
    network: Network | None


class AddFarm(BaseSchema):
    name: str
    url: str


class Farm(AddFarm):
    id: int


class FarmWithAccountAddress(Farm):
    account_address: str | None
    network: Network | None


class Liability(BaseSchema):
    coin_latest_price_usd: Decimal
    coin_name: str
    current_balance: Decimal
    total_debt: Decimal


class UserLiability(BaseSchema):
    due_at: datetime
    liabilities: list[Liability]
    total_usd_debt: Decimal
    user: User


class BaseWallet(BaseSchema):
    address: Annotated[str, StringConstraints(strip_whitespace=True)]
    is_usable_by_clients: bool | None = None
    name: str


class Wallet(BaseWallet):
    id: int
    networks: list[Network]


class Bridge(BaseSchema):
    id: int | None = None
    name: str
    url: str


class LendingProtocolLiquidationThreshold(BaseSchema):
    coin_uid: Annotated[str, StringConstraints(strip_whitespace=True, min_length=1)]
    liquidation_threshold: Decimal


class AddLendingProtocol(BaseSchema):
    name: str
    url: str


class LendingProtocol(AddLendingProtocol):
    id: int


class LendingProtocolWithAccountAddress(LendingProtocol):
    account_address: str | None
    network: Network | None


class BasePool(BaseSchema):
    name: str
    url: str


class Pool(BasePool):
    id: int
    exchange: Exchange | None = None
    farm: Farm
    is_closed: bool
    network: Network
    tvl: Decimal | None = None


class PoolPriceAlertRange(BaseSchema):
    min_price_usd: Decimal
    """Alert will trigger if price is equal or below this value"""

    max_price_usd: Decimal
    """Alert will trigger if price is equal or above this value"""


class PoolPriceAlert(PoolPriceAlertRange):
    account_id: int
    coin_id: int


class Account(BaseSchema):
    id: int
    address: str
    bridge: Bridge | None = None
    exchange: Exchange | None = None
    farm: Farm | None = None
    alias_coin: Coin | None = Field(serialization_alias="coin")
    is_pool_token: bool
    is_wallet: bool
    lending_protocol: LendingProtocol | None = None
    name: str | None = None
    ticker_symbol: str | None = None


class AddAccount(BaseSchema):
    id: int | None = None
    address: str
    coin: Coin | None = None
    coin_id: int | None = None
    is_pool_token: bool = False
    is_wallet: bool | None = None


class AddTransfer(BaseSchema):
    amount: Decimal
    asset_account: AddAccount
    from_account: AddAccount
    to_account: AddAccount


class BaseAddTransaction(BaseSchema):
    action: TransactionAction
    bridge_id: int | None = None
    exchange_id: int | None = None
    farm_id: int | None = None
    is_collateral: bool
    is_for_pooled_fund: bool = False  # Creates a linked ClientTransaction with no User
    is_deposit: bool | None = None
    lending_protocol_id: int | None = None
    user_id: int | None = None


class AddTransaction(BaseAddTransaction):
    comment: str | None = None
    confirmed_at: datetime
    fees_paid: Decimal
    network_id: int
    transfers: list[AddTransfer]
    tx_hash: str
    wallet_id: int


class ReplaceTransaction(BaseSchema):
    add_transaction: AddTransaction
    client_transaction_id: int | None
    transaction_id: int


class Transfer(BaseSchema):
    id: int
    amount: Decimal
    is_withdrawal: bool
    is_deposit: bool

    asset_account: Account
    from_account: Account
    to_account: Account

    asset_price_usd: Decimal | None


class BaseTransaction(BaseSchema):
    id: int
    confirmed_at: datetime
    has_approval: bool
    network: Network
    signer_account: Account
    transfers: list[Transfer]
    tx_hash: str
    wallet: Wallet


class Transaction(BaseTransaction):
    action: TransactionAction
    client_transaction_id: int | None = None
    comment: str
    fees_paid: Decimal | None
    internal_id: str | None = None
    is_collateral: bool
    is_deposit: bool | None


class LoanTransaction(Transaction):
    pass


class TransactionForClient(Transaction):
    cost_basis_usd: Decimal | None = None
    # Only set on non LP envs
    is_interest_payment: bool = False

    @computed_field
    @property
    def client_action(self) -> Literal["Deposit", "Interest Payment", "Swap", "Withdrawal"]:
        if self.action == TransactionAction.loan:
            if self.is_deposit:
                return "Deposit"
            else:
                return "Withdrawal"
        elif self.action == TransactionAction.spend:
            return "Interest Payment"
        elif self.action == TransactionAction.swap:
            return "Swap"
        raise ValueError(f"Unknown TransactionAction: {self.action}")

    @staticmethod
    def from_client_transaction_model(
        obj: "stabletech.client.models.ClientTransaction",
    ) -> "TransactionForClient":
        instance = TransactionForClient.model_validate(obj.transaction)
        instance.cost_basis_usd = obj.cost_basis_usd
        instance.is_interest_payment = obj.is_interest_payment or False
        return instance

    @staticmethod
    def from_transaction_model(
        obj: "stabletech.indexer.models.Transaction",
    ) -> "TransactionForClient":
        instance = TransactionForClient.model_validate(obj)
        if obj.action == TransactionAction.swap:
            instance.cost_basis_usd = calculate_swap_cost_basis(obj)
        else:
            instance.cost_basis_usd = None
        return instance


class AdminClientTransaction(BaseSchema):
    id: int
    amount: Decimal | None
    coin: Coin
    coin_id: int
    created_at: datetime
    is_deposit: bool | None
    network: Network | None
    reviewed_at: datetime | None = None
    transaction: TransactionForClient | None = None
    user_id: int


class TransactionUnclassified(BaseTransaction):
    comment: str
    fees_paid: Decimal | None
    id: int
    possible_actions: list[TransactionAction]


class TransactionClassify(BaseAddTransaction):
    id: int
    is_revenue_share: bool = False


class PoolCoinTotal(BaseSchema):
    coin: Coin
    dollar_price: Decimal
    amount_added: Decimal
    amount_current: Decimal
    amount_total: Decimal

    @staticmethod
    def with_defaults(
        coin: Coin,
        dollar_price: Decimal,
        amount_added=Decimal(0),
        amount_current=Decimal(0),
        amount_total=Decimal(0),
    ) -> "PoolCoinTotal":
        return PoolCoinTotal(
            coin=coin,
            dollar_price=dollar_price,
            amount_added=amount_added,
            amount_current=amount_current,
            amount_total=amount_total,
        )


class AddWallet(BaseWallet):
    network_ids: list[int]


class EditWallet(BaseSchema):
    id: int
    is_usable_by_clients: bool | None = None
    name: str
    network_ids: list[int]


class AccountPool(BaseSchema):
    id: int
    name: str
    exchange: Exchange | None = None
    impermanent_loss_percent: Decimal | None = None
    network: Network
    state: Literal["has-funds", "no-balance", "missing-transactions"]
    tvl: Decimal


class AccountPoolFull(AccountPool):
    coin_totals: list[PoolCoinTotal]
    k_ratio: Decimal
    tickers: list[str]
    transactions: list[Transaction]


class CoinTotal(BaseSchema):
    coin: Coin
    amount: Decimal


class LoanCoinTotal(BaseSchema):
    coin: Coin
    borrow_amount: Decimal
    collateral_amount: Decimal
    loss_amount: Decimal
    profit_amount: Decimal
    liquidation_threshold: Decimal


class AccountLoan(BaseSchema):
    id: int
    name: str
    network: Network
    total_collateral: Decimal
    total_borrowed: Decimal
    total_liquidation_threshold: Decimal


class AccountLoanFull(AccountLoan):
    coin_totals: list[LoanCoinTotal]
    transactions: list[LoanTransaction]


class ImportFailedRow(BaseSchema):
    error: Literal["already-in-db", "in-file-duplicate", "unmatched-buy", "unmatched-sell"]
    index: int
    row: list[str]


class ImportResult(BaseSchema):
    ok: bool
    imported_rows_count: int
    header: list[str]
    failed_rows: list[ImportFailedRow]


class ImportJobParams(BaseSchema):
    merge_in_file_duplicates: bool
    skip_already_in_db: bool
    user_id: int


class WalletSyncJobParams(BaseSchema):
    wallet_id: int
    initial: bool = False


class WalletSyncResult(BaseSchema):
    """Result of a wallet sync."""

    network_name: str
    provider: WalletSyncProvider
    provider_network: str
    processed_transactions: int
    errors: list[str]


class WalletSyncJobResult(BaseSchema):
    results: list[tuple[int, list[WalletSyncResult]]]


class JobResponse(BaseSchema):
    id: int
    kind: JobKind
    status: JobStatus
    created_at: datetime
    completed_at: datetime | None
    data: ImportJobParams | WalletSyncJobParams
    error: str | None
    result: ImportResult | WalletSyncJobResult | None


T = TypeVar("T")


class PaginatedResult(BaseSchema, Generic[T]):
    next: bool
    prev: bool
    items: list[T]


PaginatedResult[Transaction]  # keep to generate typescript type
PaginatedResult[TransactionUnclassified]


class TransactionsOverview(BaseSchema):
    coin_totals: list[CoinTotal]
    transactions: list[Transaction]


class Stake(BaseSchema):
    asset: Account
    amount: Decimal
    farm: Farm
    network: Network


class StakeDetails(Stake):
    transactions: list[Transaction]


# ------------------------------------------------------------------------------
# Client
# ------------------------------------------------------------------------------


class AddClientTransaction(BaseSchema):
    amount: Decimal
    coin_id: int
    is_deposit: bool
    network_id: int


class ClientTransaction(BaseSchema):
    amount: Decimal
    coin: Coin
    created_at: datetime
    id: int
    is_deposit: bool | None = None
    reviewed_at: datetime | None = None

    @staticmethod
    def from_model(obj: "stabletech.client.models.ClientTransaction"):
        instance = ClientTransaction.model_validate(obj)
        instance.is_deposit = get_is_deposit(instance)
        return instance


def get_is_deposit(client_transaction: ClientTransaction) -> bool | None:
    """
    Compute is_deposit from linked transaction's client_action if is_deposit is None.
    Falls back to the original is_deposit value if no transaction is linked.
    """
    if client_transaction.is_deposit is not None:
        return client_transaction.is_deposit

    # Try to get from linked transaction if available
    # The transaction should be loaded via eager loading
    transaction = getattr(client_transaction, 'transaction', None)
    if transaction:
        try:
            client_action = TransactionForClient._get_client_action_from_transaction(transaction)
            if client_action == "Deposit":
                return True
            elif client_action in ["Withdrawal", "Interest Payment", "Swap"]:
                return False
        except (ValueError, AttributeError):
            pass

    return client_transaction.is_deposit


def _get_client_action_from_transaction(self, transaction) -> str:
    """Helper method to get client_action from transaction using the same logic as TransactionForClient."""
    from stabletech.indexer.enums import TransactionAction

    if transaction.action == TransactionAction.loan:
        if transaction.is_deposit:
            return "Deposit"
        else:
            return "Withdrawal"
    elif transaction.action == TransactionAction.spend:
        return "Interest Payment"
    elif transaction.action == TransactionAction.swap:
        return "Swap"
    raise ValueError(f"Unknown TransactionAction: {transaction.action}")


class ClientTransactionWithUser(ClientTransaction):
    user: User | None


class Dashboard(BaseSchema):
    coins: list[Coin]
    transactions: list[TransactionForClient]


class SahabaDashboard(BaseSchema):
    coins: list[Coin]
    client_transactions: list[ClientTransaction]


class CapitalPosition(BaseSchema):
    amount: Decimal
    bought_at: datetime
    bought_usd_amount: Decimal
    coin: Coin
    coin_id: int
    id: int
    sold_at: datetime
    sold_usd_amount: Decimal

    @computed_field
    @property
    def pnl(self) -> Decimal:
        return self.sold_usd_amount - self.bought_usd_amount


class CapitalPositionSummary(BaseSchema):
    coin: Coin
    cost: Decimal
    latest_capital_positions: list[CapitalPosition] | None = None
    pnl: Decimal
    proceeds: Decimal
    volume: Decimal


class CapitalPositionSummaryList(BaseSchema):
    latest_capital_positions: list[CapitalPosition]
    summary: list[CapitalPositionSummary]
    total_cost: Decimal
    total_pnl: Decimal
    total_proceeds: Decimal
    total_volume: Decimal


class AccountedTransaction(BaseSchema):
    id: int
    tx_hash: str
    transfer_id: int
    action: str
    confirmed_at: datetime
    transaction_action: str
    amount: Decimal
    remaining_amount: Decimal
    coin: Coin | None
    asset_price_usd: Decimal | None
    ticker_symbol: str
    matched_capital_positions_count: int | None = None
    flattened_transactions_count: int | None = None
    loan_withdrawals_consumed_count: int | None = None

    @staticmethod
    def with_counts(
        accounted_transaction: "stabletech.accounting.models.AccountedTransaction",  # type: ignore
    ) -> "AccountedTransaction":
        instance = AccountedTransaction.model_validate(accounted_transaction)
        instance.matched_capital_positions_count = len(accounted_transaction.capital_positions)
        instance.flattened_transactions_count = len(
            accounted_transaction.flattened_transactions
        )
        instance.loan_withdrawals_consumed_count = len(accounted_transaction.loan_withdrawals)
        return instance


class OpenPositionSummary(BaseSchema):
    coin: Coin
    open_confirmed_at: datetime
    amount: Decimal
    avg_cost: Decimal
    latest_accounted_transactions: list[AccountedTransaction] | None = None

    @computed_field
    @property
    def unrealized_pnl(self) -> Decimal:
        if self.value and self.cost:
            return self.value - self.cost
        return Decimal("0")

    @computed_field
    @property
    def cost(self) -> Decimal:
        if self.amount and self.avg_cost:
            return self.amount * self.avg_cost
        return Decimal("0")

    @computed_field
    @property
    def value(self) -> Decimal:
        if self.coin and self.coin.latest_usd_price and self.amount:
            return self.amount * self.coin.latest_usd_price
        return Decimal("0")

    @field_validator("avg_cost")
    @classmethod
    def avg_cost_must_be_a_decimal(cls, avg_cost: Decimal | None) -> Decimal:
        return avg_cost or Decimal("0")


class OpenPositionSummaryList(BaseSchema):
    summaries: list[OpenPositionSummary]
    latest_accounted_transactions: list[AccountedTransaction]

    @computed_field
    @property
    def total_unrealized_pnl(self) -> Decimal:
        return sum([summary.unrealized_pnl for summary in self.summaries], Decimal("0"))

    @computed_field
    @property
    def total_cost(self) -> Decimal:
        return sum([summary.cost for summary in self.summaries], Decimal("0"))

    @computed_field
    @property
    def total_value(self) -> Decimal:
        return sum([summary.value for summary in self.summaries], Decimal("0"))


class AssetHolding(BaseSchema):
    coin_id: int
    coin_name: str
    coin_ticker: str
    amount: str
    value_usd: str
    price_usd: str
    change_24h: float | None


class AssetHoldingsResponse(BaseSchema):
    total_value_usd: str
    total_24h_change: float | None
    assets: list[AssetHolding]
