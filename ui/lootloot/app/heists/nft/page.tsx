"use client";

import { ApiError } from "@/lib/api";
import { AlertError } from "@/lib/components/alert";
import { dateFormatter, numberFormatter } from "@/lib/formatters";
import { HEIST_EVENTS, useGetNftClaimsHistoryQuery } from "@/lib/heist";
import { shortenSolanaAddress } from "@/lib/utils/string";
import { ArrowDownIcon, ArrowUpIcon, CheckIcon, CopyIcon } from "lucide-react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { Suspense, useCallback, useMemo, useState } from "react";

type SortableColumn =
  | "total_amount_claimed"
  | "duration_hours"
  | "total_amount_emitted"
  | "base_multiplier"
  | "ended_at";
type SortDirection = "asc" | "desc";

interface SortConfig {
  by: SortableColumn;
  order: SortDirection;
}

const defaultSort: SortConfig = {
  by: "ended_at",
  order: "desc",
};

function NftHistory() {
  const searchParams = useSearchParams();
  const nftId = searchParams.get("id");
  const [sort, setSort] = useState<SortConfig>(defaultSort);
  const [copied, setCopied] = useState(false);
  const { data, error, isLoading } = useGetNftClaimsHistoryQuery(nftId ?? "");

  const handleSort = useCallback((column: SortableColumn) => {
    setSort((prev) => ({
      by: column,
      order: prev.by === column && prev.order === "desc" ? "asc" : "desc",
    }));
  }, []);

  const SortIcon = useCallback(
    ({ column }: { column: SortableColumn }) => {
      if (sort.by !== column) return null;
      return sort.order === "asc" ? (
        <ArrowUpIcon className="inline h-4 w-4" />
      ) : (
        <ArrowDownIcon className="inline h-4 w-4" />
      );
    },
    [sort],
  );

  const SortableHeader = useCallback(
    ({ column, children }: { column: SortableColumn; children: React.ReactNode }) => {
      const handleClick = () => {
        handleSort(column);
      };
      return (
        <th
          className="cursor-pointer px-2 py-3 text-center hover:bg-muted/75 sm:px-6"
          onClick={handleClick}
        >
          <div className="flex flex-col items-center">
            <div>{children}</div>
            <SortIcon column={column} />
          </div>
        </th>
      );
    },
    [handleSort, SortIcon],
  );

  const sortedClaims = useMemo(() => {
    if (!data?.claims) return [];

    return [...data.claims].sort((a, b) => {
      let comparison = 0;
      switch (sort.by) {
        case "total_amount_claimed":
          comparison = Number(a.total_amount_claimed) - Number(b.total_amount_claimed);
          break;
        case "duration_hours":
          comparison = a.duration_hours - b.duration_hours;
          break;
        case "total_amount_emitted":
          comparison = Number(a.total_amount_emitted) - Number(b.total_amount_emitted);
          break;
        case "base_multiplier":
          comparison = a.base_multiplier - b.base_multiplier;
          break;
        case "ended_at":
          comparison = new Date(a.ended_at).getTime() - new Date(b.ended_at).getTime();
          break;
        default:
          comparison = 0;
      }
      return sort.order === "asc" ? comparison : -comparison;
    });
  }, [data?.claims, sort]);

  if (!nftId) {
    return <AlertError>NFT ID is required</AlertError>;
  }

  if (error) {
    return (
      <main className="grid gap-6 px-2 py-8 sm:justify-center">
        <AlertError>
          {error instanceof ApiError && error.response.status === 400
            ? "You do not own this NFT"
            : error.message}
        </AlertError>
      </main>
    );
  }

  const handleCopy = () => {
    void navigator.clipboard.writeText(nftId).then(() => {
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    });
  };

  return (
    <main className="grid gap-6 px-2 py-8 sm:justify-center">
      <div className="text-center">
        <h1 className="mb-4 text-2xl font-semibold tracking-tight">Recent Claims</h1>
      </div>

      {isLoading ? (
        <div className="animate-pulse space-y-4">
          <div className="mx-auto h-48 w-48 rounded-sm bg-muted" />
          <div className="h-8 w-48 rounded-sm bg-muted" />
        </div>
      ) : data ? (
        <div className="space-y-6">
          <div className="flex flex-col items-center gap-4">
            <div className="relative h-48 w-48">
              <Image
                src={data.nft_image_url}
                alt=""
                fill
                className="rounded-sm object-cover"
                unoptimized
              />
            </div>
            <div className="flex flex-col items-center gap-1">
              <h2 className="text-xl font-semibold">{data.nft_name}</h2>
              <button
                onClick={handleCopy}
                className="group flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground"
              >
                {shortenSolanaAddress(nftId)}
                <span className="inline-flex text-muted-foreground">
                  {copied ? (
                    <CheckIcon className="h-4 w-4" />
                  ) : (
                    <CopyIcon className="h-4 w-4 opacity-0 transition-opacity group-hover:opacity-100" />
                  )}
                </span>
              </button>
            </div>
          </div>

          <div className="relative -mx-4 overflow-x-auto rounded-lg border border-border bg-card shadow-xs sm:mx-0">
            <table className="w-full text-left text-sm">
              <thead className="bg-muted/50 text-muted-foreground">
                <tr>
                  <th className="px-2 py-3 text-center sm:px-6">Event</th>
                  <SortableHeader column="total_amount_claimed">
                    <span className="block">NANA</span>
                    <span className="block">Claimed</span>
                  </SortableHeader>
                  <SortableHeader column="duration_hours">
                    <span className="block">Duration</span>
                    <span className="block">(hours)</span>
                  </SortableHeader>
                  <SortableHeader column="total_amount_emitted">
                    <span className="block">Base</span>
                    <span className="block">NANA</span>
                  </SortableHeader>
                  <SortableHeader column="base_multiplier">
                    <span className="block">Multiplier</span>
                    <span className="block">to Base</span>
                  </SortableHeader>
                  <SortableHeader column="ended_at">At</SortableHeader>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {sortedClaims.map((claim) => (
                  <tr key={claim.ended_at} className="bg-card hover:bg-muted/50">
                    <td
                      className={`px-2 py-4 text-center sm:px-6 ${HEIST_EVENTS[claim.event_id]?.isBad ? "text-red-500" : "text-green-500"}`}
                    >
                      {HEIST_EVENTS[claim.event_id]?.name ?? "Unknown"}
                    </td>
                    <td className="px-2 py-4 text-center sm:px-6">
                      <div>{numberFormatter.format(Number(claim.total_amount_claimed))}</div>
                      <div className="text-xs text-muted-foreground">
                        {numberFormatter.format(
                          Number(claim.total_amount_claimed) / claim.duration_hours,
                        )}{" "}
                        / hr.
                      </div>
                    </td>
                    <td className="px-2 py-4 text-center sm:px-6">
                      {claim.duration_hours.toFixed(2)}
                    </td>
                    <td className="px-2 py-4 text-center sm:px-6">
                      {numberFormatter.format(Number(claim.total_amount_emitted))}
                    </td>
                    <td className="px-2 py-4 text-center sm:px-6">
                      {claim.base_multiplier.toFixed(2)}
                    </td>
                    <td className="px-2 py-4 text-center sm:px-6">
                      <div>{dateFormatter.format(new Date(claim.ended_at))}</div>
                      <div className="text-muted-foreground">@ {claim.location_name}</div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : null}
    </main>
  );
}

export default function NftHistoryPage() {
  return (
    <Suspense
      fallback={
        <main className="grid gap-6 px-2 py-8 sm:justify-center">
          <div className="animate-pulse space-y-4">
            <div className="mx-auto h-48 w-48 rounded-sm bg-muted" />
            <div className="h-8 w-48 rounded-sm bg-muted" />
          </div>
        </main>
      }
    >
      <NftHistory />
    </Suspense>
  );
}
