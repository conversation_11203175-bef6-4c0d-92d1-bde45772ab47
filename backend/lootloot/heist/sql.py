from decimal import Decimal

from sqlalchemy import BindParameter, ColumnElement, and_, func, literal

from lootloot.external.heist import HeistEvent
from lootloot.models import HeistClaim

ZERO = literal(0, literal_execute=True)
DAY = literal("day", literal_execute=True)


claimed_gt_zero = HeistClaim.total_amount_claimed > ZERO
claimed_eq_zero = HeistClaim.total_amount_claimed == ZERO
is_rekt = HeistClaim.event_id == literal(HeistEvent.rekt, literal_execute=True)

claimed_sum: ColumnElement[Decimal] = func.coalesce(
    func.sum(HeistClaim.total_amount_claimed).filter(claimed_gt_zero), ZERO
)

fumbled_sum: ColumnElement[Decimal] = func.coalesce(
    func.sum(HeistClaim.total_amount_emitted).filter(claimed_eq_zero), ZERO
)

defined_opposing_wallet = HeistClaim.opposing_wallet_address != None

lost_to_ambush: ColumnElement[Decimal] = func.coalesce(
    -func.sum(HeistClaim.total_amount_claimed).filter(defined_opposing_wallet),
    ZERO,
)

gained_from_ambush_unfiltered: ColumnElement[Decimal] = func.coalesce(
    -func.sum(HeistClaim.total_amount_claimed), ZERO
)
"""UNFILTERED! Use with `gained_from_ambush_condition`."""


def gained_from_ambush_condition(wallet_addresses: BindParameter):
    return and_(
        defined_opposing_wallet,
        HeistClaim.opposing_wallet_address.in_(wallet_addresses),
    )
