"use client";

import { AlertError } from "@/lib/components/alert";
import { <PERSON><PERSON> } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { Label, ValidationError } from "@/lib/components/form";
import { Input } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { useAddHeistRaffle } from "@/lib/heist";
import { superstructResolver } from "@hookform/resolvers/superstruct";
import { Plus, Ticket } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { nonempty, object, string, trimmed } from "superstruct";

/**
 * A button that opens a dialog to add a new Raffle.
 */
export function AddRaffleButton(props: Dialog.DialogTriggerProps) {
  const [open, setOpen] = useState(false);
  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Trigger {...props}>
        <span className="relative mr-3 w-8" aria-hidden>
          <Ticket strokeWidth={1} size={20} />
          <Plus size={10} className="absolute top-0 right-0" color="green" />
        </span>
        Add raffle
      </Dialog.Trigger>
      <Dialog.Content title="Add raffle">
        <AddRaffleForm
          onSuccess={() => {
            setOpen(false);
          }}
        />
      </Dialog.Content>
    </Dialog.Root>
  );
}

const AddRaffleFormStruct = object({
  entriesCount: nonempty(trimmed(string())),
  secretWord: nonempty(trimmed(string())),
  winnersCount: nonempty(trimmed(string())),
});

type AddRaffleFormStruct = typeof AddRaffleFormStruct.TYPE;

function AddRaffleForm({ onSuccess }: { onSuccess: () => void }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AddRaffleFormStruct>({
    resolver: superstructResolver(AddRaffleFormStruct, { coerce: true }),
  });

  const addRaffle = useAddHeistRaffle();

  const onSubmit = (event: React.FormEvent) => {
    void handleSubmit((data) => {
      addRaffle.mutate(
        {
          entries_count: Number(data.entriesCount),
          secret_word: data.secretWord,
          winners_count: Number(data.winnersCount),
        },
        { onSuccess },
      );
    })(event);
  };
  return (
    <form
      onSubmit={onSubmit}
      className="grid grid-cols-[auto_1fr] justify-items-end gap-x-2 gap-y-3"
    >
      {addRaffle.error && (
        <div className="col-span-full w-full">
          {<AlertError>{getErrorMessage(addRaffle.error)}</AlertError>}
        </div>
      )}

      <ValidationError className="col-start-2 -mt-1">
        {errors.secretWord?.message}
      </ValidationError>
      <Label className="mt-3">Secret Word</Label>
      <Input {...register("secretWord")} placeholder="Case insensitive but must be unique" />

      <ValidationError className="col-start-2 -mt-1">
        {errors.entriesCount?.message}
      </ValidationError>
      <Label className="mt-3">Number of Entries</Label>
      <Input
        {...register("entriesCount")}
        placeholder="Number of accepted entrants"
        type="number"
      />

      <ValidationError className="col-start-2 -mt-1">
        {errors.winnersCount?.message}
      </ValidationError>
      <Label className="mt-3">Number of Winners</Label>
      <Input
        {...register("winnersCount")}
        placeholder="Number of winenrs to pick"
        type="number"
      />

      <div className="col-span-full flex gap-4">
        <Button type="submit" disabled={addRaffle.isPending}>
          Add
        </Button>
      </div>
    </form>
  );
}
