import { AddProps, BaseAdd, nameUrlFields } from "@/components/add/common";
import { useIndexerLendingProtocolAddMutation } from "@/store/services/api";
import { useState } from "react";

export const LendingProtocolAdd = (props: AddProps) => {
  const [addLendingProtocol] = useIndexerLendingProtocolAddMutation();
  const [nameValue, setNameValue] = useState("");
  const [urlValue, setUrlValue] = useState("");

  return (
    <BaseAdd
      close={props.close}
      addMutation={addLendingProtocol}
      disableAdd={!nameValue || !urlValue}
      label={"Add a LendingProtocol"}
      onSuccess={props.onSuccess}
      submitData={{ name: nameValue, url: urlValue }}
      fields={nameUrlFields(nameValue, setNameValue, urlValue, setUrlValue)}
    />
  );
};
