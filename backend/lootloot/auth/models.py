import secrets
from datetime import datetime

from sqlalchemy import DateTime, ForeignKey, func
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    MappedAsDataclass,
    mapped_column,
    relationship,
)

from common.models import BaseModel


class Base(MappedAsDataclass, DeclarativeBase, BaseModel, kw_only=True):
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, init=False)
    pass


class User(Base, kw_only=True):
    email: Mapped[str | None] = mapped_column(unique=True, default=None)
    first_name: Mapped[str | None] = mapped_column(default=None)
    last_name: Mapped[str | None] = mapped_column(default=None)
    picture_url: Mapped[str | None] = mapped_column(default=None, repr=False)
    origin: Mapped[str]
    display_name: Mapped[str] = mapped_column(default="")
    joined_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), init=False, server_default=func.now()
    )

    # Matrica specific
    matrica_id: Mapped[str | None] = mapped_column(unique=True, default=None, repr=False)
    matrica_username: Mapped[str | None] = mapped_column(default=None)

    # No AuthToken relationship to avoid leaking the token

    @property
    def is_admin(self):
        if not self.email:
            return False
        return self.email.endswith("@stabletech.capital")


class AuthToken(Base):
    id: Mapped[str] = mapped_column(primary_key=True, repr=False)  # type: ignore
    user_id: Mapped[int] = mapped_column(
        ForeignKey(User.id), unique=True, index=True, init=False, repr=False
    )
    user: Mapped[User] = relationship()

    TOKEN_LENGTH = 128

    @staticmethod
    def create(user: User) -> "AuthToken":
        token = secrets.token_urlsafe(AuthToken.TOKEN_LENGTH)
        return AuthToken(id=token, user=user)
