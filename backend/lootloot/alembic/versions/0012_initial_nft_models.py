"""Initial NFT models

Revision ID: 0012
Revises: 0011
Create Date: 2024-01-30 20:09:46.101132

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0012"
down_revision = "0011"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "nftcollection",
        sa.<PERSON>umn("name", sa.String(), nullable=False),
        sa.Column("uid", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uid"),
    )
    op.create_table(
        "nft",
        sa.Column("address_uri", sa.String(), nullable=False),
        sa.Column("nft_collection_uid", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["nft_collection_uid"],
            ["nftcollection.uid"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("address_uri"),
    )
    op.create_index(
        op.f("ix_nft_nft_collection_uid"), "nft", ["nft_collection_uid"], unique=False
    )
    op.create_table(
        "nftlisting",
        sa.Column("auction_house", sa.String(), nullable=False),
        sa.Column("nft_address_uri", sa.String(), nullable=False),
        sa.Column("sol_price", sa.Numeric(), nullable=False),
        sa.Column(
            "at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["nft_address_uri"],
            ["nft.address_uri"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_nftlisting_nft_address_uri"), "nftlisting", ["nft_address_uri"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_nftlisting_nft_address_uri"), table_name="nftlisting")
    op.drop_table("nftlisting")
    op.drop_index(op.f("ix_nft_nft_collection_uid"), table_name="nft")
    op.drop_table("nft")
    op.drop_table("nftcollection")
    # ### end Alembic commands ###
