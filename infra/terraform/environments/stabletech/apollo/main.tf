resource "kubernetes_namespace" "namespace" {
  metadata {
    name = var.namespace
  }
}

resource "google_dns_managed_zone" "apollo_stabletech_capital" {
  name     = "apollo-stabletech-capital"
  dns_name = "apollo.stabletech.capital."

  dnssec_config {
    state = "on"
  }
}

module "stabletech" {
  source = "../modules/stabletech"

  api_domain        = "api.apollo.stabletech.capital"
  env_name          = "apollo"
  managed_zone_name = google_dns_managed_zone.apollo_stabletech_capital.name
  namespace         = var.namespace
  postgresql_db     = "apollo"
  release           = local.release
  secrets           = local.secrets
}

module "dashboard_ui" {
  source = "../modules/ui"

  dns_managed_zone    = google_dns_managed_zone.apollo_stabletech_capital
  namespace           = var.namespace
  project_id          = local.project_id
  website_domain_name = "apollo.stabletech.capital"
}
