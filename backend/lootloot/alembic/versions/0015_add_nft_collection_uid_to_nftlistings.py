"""Add nft_collection_uid to NFTListings

Revision ID: 0015
Revises: 0014
Create Date: 2024-02-14 02:25:46.286992

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0015"
down_revision = "0014"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("nftlisting", sa.Column("nft_collection_uid", sa.String(), nullable=False))
    op.create_index(
        op.f("ix_nftlisting_nft_collection_uid"),
        "nftlisting",
        ["nft_collection_uid"],
        unique=False,
    )
    op.create_foreign_key(None, "nftlisting", "nftcollection", ["nft_collection_uid"], ["uid"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "nftlisting", type_="foreignkey")
    op.drop_index(op.f("ix_nftlisting_nft_collection_uid"), table_name="nftlisting")
    op.drop_column("nftlisting", "nft_collection_uid")
    # ### end Alembic commands ###
