import secrets
from datetime import datetime
from decimal import Decimal
from enum import StrEnum, auto

from sqlalchemy import DateTime, ForeignKey, Numeric
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship

from common.models import BaseModel


class Base(DeclarativeBase, BaseModel):
    pass


class Role(StrEnum):
    admin = auto()
    client = auto()


class User(Base):
    agreement_accepted_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    earns_interest: Mapped[bool] = mapped_column(default=True, server_default="true")
    email: Mapped[str] = mapped_column(unique=True, index=True)
    first_name: Mapped[str | None]
    google_id: Mapped[str | None]
    is_active: Mapped[bool | None] = mapped_column(default=True, index=True)
    last_name: Mapped[str | None]
    password_hash: Mapped[str | None]
    picture_url: Mapped[str | None]
    role: Mapped[str] = mapped_column(server_default=Role.client.value)

    usd_invested: Mapped[Decimal] = mapped_column(server_default="0")
    """Total invested. Only relevant for LP and Diwan"""

    interest_rate: Mapped[Decimal] = mapped_column(
        Numeric(precision=4, scale=4), default=Decimal("0.07"), server_default="0.07"
    )
    """Interest rate used for calculations, stored as decimal (e.g., 0.07 for 7%)"""

    def __repr__(self) -> str:
        return f"User(email={self.email!r})"

    @property
    def is_admin(self):
        return self.role == Role.admin.value

    def set_admin_role(self):
        self.role = str(Role.admin.value)


class AuthToken(Base):
    """
    Single AuthToken per User for now - logging out will invalidate all the User's sessions.
    """

    token: Mapped[str] = mapped_column(index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey(User.id), index=True, unique=True)
    user: Mapped[User] = relationship()

    TOKEN_LENGTH = 128

    def set_token(self):
        # Set the token to a newly generated random string
        self.token = secrets.token_urlsafe(self.TOKEN_LENGTH)
