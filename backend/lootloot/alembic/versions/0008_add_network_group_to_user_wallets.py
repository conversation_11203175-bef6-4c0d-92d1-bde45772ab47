"""Add network group to user wallets

Revision ID: 0008
Revises: 0007
Create Date: 2024-01-02 18:32:30.470156

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0008"
down_revision = "0007"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "userwallet",
        sa.Column("network_group", sa.Enum("evm", "solana", native_enum=False), nullable=False),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("userwallet", "network_group")
    # ### end Alembic commands ###
