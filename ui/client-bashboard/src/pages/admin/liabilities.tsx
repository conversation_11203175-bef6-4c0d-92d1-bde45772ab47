import { DataGridTable } from "@/components/DataGridTable";
import { formatDate } from "@/helpers/formatters";
import { useIndexerLiabilitiesQuery } from "@/store/services/api";
import { User, UserLiability } from "@/types/schemas";
import { Card, CardContent, CardHeader, Grid } from "@mui/material";
import { useState } from "react";

const getName = (user: User) => {
  return user.firstName ? `${user.firstName} ${user.lastName}` : user.email;
};

const Liabilities = () => {
  const [selectedUserLiability, setSelectedUserLiability] = useState<UserLiability | null>(
    null,
  );
  const { data: liabilities } = useIndexerLiabilitiesQuery();

  return (
    liabilities && (
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Client Loans" />
            <CardContent>
              <DataGridTable
                onRowClick={(gridCell) => {
                  setSelectedUserLiability(gridCell.row);
                }}
                rows={liabilities}
                sortByField={"dueAt"}
                tableColumns={[
                  {
                    field: "user",
                    header: "User",
                    renderCell: ({ row }) => {
                      return getName(row.user);
                    },
                  },
                  {
                    field: "dueAt",
                    header: "Due On",
                    renderCell: ({ row }) => {
                      return formatDate(row.dueAt);
                    },
                  },
                  { field: "totalUsdDebt", header: "USD Debt", isDecimal: true },
                ]}
              />
            </CardContent>
          </Card>
        </Grid>

        {selectedUserLiability && (
          <Grid item xs={12}>
            <Card>
              <CardHeader title={getName(selectedUserLiability.user)} />
              <CardContent>
                <DataGridTable
                  rows={selectedUserLiability.liabilities}
                  tableColumns={[
                    { field: "coinName", header: "Coin" },
                    { field: "coinLatestPriceUsd", header: "Current Price", isDecimal: true },
                    { field: "currentBalance", header: "Current Balance", isDecimal: true },
                    { field: "totalDebt", header: "Current Debt", isDecimal: true },
                  ]}
                />
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    )
  );
};

Liabilities.acl = {
  action: "manage",
  subject: "Position",
};

export default Liabilities;
