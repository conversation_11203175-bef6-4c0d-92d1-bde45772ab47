resource "kubernetes_persistent_volume" "psql_data" {
  metadata {
    name = "${var.namespace}-psql-data"
  }
  spec {
    access_modes = ["ReadWriteMany"]
    capacity = {
      storage = "5Gi"
    }
    persistent_volume_reclaim_policy = "Delete"
    persistent_volume_source {
      host_path {
        path = "/work/temet/.volumes/${var.namespace}-psql-data"
      }
    }
    storage_class_name = "standard"
  }
}

resource "kubernetes_persistent_volume_claim" "psql_data_pvc" {
  metadata {
    name      = "${var.namespace}-psql-data-pvc"
    namespace = var.namespace
  }
  spec {
    access_modes = ["ReadWriteMany"]
    resources {
      requests = {
        storage = "5Gi"
      }
    }
    volume_name = "${var.namespace}-psql-data"
  }
}

resource "kubernetes_persistent_volume" "subql_psql_data" {
  metadata {
    name = "${var.namespace}-subql-psql-data"
  }
  spec {
    access_modes = ["ReadWriteMany"]
    capacity = {
      storage = "5Gi"
    }
    persistent_volume_reclaim_policy = "Delete"
    persistent_volume_source {
      host_path {
        path = "/work/temet/.volumes/${var.namespace}-subql-psql-data"
      }
    }
    storage_class_name = "standard"
  }
}

resource "kubernetes_persistent_volume_claim" "subql_psql_data_pvc" {
  metadata {
    name      = "${var.namespace}-subql-psql-data-pvc"
    namespace = var.namespace
  }
  spec {
    access_modes = ["ReadWriteMany"]
    resources {
      requests = {
        storage = "5Gi"
      }
    }
    volume_name = "${var.namespace}-subql-psql-data"
  }
}
resource "helm_release" "postgresql" {
  count      = var.enable_postgresql ? 1 : 0
  name       = "postgresql"
  depends_on = [kubernetes_persistent_volume_claim.psql_data_pvc]
  namespace  = var.namespace
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "postgresql"
  version    = "16.1.2"
  values     = [yamlencode(local.postgresql_values)]
}

resource "helm_release" "subqlpostgresql" {
  count      = var.enable_subql_postgresql ? 1 : 0
  name       = "subqlpostgresql"
  depends_on = [kubernetes_persistent_volume_claim.subql_psql_data_pvc]
  namespace  = var.namespace
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "postgresql"
  version    = "16.1.2"
  values     = [yamlencode(local.subql_postgresql_values)]
}
