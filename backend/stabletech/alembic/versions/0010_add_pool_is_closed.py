"""Add Pool is_closed

Revision ID: 0010
Revises: 0009
Create Date: 2022-12-20 02:55:23.143321

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0010"
down_revision = "0009"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "pool", sa.<PERSON>umn("is_closed", sa.<PERSON>(), server_default="false", nullable=False)
    )
    op.drop_constraint("pool_farm_id_name_network_id_key", "pool", type_="unique")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        "pool_farm_id_name_network_id_key", "pool", ["farm_id", "name", "network_id"]
    )
    op.drop_column("pool", "is_closed")
    # ### end Alembic commands ###
