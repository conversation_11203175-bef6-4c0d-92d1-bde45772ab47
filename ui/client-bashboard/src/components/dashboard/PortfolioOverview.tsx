import { currencyFormatter, diffCurrencyFormatter } from "@/helpers/formatters";
import { DashboardRow } from "@/helpers/portfolio-calculations";
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  FormControlLabel,
  Switch,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { useCallback, useMemo, useState } from "react";

interface Props {
  readonly rows: readonly DashboardRow[];
  readonly showInterest: boolean;

  readonly selectedRowId?: number;
  readonly onRowClick: (row: DashboardRow) => void;
}

const INITIAL_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "displayName", sort: "asc" }] },
};

export function PortfolioOverview(props: Props) {
  const { showInterest, rows, selectedRowId, onRowClick } = props;
  const [dollarValue, setDollarValue] = useState(getGlobalDollarValue());

  const hasPnlRatio = useMemo(() => rows.some((row) => row.pnlRatio !== null), [rows]);
  const has24hChange = useMemo(() => rows.some((row) => row.usd24hChange !== null), [rows]);
  const columns = useMemo(
    () =>
      computeColumns({
        showInterest,
        showPnlRatio: hasPnlRatio,
        showUsdValues: dollarValue,
        show24hChange: has24hChange,
      }),
    [dollarValue, hasPnlRatio, has24hChange, showInterest],
  );

  const handleRowClick = useCallback(
    ({ row }: { row: DashboardRow }) => {
      onRowClick(row);
    },
    [onRowClick],
  );

  const action = (
    <Box sx={{ position: "relative", width: 150, height: 24 }}>
      <FormControlLabel
        sx={{ position: "absolute", right: 0, height: "100%", marginRight: 0 }}
        label="Dollar Value"
        control={
          <Switch
            size="small"
            checked={dollarValue}
            onChange={() => {
              setDollarValue(!dollarValue);
              setGlobalDollarValue(!dollarValue);
            }}
          />
        }
      />
    </Box>
  );

  return (
    <Card>
      <CardHeader title="Portfolio Overview" action={action} />
      <CardContent>
        <DataGridPro
          autoHeight
          columns={columns}
          rows={rows}
          initialState={INITIAL_STATE}
          selectionModel={selectedRowId}
          onRowClick={handleRowClick}
        />
      </CardContent>
    </Card>
  );
}

function computeColumns({
  showInterest,
  showPnlRatio,
  showUsdValues,
  show24hChange,
}: {
  showInterest: boolean;
  showPnlRatio: boolean;
  showUsdValues: boolean;
  show24hChange: boolean;
}): GridColDef<DashboardRow, string | number>[] {
  const columns: (false | GridColDef<DashboardRow, string | number>)[] = [
    { field: "displayName", headerName: "Coin", flex: 1 },
    {
      field: "amount",
      headerName: "Deposit Amount",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row }) => currencyFormatter.format(row.amount),
    },
    showUsdValues && {
      field: "dollarValue",
      headerName: "$ Deposit",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row }) => `$ ${currencyFormatter.format(row.dollarValue)}`,
    },
    showInterest && {
      field: "interestAmount",
      headerName: "Interest Amount",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row }) => currencyFormatter.format(row.interestAmount),
    },
    showUsdValues &&
      showInterest && {
        field: "interestEarnedDollars",
        headerName: "$ Interest",
        flex: 1,
        align: "right",
        headerAlign: "right",
        renderCell: ({ row }) => `$ ${currencyFormatter.format(row.interestEarnedDollars)}`,
      },
    {
      field: "price",
      headerName: "Market Price",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row }) => `$ ${currencyFormatter.format(row.price)}`,
    },
    showPnlRatio && {
      field: "pnlRatio",
      headerName: "Unrealized Gain",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row: { pnlRatio } }) => {
        if (pnlRatio === null) return null;
        const color = pnlRatio < 0 ? "error.main" : "success.main";
        return (
          <Typography
            color={color}
            fontFamily="inherit"
            fontSize="inherit"
            fontWeight="inherit"
            fontStyle="inherit"
          >
            {diffCurrencyFormatter.format(pnlRatio * 100)}%
          </Typography>
        );
      },
    },
    show24hChange && {
      field: "usd24hChange",
      headerName: "24h Change",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: ({ row: { usd24hChange } }) => {
        if (usd24hChange === null) return "";
        const color = usd24hChange < 0 ? "error.main" : "success.main";
        return (
          <Typography
            color={color}
            fontFamily="inherit"
            fontSize="inherit"
            fontWeight="inherit"
            fontStyle="inherit"
          >
            {diffCurrencyFormatter.format(usd24hChange)} %
          </Typography>
        );
      },
    },
  ];
  return columns.filter(isNotFalse);
}

function isNotFalse<T>(value: T | false): value is T {
  return value !== false;
}

function setGlobalDollarValue(value: boolean) {
  window.localStorage.setItem("dollarValue", value ? "true" : "false");
}

function getGlobalDollarValue() {
  return window.localStorage.getItem("dollarValue") === "true";
}
