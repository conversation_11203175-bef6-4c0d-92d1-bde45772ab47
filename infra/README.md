# Developer Setup

## Minikube

[Install minikube](https://minikube.sigs.k8s.io/docs/start/) for running a local Kubernetes
cluster for development.

_Mac OS_:

```
brew install docker --cask
open /Applications/Docker.app
```

Wait for Docker Desktop to startup then increase the CPU count to 8 and Memory to 40 GB in
Preferences -> Resources then press "Apply & Restart".

Configure minikube with extra resources (depending on hardware) and start a new instance with
the code mounted in the cluster for ease of development.

```
minikube config set memory 32GB
minikube config set cpus 8

# ONE level up from root code folder
minikube start --mount-string="$PWD:/work" --mount

# Delete the cluster and all it's resources
minikube delete
```

## Lens

[Install Lens](https://k8slens.dev/) as a Kubernetes IDE.

## Terraform

### Local

Install the [Terraform CLI](https://learn.hashicorp.com/tutorials/terraform/install-cli) for
managing infrastructure as code.

After installing terraform you can create a new terraform workspace (ignore to use the `default`
workspace)

```commandline
terraform workspace new local
```

There are two separate project local environments to be setup:

1. stabletech
2. lootloot

#### Add Stabletech User

Add a file at `infra/terraform/environments/stabletech/local/terraform.tfvars` with the
following

```
admin_email = "<Gmail email you want to use locally ending in @stabletech.capital>"
```

This will create an admin User to login with using that email and "admin" as password.

#### Apply Terraform

Apply the configuration to the newly created minikube cluster

```commandline
cd infra/terraform/environments/<project>/local
terraform init

# eval command is used to set DOCKER_HOST to minikube's docker engine
eval $(minikube docker-env) && export TF_VAR_DOCKER_HOST=$DOCKER_HOST && terraform apply
```

When writing Terraform configs please be sure to use the
"[terraform fmt](https://www.terraform.io/cli/commands/fmt)" command for linting.

### Cloud

Install jq for parsing certificate data from gcloud:

```
sudo apt install jq
```

### Terraform/GCP account setup

Install gke-gcloud-auth-plugin

```
gcloud components install gke-gcloud-auth-plugin
```

Setup ../key.json file with the gcp credentials by creating a key on the service account
`sharaf-944` and downloading the key.json file from the GCP console. Needs to be at the level
`infra/terraform/environments/` to run the following commands.

On first setup un

```
gcloud auth login                                                               # Login to GCP account pops in the browser
gcloud auth application-default login                                           # Default login to be used by Terraform
gcloud config set project <PROJECT_ID>                                          # Set project id temet-<...>
terraform login                                                                 # Login to Terraform Cloud
gcloud auth configure-docker us-west1-docker.pkg.dev                            # Configure docker to push to GCP container registry
gcloud container clusters get-credentials <PROJECT_ID>-gke --region us-west1-a  # Setup kubectl context on ./kube/config
```

On subsequent runs, releases/hotfixs create the git tag as describe on root `README.md` and run
the following commands after it is created.

```
cd infra/terraform/environments/<APP>/<ENV> # e.g. infra/terraform/environments/stabletech/qa
terraform init                              # Initialize terraform and download plugins
terraform plan                              # See what changes will be applied
terraform apply                             # Apply changes
```

### Copy an Environment's DB

To make a dump you need to:

```
kubectl --context <kubectl_context> -n stabletech-stage run postgres --image=postgres:14 -i --tty --restart=Never --rm --command -- bash
export PGUSER=postgres PGPASSWORD='...' PGHOST=... PGDATABASE=temet
pg_dump apollo | zstd -o dump.zstd
```

Without ending the above session, in another terminal, copy the dump to your machine and apply
it.

```
kubectl --context <kubectl+context> -n stabletech-stage cp postgres:/dump.zstd dump.zstd
zstd -d dump.zstd -o dump.sql
```

From the root of the project

```
cp dump.sql infra/tmp/load.dump.sql
```

By moving the sql into the infra/tmp folder, the infra/docker/python-runner-dev.entrypoint will
load the dump into your local DB.
