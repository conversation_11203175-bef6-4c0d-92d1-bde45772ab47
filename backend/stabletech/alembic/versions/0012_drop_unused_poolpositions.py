"""Drop unused PoolPositions

Revision ID: 0012
Revises: 0011
Create Date: 2022-12-28 08:03:37.925730

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0012"
down_revision = "0011"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_pool_position_first_coin_id", table_name="pool_position")
    op.drop_index("ix_pool_position_is_active", table_name="pool_position")
    op.drop_index("ix_pool_position_second_coin_id", table_name="pool_position")
    op.drop_table("pool_position")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "pool_position",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("is_active", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column("dex_name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("first_coin_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "first_coin_amount_added",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "first_coin_amount_removed",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "k_ratio", sa.NUMERIC(precision=32, scale=18), autoincrement=False, nullable=False
        ),
        sa.Column("second_coin_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "second_coin_amount_added",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "second_coin_amount_removed",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["first_coin_id"], ["coin.id"], name="pool_position_first_coin_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["second_coin_id"], ["coin.id"], name="pool_position_second_coin_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="pool_position_pkey"),
    )
    op.create_index(
        "ix_pool_position_second_coin_id", "pool_position", ["second_coin_id"], unique=False
    )
    op.create_index("ix_pool_position_is_active", "pool_position", ["is_active"], unique=False)
    op.create_index(
        "ix_pool_position_first_coin_id", "pool_position", ["first_coin_id"], unique=False
    )
    # ### end Alembic commands ###
