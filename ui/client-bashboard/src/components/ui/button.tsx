import { cva, cx, type VariantProps } from "cva";
import { forwardRef } from "react";

export const button = cva({
  base: "inline-flex items-center justify-center rounded-sm whitespace-nowrap focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-1 focus-visible:ring-offset-background focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-60 dark:focus-visible:ring-ring/80",
  variants: {
    variant: {
      primary: "bg-primary font-medium text-primary-foreground shadow-xs hover:bg-primary/90",
      secondary:
        "bg-secondary font-medium text-secondary-foreground shadow-xs hover:bg-secondary/70",
      outline:
        "border border-border bg-transparent font-medium shadow-xs hover:bg-accent hover:text-accent-foreground",
      destructive:
        "bg-destructive font-medium text-destructive-foreground shadow-xs hover:bg-destructive/90",
      ghost: "font-medium hover:bg-accent hover:text-accent-foreground",
      link: "text-primary underline-offset-4 hover:underline",
    },
    size: {
      sm: "h-8 px-3 text-xs",
      md: "h-9 px-4 text-sm",
      lg: "h-10 px-8 text-base",
      icon: "h-9 w-9",
    },
  },
  defaultVariants: {
    variant: "primary",
    size: "md",
  },
});

interface ButtonProps
  extends React.ComponentPropsWithoutRef<"button">,
    VariantProps<typeof button> {}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(function Button(props, ref) {
  return (
    <button ref={ref} type="button" {...props} className={cx(button(props), props.className)} />
  );
});
