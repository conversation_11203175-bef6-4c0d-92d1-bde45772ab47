import { authToken, tokenInvalidTopic } from "@/lib/auth-ctx";
import { QueryFunctionContext } from "@tanstack/react-query";
import * as ss from "superstruct";

interface AuthConfig {
  readonly token: string;
  readonly onInvalidToken: () => void;
}

const DEFAULT_AUTH_CONFIG: AuthConfig = {
  get token() {
    return authToken.current;
  },
  onInvalidToken: tokenInvalidTopic.notify,
};

type ApiQueryKey =
  | readonly [string, ...(string | number)[]]
  | readonly [string, ...(string | number)[], Record<string, UrlSearchValue>];

function createApiQuery(authConfig: AuthConfig) {
  return async function <T>(ctx: QueryFunctionContext<ApiQueryKey>): Promise<T> {
    return executeApiQuery<T>(authConfig, ctx);
  };
}

/**
 * Use as `queryFn` for `useQuery` to fetch data from the API.
 *
 * @example
 * const itemListQuery = useQuery({
 *   queryKey: ["items", { active: true, limit: 10 }],
 *   queryFn: apiQuery<ItemListResponse>,
 * })
 *
 * @example
 * const itemQuery = useQuery({
 *  queryKey: ["items", 123],
 *  queryFn: apiQuery<ItemResponse>,
 * })
 */
export const apiQuery = createApiQuery(DEFAULT_AUTH_CONFIG);

async function executeApiQuery<T>(
  authConfig: AuthConfig,
  ctx: QueryFunctionContext<ApiQueryKey>,
): Promise<T> {
  const keysExceptLast = ctx.queryKey.slice(0, -1) as (string | number)[];
  let url = ["/api", ...keysExceptLast].join("/");
  const lastKey = ctx.queryKey.at(-1);
  url +=
    typeof lastKey !== "object"
      ? `/${lastKey}`
      : getUrlSearch(Object.entries(lastKey).sort(compareRecordValues));
  const headers: Record<string, string> = { Accept: "application/json" };
  if (authConfig.token) headers.Authorization = `Bearer ${authConfig.token}`;
  const res = await fetch(url, { headers });
  if (res.status === 401) {
    authConfig.onInvalidToken();
  }
  if (!res.ok) {
    throw await ApiError.fromResponse(res);
  }
  return res.json() as Promise<T>;
}

interface MutationParams {
  readonly method: "POST" | "PUT" | "DELETE" | "PATCH";
  readonly resource: string;
  readonly search?: readonly NameValuePair[];
  readonly body?: unknown;
  readonly auth?: boolean;
  readonly authConfig?: AuthConfig;
}

/**
 * Use as `mutationFn` for `useMutation` to perform a mutation on the API.
 *
 * @example
 * const createItem = useMutation({
 *   mutationFn: async (body: CreateItem) => {
 *     return apiMutation({
 *       method: "POST",
 *       resource: "items",
 *       body,
 *     });
 *   },
 * });
 */
export async function apiMutation<T>({
  method,
  resource,
  search,
  body,
  auth = true,
  authConfig = DEFAULT_AUTH_CONFIG,
}: MutationParams): Promise<T> {
  const headers: Record<string, string> = { Accept: "application/json" };
  if (auth) headers.Authorization = `Bearer ${authConfig.token}`;
  if (body) headers["Content-Type"] = "application/json";
  const searchString = search ? getUrlSearch(search) : "";
  const res = await fetch(`/api/${resource}${searchString}`, {
    method,
    headers,
    body: JSON.stringify(body),
  });
  if (auth && res.status === 401) {
    authConfig.onInvalidToken();
  }
  if (!res.ok) {
    throw await ApiError.fromResponse(res, true);
  }
  return res.json() as Promise<T>;
}

const ApiErrorBody = ss.type({
  detail: ss.union([
    ss.nonempty(ss.string()),
    ss.nonempty(
      ss.array(
        ss.type({
          type: ss.string(),
          msg: ss.string(),
          loc: ss.array(ss.string()),
          input: ss.unknown(),
          ctx: ss.type({
            max_length: ss.optional(ss.number()),
            min_length: ss.optional(ss.number()),
          }),
        }),
      ),
    ),
  ]),
});

type ApiErrorBody = ss.Infer<typeof ApiErrorBody>;

/** Error thrown by default when the API returns a >=400 response. */
export class ApiError extends Error {
  readonly response: Response;
  readonly body: ApiErrorBody | null;

  constructor(response: Response, body: ApiErrorBody | null, message: string) {
    super(message);
    this.name = "ApiError";
    this.response = response;
    this.body = body;
  }

  static async fromResponse(response: Response, mutation = false): Promise<ApiError> {
    let rawBody: unknown;
    if (response.headers.get("Content-Type")?.startsWith("application/json")) {
      try {
        rawBody = await response.json();
      } catch {
        // Ignore error
      }
    }
    const body = ss.is(rawBody, ApiErrorBody) ? rawBody : null;

    const message =
      (mutation ? "Failed mutation" : "Failed to fetch data") +
      ` (${response.status} ${response.statusText})`;

    return new ApiError(response, body, message);
  }
}

type UrlSearchValue = string | number | boolean | null | undefined;

type NameValuePair = readonly [string, UrlSearchValue];

/**
 * @param params Parameters names and their respective values. The
 * parameters with values of `null` or `undefined` are omitted.
 * @returns URL search string with a leading `?` or empty string
 */
export function getUrlSearch(params: readonly NameValuePair[]): string {
  const searchParams = new URLSearchParams();
  for (const [name, value] of params) {
    if (value != null) searchParams.append(name, `${value}`);
  }

  const search = searchParams.toString();
  if (search.length === 0) return "";

  return `?${search}`;
}

function compareRecordValues([keyA]: [string, unknown], [keyB]: [string, unknown]): number {
  return keyA < keyB ? -1 : keyA > keyB ? 1 : 0;
}
