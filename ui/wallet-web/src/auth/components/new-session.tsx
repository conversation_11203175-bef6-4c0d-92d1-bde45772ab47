import { UnifiedAccount } from "@/auth/accounts";
import { MetadataIcon } from "@/auth/walletkit-metadata-icon";
import { SOLANA_CHAINS } from "@/auth/walletkit-utils";
import { AlertError } from "@/lib/components/alert";
import { Button, button } from "@/lib/components/button";
import { Label } from "@/lib/components/form";
import { Input } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { useLazyRefValue } from "@/lib/hooks/lazy";
import WalletKitClient, { WalletKitTypes } from "@reown/walletkit";
import { useMutation, UseQueryResult } from "@tanstack/react-query";
import { SessionTypes } from "@walletconnect/types";
import { buildApprovedNamespaces, getSdkError } from "@walletconnect/utils";
import jsQR from "jsqr";
import { useEffect, useRef, useState } from "react";

interface NewSessionProps {
  accounts: readonly UnifiedAccount[];
  qrCodeFileId: string;
  selectedAccount: UnifiedAccount | undefined;
  getWalletKit: () => Promise<WalletKitClient>;
  activeSessions: UseQueryResult<Record<string, SessionTypes.Struct>>;
}

export function NewSession({
  accounts,
  qrCodeFileId,
  selectedAccount,
  getWalletKit,
  activeSessions,
}: NewSessionProps) {
  const waitForProposal = useLazyRefValue(newWaitFor);
  const [proposal, setProposal] = useState<WalletKitTypes.SessionProposal>();

  const pairing = useMutation({
    async mutationFn(data: File | DataTransfer | undefined) {
      const uri = await extractWalletKitUri(data);
      const walletKit = await getWalletKit();
      await walletKit.pair({ uri });
      const didTimeout = await waitForProposal.start(5000);
      if (didTimeout) {
        throw new Error("No session proposal received, try with a new QR code or link");
      }
    },
  });

  const acceptSession = useMutation({
    async mutationFn(proposal: WalletKitTypes.SessionProposal) {
      const walletKit = await getWalletKit();
      const addresses = accounts.map((acc) => acc.address);
      await handleSessionProposalAcceptance(walletKit, addresses, proposal);
    },
    onSettled() {
      void activeSessions.refetch({ cancelRefetch: false });
      pairing.reset();
      setProposal(undefined);
    },
  });

  const rejectSession = useMutation({
    async mutationFn(proposal: WalletKitTypes.SessionProposal) {
      const walletKit = await getWalletKit();
      await walletKit.rejectSession({
        id: proposal.id,
        reason: getSdkError("USER_REJECTED"),
      });
    },
    onSettled() {
      void activeSessions.refetch({ cancelRefetch: false });
      pairing.reset();
      setProposal(undefined);
    },
  });

  const cleanupPromiseRef = useRef<Promise<void>>(null);
  const refetchActiveSessions = activeSessions.refetch;

  useEffect(() => {
    function handleSessionDelete() {
      void refetchActiveSessions({ cancelRefetch: false });
    }

    function handleSessionProposal(proposal: WalletKitTypes.SessionProposal) {
      setProposal(proposal);
      waitForProposal.done();
    }

    async function setupWalletKit() {
      await cleanupPromiseRef.current; // ensure that cleanup is done before proceeding
      const walletKit = await getWalletKit();
      walletKit.on("session_delete", handleSessionDelete);
      walletKit.on("session_proposal", handleSessionProposal);
      return function cleanup() {
        walletKit.removeListener("session_delete", handleSessionDelete);
        walletKit.removeListener("session_proposal", handleSessionProposal);
      };
    }

    const setupPromise = setupWalletKit();
    return () => {
      const currentCleanupPromise = cleanupPromiseRef.current ?? Promise.resolve();
      cleanupPromiseRef.current = currentCleanupPromise.then(async () => {
        const cleanup = await setupPromise;
        cleanup();
      });
    };
  }, [getWalletKit, refetchActiveSessions, waitForProposal]);
  return (
    <div className="flex flex-col items-center gap-2">
      <h3 className="text-center text-lg font-medium">New Session</h3>

      {proposal ? (
        <div className="grid w-full gap-4 rounded-md border border-gray-200 bg-white p-4">
          <div className="flex items-center gap-2">
            <p className="text-sm font-light opacity-75">Session request from</p>
            <MetadataIcon metadata={proposal.params.proposer.metadata} variant="proposal" />
            <h3>{proposal.params.proposer.metadata.name}</h3>
            <p className="text-sm font-light">
              <a
                href={proposal.params.proposer.metadata.url}
                target="_blank"
                rel="noreferrer nofollow"
                className="text-primary underline-offset-4 hover:underline"
              >
                {proposal.params.proposer.metadata.url}
              </a>
            </p>
            <p className="text-sm">{proposal.params.proposer.metadata.description}</p>
          </div>

          <div className="text-sm">
            <p>This app wants to connect to your wallet.</p>
            {!selectedAccount && (
              <p className="mt-2 text-amber-600">
                Warning: No wallet selected. Please create or import a wallet first.
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="destructive"
              onClick={() => {
                rejectSession.mutate(proposal);
              }}
            >
              Reject
            </Button>
            <Button
              disabled={!selectedAccount}
              onClick={() => {
                acceptSession.mutate(proposal);
              }}
            >
              Accept
            </Button>
          </div>
        </div>
      ) : (
        <div className="grid w-full gap-4 rounded-md border border-gray-100 p-4">
          <div className="text-sm">
            <p>Connect to a dApp by scanning a QR code or pasting a WalletConnect URI.</p>
            {!selectedAccount && (
              <p className="mt-2 text-amber-600">
                Warning: No wallet selected. Please create or import a wallet first.
              </p>
            )}
          </div>

          <div className="grid w-72 gap-2">
            <Input
              placeholder="Paste link or image with QR code"
              className="text-center"
              disabled={pairing.isPending}
              onPaste={(e) => {
                pairing.mutate(e.clipboardData);
              }}
              value=""
              onChange={noop}
            />
            <span className="text-center">or</span>
            <Label
              htmlFor={qrCodeFileId}
              className={button({
                variant: "outline",
                className: [
                  "hover:cursor-pointer",
                  pairing.isPending && "pointer-events-none opacity-60",
                ],
              })}
            >
              Select image with QR code
            </Label>
            <input
              id={qrCodeFileId}
              className="sr-only"
              disabled={pairing.isPending}
              name="qrCode"
              type="file"
              accept="image/*"
              multiple={false}
              onChange={(e) => {
                pairing.mutate(e.target.files?.[0]);
              }}
            />
            {pairing.error && (
              <AlertError onClear={pairing.reset}>{getErrorMessage(pairing.error)}</AlertError>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

async function handleSessionProposalAcceptance(
  walletKit: WalletKitClient,
  addresses: readonly string[],
  proposal: WalletKitTypes.SessionProposal,
) {
  try {
    const approvedNamespaces = buildApprovedNamespaces({
      proposal: proposal.params,
      supportedNamespaces: {
        solana: {
          chains: [...SOLANA_CHAINS],
          methods: ["solana_signTransaction", "solana_signMessage"],
          events: ["accountsChanged", "chainChanged"],
          accounts: SOLANA_CHAINS.flatMap((chain) =>
            addresses.map((addr) => `${chain}:${addr}`),
          ),
        },
      },
    });

    await walletKit.approveSession({
      id: proposal.id,
      namespaces: approvedNamespaces,
    });
  } catch (error) {
    // use the error.message to show toast/info-box letting the user know that the connection attempt was unsuccessful
    await walletKit.rejectSession({
      id: proposal.id,
      reason: getSdkError("USER_REJECTED"),
    });
    throw error;
  }
}

async function extractWalletKitUri(data: File | DataTransfer | undefined): Promise<string> {
  if (!data) {
    throw new Error("No image or link available");
  }
  if (data instanceof File) {
    return extractWalletKitUriFromFile(data);
  }
  return extractWalletKitUriFromClipboard(data);
}

async function extractWalletKitUriFromClipboard(data: DataTransfer): Promise<string> {
  const text = data.getData("text");
  if (text.startsWith("wc:")) {
    return text;
  }
  const file = data.files[0];
  if (!file) {
    throw new Error("No valid link or image found in clipboard");
  }
  return await extractWalletKitUriFromFile(file);
}

async function extractWalletKitUriFromFile(file: File | undefined): Promise<string> {
  if (!file) {
    throw new Error("No image selected");
  }
  if (!file.type.startsWith("image/")) {
    throw new Error("Unsupported file type");
  }
  const bitmap = await window.createImageBitmap(file);
  const canvas = new OffscreenCanvas(bitmap.width, bitmap.height);
  const ctx = canvas.getContext("2d");
  if (!ctx) {
    throw new Error("Failed to create canvas context");
  }
  ctx.drawImage(bitmap, 0, 0);
  const imageData = ctx.getImageData(0, 0, bitmap.width, bitmap.height);
  const code = jsQR(imageData.data, imageData.width, imageData.height, {
    inversionAttempts: "dontInvert",
  });
  if (code == null) {
    throw new Error("No QRCode found in the image");
  }
  if (!code.data.startsWith("wc:")) {
    throw new Error("QRCode is invalid");
  }
  return code.data;
}
/**
 * Create a new wait-for utility.
 *
 * This is useful for waiting for an arbitrary event. Calling `start` will return a promise that
 * resolves when `done` is called or when the timeout is reached.
 */
function newWaitFor() {
  let resolve: (didTimeout: boolean) => void = noop;

  function done() {
    resolve(false);
    resolve = noop;
  }

  async function start(timeout: number): Promise<boolean> {
    const promise = new Promise<boolean>((_resolve) => {
      resolve = _resolve;
    });

    const currentResolve = resolve; // capture the current promise resolve function
    const timeoutId = setTimeout(() => {
      currentResolve(true);
    }, timeout);

    void promise.finally(() => {
      clearTimeout(timeoutId);
    });

    return promise;
  }

  return { done, start };
}

function noop() {
  // no-op
}
