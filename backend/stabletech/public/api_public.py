from fastapi import APIRouter, Response
from pydantic import BaseModel

from common.psql import DatabaseSession
from stabletech.utils import slack

router = APIRouter()


class GetStartedRequest(BaseModel):
    email: str


@router.post("/get-started")
def get_started(request: GetStartedRequest, db: DatabaseSession):
    """
    Endpoint for landing page "Get Started" form submissions.
    Posts the investor email to Slack and returns 200 OK.
    """

    slack.post_message(
        slack.stable_tech_channel(),
        f"New investor interested in Stable Tech: {request.email}",
    )

    return Response(status_code=200)
