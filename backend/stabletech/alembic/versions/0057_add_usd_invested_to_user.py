"""Add usd_invested to User

Revision ID: 0057
Revises: 0056
Create Date: 2024-07-19 17:06:13.303666

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0057"
down_revision = "0056"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user", sa.Column("usd_invested", sa.Numeric(), server_default="0", nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "usd_invested")
    # ### end Alembic commands ###
