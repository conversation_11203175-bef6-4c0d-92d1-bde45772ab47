import { NftList } from "@/app/nfts/[collection-uid]/nft-list";

export function generateStaticParams() {
  // TODO: find a way to accept any collection uid param
  return ["theheist", "the_heist_land", "the_heist_generations"].map((collectionUid) => ({
    "collection-uid": collectionUid,
  }));
}

export default async function NftCollection(props: {
  params: Promise<{ "collection-uid": string }>;
}) {
  const params = await props.params;

  const { "collection-uid": collectionUid } = params;

  return (
    <main className="p-4">
      {!collectionUid ? "Invalid collection uid" : <NftList collectionUid={collectionUid} />}
    </main>
  );
}
