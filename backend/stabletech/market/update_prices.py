"""Go through Transfers populating their asset prices."""

import asyncio
import logging
from decimal import Decimal

import httpx
from sqlalchemy.orm import Session
from sqlalchemy.orm.exc import StaleDataError

from common.pricer.client import PricerClient
from common.psql import SessionLocal
from stabletech.indexer.models import Account, Transaction, Transfer

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


async def main():
    total_processed_count = 0
    all_transfer_ids_skipped = set[int]()
    pricer = PricerClient.from_env()
    with SessionLocal() as db:
        while True:
            transfers_count, processed_count, skipped = await process_batch(
                db, pricer, skip=all_transfer_ids_skipped
            )
            total_processed_count += processed_count
            all_transfer_ids_skipped |= skipped
            if transfers_count == 0:
                logger.info("Processed %d transfers", processed_count)
                logger.info(
                    "Skipped %d transfers: %s",
                    len(all_transfer_ids_skipped),
                    all_transfer_ids_skipped,
                )
                logger.info("No more transfers to process, exiting")
                break


async def process_batch(
    db: Session, pricer: PricerClient, skip: set[int]
) -> tuple[int, int, set[int]]:
    """Process a batch of transfers.

    Args:
        db (Session): SQLAlchemy session
        pricer (PricerClient): Pricer client
        skip (set[int]): a set of transfer ids to skip

    Returns a tuple of (transfers_count, processed_count, transfer_ids_skipped).
    """
    transfer_ids_skipped: set[int] = set()
    processed_count = 0
    sleep_seconds = 0

    transfers_batch: list[Transfer] = (
        db.query(Transfer)
        .where(
            Transfer.id.not_in(skip),
            Transfer.asset_price_usd == None,
            Transfer.nft_id == None,  # We don't handle NFT pricing yet
            Account.id == Transfer.asset_account_id,
            Account.coin != None,
            Transaction.id == Transfer.transaction_id,
        )
        .order_by(Transaction.confirmed_at.desc())
        .limit(10)
        .all()
    )
    transfers_count = len(transfers_batch)

    for transfer in transfers_batch:
        logger.info(
            "Populating asset price for transfer %d (uid=%s, timestamp=%s)",
            transfer.id,
            transfer.asset_account.alias_coin.uid,
            transfer.transaction.confirmed_at.isoformat(),
        )

        if transfer.asset_account.alias_coin.is_dollar:
            transfer.asset_price_usd = Decimal("1")
            logger.debug("Asset price for transfer %d is 1 (asset is USD)", transfer.id)
            continue

        try:
            data = await pricer.get_coin_history(
                coin_id=transfer.asset_account.alias_coin.uid,
                date=transfer.transaction.confirmed_at.date(),
            )
        except httpx.HTTPStatusError as e:
            logger.warning(
                "Got status code %d for transfer %d, skipping",
                e.response.status_code,
                transfer.id,
                extra={"resp": e.response, "url": e.request.url},
            )
            transfer_ids_skipped.add(transfer.id)
            if e.response.status_code in (429, 503):
                # Transfers would be stale with a higher probability after
                # waiting the "retry-after", so we stop processing.
                sleep_seconds = get_retry_after(e.response.headers) or 60
                break
            continue

        market_data = data.market_data
        current_price = market_data.current_price if market_data else None
        if not current_price:
            logger.warning("No pricing data for transfer %d, skipping", transfer.id)
            transfer_ids_skipped.add(transfer.id)
            continue

        transfer.asset_prices_json = current_price

        asset_price_usd = current_price.get("usd")
        if asset_price_usd is None:
            logger.warning("No asset price in USD for transfer %d", transfer.id)
            transfer_ids_skipped.add(transfer.id)
            continue

        asset_price_usd = Decimal(str(asset_price_usd))
        transfer.asset_price_usd = asset_price_usd
        logger.debug("Asset price in USD for transfer %d is %s", transfer.id, asset_price_usd)

        processed_count += 1

    try:
        db.commit()
    except StaleDataError as e:
        logger.warning("Got StaleDataError: %s", e)
        db.rollback()
        processed_count = 0

    if sleep_seconds > 0:
        logger.info("Sleeping for %.2f seconds", sleep_seconds)
        await asyncio.sleep(sleep_seconds)

    return transfers_count, processed_count, transfer_ids_skipped


def get_retry_after(headers: httpx.Headers) -> float | None:
    retry_after = headers.get("Retry-After")
    if not retry_after:
        return None
    try:
        return max(0, float(retry_after))
    except ValueError:
        return None


asyncio.run(main())
