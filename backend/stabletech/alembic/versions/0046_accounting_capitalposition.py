"""Accounting CapitalPosition

Revision ID: 0046
Revises: 0045
Create Date: 2024-01-04 08:00:32.128888

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0046"
down_revision = "0045"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "capitalposition",
        sa.Column("amount", sa.Numeric(precision=48, scale=18), nullable=False),
        sa.Column("coin_id", sa.Integer(), nullable=False),
        sa.Column("is_imported", sa.<PERSON>(), nullable=False),
        sa.Column("bought_at", sa.DateTime(), nullable=False),
        sa.Column("bought_usd_amount", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("sold_at", sa.DateTime(), nullable=True),
        sa.Column("sold_usd_amount", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["coin_id"],
            ["coin.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_capitalposition_id"), "capitalposition", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_capitalposition_id"), table_name="capitalposition")
    op.drop_table("capitalposition")
    # ### end Alembic commands ###
