import {
  Button,
  Checkbox,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControlLabel,
  FormGroup,
} from "@mui/material";
import { DataGridPro, GridColDef } from "@mui/x-data-grid-pro";
import { useEffect, useMemo, useState } from "react";

import type { ImportFailedRow, ImportResult } from "@/types/schemas";

interface CloseParams {
  readonly skipAlreadyInDb?: boolean;
  readonly mergeInFileDuplicates?: boolean;
}

interface Props {
  readonly data: ImportResult | undefined;
  readonly onClose: (params?: CloseParams) => void;
}

const EMPTY_ARRAY = [] as const;

export default function ImportFailureDialog({ data, onClose }: Props) {
  const [persistedData, setPersistedData] = useState(data);
  useEffect(() => {
    // Persist data to avoid flickering when the dialog is closed (out transition)
    if (data != null) setPersistedData(data);
  }, [data]);

  const open = data != null && !data.ok;
  const lastAvailableData = data ?? persistedData;
  const header = lastAvailableData?.header ?? EMPTY_ARRAY;
  const rows = lastAvailableData?.failedRows ?? EMPTY_ARRAY;

  const columns = useMemo(() => computeColumns(header, rows[0]?.row), [header, rows]);
  const skipEnabled = useMemo(() => rows.some((row) => row.error === "already-in-db"), [rows]);
  const mergeEnabled = useMemo(
    () => rows.some((row) => row.error === "in-file-duplicate"),
    [rows],
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      component="form"
      onSubmit={(event) => {
        event.preventDefault();
        const formData = new FormData(event.currentTarget as unknown as HTMLFormElement);
        onClose({
          skipAlreadyInDb: formData.get("skipAlreadyInDb") === "on",
          mergeInFileDuplicates: formData.get("mergeInFileDuplicates") === "on",
        });
      }}
      fullWidth
      maxWidth="lg"
    >
      <DialogTitle>Failed to import</DialogTitle>
      <DialogContent>
        <DialogContentText gutterBottom>
          No new data was imported due to failures on some entries. The below entries are the
          ones that caused the failure. One reason might be entries that are already present in
          your portfolio. Other reason might be duplicates in the file, which are entries with
          same timestamp, buy currency and sell currency. You may choose to skip adding the ones
          already found, and to merge the in file duplicates, and then <em>proceed</em> the
          import, or <em>cancel</em> and change their timestamps in the CSV and re-upload it.
        </DialogContentText>
        <DataGridPro
          sx={{ height: 380 }}
          columns={columns}
          rows={rows}
          getRowId={getRowId}
          pagination
          autoPageSize
        />
        <FormGroup>
          <FormControlLabel
            name="skipAlreadyInDb"
            disabled={!skipEnabled}
            required={skipEnabled}
            control={<Checkbox />}
            label={labelMaybeDisabled("Skip already present in portfolio", skipEnabled)}
          />
          <FormControlLabel
            name="mergeInFileDuplicates"
            disabled={!mergeEnabled}
            required={mergeEnabled}
            control={<Checkbox />}
            label={labelMaybeDisabled("Merge duplicates in the file", mergeEnabled)}
          />
        </FormGroup>
      </DialogContent>
      <DialogActions sx={{ justifyContent: "flex-start" }}>
        <Button onClick={() => onClose({})}>Cancel</Button>
        <Button type="submit">Proceed</Button>
      </DialogActions>
    </Dialog>
  );
}

function computeColumns(
  header: readonly string[],
  firstRow?: readonly string[],
): GridColDef<ImportFailedRow, unknown, unknown>[] {
  if (header.length === 0) {
    if (firstRow == null) return [];
    // no header names, but with appropriate number of columns for rows
    header = firstRow.map(() => "") ?? [];
  }
  return [
    {
      field: "reason",
      headerName: "Reason",
      width: 180,
      valueGetter: getReason,
    },
    {
      field: "index",
      headerName: "#",
      width: 70,
      valueGetter: (params) => params.row.index + 1,
    },
    ...header.map(
      (name, i): GridColDef<ImportFailedRow> => ({
        field: `col${i}`,
        headerName: name,
        valueGetter: (params) => params.row.row[i],
      }),
    ),
  ];
}

function getReason({ row }: { readonly row: ImportFailedRow }) {
  switch (row.error) {
    case "already-in-db":
      return "Already present in portfolio";
    case "in-file-duplicate":
      return "Duplicate in the file";
    default:
      return row.error;
  }
}

function getRowId({ index }: ImportFailedRow) {
  return index;
}

function labelMaybeDisabled(label: string, enabled: boolean): string {
  return enabled ? label : `${label} (disabled, none found)`;
}
