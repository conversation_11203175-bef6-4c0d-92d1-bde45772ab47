"""Add DOT Network

Revision ID: 0026
Revises: 0025
Create Date: 2023-07-10 23:32:42.055975

"""

from alembic import op
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0026"
down_revision = "0025"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Network(Base):
    id = Column(Integer, primary_key=True)
    covalent_chain_id: int = Column(Integer)
    explorer_url: str = Column(String)
    name: str = Column(String, nullable=False, index=True, unique=True)
    __tablename__ = "network"


def upgrade() -> None:
    session = Session(bind=op.get_bind())
    name = "Polkadot"
    if not session.query(Network).filter_by(name=name).first():
        session.add(Network(name=name))
    session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
