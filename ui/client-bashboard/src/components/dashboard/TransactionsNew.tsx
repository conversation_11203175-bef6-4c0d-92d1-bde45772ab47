import { Card } from "@/components/ui/card";
import { currencyFormatter } from "@/helpers/formatters";
import { SimpleTransaction, simplifyTransactions } from "@/helpers/transaction-simplifier";
import { Coin, TransactionForClient } from "@/types/schemas";
import { cx } from "cva";
import dayjs from "dayjs";
import { Filter, X } from "lucide-react";
import { useMemo } from "react";

interface TransactionsNewProps {
  transactions: readonly TransactionForClient[];
  selectedCoin: Coin | null;
  averageCost: number | null;
  onClearSelectedCoin: () => void;
}

export function TransactionsNew({
  transactions,
  selectedCoin,
  averageCost,
  onClearSelectedCoin,
}: TransactionsNewProps) {
  // Combine transfers of the same coin and sort
  const tableRows = useMemo(() => {
    const processedTransactions = transactions
      .map(simplifyTransactions)
      .map(processTransaction)
      .sort(sortTransactions);

    return processedTransactions.map((tx) => {
      const dateText = (
        <span className="text-sm text-gray-600 font-stretch-semi-condensed">
          {formatTransactionDate(tx.confirmedAt)}
        </span>
      );

      const transfers = tx.transfers.map((transfer, index) => {
        const isNegative = transfer.isWithdrawal && tx.action !== "spend";
        return (
          <div
            key={transfer.id}
            className={cx(
              "text-sm font-medium",
              index > 0 && "mt-px",
              tx.clientAction === "Interest Payment"
                ? "text-primary"
                : isNegative
                  ? "text-red-700"
                  : "text-green-700",
            )}
          >
            {isNegative ? "-" : "+"}
            {currencyFormatter.format(Number(transfer.amount))}{" "}
            {transfer.coin?.ticker.toUpperCase()}
          </div>
        );
      });

      const txHash = (
        <a
          className="inline-block text-sm text-gray-600 hover:text-gray-800 hover:underline"
          href={`${tx.network.explorerUrl}/tx/${tx.txHash}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {tx.txHash.length > 10
            ? tx.txHash.slice(0, 6) + "…" + tx.txHash.slice(-4)
            : tx.txHash}
        </a>
      );

      const actionText = (
        <span
          className={cx(
            "text-sm font-medium",
            tx.clientAction === "Interest Payment"
              ? "text-primary"
              : tx.clientAction === "Deposit"
                ? "text-green-700"
                : tx.clientAction === "Withdrawal"
                  ? "text-red-700"
                  : "text-slate-700",
          )}
        >
          {tx.clientAction}
        </span>
      );

      return (
        <tr key={tx.id} className="border-border align-top not-last:border-b hover:bg-gray-50">
          {/* Mobile layout (2 columns) - visible on small screens, hidden on md+ */}
          <td className="py-4 pr-2 pl-4 md:hidden">
            <div>{actionText}</div>
            <div className="mt-px">{dateText}</div>
          </td>
          <td className="py-4 pr-4 pl-2 text-right md:hidden">
            {transfers}
            <div className="mt-px">{txHash}</div>
          </td>

          {/* Desktop layout (4 columns) - hidden on small screens, visible on md+ */}
          <td className="hidden py-4 pr-2 pl-4 text-left md:table-cell">{dateText}</td>
          <td className="hidden px-2 py-4 text-center md:table-cell">{actionText}</td>
          <td className="hidden px-2 py-4 text-right md:table-cell">{transfers}</td>
          <td className="hidden py-4 pr-4 pl-2 text-right md:table-cell">{txHash}</td>
        </tr>
      );
    });
  }, [transactions]);

  const selectedCoinBadge = selectedCoin && (
    <span className="flex max-w-64 min-w-0 items-center gap-1 rounded bg-blue-50 py-1 pr-1 pl-2 text-xs font-medium text-blue-800">
      <Filter className="size-4 flex-none fill-blue-800" />
      <span className="overflow-hidden text-ellipsis">
        {selectedCoin.name} ({selectedCoin.ticker.toUpperCase()})
      </span>
      <button
        onClick={onClearSelectedCoin}
        className="-m-px flex-none rounded-xs p-px text-blue-800 hover:bg-blue-100 hover:text-blue-900 focus-visible:ring-2 focus-visible:ring-ring focus-visible:outline-0"
      >
        <X className="size-4" />
        <span className="sr-only">Clear filter</span>
      </button>
    </span>
  );

  const averageCostBadge = averageCost != null && (
    <div className="rounded bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
      <span>Weighted Average: ${currencyFormatter.format(averageCost)}</span>
    </div>
  );

  return (
    <Card padding="none">
      <div className="flex flex-col items-start gap-2 border-b border-border p-4 sm:flex-row sm:items-center sm:gap-4">
        <h2 className="flex-none text-base font-semibold text-primary">Transactions</h2>
        {(selectedCoinBadge || averageCostBadge) && (
          <div className="flex min-w-0 flex-auto items-center gap-2">
            {selectedCoinBadge}
            {averageCostBadge}
          </div>
        )}
      </div>

      <div className="overflow-x-auto">
        <table className="w-full table-auto">
          <thead className="sr-only">
            <tr>
              {/* Mobile view headers (hidden on md+) */}
              <th className="md:hidden">Transaction</th>
              <th className="md:hidden">Details</th>

              {/* Desktop view headers (hidden on small screens) */}
              <th className="hidden md:table-cell">Date</th>
              <th className="hidden md:table-cell">Action</th>
              <th className="hidden md:table-cell">Transfers</th>
              <th className="hidden md:table-cell">Tx Hash</th>
            </tr>
          </thead>
          <tbody>{tableRows}</tbody>
        </table>
      </div>
    </Card>
  );
}

// Custom date formatter for "Mar 15, 2025 09:32 AM" format
const formatTransactionDate = (date: dayjs.ConfigType) => {
  return dayjs(date).format("MMM D, YYYY hh:mm A");
};

function processTransaction(transaction: SimpleTransaction<TransactionForClient>) {
  return {
    ...transaction,
    confirmedAt: new Date(transaction.confirmedAt),
  };
}

type ProcessedTransaction = ReturnType<typeof processTransaction>;

function sortTransactions(a: ProcessedTransaction, b: ProcessedTransaction) {
  return b.confirmedAt.getTime() - a.confirmedAt.getTime();
}
