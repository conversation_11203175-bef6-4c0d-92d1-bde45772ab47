from sqlalchemy import select

from lootloot.auth.models import AuthToken, User
from lootloot.base import BaseManager
from lootloot.kinds import UserOrigin


class AuthTokenManager(BaseManager):
    def get_by_token(self, token: str) -> AuthToken | None:
        return self.db.execute(select(AuthToken).filter_by(id=token)).scalar_one_or_none()

    def create(self, user: User) -> str:
        auth_token = AuthToken.create(user)
        self.db.add(auth_token)
        return auth_token.id

    def get_or_create(self, user: User) -> str:
        token = None
        if user.id:
            token = self.db.execute(
                select(AuthToken.id).filter_by(user_id=user.id)
            ).scalar_one_or_none()
        if not token:
            token = self.create(user)
        return token


class UserManager(BaseManager):
    def get_by_email(self, email: str) -> User | None:
        return self.db.execute(select(User).filter_by(email=email)).scalar_one_or_none()

    def get_by_id(self, user_id: int) -> User | None:
        return self.db.execute(select(User).filter_by(id=user_id)).scalar_one_or_none()

    def get_by_matrica_id(self, matrica_id: str) -> User | None:
        return self.db.scalar(select(User).where(User.matrica_id == matrica_id))

    def create_from_jwt_payload(self, email: str, payload: dict[str, str]) -> User:
        """Create an User from the payload of a decoded JWT."""
        user = User(
            email=email,
            first_name=payload.get("given_name"),
            last_name=payload.get("family_name"),
            picture_url=payload.get("picture"),
            origin=UserOrigin.google.value,
        )
        self.db.add(user)
        return user

    def create_from_phantom(self) -> User:
        user = User(
            origin=UserOrigin.phantom.value,
        )
        self.db.add(user)
        return user

    def create_from_matrica(self, matrica_id: str, username: str) -> User:
        user = User(
            origin=UserOrigin.matrica.value,
            matrica_id=matrica_id,
            matrica_username=username,
        )
        self.db.add(user)
        return user

    def all(self):
        return self.db.scalars(select(User)).all()
