"use client";

import { UnifiedAccount, useDerivedWallets } from "@/auth/accounts";
import { CreateTestAccount } from "@/auth/create-test-account";
import { KeyDecoderInput, useGetKeyDecoder } from "@/auth/key-decoder-input";
import { LOCAL_KEY_LIST_KEY } from "@/auth/local-keys";
import { getBalance } from "@/auth/unified-wallet/balance";
import { sendTransfer } from "@/auth/unified-wallet/transfer";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { CopyButton } from "@/lib/components/copy-button";
import { Label } from "@/lib/components/form";
import { Input } from "@/lib/components/input";
import * as Select from "@/lib/components/select";
import { getErrorMessage } from "@/lib/error";
import { shortenAddress } from "@/lib/utils/string";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ReactNode, useId } from "react";

interface UnifiedWalletProps {
  readonly importTrigger: ReactNode;
  readonly selectedAccountId: string;
  readonly onSelectAccount: (id: string) => void;
}

export function UnifiedWallet({
  importTrigger,
  selectedAccountId,
  onSelectAccount,
}: UnifiedWalletProps) {
  const id = useId();
  const accountId = `account-${id}`;
  const amountId = `amount-${id}`;
  const toId = `to-${id}`;

  // Get all non-master accounts by not specifying a master account
  const { accounts, isLoading: isLoadingAccounts, error } = useDerivedWallets();

  const selectedAccount = accounts.find((acc) => acc.id === selectedAccountId);

  const queryClient = useQueryClient();

  // Balance query
  const balanceQuery = useQuery({
    enabled: selectedAccount != null,
    queryKey: ["balance", selectedAccount],
    refetchInterval: 20_000,
    queryFn: async () => {
      if (!selectedAccount) throw new Error("No account selected");
      const result = await getBalance(selectedAccount);
      return result;
    },
  });

  const getKeyDecoder = useGetKeyDecoder();

  // Transfer mutation
  const transferMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      if (!selectedAccount) throw new Error("No account selected");

      const amount = formData.get("amount");
      const to = formData.get("to");
      if (typeof amount !== "string") throw new Error("'amount' must be a string");
      if (typeof to !== "string") throw new Error("'to' must be a string");

      const parsedAmount = Number.parseFloat(amount);
      if (parsedAmount <= 0) throw new Error("Amount must be greater than 0");
      if (!Number.isFinite(parsedAmount)) throw new Error("Invalid amount");

      const decoder = await getKeyDecoder(formData);

      return await sendTransfer(selectedAccount, {
        amount: parsedAmount,
        to,
        decoder,
      });
    },
    onSuccess() {
      void balanceQuery.refetch();
    },
  });

  const handleTransfer = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    transferMutation.mutate(formData);
  };

  const getAccountLabel = (account: UnifiedAccount) => {
    const address = shortenAddress(account.address);
    const badge =
      account.provider === "local" ? (
        <span className="ml-2 rounded-sm bg-blue-100 px-2 py-0.5 text-xs text-blue-800">
          Local - {account.network}
        </span>
      ) : (
        <span className="ml-2 rounded-sm bg-purple-100 px-2 py-0.5 text-xs text-purple-800">
          CubeSigner - solana
        </span>
      );
    return (
      <div className="flex items-center">
        <span>{address}</span>
        {badge}
      </div>
    );
  };

  return (
    <div className="mt-4 grid w-full min-w-72 justify-items-center gap-6">
      <div className="grid w-full grid-cols-[auto_1fr] items-center gap-2">
        <Label htmlFor={accountId} className="text-right">
          Account
        </Label>
        <div className="grid grid-cols-[1fr_auto] gap-1">
          <Select.Root value={selectedAccountId} onValueChange={onSelectAccount}>
            <Select.Trigger
              id={accountId}
              placeholder={isLoadingAccounts ? "Loading accounts..." : "Select an account"}
            />
            <Select.Content>
              {accounts.map((account) => (
                <Select.Item key={account.id} value={account.id}>
                  {getAccountLabel(account)}
                </Select.Item>
              ))}
            </Select.Content>
          </Select.Root>
          <CopyButton
            aria-label="Copy Address"
            title="Copy Address"
            className="rounded-md"
            text={selectedAccount?.address ?? null}
          />
        </div>

        {error && <AlertError className="col-span-full">{getErrorMessage(error)}</AlertError>}

        <div className="col-span-full mb-4 text-center">
          <span className="text-sm font-light">
            Balance:{" "}
            {balanceQuery.isLoading ? (
              <span className="inline-block w-20 animate-pulse rounded-sm bg-muted">
                &nbsp;
              </span>
            ) : balanceQuery.isSuccess ? (
              `${balanceQuery.data} ${selectedAccount?.network === "solana" ? "SOL" : "ETH"}`
            ) : balanceQuery.isError ? (
              "Error"
            ) : (
              "–"
            )}
          </span>
        </div>

        <form
          className="col-span-full grid w-full grid-cols-[auto_1fr] items-center gap-2"
          onSubmit={handleTransfer}
        >
          <Label htmlFor={amountId} className="text-right">
            Amount
          </Label>
          <Input
            id={amountId}
            name="amount"
            type="number"
            min={0}
            step={0.001}
            required
            disabled={!selectedAccount}
          />

          <Label htmlFor={toId} className="text-right">
            To Address
          </Label>
          <Input id={toId} name="to" type="text" required disabled={!selectedAccount} />

          {selectedAccount?.provider === "local" && (
            <KeyDecoderInput
              className="col-span-full"
              disabled={!selectedAccount}
              keyId={selectedAccount.key.id}
            />
          )}

          <div className="col-span-full grid min-w-0 gap-2 text-center">
            {transferMutation.isError && (
              <AlertError>{getErrorMessage(transferMutation.error)}</AlertError>
            )}
            <Button
              type="submit"
              disabled={!selectedAccount || transferMutation.isPending}
              className="justify-self-center"
            >
              Send
            </Button>
          </div>
        </form>
      </div>

      <div className="grid w-full gap-4">
        <div className="grid grid-cols-2 gap-4">
          {importTrigger}
          <CreateTestAccount
            onCreate={() =>
              void queryClient.invalidateQueries({ queryKey: LOCAL_KEY_LIST_KEY })
            }
          />
        </div>
      </div>
    </div>
  );
}
