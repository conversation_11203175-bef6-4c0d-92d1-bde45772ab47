import { But<PERSON> } from "@/lib/components/button";
import { CheckIcon, CopyIcon, EyeIcon, EyeOffIcon } from "lucide-react";
import { useState } from "react";

interface MnemonicDisplayProps {
  words: string[];
}

export function MnemonicDisplay({ words }: MnemonicDisplayProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [copied, setCopied] = useState(false);

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  const copyToClipboard = () => {
    // Use the clipboard API to copy text
    navigator.clipboard
      .writeText(words.join(" "))
      .then(() => {
        setCopied(true);
        setTimeout(() => {
          setCopied(false);
        }, 2000);
      })
      .catch(() => {
        // Silent fail - typically happens due to permission issues
      });
  };

  return (
    <div className="flex w-full flex-col gap-4">
      {/* Words grid */}
      <div className="user-select-none rounded bg-gray-200/80 p-4 select-none dark:bg-slate-800/80">
        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-base font-medium">Your Recovery Phrase</h3>
          <div className="flex gap-2">
            <Button
              onClick={() => {
                copyToClipboard();
              }}
              disabled={!isVisible || copied}
              className="text-gray-400 transition-colors hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-50 dark:hover:text-gray-200"
              aria-label="Copy recovery phrase to clipboard"
              title="Copy to clipboard"
              size="icon"
            >
              {copied ? (
                <CheckIcon size={20} className="text-green-500" />
              ) : (
                <CopyIcon size={20} />
              )}
            </Button>
            <Button
              onClick={toggleVisibility}
              className="text-gray-400 transition-colors hover:text-gray-600 dark:hover:text-gray-200"
              aria-label={isVisible ? "Hide recovery phrase" : "Show recovery phrase"}
              title={isVisible ? "Hide recovery phrase" : "Show recovery phrase"}
              size="icon"
            >
              {isVisible ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-3 gap-x-4 gap-y-4 md:grid-cols-4">
          {words.map((word, index) => (
            <div
              key={index}
              className="flex min-w-[110px] items-center gap-2 rounded bg-gray-300/50 p-2 px-3 dark:bg-slate-700/50"
            >
              <span className="text-xs text-gray-500 dark:text-gray-400">{index + 1}.</span>
              {isVisible ? (
                <span className="font-mono text-sm font-medium">{word}</span>
              ) : (
                <span className="font-mono text-sm font-medium">••••••</span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Warning message */}
      <div className="flex flex-col items-start gap-2 rounded border border-amber-500/20 bg-amber-500/10 p-3 text-amber-600 dark:text-amber-400">
        <p className="flex items-center gap-2 text-xs">
          <span className="flex-shrink-0">⚠️</span> Never share your recovery phrase. Anyone
          with these words can take your assets.
        </p>
        <p className="flex items-center gap-2 text-xs">
          <span className="text-amber-500">⚠️</span>
          Write down these words in order and keep them safe. They can be used to recover your
          wallet.
        </p>
        <p className="flex items-center gap-2 text-xs">
          <span className="text-amber-500">⚠️</span>
          Do not take screenshots or copy these words digitally.
        </p>
        <p className="flex items-center gap-2 text-xs">
          <span className="text-amber-500">⚠️</span>
          Anyone with access to these words can access your wallet.
        </p>
      </div>
    </div>
  );
}
