name: Backend Tests

on:
  push:
    branches: [main, stage]
    paths: ["backend/**", ".github/workflows/backend-tests.yml"]
  pull_request:
    branches: [main, stage]
    paths: ["backend/**", ".github/workflows/backend-tests.yml"]
jobs:
  test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: backend
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.12
          cache: pip
          cache-dependency-path: backend/common/requirements.txt
      - run: pip install -r common/requirements.txt

      # - name: Wallet tests
      #   run: pytest wallet
