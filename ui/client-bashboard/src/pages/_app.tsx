// ** Next Imports
import type { NextPage } from "next";
import type { AppProps } from "next/app";
import Head from "next/head";
import { Router } from "next/router";

// ** Store Imports
import { store } from "@/store";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Provider } from "react-redux";

// ** Loader Import
import NProgress from "nprogress";

// Create a client
const queryClient = new QueryClient();

// ** Emotion Imports
import type { EmotionCache } from "@emotion/cache";
import { CacheProvider } from "@emotion/react";

// ** Day.js Imports
import dayjs from "dayjs";
import localizedFormat from "dayjs/plugin/localizedFormat";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";

// ** Config Imports
import themeConfig from "@/configs/themeConfig";

// ** Component Imports
import AclGuard from "@/@core/components/auth/AclGuard";
import AuthGuard from "@/@core/components/auth/AuthGuard";
import GuestGuard from "@/@core/components/auth/GuestGuard";
import WindowWrapper from "@/@core/components/window-wrapper";
import ThemeComponent from "@/@core/theme/ThemeComponent";
import { AgreementGuard } from "@/components/AgreementGuard";
import UserLayout from "@/layouts/UserLayout";

// ** Spinner Import
import Spinner from "@/@core/components/spinner";

// ** Contexts
import { SettingsConsumer, SettingsProvider } from "@/@core/context/settingsContext";
import { defaultACLObj } from "@/configs/acl";
import { FirebaseAuthProvider } from "@/hooks/useAuth";

// ** Utils Imports
import { createEmotionCache } from "@/@core/utils/create-emotion-cache";
import { UrlNormalizer } from "@/helpers/url-normalizer";

// ** React Perfect Scrollbar Style
import "react-perfect-scrollbar/dist/css/styles.css";

// ** Global css styles
import "@/styles/globals.css";

import { LicenseInfo } from "@mui/x-license";

LicenseInfo.setLicenseKey(
  "0a923fcb2d5eaf6788822686187a7373Tz05NTQ3OSxFPTE3NTQyNTI0MzAwMDAsUz1wcm8sTE09c3Vic2NyaXB0aW9uLFBWPWluaXRpYWwsS1Y9Mg==",
);

// ** Extend App Props with Emotion
type ExtendedAppProps = AppProps & {
  Component: NextPage;
  emotionCache: EmotionCache;
};

interface GuardProps {
  authGuard: boolean;
  guestGuard: boolean;
  children: React.JSX.Element;
  spinner?: React.JSX.Element | undefined | null;
}

const clientSideEmotionCache = createEmotionCache();

// ** Pace Loader
if (themeConfig.routingLoader) {
  Router.events.on("routeChangeStart", () => {
    NProgress.start();
  });
  Router.events.on("routeChangeError", () => {
    NProgress.done();
  });
  Router.events.on("routeChangeComplete", () => {
    NProgress.done();
  });
}

// ** Day.js configuration
dayjs.extend(localizedFormat);
dayjs.extend(relativeTime);
dayjs.extend(utc);

const Guard = ({ children, authGuard, guestGuard, spinner = null }: GuardProps) => {
  if (guestGuard) {
    return <GuestGuard fallback={spinner}>{children}</GuestGuard>;
  } else if (!guestGuard && !authGuard) {
    return <>{children}</>;
  } else {
    return <AuthGuard fallback={spinner}>{children}</AuthGuard>;
  }
};

// ** Configure JSS & ClassName
const App = (props: ExtendedAppProps) => {
  const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;

  // Variables
  const getLayout = Component.getLayout ?? ((page) => <UserLayout>{page}</UserLayout>);

  const setConfig = Component.setConfig ?? undefined;

  const authGuard = Component.authGuard ?? true;

  const guestGuard = Component.guestGuard ?? false;

  const aclAbilities = Component.acl ?? defaultACLObj;

  const spinner = Component.getSpinner?.() ?? <Spinner />;

  return (
    <>
      <UrlNormalizer />
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <CacheProvider value={emotionCache}>
            <Head>
              <title>{`${themeConfig.templateName}`}</title>
              <meta name="description" content={`${themeConfig.templateName}`} />
              <meta name="keywords" content="Stable Tech" />
              <meta name="viewport" content="initial-scale=1, width=device-width" />
            </Head>

            <FirebaseAuthProvider>
              <SettingsProvider {...(setConfig ? { pageSettings: setConfig() } : {})}>
                <SettingsConsumer>
                  {({ settings }) => {
                    return (
                      <ThemeComponent settings={settings}>
                        <WindowWrapper spinner={spinner}>
                          <AclGuard
                            aclAbilities={aclAbilities}
                            guestGuard={guestGuard}
                            spinner={spinner}
                          >
                            <Guard
                              authGuard={authGuard}
                              guestGuard={guestGuard}
                              spinner={spinner}
                            >
                              <AgreementGuard>
                                {getLayout(<Component {...pageProps} />)}
                              </AgreementGuard>
                            </Guard>
                          </AclGuard>
                        </WindowWrapper>
                      </ThemeComponent>
                    );
                  }}
                </SettingsConsumer>
              </SettingsProvider>
            </FirebaseAuthProvider>
          </CacheProvider>
        </QueryClientProvider>
      </Provider>
    </>
  );
};

export default App;
