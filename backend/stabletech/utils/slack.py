from slack_sdk import <PERSON><PERSON><PERSON>
from slack_sdk.signature import SignatureVerifier

from common.conf import logger, settings

TEAM_ID = "T030PAETPNZ"

slack_token = settings.slack_bot_token
slack_client = WebClient(token=slack_token)
signature_verifier = SignatureVerifier(settings.slack_signing_secret)


def stable_tech_channel():
    if settings.is_production:
        # _stable_tech channel
        return "C05UCMPFETE"
    else:
        # _test_stable_tech channel
        return "C05UNLK5RQ9"


def post_message(channel: str, text: str):
    if not settings.slack_bot_token:
        logger.info(f"Slack message to {channel}: {text}")
    else:
        slack_client.chat_postMessage(
            channel=channel,
            text=f"Environment: {settings.env_name}\nMessage: {text}",
        )
