// ** Type Imports
import { Skin, ThemeColor } from "@/@core/layouts/types";
import { colors } from "@/context/env";
import { PaletteMode } from "@mui/material";

const DefaultPalette = (mode: PaletteMode, skin: Skin, themeColor: ThemeColor) => {
  const isLight = mode === "light";

  // ** Vars
  const lightColor = colors.lightColor;
  const darkColor = colors.darkColor;
  const mainColor = isLight ? lightColor : darkColor;

  const primaryGradient = () => {
    switch (themeColor) {
      case "primary":
        return isLight ? colors.primaryGradientLight : colors.primaryGradientDark;
      case "secondary":
        return "#9C9FA4";
      case "success":
        return "#93DD5C";
      case "error":
        return "#FF8C90";
      case "warning":
        return "#FFCF5C";
      default:
        return "#6ACDFF";
    }
  };

  const defaultBgColor = () => {
    if (skin === "bordered" && isLight) {
      return "#FFF";
    } else if (skin === "bordered" && mode === "dark") {
      return colors.paperBgDark;
    } else if (isLight) {
      return colors.bgLight;
    } else return colors.bgDark;
  };

  return {
    customColors: {
      dark: `rgb(${darkColor})`,
      main: `rgb(${mainColor})`,
      light: `rgb(${lightColor})`,
      darkBg: colors.bgDark,
      lightBg: colors.bgLight,
      primaryGradient: primaryGradient(),
      bodyBg: isLight ? colors.bgLight : colors.bgDark, // Same as palette.background.default but doesn't consider bordered skin
      tableHeaderBg: isLight ? "#F9FAFC" : colors.tableHeaderBgDark,
    },
    common: {
      black: "#000",
      white: "#FFF",
    },
    mode: mode,
    primary: {
      light: "#9E69FD",
      main: isLight ? colors.primaryMainLight : colors.primaryMainDark,
      dark: isLight ? colors.primaryDarkLight : colors.primaryDarkDark,
      contrastText: isLight ? colors.primaryContrastTextLight : colors.primaryContrastTextDark,
    },
    secondary: {
      light: "#9C9FA4",
      main: isLight ? colors.secondaryMainLight : colors.secondaryMainDark,
      dark: "#777B82",
      contrastText: "#FFF",
    },
    success: {
      light: "#6AD01F",
      main: "#56CA00",
      dark: "#4CB200",
      contrastText: "#FFF",
    },
    error: {
      light: "#FF6166",
      main: "#FF4C51",
      dark: "#E04347",
      contrastText: "#FFF",
    },
    warning: {
      light: "#FFCA64",
      main: "#FFB400",
      dark: "#E09E00",
      contrastText: "#FFF",
    },
    info: {
      light: "#32BAFF",
      main: "#16B1FF",
      dark: "#139CE0",
      contrastText: "#FFF",
    },
    grey: {
      50: "#FAFAFA",
      100: "#F5F5F5",
      200: "#EEEEEE",
      300: "#E0E0E0",
      400: "#BDBDBD",
      500: "#9E9E9E",
      600: "#757575",
      700: "#616161",
      800: "#424242",
      900: "#212121",
      A100: "#D5D5D5",
      A200: "#AAAAAA",
      A400: "#616161",
      A700: "#303030",
    },
    text: {
      primary: `rgba(${mainColor}, ${colors.textPrimaryAlpha})`,
      secondary: `rgba(${mainColor}, 0.68)`,
      disabled: `rgba(${mainColor}, 0.38)`,
    },
    divider: `rgba(${mainColor}, 0.12)`,
    background: {
      paper: isLight ? "#FFF" : colors.paperBgDark,
      default: defaultBgColor(),
    },
    action: {
      active: `rgba(${mainColor}, 0.54)`,
      hover: `rgba(${mainColor}, 0.04)`,
      selected: `rgba(${mainColor}, 0.08)`,
      disabled: `rgba(${mainColor}, 0.3)`,
      disabledBackground: `rgba(${mainColor}, 0.18)`,
      focus: `rgba(${mainColor}, 0.12)`,
    },
  };
};

export default DefaultPalette;
