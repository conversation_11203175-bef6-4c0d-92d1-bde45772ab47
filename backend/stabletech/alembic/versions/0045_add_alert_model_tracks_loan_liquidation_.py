"""Add alert model - tracks loan liquidation alerts

Revision ID: 0045
Revises: 0044
Create Date: 2023-12-20 20:45:43.614863

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0045"
down_revision = "0044"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "alert",
        sa.<PERSON>umn("account_id", sa.Integer(), nullable=False),
        sa.Column("collateral_amount", sa.Numeric(precision=48, scale=18), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("sent_at", sa.DateTime(), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("is_active", sa.<PERSON>(), server_default="true", nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["account.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_alert_account_id"), "alert", ["account_id"], unique=False)
    op.create_index(op.f("ix_alert_id"), "alert", ["id"], unique=False)
    op.create_index(op.f("ix_alert_is_active"), "alert", ["is_active"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_alert_is_active"), table_name="alert")
    op.drop_index(op.f("ix_alert_id"), table_name="alert")
    op.drop_index(op.f("ix_alert_account_id"), table_name="alert")
    op.drop_table("alert")
    # ### end Alembic commands ###
