variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "replicas" {
  description = "Number of Pods to run"
  type        = number
  default     = 1
  nullable    = false
}

variable "python_runner_image" {
  description = "python-runner fully qualified image name"
  type        = string
  nullable    = false
}

variable "create_neg" {
  description = "Create NEG for the service"
  type        = bool
  default     = false
  nullable    = false
}

variable "config_maps" {
  description = "ConfigMap names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "secrets" {
  description = "Secret names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "auto_reload" {
  description = "Enable auto reload when files change, for development only"
  type        = bool
  default     = false
  nullable    = false
}

variable "host_volumes" {
  description = "Host paths to mount into containers, for development only"
  type = list(object({
    name       = string
    host_path  = string
    mount_path = string
  }))
  default  = []
  nullable = false
}

# When in development the server might get broken regularly, but we don't want to
# restart the pod all the time. Use this to disable the liveness probe.
variable "disable_liveness_probe" {
  description = "Disable liveness probe, for development only"
  type        = bool
  default     = false
  nullable    = false
}
