/** @type {typeof import("superstruct")} */
const s = require("superstruct");

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "export",
  trailingSlash: true, // e.g. /auth emits /auth/index.html
  env: {
    AUTH_PROVIDERS: validateAuthProviders(process.env.AUTH_PROVIDERS),
    ENV_NAME: requiredEnv("ENV_NAME"),
    HELIUS_API_KEY: requiredEnv("HELIUS_API_KEY"),
    MATRICA_CLIENT_ID: requiredEnv("MATRICA_CLIENT_ID"),
  },
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: `http://localhost:${process.env.LOCAL_API_PORT ?? 8080}/:path*`,
      },
    ];
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  serverExternalPackages: ["@sentry/nextjs"],
  webpack: (config, { dev }) => {
    if (!dev) {
      config.devtool = "hidden-source-map";
    }
    return config;
  },
};

/** @param {string | undefined} providers */
function validateAuthProviders(providers) {
  if (!providers) throw new Error("AUTH_PROVIDERS is required");
  const obj = JSON.parse(providers);
  const authProvidersStruct = s.record(
    s.string(),
    s.object({
      clientId: s.nonempty(s.string()),
    }),
  );
  s.assert(obj, authProvidersStruct);
  return providers;
}

/** @param {string} name Environment variable name */
function requiredEnv(name) {
  const value = process.env[name];
  if (!value) throw new Error(`${name} is required`);
  return value;
}

// Sentry

const { withSentryConfig } = require("@sentry/nextjs");

module.exports = withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  // Suppresses source map uploading logs during build
  silent: true,
  org: "stable-tech",
  project: "lootloot-ui",

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Hides source maps from generated client bundles
  hideSourceMaps: true,
  sourcemaps: {
    deleteSourceMapsAfterUpload: true,
  },

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors.
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});
