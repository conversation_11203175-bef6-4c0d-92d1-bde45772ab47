"""Fix wallet uniqueness

Revision ID: 0042
Revises: 0041
Create Date: 2023-10-19 08:59:06.341292

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0042"
down_revision = "0041"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_index(
        "ix_wallet_user_id_address",
        "wallet",
        [sa.text("coalesce(user_id, 0)"), "address"],
        unique=True,
    )
    op.create_index(
        "ix_wallet_user_id_name",
        "wallet",
        [sa.text("coalesce(user_id, 0)"), "name"],
        unique=True,
    )
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("wallet_address_user_id_key", "wallet", type_="unique")
    op.drop_constraint("wallet_name_user_id_key", "wallet", type_="unique")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint("wallet_name_user_id_key", "wallet", ["name", "user_id"])
    op.create_unique_constraint("wallet_address_user_id_key", "wallet", ["address", "user_id"])
    # ### end Alembic commands ###
    op.drop_index("ix_wallet_user_id_name", table_name="wallet")
    op.drop_index("ix_wallet_user_id_address", table_name="wallet")
