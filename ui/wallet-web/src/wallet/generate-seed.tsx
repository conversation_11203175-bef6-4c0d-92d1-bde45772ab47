import { AlertError } from "@/lib/components/alert";
import { But<PERSON> } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { getErrorMessage } from "@/lib/error";
import { MnemonicStrength, generateMnemonic } from "@/lib/mnemonic";
import { useCreateWalletMutation } from "@/wallet/api";
import { MnemonicDisplay } from "@/wallet/mnemonic-display";
import { MnemonicValidation } from "@/wallet/mnemonic-validation";
import { ShieldIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface GenerateSeedProps {
  setIsOpen: (open: boolean) => void;
  isOpen: boolean;
}

export function GenerateSeed({ setIsOpen, isOpen }: GenerateSeedProps) {
  const [generatedMnemonic, setGeneratedMnemonic] = useState<string>();
  const [showMnemonicValidation, setShowMnemonicValidation] = useState(false);
  const [mnemonicStrength, setMnemonicStrength] = useState<MnemonicStrength>(256);

  useEffect(() => {
    if (!isOpen) setGeneratedMnemonic(undefined);
  }, [isOpen]);

  const createWalletMutation = useCreateWalletMutation({
    validation: () => {
      if (!generatedMnemonic) throw new Error("No mnemonic generated");
    },
    onSuccess: () => {
      setIsOpen(false);
      setGeneratedMnemonic(undefined);
    },
  });

  const handleGenerateMnemonic = () => {
    const mnemonic = generateMnemonic(mnemonicStrength);
    setGeneratedMnemonic(mnemonic);
    setShowMnemonicValidation(false);
  };

  const mnemonicDisplay =
    generatedMnemonic && !showMnemonicValidation ? (
      <Dialog.Content title="Generated Seed Phrase" className="lg:max-w-lg">
        <div id="create-wallet-form" className="grid gap-4">
          <div className="grid gap-2">
            <MnemonicDisplay words={generatedMnemonic.split(" ")} />
            {createWalletMutation.isError && (
              <AlertError>{getErrorMessage(createWalletMutation.error)}</AlertError>
            )}
            <Button
              onClick={() => {
                setShowMnemonicValidation(true);
              }}
              className="mt-2"
            >
              Create Wallet
            </Button>
          </div>
        </div>
      </Dialog.Content>
    ) : null;
  const mnemonicValidation =
    generatedMnemonic && showMnemonicValidation ? (
      <Dialog.Content title="Validate Seed Phrase" className="lg:max-w-lg">
        <div id="create-wallet-form" className="grid gap-4">
          <MnemonicValidation
            expectedWords={generatedMnemonic.split(" ")}
            onSuccess={(mnemonic, password) => {
              // Start the async process
              createWalletMutation.mutate({ mnemonic, password });
              setShowMnemonicValidation(false);
              setIsOpen(false);
              // Return null to indicate no error
              return null;
            }}
            onCancel={() => {
              setShowMnemonicValidation(false);
              setGeneratedMnemonic(undefined);
              setIsOpen(false);
            }}
          />
        </div>
      </Dialog.Content>
    ) : null;
  const content = (
    <Dialog.Content title="Generate Seed Phrase" className="lg:max-w-lg">
      <div className="flex flex-col gap-6 py-2">
        <div className="flex w-full flex-col items-center gap-4 rounded-lg bg-gray-100 p-6 text-center dark:bg-slate-800/70">
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary">
            <ShieldIcon size={30} className="text-primary" />
          </div>

          <h3 className="text-lg font-semibold">Secure Your Wallet</h3>

          <p className="text-sm text-muted-foreground">
            Your recovery phrase is the only way to restore your wallet. Keep it safe and never
            share it.
          </p>

          {/* Word count selection */}
          <div className="flex w-full max-w-sm gap-3">
            <Button
              variant={mnemonicStrength === 128 ? "primary" : "outline"}
              onClick={() => {
                setMnemonicStrength(128);
              }}
              className="flex-1"
            >
              12 Words
            </Button>
            <Button
              variant={mnemonicStrength === 256 ? "primary" : "outline"}
              onClick={() => {
                setMnemonicStrength(256);
              }}
              className="flex-1"
            >
              24 Words
            </Button>
          </div>
        </div>

        {/* Footer actions */}
        <Button
          variant="primary"
          size="lg"
          className="mt-4 w-full"
          onClick={handleGenerateMnemonic}
        >
          Generate {mnemonicStrength === 128 ? "12" : "24"}-Word Recovery Phrase
        </Button>
      </div>
    </Dialog.Content>
  );

  return generatedMnemonic ? (
    <>
      {mnemonicDisplay}
      {mnemonicValidation}
    </>
  ) : (
    content
  );
}
