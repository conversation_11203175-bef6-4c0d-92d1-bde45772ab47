import { isLocal } from "@/context/env";
import { useEffect } from "react";

export function UrlNormalizer() {
  useEffect(() => {
    normalizeUrl();
  }, []);
  return null;
}

function normalizeUrl() {
  let needReplace = false;

  let pathname = location.pathname;

  // Remove index.html from the end of the path (if present).
  // At the time of writing, Google bucket serving doesn't seem to support
  // configuration to change its behavior of redirecting to /a/b/index.html
  // when trying to access resource /a/b (no ending slash). So we edit the
  // URL here to replace the ending /index.html with just /.
  if (location.pathname.endsWith("/index.html")) {
    pathname = pathname.slice(0, -10);
    needReplace = true;
  }

  // Redirect to https if we're not on local env and using http.
  let origin = location.origin;
  if (!isLocal && location.protocol === "http:") {
    origin = `https:${origin.slice(location.protocol.length)}`;
    needReplace = true;
  }

  if (needReplace) {
    location.replace(`${origin}${pathname}${location.search}${location.hash}`);
  }
}
