import { HolderTable } from "@/app/holders/holder-table";
import { NavigationProvider } from "@/lib/navigation";
import { Suspense } from "react";

export default function Holders() {
  return (
    <main className="grid gap-6 px-2 py-8 sm:justify-center">
      <h1 className="text-center text-2xl">Holders</h1>
      <Suspense fallback={<HolderTable />}>
        <NavigationProvider>
          <HolderTable />
        </NavigationProvider>
      </Suspense>
    </main>
  );
}
