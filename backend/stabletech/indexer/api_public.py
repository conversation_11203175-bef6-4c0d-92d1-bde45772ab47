import logging

from fastapi import APIRouter, Header

from common.helius.models import Transaction
from common.psql import DatabaseSession
from stabletech.indexer.helius.queries import save_solana_transactions

router = APIRouter()

logger = logging.getLogger(__name__)


@router.post("/helius/webhook")
def helius_webhook(
    db: DatabaseSession,
    transactions_data: list[Transaction],
    authorization: str = Header(default=None),
):
    """
    Webhook called by <PERSON><PERSON> to notify us of new Solana transactions.
        * Endpoint is manually configured on the Helius platform
    """
    if authorization != "Qw@50rg#WumG":
        logger.error(f"Invalid credential: {authorization}")
        return
    logger.info("Saving on helius webhook: %s", transactions_data)
    transactions = {transaction.signature: transaction for transaction in transactions_data}

    save_solana_transactions(db, transactions_data=transactions)
