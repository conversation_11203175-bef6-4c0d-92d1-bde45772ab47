"""Drop primary keys non unique indexes

Revision ID: 0055
Revises: 0054
Create Date: 2024-06-11 15:23:04.882722

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0055"
down_revision = "0054"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_account_id", table_name="account")
    op.drop_index("ix_alert_id", table_name="alert")
    op.drop_index("ix_aliascoin_id", table_name="aliascoin")
    op.drop_index("ix_authtoken_id", table_name="authtoken")
    op.drop_index("ix_blockfillsinstrument_id", table_name="blockfillsinstrument")
    op.drop_index("ix_bridge_id", table_name="bridge")
    op.drop_index("ix_capitalposition_id", table_name="capitalposition")
    op.drop_index("ix_clienttransaction_id", table_name="clienttransaction")
    op.drop_index("ix_coin_id", table_name="coin")
    op.drop_index("ix_covalentsync_id", table_name="covalentsync")
    op.drop_index("ix_exchange_id", table_name="exchange")
    op.drop_index("ix_farm_id", table_name="farm")
    op.drop_index("ix_lendingprotocol_id", table_name="lendingprotocol")
    op.drop_index("ix_network_id", table_name="network")
    op.drop_index("ix_nft_id", table_name="nft")
    op.drop_index("ix_poolpricealert_id", table_name="poolpricealert")
    op.drop_index("ix_transaction_id", table_name="transaction")
    op.drop_index("ix_transfer_id", table_name="transfer")
    op.drop_index("ix_user_id", table_name="user")
    op.drop_index("ix_wallet_id", table_name="wallet")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_wallet_id", "wallet", ["id"], unique=False)
    op.create_index("ix_user_id", "user", ["id"], unique=False)
    op.create_index("ix_transfer_id", "transfer", ["id"], unique=False)
    op.create_index("ix_transaction_id", "transaction", ["id"], unique=False)
    op.create_index("ix_poolpricealert_id", "poolpricealert", ["id"], unique=False)
    op.create_index("ix_nft_id", "nft", ["id"], unique=False)
    op.create_index("ix_network_id", "network", ["id"], unique=False)
    op.create_index("ix_lendingprotocol_id", "lendingprotocol", ["id"], unique=False)
    op.create_index("ix_farm_id", "farm", ["id"], unique=False)
    op.create_index("ix_exchange_id", "exchange", ["id"], unique=False)
    op.create_index("ix_covalentsync_id", "covalentsync", ["id"], unique=False)
    op.create_index("ix_coin_id", "coin", ["id"], unique=False)
    op.create_index("ix_clienttransaction_id", "clienttransaction", ["id"], unique=False)
    op.create_index("ix_capitalposition_id", "capitalposition", ["id"], unique=False)
    op.create_index("ix_bridge_id", "bridge", ["id"], unique=False)
    op.create_index("ix_blockfillsinstrument_id", "blockfillsinstrument", ["id"], unique=False)
    op.create_index("ix_authtoken_id", "authtoken", ["id"], unique=False)
    op.create_index("ix_aliascoin_id", "aliascoin", ["id"], unique=False)
    op.create_index("ix_alert_id", "alert", ["id"], unique=False)
    op.create_index("ix_account_id", "account", ["id"], unique=False)
    # ### end Alembic commands ###
