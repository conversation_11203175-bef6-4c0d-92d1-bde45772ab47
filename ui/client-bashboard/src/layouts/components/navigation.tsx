import { useAuth } from "@/hooks/useAuth";
import { cx } from "cva";
import { BanknoteArrowUp, BarChart2, LogOut, LucideIcon, ShieldUser } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/router";

const getLinkStyles = (isActive: boolean) =>
  cx(
    "flex h-full cursor-default flex-col items-center justify-center gap-1 p-1 text-xs transition-all",
    isActive ? "text-primary text-shadow-2xs" : "hover:text-primary hover:text-shadow-2xs",
  );

export function LogoutButton() {
  const { logout } = useAuth();
  return (
    <button className={getLinkStyles(false)} onClick={logout}>
      <LogOut size={20} />
      <span>Logout</span>
    </button>
  );
}

interface NavLinkProps {
  readonly href: string;
  readonly icon: LucideIcon;
  readonly label: string;
}

export function NavLink({ href, icon: Icon, label }: NavLinkProps) {
  // Remove trailing slash from href for comparison with pathname
  const normalizedHref = href.endsWith("/") ? href.slice(0, -1) : href;
  const { pathname } = useRouter();
  const isActive = pathname === normalizedHref || pathname.startsWith(`${normalizedHref}/`);

  return (
    <Link
      href={href}
      className={getLinkStyles(isActive)}
      aria-current={isActive ? "page" : undefined}
    >
      <Icon size={20} />
      <span>{label}</span>
    </Link>
  );
}

/**
 * Hook that provides navigation links for the application.
 * Returns a React fragment with the appropriate links based on user role.
 */
export function useLinks() {
  const { user } = useAuth();
  const isAdmin = user?.role === "admin";

  return (
    <>
      {isAdmin && (
        <NavLink href="/portfolio/transactions/new/" icon={ShieldUser} label="Admin" />
      )}
      <NavLink href="/dashboard/" icon={BarChart2} label="Dashboard" />
      <NavLink href="/client/deposit-withdraw/" icon={BanknoteArrowUp} label="Transfer" />
      <LogoutButton />
    </>
  );
}
