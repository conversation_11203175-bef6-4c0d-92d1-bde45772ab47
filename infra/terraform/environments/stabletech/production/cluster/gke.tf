# GKE cluster
resource "google_container_cluster" "primary" {
  name     = "${local.project_id}-gke"
  location = "${var.region}-${var.zone}"

  remove_default_node_pool = true
  initial_node_count       = 1

  network    = "projects/${local.project_id}/global/networks/${google_compute_network.vpc.name}"
  subnetwork = "projects/${local.project_id}/regions/${var.region}/subnetworks/${google_compute_subnetwork.subnet.name}"

  ip_allocation_policy {
    cluster_secondary_range_name  = google_compute_subnetwork.subnet.secondary_ip_range.0.range_name
    services_secondary_range_name = google_compute_subnetwork.subnet.secondary_ip_range.1.range_name
  }

  workload_identity_config {
    workload_pool = "${local.project_id}.svc.id.goog"
  }
  lifecycle {
    ignore_changes = [node_config]
  }
}

# Separately Managed Node Pool
resource "google_container_node_pool" "primary_nodes" {
  name               = "${google_container_cluster.primary.name}-node-pool"
  location           = "${var.region}-${var.zone}"
  cluster            = google_container_cluster.primary.name
  initial_node_count = 1

  node_locations = [
    "${var.region}-${var.zone}",
    "${var.region}-${var.zone_secondary}",
  ]
  node_config {
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/compute",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
    ]

    labels = {
      env = local.project_id
    }

    machine_type = "e2-standard-4"
    tags         = ["gke-node", "${local.project_id}-gke"]
    metadata = {
      disable-legacy-endpoints = "true"
    }
  }

  autoscaling {
    # Per-zone node count
    max_node_count  = 1
    min_node_count  = 1
    location_policy = "BALANCED"
  }

  management {
    auto_repair  = true
    auto_upgrade = true
  }
  upgrade_settings {
    max_surge       = 3
    max_unavailable = 3
  }
}
