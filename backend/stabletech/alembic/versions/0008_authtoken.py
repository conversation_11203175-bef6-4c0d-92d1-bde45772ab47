"""AuthToken

Revision ID: 0008
Revises: 0007
Create Date: 2022-12-16 03:02:30.018673

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0008"
down_revision = "0007"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("pool_name_key", "pool", type_="unique")
    op.create_unique_constraint(None, "pool", ["farm_id", "name", "network_id"])

    op.create_table(
        "authtoken",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("token", sa.String(), nullable=False),
        sa.<PERSON>umn("user_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_authtoken_token"), "authtoken", ["token"], unique=False)
    op.create_index(op.f("ix_authtoken_user_id"), "authtoken", ["user_id"], unique=True)
    op.alter_column("user", "email", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("user", "email", existing_type=sa.VARCHAR(), nullable=True)
    op.drop_index(op.f("ix_authtoken_user_id"), table_name="authtoken")
    op.drop_index(op.f("ix_authtoken_token"), table_name="authtoken")
    op.drop_table("authtoken")

    op.drop_constraint("pool_farm_id_name_network_id_key", "pool", type_="unique")

    op.create_unique_constraint("pool_name_key", "pool", ["name"])
    # ### end Alembic commands ###
