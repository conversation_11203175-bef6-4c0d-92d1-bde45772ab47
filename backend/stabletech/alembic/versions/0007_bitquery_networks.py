"""BitQuery Networks

Revision ID: 0007
Revises: 0006
Create Date: 2022-12-15 20:40:59.878577

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0007"
down_revision = "0006"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Network(Base):
    id = Column(Integer, primary_key=True)
    bitquery_is_enabled: bool = Column(Boolean, default=True)
    name: str = Column(String, nullable=False, index=True)
    __tablename__ = "network"


names_to_add = ("Osmosis", "Terra", "Icon", "Fusion", "Moonriver", "Arbitrum", "Aurora")


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "network",
        sa.Column("bitquery_is_enabled", sa.<PERSON>(), server_default="false", nullable=False),
    )
    # ### end Alembic commands ###

    # Add non-BitQuery networks
    session = Session(bind=op.get_bind())
    for network in session.query(Network).filter(
        Network.name.in_(("Elrond", "Ethereum", "Harmony", "Matic", "Solana", "Tezos"))
    ):
        network.bitquery_is_enabled = True
    for name in names_to_add:
        session.add(Network(name=name))

    session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("network", "bitquery_is_enabled")

    session = Session(bind=op.get_bind())
    session.query(Network).filter(Network.name.in_(names_to_add)).delete()
    session.commit()
    # ### end Alembic commands ###
