"use client";

import { useWalletAuthMessage } from "@/lib/auth";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { Label, ValidationError } from "@/lib/components/form";
import { Input } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { phantomSignature } from "@/lib/phantom";
import { useAddPhantomWallet } from "@/lib/wallets";
import { superstructResolver } from "@hookform/resolvers/superstruct";
import * as RadioGroup from "@radix-ui/react-radio-group";
import { useConnection, useWallet } from "@solana/wallet-adapter-react";
import { useMutation } from "@tanstack/react-query";
import { Plus, Wallet } from "lucide-react";
import Image from "next/image";
import { useId, useState } from "react";
import { useForm } from "react-hook-form";
import { nonempty, object, string, trimmed } from "superstruct";

/**
 * A button that opens a dialog to add a wallet.
 */
export function AddWalletButton(props: Dialog.DialogTriggerProps) {
  const [open, setOpen] = useState(false);
  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Trigger {...props}>
        <span className="relative mr-3 w-8" aria-hidden>
          <Wallet strokeWidth={1} size={20} />
          <Plus size={13} className="absolute top-0 right-0" />
        </span>
        Add wallet
      </Dialog.Trigger>
      <Dialog.Content title="Add wallet">
        <AddPhantomWalletForm
          onSuccess={() => {
            setOpen(false);
          }}
        />
      </Dialog.Content>
    </Dialog.Root>
  );
}

const AddWalletFormStruct = object({
  name: nonempty(trimmed(string())),
});

type AddWalletFormStruct = typeof AddWalletFormStruct.TYPE;

function AddPhantomWalletForm({ onSuccess }: { onSuccess: () => void }) {
  const id = useId();
  const walletId = `${id}wallet`;
  const addressId = `${id}address`;
  const nameId = `${id}name`;

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AddWalletFormStruct>({
    resolver: superstructResolver(AddWalletFormStruct, { coerce: true }),
  });

  const { connection } = useConnection();
  const walletCtx = useWallet();

  const addPhantomWallet = useAddPhantomWallet();
  const walletAuthMessage = useWalletAuthMessage(walletCtx.publicKey?.toString());

  const addWallet = useMutation({
    mutationFn: async ({ name }: { name: string }) => {
      const { signature, address, message } = await phantomSignature({
        connection,
        publicKey: walletCtx.publicKey,
        signTransaction: walletCtx.signTransaction,
        walletAuthMessage,
      });
      return addPhantomWallet.mutateAsync({
        signature,
        name,
        address,
        message,
      });
    },
  });

  const onSubmit = (event: React.FormEvent) => {
    void handleSubmit((data) => {
      addWallet.mutate({ name: data.name }, { onSuccess });
    })(event);
  };

  const error = addWallet.error ?? walletAuthMessage.error;

  const disableSubmit =
    !walletCtx.connected || walletAuthMessage.isPending || addWallet.isPending;

  return (
    <form
      onSubmit={onSubmit}
      className="grid grid-cols-[auto_1fr] justify-items-end gap-x-2 gap-y-3"
    >
      <Label htmlFor={walletId} className="mt-3">
        Wallet
      </Label>
      <RadioGroup.Root
        aria-labelledby={walletId}
        className="flex w-full flex-col items-start rounded-md border p-1 shadow-xs"
        value={walletCtx.connected ? (walletCtx.wallet?.adapter.name ?? "") : ""}
        onValueChange={(value) => {
          const wallet = walletCtx.wallets.find((w) => w.adapter.name === value);
          if (!wallet) return;
          walletCtx.select(wallet.adapter.name);
          wallet.adapter.connect().catch((connectError: unknown) => {
            if (
              connectError instanceof Error &&
              connectError.message.startsWith("User rejected the request")
            ) {
              // can safely ignore
              return;
            }
            throw connectError;
          });
        }}
      >
        {walletCtx.wallets.map((wallet) => (
          <RadioGroup.Item
            key={wallet.adapter.name}
            className="flex items-center gap-2 rounded-md p-1 outline-hidden focus-visible:ring-2 focus-visible:ring-ring/50 aria-checked:bg-primary/15 dark:focus-visible:ring-ring/80 dark:aria-checked:bg-primary/60"
            value={wallet.adapter.name}
          >
            <Image src={wallet.adapter.icon} alt="" width={24} height={24} />
            {wallet.adapter.name}
          </RadioGroup.Item>
        ))}
      </RadioGroup.Root>

      <Label htmlFor={addressId} className="mt-2">
        Address
      </Label>
      <Input
        readOnly
        aria-readonly
        value={walletCtx.publicKey?.toString() ?? "ℹ️ Connect a wallet first"}
        id={addressId}
      />

      <Label htmlFor={nameId} className="mt-2">
        Name
      </Label>
      <Input
        {...register("name")}
        id={nameId}
        placeholder="My wallet"
        aria-invalid={Boolean(errors.name)}
      />
      <ValidationError className="col-start-2 -mt-1">{errors.name?.message}</ValidationError>

      {error && (
        <div className="col-span-full w-full">
          {<AlertError>{getErrorMessage(error)}</AlertError>}
        </div>
      )}

      {/* {!walletCtx.connected && (
        <p className="col-span-full w-full rounded-md bg-secondary/70 p-0.5 text-center text-sm font-light">
          ℹ️ Connect to a wallet first
        </p>
      )} */}

      <div className="col-span-full flex gap-4">
        <Button
          variant="secondary"
          onClick={() => {
            void walletCtx.disconnect();
          }}
          disabled={!walletCtx.connected}
        >
          Disconnect
        </Button>
        <Button type="submit" disabled={disableSubmit}>
          Add
        </Button>
      </div>
    </form>
  );
}
