import type { Key } from "@/lib/secret-store";
import base58 from "bs58";
import { keccak256 } from "ethereum-cryptography/keccak";
import { toHex } from "ethereum-cryptography/utils";

/**
 * Returns the address for a key
 * @returns The address as a string
 */
export function getAddress(key: Key): string {
  return key.metadata.network === "solana"
    ? getSolanaAddress(key.publicKey)
    : getEthereumAddress(key.publicKey);
}

/**
 * Returns a shortened version of an address
 * @returns The shortened address as a string
 */
export function getShortenedAddress(key: Key): string {
  if (key.metadata.network === "solana") {
    const solAddr = getSolanaAddress(key.publicKey);
    return `${solAddr.slice(0, 4)}…${solAddr.slice(-4)}`;
  }
  const ethAddr = getEthereumAddress(key.publicKey);
  return `${ethAddr.slice(0, 6)}…${ethAddr.slice(-4)}`;
}

/**
 * Converts a public key to a Solana address
 * @returns The Solana address as a base58 string
 */
function getSolanaAddress(publicKey: Uint8Array): string {
  return base58.encode(new Uint8Array(publicKey));
}

/**
 * Converts a public key to an Ethereum address
 * @returns The Ethereum address as a hex string with 0x prefix
 */
function getEthereumAddress(publicKey: Uint8Array): string {
  return "0x" + toHex(keccak256(publicKey.slice(1)).slice(-20));
}
