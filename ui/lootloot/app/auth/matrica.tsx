"use client";

import { AlertError } from "@/lib/components/alert";
import btnMatricaDark from "@/lib/components/assets/btn_matrica_signin_dark.svg";
import btnMatricaLight from "@/lib/components/assets/btn_matrica_signin_light.svg";
import { getErrorMessage } from "@/lib/error";
import { useLoginWithMatrica } from "@/lib/matrica-auth";
import Image from "next/image";
import { useRouter } from "next/navigation";

type ButtonProps = React.ComponentPropsWithoutRef<"button">;

export function SignInWithMatrica() {
  const router = useRouter();
  const matricaLogin = useLoginWithMatrica();
  const handleClick = () => {
    matricaLogin.mutate(router);
  };
  return (
    <>
      <SignInButton onClick={handleClick} disabled={matricaLogin.isPending} />
      {matricaLogin.isError && <AlertError>{getErrorMessage(matricaLogin.error)}</AlertError>}
    </>
  );
}

function SignInButton(props: Omit<ButtonProps, "children">) {
  return (
    <button
      {...props}
      className="rounded-sm pt-[3px] hover:opacity-85 focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-60 dark:focus-visible:ring-ring/80"
    >
      <Image
        src={btnMatricaLight}
        alt="Sign in with Matrica"
        width={221}
        height={54}
        className="inline-block dark:hidden"
      />
      <Image
        src={btnMatricaDark}
        alt="Sign in with Matrica"
        width={221}
        height={54}
        className="hidden dark:inline-block"
      />
    </button>
  );
}
