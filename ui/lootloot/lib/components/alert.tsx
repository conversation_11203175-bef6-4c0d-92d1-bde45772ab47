import { But<PERSON> } from "@/lib/components/button";
import { cx } from "class-variance-authority";
import { Check, CircleX } from "lucide-react";

export function AlertError({
  children,
  className,
  onClear,
}: {
  children: React.ReactNode;
  className?: string | undefined;
  onClear?: () => void;
}) {
  return (
    <div
      role="alert"
      className={cx(
        "relative grid w-full min-w-0 items-center gap-3 rounded-lg border border-destructive bg-red-50/80 px-4 py-3 text-sm dark:bg-red-900/40",
        onClear ? "grid-cols-[auto_1fr_auto]" : "grid-cols-[auto_1fr]",
        className,
      )}
    >
      <span>❗</span>
      <span className="min-w-0 break-words">{children}</span>
      {onClear && (
        <Button
          onClick={onClear}
          variant="ghost"
          size="icon"
          className="h-6! w-6! rounded-full! hover:bg-accent/70!"
        >
          <CircleX strokeWidth={1.25} />
        </Button>
      )}
    </div>
  );
}

export function AlertSuccess({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string | undefined;
}) {
  return (
    <div
      role="alert"
      className={cx(
        "relative flex w-full items-center gap-3 rounded-lg border bg-green-50/80 px-4 py-3 text-sm dark:bg-green-900/40",
        className,
      )}
    >
      <span>
        <Check />
      </span>{" "}
      <span>{children}</span>
    </div>
  );
}
