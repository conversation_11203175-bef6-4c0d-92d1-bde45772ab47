import { Card } from "@/components/ui/card";
import { TransactionForClient } from "@/types/schemas";
import { cx } from "cva";
import { useMemo, useState } from "react";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface PortfolioLineChartSahabaProps {
  transactions: readonly TransactionForClient[];
}

interface DataPoint {
  date: Date;
  portfolioValue: number;
  interestEarned: number;
  name: string; // For recharts label
  day: number; // Day of the month
  month: number; // Month (0-11)
  year: number; // Full year
  isFirstInMonth: boolean; // Whether this is the first data point in the month
}

type TimeRange = "month" | "year" | "all";

type DisplayMode = "portfolio" | "earnings";

// Custom currency formatter
const formatCurrency = (value: number) => {
  const num = Math.round(value);
  if (num >= 1_000_000) {
    const millions = num / 1_000_000;
    return "$" + (millions % 1 === 0 ? millions.toFixed(0) : millions.toFixed(1)) + "M";
  } else if (num >= 1000) {
    const thousands = num / 1000;
    return "$" + (thousands % 1 === 0 ? thousands.toFixed(0) : thousands.toFixed(1)) + "K";
  }
  return "$" + num.toLocaleString();
};

// Month names for formatting
const monthNames = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

// Function to generate data points without transactions
function generateDataPoints(startDate: Date, endDate: Date): DataPoint[] {
  const dataPoints: DataPoint[] = [];

  // Create monthly data points from start to end date
  const currentDate = new Date(startDate);
  currentDate.setDate(1); // Start from the first day of the month

  while (currentDate <= endDate) {
    const day = currentDate.getDate();
    const month = currentDate.getMonth();
    const year = currentDate.getFullYear();

    // Check if this is the first data point in this month
    const isFirstInMonth =
      dataPoints.length === 0 ||
      dataPoints.at(-1)?.month !== month ||
      dataPoints.at(-1)?.year !== year;

    dataPoints.push({
      date: new Date(currentDate),
      portfolioValue: 0, // Will be calculated later when applying transactions
      interestEarned: 0, // Will be calculated later when applying transactions
      name: `${month + 1}/${day}/${year.toString().slice(2)}`,
      day,
      month,
      year,
      isFirstInMonth,
    });

    // Move to next month
    currentDate.setMonth(currentDate.getMonth() + 1);
  }

  return dataPoints;
}

// Function to apply transactions to data points
function applyTransactionsToDataPoints(
  dataPoints: DataPoint[],
  transactions: readonly TransactionForClient[],
): DataPoint[] {
  if (dataPoints.length === 0) return dataPoints;

  // Constants for interest calculation
  const ANNUAL_INTEREST_RATE = 0.07; // 7% yearly interest
  const DAILY_INTEREST_RATE = ANNUAL_INTEREST_RATE / 365;

  // Sort transactions by date (oldest first)
  const sortedTransactions = [...transactions].sort(
    (a, b) => new Date(a.confirmedAt).getTime() - new Date(b.confirmedAt).getTime(),
  );

  // Create a timeline of all events (transactions and data points)
  const timeline: {
    date: Date;
    isTransaction: boolean;
    transaction?: TransactionForClient;
    dataPointIndex?: number;
  }[] = [];

  // Add transactions to timeline
  for (const tx of sortedTransactions) {
    timeline.push({
      date: new Date(tx.confirmedAt),
      isTransaction: true,
      transaction: tx,
    });
  }

  // Add data points to timeline
  for (const [index, dataPoint] of dataPoints.entries()) {
    timeline.push({
      date: new Date(dataPoint.date),
      isTransaction: false,
      dataPointIndex: index,
    });
  }

  // Sort timeline by date
  timeline.sort((a, b) => a.date.getTime() - b.date.getTime());

  // Process the timeline
  let runningBalance = 0;
  let totalInterestEarned = 0;
  let lastDate: Date | null = null;
  const updatedDataPoints = [...dataPoints];

  for (const event of timeline) {
    const currentDate = event.date;

    // If we have a previous date, calculate interest for the period
    if (lastDate && runningBalance > 0) {
      const daysDifference =
        (currentDate.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24);
      const interestForPeriod = runningBalance * DAILY_INTEREST_RATE * daysDifference;
      totalInterestEarned += interestForPeriod;
    }

    // If this is a transaction, update the balance
    if (event.isTransaction && event.transaction) {
      const tx = event.transaction;
      const amount = tx.transfers[0]?.amount ? Number.parseFloat(tx.transfers[0].amount) : 0;
      const assetPrice = tx.transfers[0]?.assetPriceUsd
        ? Number.parseFloat(tx.transfers[0].assetPriceUsd)
        : 0;
      const usdAmount = amount * assetPrice;

      // Don't include interest payments in the running balance calculation
      if (!(tx.isInterestPayment || tx.clientAction === "Interest Payment")) {
        if (tx.transfers[0]?.isWithdrawal) {
          runningBalance -= usdAmount;
        } else {
          runningBalance += usdAmount;
        }
      }
    }

    // If this is a data point, update its values
    if (!event.isTransaction && event.dataPointIndex !== undefined) {
      updatedDataPoints[event.dataPointIndex] = {
        ...updatedDataPoints[event.dataPointIndex],
        portfolioValue: runningBalance,
        interestEarned: totalInterestEarned,
      };
    }

    // Update last date
    lastDate = currentDate;
  }

  return updatedDataPoints;
}

export function PortfolioLineChartSahaba({ transactions }: PortfolioLineChartSahabaProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>("all");
  const [displayMode, setDisplayMode] = useState<DisplayMode>("portfolio");

  // Define hardcoded colors
  const portfolioColor = "var(--primary)"; // Desert dune color
  const interestColor = "var(--secondary)"; // Desert sky dark color
  const chartData = useMemo(() => {
    if (transactions.length === 0) return [];
    // Filter transactions based on selected time range
    const now = new Date();
    const filteredTransactions = transactions.filter((tx) => {
      const txDate = new Date(tx.confirmedAt);
      if (timeRange === "month") {
        // Last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(now.getDate() - 30);
        return txDate >= thirtyDaysAgo;
      } else if (timeRange === "year") {
        // Last 365 days
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(now.getFullYear() - 1);
        return txDate >= oneYearAgo;
      }
      // All time
      return true;
    });

    // Handle case when there are no filtered transactions
    if (filteredTransactions.length === 0) return [];

    // Get date range for data points
    const sortedTransactions = [...filteredTransactions].sort(
      (a, b) => new Date(a.confirmedAt).getTime() - new Date(b.confirmedAt).getTime(),
    );

    const firstTransactionDate = new Date(sortedTransactions[0].confirmedAt);
    const startDate = new Date(firstTransactionDate);
    startDate.setMonth(startDate.getMonth() - 1); // Start one month before first transaction

    const endDate = new Date(); // End at today

    // Generate data points without transactions
    const baseDataPoints = generateDataPoints(startDate, endDate);

    // Apply transactions to data points
    const finalDataPoints = applyTransactionsToDataPoints(baseDataPoints, filteredTransactions);

    return finalDataPoints;
  }, [transactions, timeRange]);

  // Handle time range change
  const handleTimeRangeChange = (range: TimeRange) => {
    setTimeRange(range);
  };

  function CustomizedTick(props: { x: number; y: number; payload: { index: number } }) {
    const { x, y, payload } = props;
    const dataPoint = chartData[payload.index];
    if (!dataPoint) return <g />;

    // Check if this is the first data point or if the year has changed
    const isFirstDataPoint = payload.index === 0;
    const previousDataPoint = payload.index > 0 ? chartData[payload.index - 1] : null;
    const isYearChange = previousDataPoint && previousDataPoint.year !== dataPoint.year;
    const shouldShowYear = isFirstDataPoint || isYearChange;

    return (
      <g transform={`translate(${x},${y})`}>
        <text x={0} y={0} dy={16} fill="#555555" fontSize={12} textAnchor="middle">
          {dataPoint.isFirstInMonth ? (
            <>
              <tspan x="0">{dataPoint.day}</tspan>
              <tspan x="0" dy="15">
                {monthNames[dataPoint.month]}
              </tspan>
              {shouldShowYear && (
                <tspan x="0" dy="15" fontSize={10} fill="#888888">
                  {dataPoint.year}
                </tspan>
              )}
            </>
          ) : (
            <tspan x="0">{dataPoint.day}</tspan>
          )}
        </text>
      </g>
    );
  }
  // Custom tooltip component
  const CustomTooltip = ({
    active,
    payload,
  }: {
    active?: boolean;
    payload?: { value: number; payload: DataPoint }[];
  }) => {
    if (active && payload && payload.length) {
      const date = new Date(payload[0].payload.date);
      const day = date.getDate();
      const monthName = monthNames[date.getMonth()];
      const year = date.getFullYear();

      const activeColor = displayMode === "portfolio" ? portfolioColor : interestColor;
      const value = payload[0].value;

      return (
        <div className="rounded bg-white/85 px-3 py-2 text-sm shadow-sm">
          <p className="mb-1 font-medium text-gray-600">{`${monthName} ${day.toString().padStart(2, "0")}, ${year}`}</p>
          <p className="flex items-center font-semibold" style={{ color: activeColor }}>
            <span
              className="mr-1.5 inline-block h-2 w-2 rounded-full"
              style={{ backgroundColor: activeColor }}
            ></span>
            {formatCurrency(value)}
          </p>
        </div>
      );
    }
    return null;
  };

  // Define chart components based on display mode
  let chart, yAxis;
  if (displayMode === "portfolio") {
    chart = (
      <Line
        type="monotone"
        dataKey="portfolioValue"
        name="Portfolio Value"
        stroke={portfolioColor}
        strokeWidth={2}
        dot={false}
        activeDot={{ fill: portfolioColor, stroke: "white", strokeWidth: 1, r: 4 }}
      />
    );
    yAxis = (
      <YAxis
        stroke={portfolioColor}
        tick={{ fill: portfolioColor, fontSize: 13 }}
        tickLine={false}
        axisLine={false}
        tickFormatter={formatCurrency}
      />
    );
  } else {
    chart = (
      <Line
        type="monotone"
        dataKey="interestEarned"
        name="Lifetime Earnings"
        stroke={interestColor}
        strokeWidth={2}
        dot={false}
        activeDot={{ fill: interestColor, stroke: "white", strokeWidth: 1, r: 4 }}
      />
    );
    yAxis = (
      <YAxis
        orientation="left"
        stroke={interestColor}
        tick={{ fill: interestColor, fontSize: 13 }}
        tickLine={false}
        axisLine={false}
        tickFormatter={formatCurrency}
      />
    );
  }
  return (
    <Card>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h2 className="relative mb-2 pb-1.5 text-xl font-medium text-[var(--desert-text-dark)]">
          Portfolio Growth
          <span
            className="absolute bottom-0 left-0 h-0.5 w-6 rounded-sm"
            style={{ backgroundColor: portfolioColor }}
          ></span>
        </h2>

        {/* Minimalist display mode toggle */}
        <div className="mb-4 flex justify-center">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setDisplayMode("portfolio")}
              className={cx(
                "relative px-2 py-1 text-base font-medium transition-all duration-200",
                displayMode === "portfolio"
                  ? "text-primary opacity-100"
                  : "text-[var(--desert-text-dark)] opacity-60",
              )}
            >
              Portfolio Value
              {displayMode === "portfolio" && (
                <span
                  className="absolute bottom-0 left-0 h-0.5 w-full"
                  style={{ backgroundColor: portfolioColor }}
                ></span>
              )}
            </button>
            <span className="text-gray-300">|</span>
            <button
              onClick={() => setDisplayMode("earnings")}
              className={cx(
                "relative px-2 py-1 text-base font-medium transition-all duration-200",
                displayMode === "earnings"
                  ? "text-secondary opacity-100"
                  : "text-[var(--desert-text-dark)] opacity-60",
              )}
            >
              Lifetime Earnings
              {displayMode === "earnings" && (
                <span
                  className="absolute bottom-0 left-0 h-0.5 w-full"
                  style={{ backgroundColor: interestColor }}
                ></span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Minimalist time range selector */}
      <div className="mb-4 flex justify-center">
        <div className="flex items-center space-x-6 text-sm">
          <button
            onClick={() => handleTimeRangeChange("month")}
            className={cx(
              "relative px-2 py-1 text-sm font-medium transition-all duration-200",
              timeRange === "month"
                ? displayMode === "portfolio"
                  ? "text-primary opacity-100"
                  : "text-secondary opacity-100"
                : "text-[var(--desert-text-dark)] opacity-60",
            )}
          >
            30 Days
            {timeRange === "month" && (
              <span
                className="absolute bottom-0 left-0 h-0.5 w-full"
                style={{
                  backgroundColor: displayMode === "portfolio" ? portfolioColor : interestColor,
                }}
              ></span>
            )}
          </button>
          <button
            onClick={() => handleTimeRangeChange("year")}
            className={cx(
              "relative px-2 py-1 text-sm font-medium transition-all duration-200",
              timeRange === "year"
                ? displayMode === "portfolio"
                  ? "text-primary opacity-100"
                  : "text-secondary opacity-100"
                : "text-[var(--desert-text-dark)] opacity-60",
            )}
          >
            1 Year
            {timeRange === "year" && (
              <span
                className="absolute bottom-0 left-0 h-0.5 w-full"
                style={{
                  backgroundColor: displayMode === "portfolio" ? portfolioColor : interestColor,
                }}
              ></span>
            )}
          </button>
          <button
            onClick={() => handleTimeRangeChange("all")}
            className={cx(
              "relative px-2 py-1 text-sm font-medium transition-all duration-200",
              timeRange === "all"
                ? displayMode === "portfolio"
                  ? "text-primary opacity-100"
                  : "text-secondary opacity-100"
                : "text-[var(--desert-text-dark)] opacity-60",
            )}
          >
            All Time
            {timeRange === "all" && (
              <span
                className="absolute bottom-0 left-0 h-0.5 w-full"
                style={{
                  backgroundColor: displayMode === "portfolio" ? portfolioColor : interestColor,
                }}
              ></span>
            )}
          </button>
        </div>
      </div>

      {/* Chart container - more minimalistic */}
      <div className="mt-2 h-80 rounded-lg bg-white/25 p-4">
        {chartData.length === 0 ? (
          /* No data message */
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <div className="mb-2 text-gray-400">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <p className="text-sm text-gray-500">No transaction data available</p>
              <p className="mt-1 text-xs text-gray-400">
                {displayMode === "portfolio" ? "Portfolio value" : "Lifetime earnings"} will
                appear here once you have transactions
              </p>
            </div>
          </div>
        ) : (
          /* Recharts Line Chart */
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{
                top: 5,
                right: 30,
                left: 10,
                bottom: 30,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.00)" />
              <XAxis
                dataKey="name"
                tick={CustomizedTick}
                axisLine={{ stroke: "rgba(0,0,0,0.05)" }}
              />
              {yAxis}
              <Tooltip content={<CustomTooltip />} />
              {chart}
            </LineChart>
          </ResponsiveContainer>
        )}
      </div>
    </Card>
  );
}
