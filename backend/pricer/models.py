from datetime import datetime, timezone
from decimal import Decimal

from sqlalchemy import DateTime, Index, Numeric
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    MappedAsDataclass,
    mapped_column,
)


class Base(MappedAsDataclass, DeclarativeBase, kw_only=True):
    """Base model class for SQLAlchemy."""

    pass


class CoinHistoricalPrice(Base):
    """Model for storing historical price data for coins."""

    __tablename__ = "coin_historical_price"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, init=False)
    coingecko_id: Mapped[str] = mapped_column()
    timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    price_usd: Mapped[Decimal] = mapped_column(Numeric(precision=32, scale=18))
    volume_24h_usd: Mapped[Decimal] = mapped_column(Numeric(precision=32, scale=18))
    market_cap_usd: Mapped[Decimal] = mapped_column(Numeric(precision=32, scale=18))

    __table_args__ = (
        Index(
            "ix_coin_historical_price_coin_timestamp_desc",
            coingecko_id,
            timestamp.desc(),
            unique=True,
        ),
    )


class CoinCollectionLock(Base):
    """Model for managing coin price collection locks and tracking."""

    __tablename__ = "coin_collection_lock"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, init=False)
    coingecko_id: Mapped[str] = mapped_column(unique=True)
    last_updated_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), default=None
    )
    failure_count: Mapped[int] = mapped_column(default=0)
    last_failure_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), default=None
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default_factory=lambda: datetime.now(timezone.utc), init=False
    )
