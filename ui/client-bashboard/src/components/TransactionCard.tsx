import { ConfirmationDialog, useConfirmationDialog } from "@/components/ConfirmationDialog";
import { Toast, useToast } from "@/components/Toast";
import { TransactionEdit } from "@/components/transaction/TransactionEdit";
import { enablePooledFund } from "@/context/env";
import { addressFormatter, formatDecimal, formatTxDate } from "@/helpers/formatters";
import {
  useIndexerTransactionResetMutation,
  useIndexerTransferDeleteMutation,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import {
  Account,
  Transaction,
  TransactionUnclassified,
  Transfer,
  Wallet,
} from "@/types/schemas";
import {
  ArrowRightAltRounded,
  Close,
  Edit,
  MoneyOff,
  RemoveCircleOutline,
  Warning,
} from "@mui/icons-material";
import { Box, IconButton, Link, Stack as MuiStack, Tooltip } from "@mui/material";
import Button from "@mui/material/Button";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Chip from "@mui/material/Chip";
import Grid from "@mui/material/Grid";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { ReactNode, useState } from "react";

interface TransactionCardProps {
  isLoading: boolean;
  transaction: Transaction | TransactionUnclassified;
  showTransferDelete?: boolean;
  startElement?: ReactNode;
  endElement?: ReactNode;
}

const formatAccount = (account: Account, wallet: Wallet) => {
  const ticker = account.coin?.ticker.toUpperCase() ?? account.tickerSymbol;
  if (ticker) {
    return (
      <Stack direction={"row"} gap={2} alignItems={"center"}>
        <Typography color={"secondary"}>{ticker}</Typography>
        <Typography color={"primary"} fontSize={12}>
          {account.name}
        </Typography>
        {account.isPoolToken && <MoneyOff sx={{ fontSize: 16, color: "secondary.main" }} />}
      </Stack>
    );
  }
  if (account.name) {
    return (
      <Typography
        color={
          account.address.toLowerCase() == wallet.address.toLowerCase()
            ? "primary"
            : "secondary"
        }
      >
        {account.name}
      </Typography>
    );
  }

  return <Typography color={"primary"}>{addressFormatter(account.address)}</Typography>;
};

export const TransactionCard = (props: TransactionCardProps) => {
  const { transaction } = props;
  const [isEditing, setIsEditing] = useState(false);
  const [toastProps, toast] = useToast();

  return (
    <Card sx={{ position: "relative" }}>
      <CardContent>
        {isEditing ? (
          <TransactionEdit
            transaction={transaction}
            onSaved={() => {
              setIsEditing(false);
              toast({
                message: "Transaction updated successfully",
                severity: "success",
                timeout: 5000,
              });
            }}
          />
        ) : (
          <TransactionReadonlyContent {...props} />
        )}
      </CardContent>
      <IconButton
        aria-label="Edit"
        title="Edit"
        color={isEditing ? "error" : "secondary"}
        onClick={() => {
          setIsEditing(!isEditing);
        }}
        size="small"
        sx={{ position: "absolute", top: 4, right: 4 }}
      >
        {isEditing ? <Close /> : <Edit />}
      </IconButton>
      <Toast {...toastProps} />
    </Card>
  );
};

function TransactionReadonlyContent(props: TransactionCardProps) {
  const { transaction } = props;
  const [deleteTransfer, transferDeletion] = useIndexerTransferDeleteMutation();
  const [confirmationDialogProps, confirm] = useConfirmationDialog();
  const transfers = [...transaction.transfers].sort((b, a) =>
    a.isWithdrawal === b.isWithdrawal ? 0 : a.isWithdrawal ? -1 : 1,
  );

  return (
    <Grid container spacing={4} alignItems={"center"} columns={15}>
      {props.startElement}

      <Grid item xs={8}>
        <Stack gap={2}>
          <Stack direction="row" gap={4} alignItems="center">
            <Stack spacing={2} flex={3}>
              <Stack direction={"row"} alignItems={"baseline"} spacing={2}>
                <Typography color={"primary"} fontSize={12}>
                  @
                </Typography>
                <Typography color={"secondary"}>
                  {formatTxDate(transaction.confirmedAt)}
                </Typography>
              </Stack>
              <Stack direction={"row"} alignItems={"baseline"} spacing={2}>
                <Typography color={"primary"} fontSize={12}>
                  network
                </Typography>
                <Typography color={"secondary"}>{transaction.network.name}</Typography>
              </Stack>
              <Stack direction={"row"} alignItems={"baseline"} spacing={2}>
                <Typography color={"primary"} fontSize={12}>
                  signer
                </Typography>
                {formatAccount(transaction.signerAccount, transaction.wallet)}
              </Stack>
              <Stack direction={"row"} alignItems={"baseline"} spacing={2}>
                <Typography color={"primary"} fontSize={12}>
                  tx hash
                </Typography>
                <Link
                  href={`${transaction.network.explorerUrl}/tx/${transaction.txHash}`}
                  target={"_blank"}
                  color={"secondary"}
                >
                  {addressFormatter(transaction.txHash)}
                </Link>
              </Stack>
            </Stack>

            <Box flex={5}>
              {transfers.length > 0 ? (
                <Stack gap={2}>
                  {transfers.map((transfer: Transfer) => (
                    <Stack
                      direction="row"
                      key={transfer.id}
                      gap={3}
                      alignItems="center"
                      sx={{
                        "&:not(:last-child)": {
                          borderBottom: "1px solid",
                          borderBottomColor: "divider",
                          paddingBottom: 2,
                        },
                      }}
                    >
                      {renderTransfer(transaction, transfer)}
                      {props.showTransferDelete && (
                        <>
                          <Button
                            aria-label="Delete transfer"
                            title="Delete transfer"
                            color="error"
                            disabled={props.isLoading || transferDeletion.isLoading}
                            onClick={() => {
                              confirm({
                                action: () => deleteTransfer(transfer.id).unwrap(),
                                title: "Delete Transfer",
                                message: (
                                  <Stack gap={4}>
                                    <Typography>
                                      Are you sure you want to delete this transfer?
                                    </Typography>
                                    {renderTransfer(transaction, transfer)}
                                    <Typography color="error">
                                      This action cannot be undone.
                                    </Typography>
                                  </Stack>
                                ),
                              });
                            }}
                            size="small"
                            sx={{ p: 1, m: -1, minWidth: 0, minHeight: 0 }}
                          >
                            <RemoveCircleOutline fontSize="small" />
                          </Button>
                          <ConfirmationDialog {...confirmationDialogProps} />
                        </>
                      )}
                    </Stack>
                  ))}
                </Stack>
              ) : (
                transaction.hasApproval && (
                  <Grid container item justifyContent={"center"}>
                    <Chip label={`Approval`} color={"primary"} variant={"outlined"} />
                  </Grid>
                )
              )}
            </Box>
          </Stack>
          {transaction.comment && (
            <Stack direction="row" alignItems="baseline" gap={2}>
              <Typography color="primary" fontSize={12}>
                comment
              </Typography>
              <Typography color="secondary">{transaction.comment}</Typography>
            </Stack>
          )}
        </Stack>
      </Grid>

      {props.endElement}
    </Grid>
  );
}

interface TransactionCardHistoryProps {
  readonly isLoading: boolean;
  readonly transaction: Transaction;
}

const getCompanyName = (internalId: string | null): string | null => {
  if (!internalId) return null;
  const [companyName] = internalId.split(":");
  return companyName.charAt(0).toUpperCase() + companyName.slice(1);
};

export function TransactionCardHistory(props: TransactionCardHistoryProps) {
  const { isLoading, transaction } = props;
  const [resetTransaction, resetState] = useIndexerTransactionResetMutation();

  const [confirmationDialogProps, confirm] = useConfirmationDialog();

  const handleReset = () => {
    confirm({
      action: () => resetTransaction(transaction.id).unwrap(),
      title: "Reset Transaction",
      message: (
        <Stack gap={4}>
          <Typography>Are you sure you want to reset this transaction?</Typography>
          <Typography
            bgcolor="background.default"
            borderRadius={1}
            fontFamily="monospace"
            px={2}
            py={1}
            sx={{ wordBreak: "break-all" }}
          >
            {transaction.txHash}
          </Typography>
          <Typography fontSize="small">Network: {transaction.network.name}</Typography>
          <Typography fontSize="small">
            Confirmed at {new Date(transaction.confirmedAt).toString()}
          </Typography>
        </Stack>
      ),
    });
  };

  const resetError =
    resetState.error != null
      ? getErrorDetail(resetState.error, { fallback: "Unknown error" })
      : "";
  const resetErrorIcon = resetError.length > 0 && (
    <Tooltip
      title={
        <>
          <span>Failed, click to try again.</span>
          <br />
          <span>Detail: {resetError}</span>
        </>
      }
    >
      <Warning />
    </Tooltip>
  );

  return (
    <>
      <TransactionCard
        isLoading={isLoading}
        transaction={transaction}
        startElement={
          <Grid item xs={2} display="flex" justifyContent="center">
            <MuiStack spacing={1}>
              <Chip label={transaction.action.toUpperCase()} />
              {transaction.internalId && (
                <Chip
                  label={getCompanyName(transaction.internalId)}
                  variant="outlined"
                  color="secondary"
                />
              )}
              {transaction.clientTransactionId && (
                <Chip label={enablePooledFund ? "Fund" : "Client"} variant="outlined" />
              )}
            </MuiStack>
          </Grid>
        }
        endElement={
          <Grid item xs={5} display="flex" justifyContent="center">
            <Button
              color="error"
              variant="outlined"
              disabled={isLoading || resetState.isLoading}
              onClick={handleReset}
              endIcon={resetErrorIcon}
            >
              Reset
            </Button>
          </Grid>
        }
      />
      <ConfirmationDialog {...confirmationDialogProps} />
    </>
  );
}

function renderTransfer(
  transaction: Transaction | TransactionUnclassified,
  transfer: Transfer,
) {
  const color = transfer.isWithdrawal ? "error.main" : "success.main";
  return (
    <Stack gap={2} width="100%">
      <Stack direction="row" gap={2}>
        <Typography color={color}>
          {formatDecimal(Number.parseFloat(transfer.amount))}
        </Typography>
        {formatAccount(transfer.assetAccount, transaction.wallet)}
      </Stack>
      <Stack direction="row" pl={6} gap={2}>
        {formatAccount(transfer.fromAccount, transaction.wallet)}
        <ArrowRightAltRounded />
        {formatAccount(transfer.toAccount, transaction.wallet)}
      </Stack>
    </Stack>
  );
}
