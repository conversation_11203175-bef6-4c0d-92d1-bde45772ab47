import random

from fastapi import APIRouter
from sqlalchemy.exc import IntegrityError

from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from lootloot import schemas
from lootloot.auth.crud import UserManager
from lootloot.heist.bots.crud import (
    NonFungibleInventoryManager,
    Ra<PERSON>EntryManager,
    RaffleManager,
)
from lootloot.schemas import AddRaffle

router = APIRouter()


@router.put("/raffle")
def create_raffle(raffle: AddRaffle, db: DatabaseSession):
    if raffle.winners_count <= 0:
        raise HTTP400Exception("Must have winners")
    if raffle.winners_count > raffle.entries_count:
        raise HTTP400Exception("Cannot have more winners than entries")
    try:
        RaffleManager(db).create(raffle)
        db.commit()
    except IntegrityError:
        raise HTTP400Exception("Secret word already in use - please pick another")


@router.post("/raffle/end")
def end_raffle(secret_word: str, db: DatabaseSession):
    secret_word = secret_word.lower()
    non_fungible_inventory_manager = NonFungibleInventoryManager(db)
    raffle = RaffleManager(db).get_by_secret_word(secret_word)
    if not raffle:
        raise HTTP400Exception("Raffle with that secret word not found")
    if not raffle.is_active:
        raise HTTP400Exception("Raffle already ended")
    raffle.is_active = False
    # Commit to close raffle out
    db.commit()

    entries = RaffleEntryManager(db).get_entries_for_raffle(raffle.id)
    winners_count = min(len(entries), raffle.winners_count)
    prizes = non_fungible_inventory_manager.get_all()
    if len(prizes) < winners_count:
        raise HTTP400Exception("Not enough prizes to draw winners")

    # Random select prizes and winners
    winning_entries = random.sample(entries, winners_count)
    winning_prizes = random.sample(prizes, winners_count)
    for entry, prize in zip(winning_entries, winning_prizes):
        non_fungible_inventory_manager.create_withdrawal(
            prize, entry.user_id, raffle_id=raffle.id
        )
    db.commit()


@router.get("/users", response_model=schemas.ListUsersResp)
def get_users(db: DatabaseSession):
    return schemas.ListUsersResp(items=UserManager(db).all())
