import { addressFormatter } from "@/helpers/formatters";
import {
  useIndexerFarmsQuery,
  useIndexerFarmsWithAccountAddressQuery,
} from "@/store/services/api";
import { Farm, FarmWithAccountAddress } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { Dispatch, SyntheticEvent } from "react";

interface FarmSearchProps {
  disabled?: boolean;
  hidden?: boolean;
  selectedFarm: Farm | null;
  setSelectedFarm: Dispatch<Farm | null>;
}

export const FarmSearch = (props: FarmSearchProps) => {
  const selectedFarm = props.selectedFarm;
  const setSelectedFarm = props.setSelectedFarm;
  const { data: farmsData } = useIndexerFarmsQuery();

  return (
    <Autocomplete
      fullWidth
      hidden={props.hidden}
      disabled={props.disabled}
      renderInput={(params) => <TextField {...params} label="Farm" />}
      options={farmsData || []}
      getOptionLabel={(option) => `${option.name}`}
      onChange={(event: unknown, newValue: Farm | null) => {
        setSelectedFarm(newValue);
      }}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      value={selectedFarm}
    />
  );
};

interface FarmSearchWithAccountAddressProps {
  selectedFarm: FarmWithAccountAddress | null;
  setSelectedFarm: Dispatch<FarmWithAccountAddress | null>;
}

export const FarmSearchWithAccountAddress = (props: FarmSearchWithAccountAddressProps) => {
  const { data: farms } = useIndexerFarmsWithAccountAddressQuery();
  return (
    <Autocomplete
      fullWidth
      renderInput={(params) => <TextField {...params} label="Farm" />}
      options={farms || []}
      getOptionLabel={(option) =>
        `${option.name} | ${option.network?.name ?? "—"} | ${option.accountAddress ? addressFormatter(option.accountAddress) : "—"}`
      }
      isOptionEqualToValue={(option, value) =>
        option.id === value.id &&
        option.accountAddress === value.accountAddress &&
        option.network?.id === value.network?.id
      }
      onChange={(_: SyntheticEvent, newValue: FarmWithAccountAddress | null) => {
        props.setSelectedFarm(newValue);
      }}
      value={props.selectedFarm}
    />
  );
};
