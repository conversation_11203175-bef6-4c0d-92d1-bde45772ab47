name: Lootloot CI
on:
  push:
    branches: [main]
    paths: ["ui/lootloot/**", ".github/workflows/lootloot-ci.yml"]
  pull_request:
    paths: ["ui/lootloot/**", ".github/workflows/lootloot-ci.yml"]
permissions:
  contents: read
  pull-requests: read
jobs:
  checks:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ui/lootloot
    outputs:
      envs-to-deploy: ${{ steps.envs-to-deploy.outputs.env_names }}
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 10
      - uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: pnpm
          cache-dependency-path: ui/lootloot/pnpm-lock.yaml
      - run: pnpm install --frozen-lockfile --ignore-scripts --prefer-offline
      - run: ./node_modules/.bin/tsc --build
      - run: ./node_modules/.bin/eslint --max-warnings 0 .
      - name: Define environments to deploy
        id: envs-to-deploy
        if: github.event_name == 'push'
        run: |
          if [[ $GITHUB_REF == "refs/heads/main" ]]; then
            env_names='["production"]'
          else
            echo "Error: unexpected ref $GITHUB_REF"
            exit 1
          fi
          echo "env_names=$env_names" >> $GITHUB_OUTPUT

  deploy:
    needs: checks
    strategy:
      fail-fast: false
      matrix:
        env-name: ${{ fromJson(needs.checks.outputs.envs-to-deploy) }}
    concurrency:
      group: lootloot-deploy-${{ matrix.env-name }}
      cancel-in-progress: false
    environment: lootloot-${{ matrix.env-name }}
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    permissions:
      contents: read
      id-token: write
    defaults:
      run:
        working-directory: ui/lootloot
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: 10
      - uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: pnpm
          cache-dependency-path: ui/lootloot/pnpm-lock.yaml
      - run: pnpm install --frozen-lockfile --prefer-offline

      - uses: actions/cache@v4
        with:
          path: ui/lootloot/.next/cache
          key: "${{ runner.os }}-nextjs-${{ hashFiles('ui/lootloot/pnpm-lock.yaml') }}-${{ matrix.env-name }}-${{ github.sha }}"
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('ui/lootloot/pnpm-lock.yaml') }}-${{ matrix.env-name }}-
            ${{ runner.os }}-nextjs-${{ hashFiles('ui/lootloot/pnpm-lock.yaml') }}-

      - name: Setup environment variables for building the app
        # .env.local has more precedence than .env
        # see https://nextjs.org/docs/pages/building-your-application/configuring/environment-variables#environment-variable-load-order
        run: cp config/${{ matrix.env-name }}.env .env.local

      - name: Build
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ENVIRONMENT: ${{ matrix.env-name }}
          SENTRY_RELEASE: lootloot@${{ github.sha }}
        run: pnpm build

      - uses: google-github-actions/auth@v2
        id: auth
        with:
          access_token_lifetime: 1200s
          project_id: temet-359508
          workload_identity_provider: projects/639860733581/locations/global/workloadIdentityPools/github-actions/providers/temet-gh-actions

      - uses: google-github-actions/setup-gcloud@v2
        with:
          version: ">= 416.0.0"

      - name: Upload static files
        run: >-
          gcloud storage rsync
          --checksums-only
          --cache-control="public, max-age=31536000, immutable"
          --exclude='.*\.js\.map$'
          -r out/_next/static/
          "gs://${{ vars.WEB_BUCKET }}/_next/static/"

      - name: Upload non-static files
        run: >-
          gcloud storage rsync
          --checksums-only
          --cache-control="public, max-age=300"
          --exclude='.*\.js\.map$'
          --exclude='_next/static/'
          -r out/
          "gs://${{ vars.WEB_BUCKET }}/"
