"""Account.is_pool_token

Revision ID: 0060
Revises: 0059
Create Date: 2025-01-08 09:06:24.317352

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0060"
down_revision = "0059"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "account",
        sa.<PERSON>umn("is_pool_token", sa.<PERSON>(), server_default="false", nullable=False),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("account", "is_pool_token")
    # ### end Alembic commands ###
