import { TransferAdd } from "@/components/add/TransferAdd";
import {
  State as AttributesState,
  TransactionAttributes,
} from "@/components/transaction/Attributes";
import {
  Classification,
  State as ClassificationState,
} from "@/components/transaction/Classification";
import { enableDummyAddTransactions } from "@/context/env";
import { updateStateField } from "@/helpers/state";
import {
  useIndexerTransactionAddMutation,
  useLazyIndexerAccountQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AddTransfer, TransactionAction, User } from "@/types/schemas";
import { Add } from "@mui/icons-material";
import Alert from "@mui/material/Alert";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import LinearProgress from "@mui/material/LinearProgress";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { captureException } from "@sentry/nextjs";
import { useState } from "react";

const ALL_ACTIONS: readonly TransactionAction[] = [
  "bridge",
  "income",
  "loan",
  "pool",
  "spend",
  "stake",
  "swap",
];

export interface TransactionToAdd extends AttributesState, ClassificationState {
  transfers: AddTransfer[];
}

interface Props {
  clientTransactionId?: number | null;
  enabledActions?: TransactionAction[]; // All are enabled when undefined
  isClient?: boolean;
  onSuccess: () => void;
  user?: User | null;
}

export function TransactionAdd(props: Props) {
  const { clientTransactionId, enabledActions, isClient, onSuccess, user } = props;
  const possibleActions = enabledActions ?? ALL_ACTIONS;

  const getEmptyTransactionToAdd = (
    transaction: Partial<TransactionToAdd> = {},
  ): TransactionToAdd => {
    return {
      transfers: [],

      // Attributes
      comment: null,
      confirmedAt: transaction.confirmedAt ?? null,
      feesPaid: transaction.feesPaid ?? "0",
      network: transaction.network ?? null,
      txHash: "",
      wallet: transaction.wallet ?? null,

      // Classification
      action: transaction.action ?? null,
      bridge: null,
      clientTransactionId: clientTransactionId ?? null,
      exchange: null,
      farm: null,
      isClientLoan: false,
      isCollateral: null,
      isDeposit: null,
      isForPooledFund: false,
      lendingProtocol: null,
      user: user ?? null,
    };
  };

  const [isClientLoan, setIsClientLoan] = useState<boolean>(false);
  const [transactionToAdd, setTransactionToAdd] = useState<TransactionToAdd>(
    getEmptyTransactionToAdd(),
  );

  const updateTransactionToAdd = <K extends keyof TransactionToAdd>(
    field: K,
    value: TransactionToAdd[K],
  ) => {
    updateStateField(setTransactionToAdd, field, value);
  };

  const handleTransferChange = (index: number, transfer: AddTransfer | null) => {
    setTransactionToAdd((prevState) => {
      const transfers = [...prevState.transfers];
      if (transfer != null) {
        transfers[index] = transfer;
      } else {
        transfers.splice(index, 1);
      }
      return { ...prevState, transfers };
    });
  };

  const [addTransaction, addTransactionResult] = useIndexerTransactionAddMutation();
  const [fetchAccount] = useLazyIndexerAccountQuery();

  type DummyOption =
    | "Client Deposit"
    | "Client Withdrawal"
    | "Pool Deposit"
    | "Pool Withdrawal"
    | "Spend"
    | "Loan Deposit"
    | "Loan Withdrawal";

  const enabledDummyOptions: DummyOption[] = isClient
    ? ["Client Deposit", "Client Withdrawal", "Spend"]
    : ["Pool Deposit", "Pool Withdrawal", "Spend", "Loan Deposit", "Loan Withdrawal"];

  const setDummyTransactionToAdd = (option: DummyOption) => {
    let action: TransactionAction | null = null;
    let isCollateral = false;
    let isDeposit: boolean | null = null;
    let transfers: AddTransfer[] = [];

    const emptyAccount = { id: null, coin: null, coinId: null, isWallet: null };
    const btcAccount = { ...emptyAccount, address: "btc" };
    const poolAccount = { ...emptyAccount, address: "pool" };
    const usdcAccount = { ...emptyAccount, address: "usdc" };
    const walletAccount = { ...emptyAccount, address: transactionToAdd.wallet?.address || "" };

    switch (option) {
      case "Client Deposit":
        action = "loan";
        isDeposit = true;
        transfers = [
          {
            amount: "1",
            assetAccount: btcAccount,
            fromAccount: { ...emptyAccount, address: "clientLoan" },
            toAccount: walletAccount,
          },
        ];
        break;
      case "Client Withdrawal":
        action = "loan";
        isDeposit = false;
        transfers = [
          {
            amount: "1",
            assetAccount: btcAccount,
            fromAccount: walletAccount,
            toAccount: { ...emptyAccount, address: "clientLoan" },
          },
        ];
        break;
      case "Pool Deposit":
        action = "pool";
        isDeposit = true;
        transfers = [
          {
            amount: "1",
            assetAccount: btcAccount,
            fromAccount: walletAccount,
            toAccount: poolAccount,
          },
          {
            amount: "30000",
            assetAccount: usdcAccount,
            fromAccount: walletAccount,
            toAccount: poolAccount,
          },
        ];
        break;
      case "Pool Withdrawal":
        action = "pool";
        isDeposit = false;
        transfers = [
          {
            amount: "1",
            assetAccount: btcAccount,
            fromAccount: poolAccount,
            toAccount: walletAccount,
          },
          {
            amount: "30000",
            assetAccount: usdcAccount,
            fromAccount: poolAccount,
            toAccount: walletAccount,
          },
        ];
        break;
      case "Spend":
        action = "spend";
        transfers = [
          {
            amount: "1",
            assetAccount: btcAccount,
            fromAccount: walletAccount,
            toAccount: { ...emptyAccount, address: "spend" },
          },
        ];
        break;
      case "Loan Deposit":
        action = "loan";
        isCollateral = true;
        isDeposit = true;
        transfers = [
          {
            amount: "1",
            assetAccount: btcAccount,
            fromAccount: walletAccount,
            toAccount: { ...emptyAccount, address: "loan" },
          },
        ];
        setIsClientLoan(false);
        break;
      case "Loan Withdrawal":
        action = "loan";
        isCollateral = true;
        isDeposit = false;
        transfers = [
          {
            amount: "1",
            assetAccount: btcAccount,
            fromAccount: { ...emptyAccount, address: "loan" },
            toAccount: walletAccount,
          },
        ];
        setIsClientLoan(false);
        break;
    }
    const dummyTransaction: TransactionToAdd = {
      clientTransactionId: clientTransactionId ?? null,
      comment: null,
      confirmedAt: new Date().toISOString(),
      feesPaid: "0.1",
      network: transactionToAdd.network,
      transfers: transfers,
      txHash: Date.now().toString(),
      wallet: transactionToAdd.wallet,

      action: action,
      bridge: null,
      exchange: null,
      farm: null,
      isClientLoan: isClientLoan,
      isCollateral: isCollateral,
      isDeposit: isDeposit,
      isForPooledFund: false,
      lendingProtocol: null,
      user: user || null,
    };

    Promise.all(
      transfers.map((transfer, index) => {
        if (transfer.assetAccount.address && transactionToAdd.network) {
          return fetchAccount({
            address: transfer.assetAccount.address,
            networkId: transactionToAdd.network.id,
          })
            .unwrap()
            .then((account) => {
              if (account) {
                dummyTransaction.transfers[index].assetAccount = account;
              }
            })
            .catch(captureException);
        }
      }),
    ).then(() => {
      setTransactionToAdd(dummyTransaction);
    });
  };

  return (
    <Stack gap={8}>
      {enableDummyAddTransactions && (
        <Stack direction="row" alignItems="center" gap={3}>
          <Typography variant={"caption"}>
            Generate Dummy Data after selecting a Network and Wallet
          </Typography>
          {enabledDummyOptions.map((option) => {
            return (
              <Button
                key={option}
                variant={"outlined"}
                disabled={!transactionToAdd.network || !transactionToAdd.wallet}
                onClick={() => setDummyTransactionToAdd(option)}
              >
                {option}
              </Button>
            );
          })}
        </Stack>
      )}

      <Stack direction="row" gap={8}>
        <TransactionAttributes<TransactionToAdd>
          state={transactionToAdd}
          onChange={updateTransactionToAdd}
        />

        <Classification<TransactionToAdd>
          hideUserSearch={user != null || clientTransactionId != null}
          isClient={isClient}
          possibleActions={possibleActions}
          state={transactionToAdd}
          onChange={updateTransactionToAdd}
        >
          <Stack gap={4} width="100%">
            {transactionToAdd.transfers.map((transfer: AddTransfer, index: number) => {
              return (
                <TransferAdd
                  index={index}
                  key={index}
                  transaction={transactionToAdd}
                  transfer={transfer}
                  onTransferChange={handleTransferChange}
                />
              );
            })}

            <Box height={4} my={-2}>
              {addTransactionResult.isLoading && <LinearProgress color="inherit" />}
            </Box>

            <Stack direction="row" gap={6} justifyContent="space-between">
              <Button
                disabled={
                  !(
                    transactionToAdd.action &&
                    transactionToAdd.confirmedAt &&
                    transactionToAdd.feesPaid &&
                    transactionToAdd.network &&
                    transactionToAdd.txHash &&
                    transactionToAdd.wallet
                  ) || addTransactionResult.isLoading
                }
                variant={"outlined"}
                onClick={() => {
                  if (
                    transactionToAdd.action &&
                    transactionToAdd.confirmedAt &&
                    transactionToAdd.feesPaid &&
                    transactionToAdd.network &&
                    transactionToAdd.txHash &&
                    transactionToAdd.wallet
                  ) {
                    addTransaction([
                      {
                        action: transactionToAdd.action,
                        confirmedAt: transactionToAdd.confirmedAt,
                        comment: transactionToAdd.comment,
                        feesPaid: transactionToAdd.feesPaid,
                        isCollateral: transactionToAdd.isCollateral ?? false,
                        isDeposit: transactionToAdd.isDeposit,
                        isForPooledFund: transactionToAdd.isForPooledFund,
                        transfers: transactionToAdd.transfers,
                        txHash: transactionToAdd.txHash,
                        walletId: transactionToAdd.wallet.id,

                        bridgeId: transactionToAdd.bridge?.id || null,
                        exchangeId: transactionToAdd.exchange?.id || null,
                        farmId: transactionToAdd.farm?.id || null,
                        lendingProtocolId: transactionToAdd.lendingProtocol?.id || null,
                        networkId: transactionToAdd.network.id,
                        userId: transactionToAdd.user?.id || null,
                      },
                      transactionToAdd.clientTransactionId || null,
                    ]).then((result) => {
                      if ("error" in result) {
                        return;
                      }
                      setTransactionToAdd(getEmptyTransactionToAdd(transactionToAdd));
                      onSuccess();
                    });
                  }
                }}
              >
                Add New Transaction
              </Button>
              <Button
                disabled={!transactionToAdd?.action}
                onClick={() => {
                  updateTransactionToAdd("transfers", [
                    ...transactionToAdd.transfers,
                    {
                      amount: "0",
                      assetAccount: { address: "" },
                      fromAccount: { address: "" },
                      toAccount: { address: "" },
                    },
                  ]);
                }}
              >
                <Add sx={{ mr: 1 }} />
                Transfer
              </Button>
            </Stack>
          </Stack>

          {addTransactionResult.error && (
            <Alert severity="error">{getErrorDetail(addTransactionResult.error)}</Alert>
          )}
        </Classification>
      </Stack>
    </Stack>
  );
}
