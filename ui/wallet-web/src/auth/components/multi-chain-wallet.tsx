import { UnifiedAccount } from "@/auth/accounts";
import { NetworkWalletSelector } from "@/auth/components/network-wallet-selector";

interface MultiChainWalletProps {
  accounts: UnifiedAccount[];
  onAccountChange: (accountId: string) => void;
}

export function MultiChainWallet({ accounts, onAccountChange }: MultiChainWalletProps) {
  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-center text-lg">Multi-Chain Wallet</h3>
      {accounts.length > 0 ? (
        <NetworkWalletSelector onAccountChange={onAccountChange} />
      ) : (
        <div className="text-center text-sm text-gray-500">
          No wallets found. Create or import a wallet to get started.
        </div>
      )}
    </div>
  );
}
