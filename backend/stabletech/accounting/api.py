from io import BytesIO

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from openpyxl import Workbook
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet

from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from stabletech import schemas
from stabletech.accounting.bitcointax.transaction_matching import (
    match_imported_capital_positions,
)
from stabletech.accounting.crud import AccountedTransactionManager, CapitalPositionManager
from stabletech.accounting.kinds import OpenPositionSummary
from stabletech.accounting.models import CapitalPosition

router = APIRouter()


@router.delete("/capital-positions")
def delete_capital_positions(db: DatabaseSession, year: int):
    CapitalPositionManager(db).delete_imported_by_year(year)
    db.commit()


@router.get("/capital-positions/generate-tax-form-8949/{year}")
def get_capital_positions_xls(db: DatabaseSession, year: int) -> StreamingResponse:
    """Generate XLS file with capital positions for a given year, with short-term and long-term sections."""
    short_term_positions, long_term_positions = CapitalPositionManager(db).get_by_year(year)

    wb = Workbook()
    ws: Worksheet = wb.active  # type: ignore
    ws.title = f"Form 8949 {year}"

    headers = [
        "Description (a)",
        "Date Acquired (b)",
        "Date Sold (c)",
        "Proceeds (d)",
        "Cost Basis (e)",
        "Adjustment Code (f)",
        "Adjustment Amount (g)",
        "Gain or loss (h)",
    ]

    # Set column widths - description column is 30, others are 15
    ws.column_dimensions["A"].width = 30  # Description column
    for col in range(2, len(headers) + 1):
        ws.column_dimensions[chr(64 + col)].width = 18

    # Helper function to populate a section
    def populate_section(start_row: int, positions: list[CapitalPosition]) -> int:
        # Add headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            # Right align date columns
            cell.alignment = Alignment(horizontal="left")

        # Add data rows
        current_row = start_row + 1
        for position in positions:
            # Remove trailing zeros from amount
            amount_str = f"{float(position.amount):.10f}".rstrip("0").rstrip(".")
            description = f"{amount_str} {position.coin.ticker.upper()}"
            ws.cell(row=current_row, column=1, value=description)
            bought_cell = ws.cell(
                row=current_row, column=2, value=position.bought_at.strftime("%m/%d/%y")
            )
            bought_cell.alignment = Alignment(horizontal="right")

            sold_cell = ws.cell(
                row=current_row,
                column=3,
                value=position.sold_at.strftime("%m/%d/%y"),
            )
            sold_cell.alignment = Alignment(horizontal="right")
            ws.cell(row=current_row, column=4, value=int(position.sold_usd_amount))
            ws.cell(row=current_row, column=5, value=int(position.bought_usd_amount))
            # Columns 6 (Adjustment Code) and 7 (Adjustment Amount) are left empty
            ws.cell(row=current_row, column=8, value=int(position.profit))
            current_row += 1

        return current_row

    # Add title
    ws.cell(row=1, column=1, value="Form 8949 Statement")

    # Part I - Short-term positions
    ws.cell(row=3, column=1, value="Part I - (Short-Term)")
    current_row = populate_section(4, short_term_positions)

    # Add a blank row for separation
    current_row += 1

    # Part II - Long-term positions
    ws.cell(row=current_row, column=1, value="Part II - (Long-Term)")
    populate_section(current_row + 1, long_term_positions)

    # Save to BytesIO
    excel_file = BytesIO()
    wb.save(excel_file)
    excel_file.seek(0)

    return StreamingResponse(
        excel_file,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f'attachment; filename="form_8949_{year}.xlsx"'},
    )


@router.get("/capital-positions", response_model=schemas.CapitalPositionSummaryList)
def get_capital_positions(
    db: DatabaseSession,
    generated_only: bool = False,
    user_id: int | None = None,
    year: int | None = None,
) -> schemas.CapitalPositionSummaryList:
    summary, latest_capital_positions, totals = CapitalPositionManager(
        db
    ).get_capital_positions_summary(generated_only, user_id=user_id, year=year)
    if not summary:
        return schemas.CapitalPositionSummaryList(
            summary=[],
            latest_capital_positions=[],
            total_cost=0,
            total_pnl=0,
            total_proceeds=0,
            total_volume=0,
        )

    return schemas.CapitalPositionSummaryList(
        summary=[
            schemas.CapitalPositionSummary(
                coin=schemas.Coin.model_validate(coin),
                cost=cost,
                pnl=pnl,
                proceeds=proceeds,
                volume=volume,
            )
            for coin, cost, pnl, proceeds, volume in summary
        ],
        latest_capital_positions=[
            schemas.CapitalPosition.model_validate(capital_position)
            for capital_position in latest_capital_positions
        ],
        total_cost=totals[0],
        total_pnl=totals[1],
        total_proceeds=totals[2],
        total_volume=totals[3],
    )


@router.get("/capital-positions/coin/{coin_id}", response_model=schemas.CapitalPositionSummary)
def get_capital_positions_by_coin(
    db: DatabaseSession,
    coin_id: int,
    generated_only: bool = False,
    user_id: int | None = None,
    year: int | None = None,
) -> schemas.CapitalPositionSummary:
    summary, latest_capital_positions = CapitalPositionManager(db).get_summary_by_coin(
        coin_id, generated_only, user_id=user_id, year=year
    )
    summary = summary[0]
    return schemas.CapitalPositionSummary(
        coin=summary[0],
        cost=summary[1],
        pnl=summary[2],
        proceeds=summary[3],
        volume=summary[4],
        latest_capital_positions=[
            schemas.CapitalPosition.model_validate(capital_position)
            for capital_position in latest_capital_positions
        ],
    )


@router.post("/capital-positions/regenerate")
def regenerate_capital_positons(
    db: DatabaseSession, user_id: int | None = None, year: int | None = None
):
    try:
        failed_matchings = match_imported_capital_positions(db)
        if failed_matchings:
            raise HTTP400Exception(
                detail=f"Failed to match some imported capital positions ids [{', '.join(str(match.csv_row) for match in failed_matchings)}]",
            )
    except Exception as e:
        db.rollback()
        if isinstance(e, HTTP400Exception):
            raise
        raise HTTP400Exception(
            detail=f"Failed to regenerate capital positions: {str(e)}"
        ) from e


def aggregate_open_positions(
    open_positions: list[OpenPositionSummary],
) -> list[schemas.OpenPositionSummary]:
    # Went this route because the query would get unnecessarily complex
    by_alias_coin = {}
    for open_position in open_positions:
        coin, _, _, _ = open_position
        alias_coin = coin.alias_coin.coin if coin.alias_coin else coin
        if alias_coin not in by_alias_coin:
            by_alias_coin[alias_coin] = []
        by_alias_coin[alias_coin].append(open_position)

    return [
        schemas.OpenPositionSummary(
            coin=alias_coin,
            open_confirmed_at=max(
                coin_open_positions, key=lambda x: x.open_confirmed_at
            ).open_confirmed_at,
            amount=sum(x.amount for x in coin_open_positions),
            avg_cost=(
                sum(x.amount * x.avg_cost for x in coin_open_positions)
                / sum(x.amount for x in coin_open_positions)
            ),
        )
        for alias_coin, coin_open_positions in by_alias_coin.items()
    ]


@router.get("/open-positions", response_model=schemas.OpenPositionSummaryList)
def get_open_positions(db: DatabaseSession) -> schemas.OpenPositionSummaryList:
    open_positions, latest_accounted_transactions = AccountedTransactionManager(
        db
    ).get_open_positions_summary()
    aggregated_open_positions = aggregate_open_positions(open_positions)
    return schemas.OpenPositionSummaryList(
        summaries=aggregated_open_positions,
        latest_accounted_transactions=[
            schemas.AccountedTransaction.model_validate(accounted_transaction)
            for accounted_transaction in latest_accounted_transactions
        ],
    )


@router.get("/open-positions/coin/{coin_id}", response_model=schemas.OpenPositionSummary)
def get_open_positions_by_coin(
    db: DatabaseSession, coin_id: int
) -> schemas.OpenPositionSummary:
    open_positions, latest_accounted_transactions = AccountedTransactionManager(
        db
    ).get_open_positions_by_coin(coin_id)
    aggregated_open_positions = aggregate_open_positions(open_positions)
    assert (
        len(aggregated_open_positions) == 1
    ), f"Expected exactly one open position for coin_id={coin_id} but got {aggregated_open_positions}"
    open_position = aggregated_open_positions[0]
    open_position.latest_accounted_transactions = [
        schemas.AccountedTransaction.model_validate(accounted_transaction)
        for accounted_transaction in latest_accounted_transactions
    ]
    return open_position
