"""auth

Revision ID: 0002
Revises: 0001
Create Date: 2023-12-01 16:38:13.464055

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0002"
down_revision = "0001"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "authtoken",
        sa.Column("id", sa.String(), nullable=False),
        sa.<PERSON>umn("user_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_authtoken_user_id"), "authtoken", ["user_id"], unique=True)
    op.add_column("user", sa.Column("first_name", sa.String(), nullable=True))
    op.add_column("user", sa.Column("last_name", sa.String(), nullable=True))
    op.add_column("user", sa.<PERSON>umn("picture_url", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "picture_url")
    op.drop_column("user", "last_name")
    op.drop_column("user", "first_name")
    op.drop_index(op.f("ix_authtoken_user_id"), table_name="authtoken")
    op.drop_table("authtoken")
    # ### end Alembic commands ###
