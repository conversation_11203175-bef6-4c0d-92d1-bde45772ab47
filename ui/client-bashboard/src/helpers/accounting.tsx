import { getCoinDisplayName } from "@/helpers/coin";
import { AccountedTransaction } from "@/types/schemas";

export function getNumber(price: number | string | null | undefined): number {
  if (!price) return 0;
  if (typeof price === "number") {
    return price;
  }
  return Number.parseFloat(price);
}

export function getAccountedTransactionCoinDisplayName(row: AccountedTransaction): string {
  return row.coin ? getCoinDisplayName(row.coin) : row.tickerSymbol;
}
