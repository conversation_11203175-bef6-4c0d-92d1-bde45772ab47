import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);

export const TIME_FORMAT = "hh:mm:ss A";
export const DATE_FORMAT = "MM/DD/YY";
export const DATETIME_FORMAT = "MM/DD/YY hh:mm:ss A";

type InputDate = dayjs.ConfigType;

export const formatTxDate = (date: InputDate) => dayjs(date).format("ddd D MMM @ h:mm A");
export const formatDate = (date: InputDate) => dayjs(date).format(DATE_FORMAT);
export const formatDateTime = (
  date: InputDate,
  { template = DATETIME_FORMAT, utc = false }: { template?: string; utc?: boolean } = {},
) => {
  const builder = utc ? dayjs.utc : dayjs;
  return builder(date).format(template);
};

export const parseDateTime = (date: string, format = DATETIME_FORMAT, utc = false) => {
  const builder = utc ? dayjs.utc : dayjs;
  return builder(date, format);
};

export const validDateFormat = (date: string) => {
  if (!date) return;
  if (date.length < 6 && dayjs(date, "H:mm").isValid()) {
    return "H:mm";
  }
  if (date.length < 8 && dayjs(date, "MM-YYYY").isValid()) {
    return "MM-YYYY";
  }
  if (date.length < 9 && dayjs(date, "H:mm:ss").isValid()) {
    return "H:mm:ss";
  }
  if (date.length < 11 && dayjs(date, "MM-DD-YYYY").isValid()) {
    return "MM-DD-YYYY";
  }
  for (const format of [
    "MM-DD-YYYY H:mm",
    "MM-DD-YYYY H:mm:s",
    "MM-DD-YYYY H:mm:s Z",
    "MM-DD-YY hh:mm:ss A Z",
    "YYYY-MM-DDTHH:mm:ssZ",
  ]) {
    if (dayjs(date, format).isValid()) {
      return format;
    }
  }
};

// Multiply by 1 to remove trailing zeros
export const formatDecimal = (decimal: number) => (1 * decimal).toLocaleString("en-US");

// Takes a 0.2 to 20%
export const formatPercentage = (percentage: number) => `${(percentage * 100).toFixed(2)}%`;

export const currencyFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

export const diffCurrencyFormatter = new Intl.NumberFormat("en-US", {
  ...currencyFormatter.resolvedOptions(),
  signDisplay: "always",
});

export const numberFormatterMax20D = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 20, // 20 is the maximum supported
  minimumFractionDigits: 0,
});

export function ellipsisDecimals(decimals: number, numStr: string) {
  if (decimals <= 0) return numStr;
  const [whole = "", fraction = ""] = numStr.split(".");
  if (!fraction || fraction.length <= decimals) return numStr;
  return `${whole}.${fraction.slice(0, decimals)}…`;
}

export const addressFormatter = (address: string) => {
  if (address.length <= 10) return address;
  return `${address.slice(0, 5)}...${address.slice(-4)}`;
};

export const capitalizeWord = (word: string) => {
  return word.charAt(0).toUpperCase() + word.slice(1);
};
