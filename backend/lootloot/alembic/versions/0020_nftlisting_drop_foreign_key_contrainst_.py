"""NFTListing drop foreign key contrainst on NFT

Revision ID: 0020
Revises: 0019
Create Date: 2024-03-12 16:45:28.435991

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0020"
down_revision = "0019"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("nftlisting_nft_address_uri_fkey", "nftlisting", type_="foreignkey")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        "nftlisting_nft_address_uri_fkey",
        "nftlisting",
        "nft",
        ["nft_address_uri"],
        ["address_uri"],
    )
    # ### end Alembic commands ###
