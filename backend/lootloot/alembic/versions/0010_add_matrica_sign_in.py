"""Add matrica sign in

Revision ID: 0010
Revises: 0009
Create Date: 2024-01-25 17:42:11.375582

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0010"
down_revision = "0009"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column("user", sa.Column("matrica_id", sa.String(), nullable=True))
    op.add_column("user", sa.Column("matrica_username", sa.String(), nullable=True))
    op.create_unique_constraint("user_matrica_id_key", "user", ["matrica_id"])

    # We're deleting all UserWallets here
    op.execute("DELETE FROM userwallet")
    op.add_column("userwallet", sa.Column("origin", sa.String(), nullable=False))
    op.add_column("userwallet", sa.Column("sequence_number", sa.Integer(), nullable=False))

    op.drop_constraint("userwallet_user_id_address_key", "userwallet", type_="unique")
    op.create_unique_constraint(
        "userwallet_user_id_name_key", "userwallet", ["user_id", "name"]
    )


def downgrade() -> None:
    op.drop_constraint("userwallet_user_id_name_key", "userwallet", type_="unique")
    op.create_unique_constraint(
        "userwallet_user_id_address_key", "userwallet", ["user_id", "address"]
    )
    op.drop_column("userwallet", "sequence_number")
    op.drop_column("userwallet", "origin")
    op.drop_constraint("user_matrica_id_key", "user", type_="unique")
    op.drop_column("user", "matrica_username")
    op.drop_column("user", "matrica_id")
