"""Add OP Network

Revision ID: 0025
Revises: 0024
Create Date: 2023-06-14 23:43:22.972654

"""

from alembic import op
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0025"
down_revision = "0024"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Network(Base):
    id = Column(Integer, primary_key=True)
    covalent_chain_id: int = Column(Integer)
    explorer_url: str = Column(String)
    name: str = Column(String, nullable=False, index=True, unique=True)
    __tablename__ = "network"


def upgrade() -> None:
    session = Session(bind=op.get_bind())
    name = "Optimism"
    if not session.query(Network).filter_by(name=name).first():
        session.add(Network(name=name))
    session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
