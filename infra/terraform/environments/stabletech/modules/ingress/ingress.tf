variable "domain" {}
variable "managed_zone_name" {}
variable "namespace" {}
variable "service_name" {}
variable "service_port" {}

locals {
  name = replace(var.domain, ".", "-")
}

resource "google_compute_global_address" "ipv4" {
  name       = "${local.name}-ipv4"
  ip_version = "IPV4"
}

resource "google_dns_record_set" "dns_record" {
  name         = "${var.domain}."
  type         = "A"
  ttl          = 300
  managed_zone = var.managed_zone_name
  rrdatas      = [google_compute_global_address.ipv4.address]
}

resource "kubernetes_manifest" "ssl_cert" {
  manifest = {
    apiVersion = "networking.gke.io/v1"
    kind       = "ManagedCertificate"

    metadata = {
      name      = local.name
      namespace = var.namespace
    }

    spec = {
      domains = [var.domain]
    }
  }
  depends_on = [google_dns_record_set.dns_record]
}

resource "kubernetes_ingress_v1" "ingress" {
  wait_for_load_balancer = true
  metadata {
    name      = local.name
    namespace = var.namespace
    annotations = {
      "kubernetes.io/ingress.allow-http"            = "false"
      "kubernetes.io/ingress.class"                 = "gce"
      "kubernetes.io/ingress.global-static-ip-name" = google_compute_global_address.ipv4.name
      "networking.gke.io/managed-certificates"      = kubernetes_manifest.ssl_cert.manifest.metadata.name
    }
  }
  spec {
    rule {
      http {
        path {
          backend {
            service {
              name = var.service_name
              port {
                number = var.service_port
              }
            }
          }
        }
      }
    }
  }
}
