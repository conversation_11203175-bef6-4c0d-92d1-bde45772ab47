import { ManageSecurityButton } from "@/auth/security";
import { User } from "@/types/schemas";

interface UserProfileProps {
  user: User;
}

export function UserProfile({ user }: UserProfileProps) {
  return (
    <div className="flex flex-col items-center gap-2">
      {user.picture_url ? (
        <img
          className="mb-2 rounded-full shadow-md"
          src={user.picture_url}
          alt=""
          width={70}
          height={70}
          referrerPolicy="no-referrer"
        />
      ) : (
        <div className="mb-2 grid h-[70px] w-[70px] place-items-center overflow-hidden rounded-full bg-gray-200 text-4xl shadow-md">
          👤
        </div>
      )}
      <span className="font-medium">
        {[user.first_name, user.last_name].filter(Boolean).join(" ")}
      </span>
      <span className="text-sm font-light">{user.email}</span>

      <ManageSecurityButton />
    </div>
  );
}
