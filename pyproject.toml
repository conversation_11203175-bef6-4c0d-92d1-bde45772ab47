[project]
name = "temet"
version = "0"
requires-python = ">=3.12"

[tool.black]
line-length = 96
target-version = ['py312']
required-version = "25"

[tool.isort]
line_length = 96
profile = "black"
src_paths = ["backend"]

[tool.pyright]
pythonVersion = "3.12"
pythonPlatform = "Linux"
deprecateTypingAliases = true
strictListInference = true
strictDictionaryInference = true
strictSetInference = true
typeCheckingMode = "standard"
executionEnvironments = [{ root = "backend" }]

[tool.ruff]
line-length = 96
required-version = "~=0.11.9"
src = ["backend"]

[tool.ruff.lint]
select = ["E4", "E7", "E9", "F", "A"]
ignore = ["E711", "E712"]
