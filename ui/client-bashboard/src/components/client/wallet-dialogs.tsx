import {
  useAddWalletMutation,
  useArchiveWalletMutation,
  useEditWalletMutation,
} from "@/api/wallets";
import { Alert } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ConfirmationButton } from "@/components/ui/confirmation-dialog";
import * as Dialog from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Spinner } from "@/components/ui/spinner";
import { getErrorDetail } from "@/store/services/helpers";
import { AddWallet, Network, Wallet } from "@/types/schemas";
import { valibotResolver } from "@hookform/resolvers/valibot";
import { cx } from "cva";
import { Trash2 } from "lucide-react";
import React, { useEffect, useId, useRef, useState } from "react";
import { Control, Controller, useForm } from "react-hook-form";
import { toast } from "sonner";
import * as v from "valibot";

// Define the wallet form schema
const addWalletSchema = v.object({
  name: v.pipe(v.string(), v.minLength(1, "Wallet name is required")),
  address: v.pipe(v.string(), v.minLength(1, "Wallet address is required")),
  networkIds: v.pipe(v.array(v.number()), v.minLength(1, "Select at least one network")),
});

// Define edit wallet schema (address is not editable)
const editWalletSchema = v.object({
  name: v.pipe(v.string(), v.minLength(1, "Wallet name is required")),
  networkIds: v.pipe(v.array(v.number()), v.minLength(1, "Select at least one network")),
});

type AddWalletFormData = v.InferOutput<typeof addWalletSchema>;
type EditWalletFormData = v.InferOutput<typeof editWalletSchema>;

interface AddWalletDialogProps {
  children: React.ReactNode;
  networks: readonly Network[];
}

export function AddWalletDialog({ children, networks }: AddWalletDialogProps) {
  const id = useId();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    control,
  } = useForm({
    resolver: valibotResolver(addWalletSchema),
    defaultValues: {
      name: "",
      address: "",
      networkIds: [],
    },
  });

  const addWalletMutation = useAddWalletMutation();
  const { reset: resetMutation } = addWalletMutation;

  const [isOpen, setIsOpen] = useState(false);

  const handleOpenChange = (open: boolean) => {
    if (open) {
      // Reset form when dialog opens
      reset({ name: "", address: "", networkIds: [] });
      resetMutation();
    }
    setIsOpen(open);
  };

  // Handle form submission
  const onSubmit = (data: AddWalletFormData) => {
    const newWallet: AddWallet = {
      name: data.name,
      address: data.address,
      networkIds: data.networkIds,
    };

    addWalletMutation.mutate(newWallet, {
      onSuccess: () => {
        // Close the dialog and show success message
        setIsOpen(false);
        toast.success("Wallet added successfully");
      },
    });
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Dialog.Content title="Add Wallet" aria-describedby="" width="lg">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
          {addWalletMutation.error && (
            <Alert variant="error" onClear={() => addWalletMutation.reset()}>
              {getErrorDetail(addWalletMutation.error, { fallback: "Failed to add wallet" })}
            </Alert>
          )}

          <div>
            <label htmlFor={`wallet-name-${id}`} className="mb-2 block text-sm font-medium">
              Wallet Name
            </label>
            <Input
              id={`wallet-name-${id}`}
              {...register("name")}
              placeholder="Enter wallet name"
            />
            {errors.name && (
              <p className="mt-1 text-xs text-destructive">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor={`wallet-address-${id}`} className="mb-2 block text-sm font-medium">
              Wallet Address
            </label>
            <Input
              id={`wallet-address-${id}`}
              {...register("address")}
              placeholder="Enter wallet address"
            />
            {errors.address && (
              <p className="mt-1 text-xs text-destructive">{errors.address.message}</p>
            )}
          </div>

          <div>
            <span className="mb-2 block text-sm font-medium">Networks</span>
            {renderNetworkIdsControl(control, networks, id)}
            {errors.networkIds && (
              <p className="mt-1 text-xs text-destructive">{errors.networkIds.message}</p>
            )}
          </div>

          <div className="mt-4 flex justify-end gap-2">
            <Dialog.Close asChild>
              <Button type="button" variant="outline" disabled={addWalletMutation.isPending}>
                Cancel
              </Button>
            </Dialog.Close>
            <Button type="submit" disabled={addWalletMutation.isPending}>
              {addWalletMutation.isPending ? (
                <>
                  <Spinner className="mr-2 size-4" />
                  Adding...
                </>
              ) : (
                "Add Wallet"
              )}
            </Button>
          </div>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
}

interface EditWalletDialogProps {
  children: React.ReactNode;
  networks: readonly Network[];
  selectedWallet: Wallet;
}

export function EditWalletDialog({
  children,
  networks,
  selectedWallet,
}: EditWalletDialogProps) {
  const id = useId();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    control,
  } = useForm({
    resolver: valibotResolver(editWalletSchema),
    defaultValues: {
      name: "",
      networkIds: [],
    },
  });

  const editWalletMutation = useEditWalletMutation();
  const { reset: resetMutation } = editWalletMutation;

  const [isOpen, setIsOpen] = useState(false);
  const selectedWalletIdRef = useRef(selectedWallet.id);

  const resetDialog = () => {
    selectedWalletIdRef.current = selectedWallet.id;
    reset({
      name: selectedWallet.name,
      networkIds: selectedWallet.networks.map((network) => network.id),
    });
    resetMutation();
  };

  const handleOpenChange = (open: boolean) => {
    if (open) resetDialog();
    setIsOpen(open);
  };

  // Reset form in case selectedWallet changes while open
  useEffect(() => {
    if (!isOpen) return;
    if (selectedWallet.id === selectedWalletIdRef.current) return;
    resetDialog();
  });

  // Handle form submission
  const onSubmit = (data: EditWalletFormData) => {
    editWalletMutation.mutate(
      {
        id: selectedWallet.id,
        name: data.name,
        networkIds: data.networkIds,
      },
      {
        onSuccess: () => {
          setIsOpen(false);
          toast.success("Wallet updated successfully");
        },
      },
    );
  };

  // Archive wallet functionality
  const archiveWalletMutation = useArchiveWalletMutation();

  const archiveWalletAction = async () => {
    await archiveWalletMutation.mutateAsync(selectedWallet.id);
    setIsOpen(false);
    toast.success("Wallet removed successfully");
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Dialog.Content title="Edit Wallet" aria-describedby="" width="lg">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
          {editWalletMutation.error && (
            <Alert variant="error" onClear={() => editWalletMutation.reset()}>
              {getErrorDetail(editWalletMutation.error, {
                fallback: "Failed to update wallet",
              })}
            </Alert>
          )}

          <div>
            <label
              htmlFor={`edit-wallet-name-${id}`}
              className="mb-2 block text-sm font-medium"
            >
              Wallet Name
            </label>
            <Input
              id={`edit-wallet-name-${id}`}
              {...register("name")}
              placeholder="Enter wallet name"
            />
            {errors.name && (
              <p className="mt-1 text-xs text-destructive">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label
              htmlFor={`edit-wallet-address-${id}`}
              className="mb-2 block text-sm font-medium"
            >
              Wallet Address
            </label>
            <Input
              id={`edit-wallet-address-${id}`}
              value={selectedWallet.address}
              disabled
              className="bg-muted"
            />
            <p className="mt-1 text-xs text-muted-foreground">
              Address cannot be changed once added
            </p>
          </div>

          <div>
            <span className="mb-2 block text-sm font-medium">Networks</span>
            {renderNetworkIdsControl(control, networks, id)}
            {errors.networkIds && (
              <p className="mt-1 text-xs text-destructive">{errors.networkIds.message}</p>
            )}
          </div>

          <div className="mt-4 flex justify-between gap-x-3 gap-y-3">
            <ConfirmationButton
              title="Remove Wallet"
              message={
                <p>
                  Are you sure you want to remove this wallet? This action cannot be undone.
                </p>
              }
              action={archiveWalletAction}
              confirmLabel="Remove"
              asChild
            >
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="self-start"
                title="Remove Wallet"
              >
                <Trash2 className="size-auto" />
                <span className="sr-only">Remove Wallet</span>
              </Button>
            </ConfirmationButton>
            <div className="flex justify-end gap-2">
              <Dialog.Close asChild>
                <Button type="button" variant="outline" disabled={editWalletMutation.isPending}>
                  Cancel
                </Button>
              </Dialog.Close>
              <Button type="submit" disabled={editWalletMutation.isPending}>
                {editWalletMutation.isPending ? (
                  <>
                    <Spinner className="mr-2 size-4" />
                    Saving...
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </div>
          </div>
        </form>
      </Dialog.Content>
    </Dialog.Root>
  );
}

function renderNetworkIdsControl(
  control: Control<AddWalletFormData> | Control<EditWalletFormData>,
  networks: readonly Network[],
  id: string,
): React.JSX.Element {
  return (
    <Controller
      control={control as Control<AddWalletFormData | EditWalletFormData>}
      name="networkIds"
      render={({ field: { disabled, onBlur, onChange, ref, value } }) => (
        <div className="grid grid-cols-2 gap-2 min-[490px]:grid-cols-3">
          {networks.map((network) => {
            const checkboxId = `network-${network.id}-${id}`;
            const isSelected = value.includes(network.id);

            return (
              <label
                key={network.id}
                htmlFor={checkboxId}
                className={cx(
                  "flex items-center gap-2 rounded-md border p-2",
                  isSelected ? "border-primary bg-primary/10" : "border-border",
                )}
              >
                <div className="flex items-center space-x-2">
                  <Checkbox
                    ref={ref}
                    id={checkboxId}
                    disabled={disabled}
                    checked={isSelected}
                    onCheckedChange={(checked) => {
                      const newIds = checked
                        ? [...value, network.id]
                        : value.filter((id) => id !== network.id);
                      onChange(newIds);
                    }}
                    onBlur={onBlur}
                  />
                  <span>{network.name}</span>
                </div>
              </label>
            );
          })}
        </div>
      )}
    />
  );
}
