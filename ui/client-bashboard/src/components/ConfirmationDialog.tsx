import { getErrorDetail } from "@/store/services/helpers";
import { LoadingButton } from "@mui/lab";
import {
  <PERSON><PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogProps,
  DialogTitle,
} from "@mui/material";
import React, { useCallback, useId, useRef, useState } from "react";

interface Content {
  readonly confirmColor?: "error" | "warning" | "primary" | "secondary";
  readonly message?: React.ReactNode;
  readonly title?: string;
}

interface State {
  readonly error: string;
  readonly isLoading: boolean;
  readonly open: boolean;
}

const CLOSED_STATE: State = { error: "", isLoading: false, open: false };
const OPEN_STATE: State = { error: "", isLoading: false, open: true };
const LOADING_STATE: State = { error: "", isLoading: true, open: true };

export function useConfirmationDialog() {
  const [content, setContent] = useState<Content>();
  const [state, setState] = useState(CLOSED_STATE);
  const actionRef = useRef<() => void | Promise<unknown>>(noop);

  const onConfirm = useCallback(() => {
    (async function runAction() {
      try {
        setState(LOADING_STATE);
        await actionRef.current();
      } catch (error) {
        setState({
          error: getErrorDetail(error, { fallback: "Unknown error performing action" }),
          isLoading: false,
          open: true,
        });
        return;
      }
      setState(CLOSED_STATE);
    })();
  }, []);

  const onCancel = useCallback(() => {
    setState(CLOSED_STATE);
  }, []);

  const props = {
    onCancel,
    onConfirm,
    state,
    content,
  };

  const confirm = useCallback((params: { action: () => void | Promise<unknown> } & Content) => {
    const { action, ...content } = params;
    setContent(content);
    actionRef.current = action;
    setState(OPEN_STATE);
  }, []);

  return [props, confirm] as const;
}

export interface ConfirmationDialogProps extends Omit<DialogProps, "open" | "content"> {
  onCancel: () => void;
  onConfirm: () => void;
  state: State;
  content?: Content;
}

export function ConfirmationDialog(props: ConfirmationDialogProps) {
  const { onCancel, onConfirm, state, content = {}, ...dialogProps } = props;
  const {
    confirmColor = "error",
    message = "Please confirm your action.",
    title = "Are you sure?",
  } = content;

  const id = useId();
  const titleId = `diag-title${id}`;
  const descId = `diag-desc${id}`;

  return (
    <Dialog
      aria-labelledby={titleId}
      aria-describedby={descId}
      maxWidth="sm"
      onClose={onCancel}
      open={state.open}
      {...dialogProps}
    >
      <DialogTitle id={titleId}>{title}</DialogTitle>
      <DialogContent>
        {typeof message === "string" ? (
          <DialogContentText id={descId}>{message}</DialogContentText>
        ) : (
          <div id={descId}>{message}</div>
        )}
        {state.error && (
          <Alert severity="error" sx={{ mt: 4 }}>
            {state.error}
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        {/* eslint-disable-next-line jsx-a11y/no-autofocus */}
        <Button autoFocus onClick={onCancel}>
          Cancel
        </Button>
        <LoadingButton color={confirmColor} onClick={onConfirm} loading={state.isLoading}>
          Confirm
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}

function noop() {
  /* empty */
}
