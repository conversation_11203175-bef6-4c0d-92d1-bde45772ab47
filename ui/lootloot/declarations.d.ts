declare module "*.svg" {
  const value: string;
  export default value;
}

declare module "*.css" {}

// NodeJS environment variables
declare namespace NodeJS {
  interface ProcessEnv {
    /**
     * JSON object of schema Record<string, { verifier: string; clientId: string }>.
     * Validated at build time (see next.config.js).
     */
    readonly AUTH_PROVIDERS: string;
    readonly ENV_NAME: string;
    readonly HELIUS_API_KEY: string;
    readonly MATRICA_CLIENT_ID: string;
  }
}
