import { useUsersQuery } from "@/api/users";
import { SelectCombobox } from "@/components/ui/select-combobox";
import { User } from "@/types/schemas";
import { useMemo, useState } from "react";

interface UserSearchNewProps {
  onChange: (user: User | null) => void;
}

const EMPTY_USERS = [] as const;

export function UserSearchNew({ onChange }: UserSearchNewProps) {
  const { data: users = EMPTY_USERS } = useUsersQuery();
  const [value, setValue] = useState("");

  const options = useMemo(() => {
    return users.map((user) => ({
      value: user.id.toString(),
      label: `${user.firstName ?? ""} ${user.lastName ?? ""} - ${user.email}`.trim(),
    }));
  }, [users]);

  const handleValueChange = (value: string) => {
    setValue(value);
    if (!value) {
      onChange(null);
      return;
    }

    const selectedUser = users.find((user) => user.id.toString() === value) ?? null;
    onChange(selectedUser);
  };

  return (
    <SelectCombobox
      options={options}
      placeholder="Select user"
      value={value}
      onValueChange={handleValueChange}
      emptyText="No users found"
      searchText="Search users..."
    />
  );
}
