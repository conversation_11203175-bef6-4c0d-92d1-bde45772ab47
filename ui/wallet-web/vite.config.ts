import { sentryVitePlugin } from "@sentry/vite-plugin";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import path from "node:path";
import * as v from "valibot";
import { defineConfig, loadEnv, type PluginOption } from "vite";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  validateAuthProviders(env.VITE_AUTH_PROVIDERS);
  validateETHApiUrls(env.VITE_ETHEREUM_API_URL, env.VITE_SEPOLIA_API_URL);

  return {
    plugins: [
      tailwindcss(),
      react(),
      sentryVitePlugin({
        org: "stable-tech",
        project: "wallet-web",
        telemetry: false,
        sourcemaps: {
          filesToDeleteAfterUpload: ["dist/**/*.js.map"],
        },
      }) as PluginOption,
    ],
    build: {
      sourcemap: "hidden",
      target: ["chrome131", "edge131", "firefox133", "ios18.1", "safari18.1"],
    },
    resolve: {
      alias: {
        "@": path.resolve(import.meta.dirname, "src"),
      },
    },
    server: {
      proxy: {
        "/api": {
          target: `http://localhost:${env.VITE_LOCAL_API_PORT ?? 8080}`,
          rewrite: (path) => path.replace(/^\/api/u, ""),
        },
      },
    },
  };
});

function validateAuthProviders(authProviders: string | undefined) {
  if (!authProviders) throw new Error("VITE_AUTH_PROVIDERS is required");
  const obj = JSON.parse(authProviders) as unknown;
  v.assert(v.record(v.string(), v.object({ clientId: v.string() })), obj);
}

function validateETHApiUrls(
  ethereumApiUrl: string | undefined,
  sepoliaApiUrl: string | undefined,
) {
  if (!ethereumApiUrl && !sepoliaApiUrl)
    throw new Error("VITE_ETHEREUM_API_URL or VITE_SEPOLIA_API_URL is required");
}
