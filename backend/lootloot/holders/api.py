from decimal import Decimal

import httpx
from fastapi import APIRouter, HTTPException

from common.psql import DatabaseSession
from lootloot import schemas
from lootloot.auth.fastapi import CurrentUser
from lootloot.holders.crud import GroupKind, HolderRow, NFTHolderManager
from lootloot.kinds import HolderSorting
from lootloot.nfts.crud import NFTListingManager
from lootloot.utils import pricer
from lootloot.wallets.crud import UserWalletManager

router = APIRouter()
SOL_UID = "solana"


@router.get("", response_model=schemas.HolderListResp)
def get_user_holders(
    db: DatabaseSession,
    user: CurrentUser,
    nft_collection_uid="",
    sorting=HolderSorting.by_count,
):
    return get_holders(
        db,
        nft_collection_uid=nft_collection_uid,
        sorting=sorting,
        user_id=user.id,
    )


def get_holders(
    db: DatabaseSession,
    *,
    nft_collection_uid="",
    sorting=HolderSorting.by_count,
    user_id=0,
):
    try:
        prices_result = pricer.get_prices([SOL_UID])
    except httpx.HTTPError as e:
        raise HTTPException(status_code=503, detail="Prices are unavailable") from e

    if SOL_UID not in prices_result:
        raise HTTPException(status_code=503, detail="SOL price is unavailable")

    sol_price = prices_result.get(SOL_UID)
    if not sol_price or not hasattr(sol_price, "usd"):
        raise HTTPException(status_code=503, detail="SOL price is unavailable")
    sol_usd_price = Decimal(str(sol_price.usd))

    nft_holder_manager = NFTHolderManager(db)
    nft_collection_sol_prices = NFTListingManager(db).get_nft_collections_sol_prices(
        uids=[nft_collection_uid] if nft_collection_uid else []
    )

    if not nft_collection_sol_prices:
        raise HTTPException(status_code=404, detail="NFT collection not found")

    ranking_list = nft_holder_manager.get(
        nft_collection_sol_prices=nft_collection_sol_prices,
        sorting=sorting,
        user_id=user_id,
    )

    wallets = UserWalletManager(db).all_from_user(user_id)
    address_uris = [wallet.address_uri for wallet in wallets]

    items = [
        holder_row_to_item(row, user_id=user_id, address_uris=address_uris)
        for row in ranking_list
    ]
    return schemas.HolderListResp(items=items, sol_usd_price=sol_usd_price)


def holder_row_to_item(
    row: HolderRow, user_id=0, address_uris: list[str] = []
) -> schemas.HolderItem:
    match row.group_kind:
        case GroupKind.ADDRESS:
            kind = "address"
            value = row.group_id
            own = row.group_id in address_uris
        case GroupKind.USER:
            kind = "user"
            value = row.group_name or ""
            own = row.group_id == str(user_id)
        case _:
            raise ValueError(f"Unknown group kind: {row.group_kind}")

    return schemas.HolderItem(
        rank=row.rank,
        kind=kind,
        value=value,
        nft_count=row.nft_count,
        sol_amount=row.sol_amount,
        own=own,
    )
