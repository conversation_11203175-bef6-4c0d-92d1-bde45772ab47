import { useAuthUsersQuery } from "@/store/services/api";
import { User } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { Dispatch, useEffect, useMemo } from "react";

interface UserSearchProps {
  fullWidth?: boolean;
  includeAdmins?: boolean;
  includeUser?: User | undefined;
  label?: string;
  selectedUser: User | null;
  setSelectedUser: Dispatch<User | null>;
  setDefaultUser?: boolean;
}

export const UserSearch = ({
  selectedUser,
  setSelectedUser,
  includeAdmins,
  includeUser,
  fullWidth,
  label,
  setDefaultUser,
}: UserSearchProps) => {
  const { data: usersData } = useAuthUsersQuery(includeAdmins ?? false);
  const users = useMemo(() => {
    if (includeUser) return [includeUser, ...(usersData ?? [])];
    return usersData ?? [];
  }, [includeUser, usersData]);

  useEffect(() => {
    if (setDefaultUser && selectedUser == null && users.length > 0) {
      setSelectedUser(users[0]);
    }
  });

  // De-select user when component unmounts
  useEffect(
    () => () => {
      setSelectedUser(null);
    },
    [], // eslint-disable-line react-hooks/exhaustive-deps -- only on unmount
  );

  return (
    <Autocomplete
      fullWidth={fullWidth}
      renderInput={(params) => <TextField {...params} label={label || "User"} />}
      options={users}
      getOptionLabel={(option) => `${option.firstName} ${option.lastName} - ${option.email}`}
      onChange={(event: unknown, newValue: User | null) => {
        setSelectedUser(newValue);
      }}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      value={selectedUser}
    />
  );
};
