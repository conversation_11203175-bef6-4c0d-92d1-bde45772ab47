import { useMarketCoinsQuery } from "@/store/services/api";
import { coinFmt } from "@/types/market";
import { Coin, CoinWithAssetAddress } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { skipToken } from "@reduxjs/toolkit/query";
import { Dispatch, SyntheticEvent, useState } from "react";

interface CoinSearchProps {
  coinsToSearch?: Coin[];
  disabled?: boolean;
  onSearchChange?: (search: string) => void;
  selectedCoin: Coin | null;
  setSelectedCoin: Dispatch<Coin | null>;
  withPrices?: boolean;
}

export const CoinSearch = (props: CoinSearchProps) => {
  const [coinSearch, setCoinSearch] = useState<string>("");
  const { data: coinsData } = useMarketCoinsQuery(
    props.coinsToSearch == null
      ? {
          coin: coinSearch || "",
          withPrices: props.withPrices ?? false,
        }
      : skipToken,
  );
  const defaultOnChange = (_: SyntheticEvent, coin: Coin | null) => {
    if (coin) props.setSelectedCoin(coin);
  };

  return (
    <Autocomplete
      disabled={props.disabled}
      fullWidth={true}
      renderInput={(params) => <TextField {...params} label="Coin" />}
      renderOption={defaultRenderOption}
      options={props.coinsToSearch || coinsData || []}
      noOptionsText={"No search results"}
      getOptionLabel={(option: Coin) => coinFmt(option)}
      isOptionEqualToValue={(option, value) => option.id == value.id}
      onChange={defaultOnChange}
      onInputChange={(event, value) => {
        if (event && event.type != "click") {
          setCoinSearch(value);
          props.onSearchChange?.(value);
        }
        if (!value) {
          props.setSelectedCoin(null);
        }
      }}
      value={props.selectedCoin}
    />
  );
};

const defaultRenderOption = (props: object, option: Coin) => {
  return (
    <li {...props} key={option.id}>
      {coinFmt(option)}
    </li>
  );
};

interface CoinSearchWithAssetAddressBaseProps {
  disabled?: boolean;
  selectedCoin: Coin | null;
  setSelectedCoin: Dispatch<CoinWithAssetAddress | null>;
  coinsData?: CoinWithAssetAddress[];
  setCoinSearch: Dispatch<string>;
}

export const CoinSearchWithAssetAddressBase = (props: CoinSearchWithAssetAddressBaseProps) => {
  const defaultOnChange = (_: SyntheticEvent, coin: Coin | CoinWithAssetAddress | null) => {
    if (coin && "assetAddress" in coin) props.setSelectedCoin(coin);
  };

  return (
    <Autocomplete
      disabled={props.disabled}
      fullWidth={true}
      renderInput={(params) => <TextField {...params} label="Coin" />}
      renderOption={renderOption}
      options={props.coinsData || []}
      noOptionsText={"None - please input asset address first"}
      getOptionLabel={(option: Coin) => coinFmt(option)}
      isOptionEqualToValue={(option, value) => option.id == value.id}
      onChange={defaultOnChange}
      onInputChange={(event, value) => {
        if (event && event.type != "click") {
          props.setCoinSearch(value);
        }
        if (!value) {
          props.setSelectedCoin(null);
        }
      }}
      value={props.selectedCoin}
    />
  );
};

const renderOption = (props: object, option: Coin) => {
  return (
    <li {...props} key={option.id}>
      {coinFmt(option)}
    </li>
  );
};
