"""Transaction.internal_id

Revision ID: 0062
Revises: 0061
Create Date: 2025-02-03 21:53:19.573570

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0062"
down_revision = "0061"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("transaction", sa.Column("internal_id", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_transaction_internal_id"), "transaction", ["internal_id"], unique=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_transaction_internal_id"), table_name="transaction")
    op.drop_column("transaction", "internal_id")
    # ### end Alembic commands ###
