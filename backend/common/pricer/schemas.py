import pydantic


class BaseSchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(from_attributes=True)


class Error(BaseSchema):
    detail: str


class Coin(BaseSchema):
    id: str
    name: str
    symbol: str
    market_cap_usd: float | None = None


class Price(BaseSchema):
    usd: float
    last_updated_at: int
    usd_24h_change: float | None


class HistoricMarketData(BaseSchema):
    current_price: dict[str, float]
    market_cap: dict[str, float]
    total_volume: dict[str, float]


class CoinHistory(BaseSchema):
    id: str
    symbol: str
    name: str
    market_data: HistoricMarketData | None = None
