import { AddProps, BaseAdd, nameUrlFields } from "@/components/add/common";
import { useIndexerFarmAddMutation } from "@/store/services/api";
import { useState } from "react";

export const FarmAdd = (props: AddProps) => {
  const [addFarm] = useIndexerFarmAddMutation();
  const [nameValue, setNameValue] = useState("");
  const [urlValue, setUrlValue] = useState("");

  return (
    <BaseAdd
      addMutation={addFarm}
      label={"Add a Farm"}
      close={props.close}
      disableAdd={!nameValue || !urlValue}
      submitData={{ name: nameValue, url: urlValue }}
      onSuccess={props.onSuccess}
      fields={nameUrlFields(nameValue, setNameValue, urlValue, setUrlValue)}
    />
  );
};
