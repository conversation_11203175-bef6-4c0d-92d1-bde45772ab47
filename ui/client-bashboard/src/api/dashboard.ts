import { apiQuery } from "@/api/api";
import { Dashboard, SahabaDashboard, User, TransactionForClient, ClientTransaction, Coin } from "@/types/schemas";
import { useQuery, UseQueryResult } from "@tanstack/react-query";

export function useDashboardQuery(user: User | null | undefined): UseQueryResult<Dashboard | SahabaDashboard> {
  return useQuery({
    queryKey: ["client", "dashboard", { user_id: user?.id }],
    queryFn: apiQuery<Dashboard | SahabaDashboard>,
    enabled: user != null,
    refetchInterval: 10_000,
  });
}

// Type guard to check if dashboard is SahabaDashboard
export function isSahabaDashboard(dashboard: Dashboard | SahabaDashboard): dashboard is SahabaDashboard {
  return 'clientTransactions' in dashboard;
}

// Helper function to get transactions from either dashboard type
export function getDashboardTransactions(dashboard: Dashboard | SahabaDashboard): TransactionForClient[] {
  if (isSahabaDashboard(dashboard)) {
    // Convert client transactions to TransactionForClient format for compatibility
    return dashboard.clientTransactions.map(convertClientTransactionToTransactionForClient);
  }
  return dashboard.transactions;
}

// Helper function to get client transactions (only available for sahaba)
export function getDashboardClientTransactions(dashboard: Dashboard | SahabaDashboard): ClientTransaction[] {
  if (isSahabaDashboard(dashboard)) {
    return dashboard.clientTransactions;
  }
  return [];
}

// Helper function to get coins from either dashboard type
export function getDashboardCoins(dashboard: Dashboard | SahabaDashboard): Coin[] {
  return dashboard.coins;
}

// Convert ClientTransaction to TransactionForClient for compatibility with existing components
function convertClientTransactionToTransactionForClient(clientTransaction: ClientTransaction): TransactionForClient {
  // Use computed_is_deposit which falls back to transaction data when is_deposit is null
  const isDeposit = (clientTransaction as any).computedIsDeposit ?? clientTransaction.isDeposit;

  // Determine if this is a revenue share (interest payment)
  const isRevenueShare = false; // We'll need to determine this from the client transaction data

  return {
    id: -clientTransaction.id, // Use negative ID to avoid conflicts
    confirmedAt: clientTransaction.createdAt,
    hasApproval: true,
    network: {
      id: 1, // Default to Fiat network
      name: "Fiat",
      isUsableByClients: true,
      explorerUrl: null,
    },
    signerAccount: {
      id: -clientTransaction.id,
      address: "client-transaction",
      bridge: null,
      exchange: null,
      farm: null,
      coin: clientTransaction.coin,
      isPoolToken: false,
      isWallet: false,
      lendingProtocol: null,
      name: "Client Transaction",
      tickerSymbol: null,
    },
    transfers: [{
      id: -clientTransaction.id,
      amount: clientTransaction.amount,
      isWithdrawal: !isDeposit,
      isDeposit: isDeposit || false,
      assetAccount: {
        id: -clientTransaction.id,
        address: "client-transaction",
        bridge: null,
        exchange: null,
        farm: null,
        coin: clientTransaction.coin,
        isPoolToken: false,
        isWallet: false,
        lendingProtocol: null,
        name: "Client Transaction",
        tickerSymbol: null,
      },
      fromAccount: {
        id: -clientTransaction.id,
        address: "client-transaction",
        bridge: null,
        exchange: null,
        farm: null,
        coin: clientTransaction.coin,
        isPoolToken: false,
        isWallet: false,
        lendingProtocol: null,
        name: "Client Transaction",
        tickerSymbol: null,
      },
      toAccount: {
        id: -clientTransaction.id,
        address: "client-transaction",
        bridge: null,
        exchange: null,
        farm: null,
        coin: clientTransaction.coin,
        isPoolToken: false,
        isWallet: false,
        lendingProtocol: null,
        name: "Client Transaction",
        tickerSymbol: null,
      },
      assetPriceUsd: "1", // Assume 1:1 USD for client transactions
    }],
    txHash: `client-transaction-${clientTransaction.id}`,
    wallet: {
      id: -clientTransaction.id,
      address: "client-transaction",
      name: "Client Transaction",
      isUsableByClients: false,
      networks: [],
    },
    action: isDeposit ? "loan" : "loan", // Default to loan action
    clientTransactionId: clientTransaction.id,
    comment: isRevenueShare ? "Revenue Share" : "Client Transaction",
    feesPaid: null,
    internalId: null,
    isCollateral: false,
    isDeposit: isDeposit,
    costBasisUsd: null,
    isInterestPayment: isRevenueShare,
    clientAction: isDeposit ? "Deposit" : "Withdrawal",
  };
}
