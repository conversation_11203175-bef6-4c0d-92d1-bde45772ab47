"""drop_season_3_data

Revision ID: 0032
Revises: 0031
Create Date: 2024-12-13 16:34:41.338301

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0032"
down_revision = "0031"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop tables in order of dependencies
    op.drop_index("ix_raw_heist_claim_unprocessed", table_name="rawheistclaim")
    op.drop_index("ix_raw_heist_claim_requested_at", table_name="rawheistclaim")
    op.drop_table("rawheistclaim")
    op.drop_index("ix_raw_heist_deed_claim_unprocessed", table_name="rawheistdeedclaim")
    op.drop_table("rawheistdeedclaim")
    op.drop_index(op.f("ix_heistclaim_wallet_address"), table_name="heistclaim")
    op.drop_index(op.f("ix_heistclaim_opposing_wallet_address"), table_name="heistclaim")
    op.drop_index("ix_heist_claim_ended_at_game_id", table_name="heistclaim")
    op.drop_table("heistclaim")
    op.drop_table("heistlocation")
    op.drop_table("heistdeedclaim")

    op.create_table(
        "heistdeedclaim",
        sa.Column("kind", sa.String(), nullable=False),
        sa.Column("amount", sa.Numeric(), nullable=False),
        sa.Column("at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("wallet_address", sa.String(), nullable=False),
        sa.Column("nft_address", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "heistlocation",
        sa.Column("game_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("location_type", sa.String(), nullable=False),
        sa.Column("risk", sa.String(), nullable=False),
        sa.Column("district_id", sa.Integer(), nullable=True),
        sa.Column("district_name", sa.String(), nullable=True),
        sa.Column("active_chimp_count", sa.Integer(), nullable=False),
        sa.Column("active_orangutan_count", sa.Integer(), nullable=False),
        sa.Column("active_gorilla_count", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "heistclaim",
        sa.Column("game_id", sa.Integer(), nullable=False),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("ended_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("total_amount_emitted", sa.Numeric(), nullable=False),
        sa.Column("total_amount_claimed", sa.Numeric(), nullable=False),
        sa.Column("nft_address", sa.String(), nullable=False),
        sa.Column("wallet_address", sa.String(), nullable=False),
        sa.Column("location_id", sa.Integer(), nullable=False),
        sa.Column("event_id", sa.Integer(), nullable=False),
        sa.Column("arresting_officer_address", sa.String(), nullable=True),
        sa.Column("arresting_officer_wallet_address", sa.String(), nullable=True),
        sa.Column("is_bonus_roll", sa.Boolean(), nullable=False),
        sa.Column("secondary_event", sa.String(), nullable=True),
        sa.Column("opposing_nft_address", sa.String(), nullable=True),
        sa.Column("opposing_wallet_address", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["location_id"],
            ["heistlocation.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_heist_claim_ended_at_game_id",
        "heistclaim",
        [sa.text("ended_at DESC"), "game_id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_heistclaim_opposing_wallet_address"),
        "heistclaim",
        ["opposing_wallet_address"],
        unique=False,
    )
    op.create_index(
        op.f("ix_heistclaim_wallet_address"), "heistclaim", ["wallet_address"], unique=False
    )
    op.create_table(
        "rawheistdeedclaim",
        sa.Column("address", sa.String(), nullable=False),
        sa.Column("data_hash", postgresql.BYTEA(), nullable=False),
        sa.Column("requested_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("data", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("processed_id", sa.Integer(), nullable=True),
        sa.Column("unprocessable", sa.Boolean(), server_default="false", nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["processed_id"],
            ["heistdeedclaim.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("address", "data_hash"),
        sa.UniqueConstraint("processed_id"),
    )
    op.create_index(
        "ix_raw_heist_deed_claim_unprocessed",
        "rawheistdeedclaim",
        [sa.text("(processed_id IS NULL)"), "unprocessable"],
        unique=False,
    )
    op.create_table(
        "rawheistclaim",
        sa.Column("requested_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("data", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("processed_id", sa.Integer(), nullable=True),
        sa.Column("unprocessable", sa.Boolean(), server_default="false", nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["processed_id"],
            ["heistclaim.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_raw_heist_claim_requested_at",
        "rawheistclaim",
        [sa.text("requested_at DESC")],
        unique=False,
    )
    op.create_index(
        "ix_raw_heist_claim_unprocessed",
        "rawheistclaim",
        [sa.text("(processed_id IS NULL)"), "unprocessable"],
        unique=False,
    )


def downgrade() -> None:
    # Cannot restore deleted data
    pass
