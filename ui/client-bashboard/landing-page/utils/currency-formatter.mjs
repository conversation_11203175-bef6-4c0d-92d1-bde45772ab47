/**
 * Currency Formatter Utility
 *
 * This module provides common functions for formatting currency values
 * across the Stable Tech application.
 */

/**
 * Format a currency value for display with compact notation for large numbers
 * @param {number} value - The numeric value to format
 * @param {Object} options - Formatting options
 * @param {number} [options.threshold=100000000] - Threshold for using compact notation (default: 100M)
 * @param {boolean} [options.includeSymbol=true] - Whether to include the currency symbol
 * @returns {string} - Formatted currency string with M, B, T suffixes for large numbers
 */
export const formatCompactCurrency = (value, options = {}) => {
  const { threshold = 99_999_999, includeSymbol = true } = options;

  // Use compact notation for large numbers
  if (value > threshold) {
    const formatter = new Intl.NumberFormat("en-US", {
      style: includeSymbol ? "currency" : "decimal",
      currency: "USD",
      notation: "compact",
      compactDisplay: "short",
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    });
    return formatter.format(value);
  }

  // Use standard formatting for smaller numbers
  return formatCurrency(value, options);
};

/**
 * Format a currency value for display with the specified options
 * @param {number} value - The numeric value to format
 * @param {Object} options - Formatting options
 * @param {number} [options.minimumFractionDigits=0] - Minimum number of fraction digits
 * @param {number} [options.maximumFractionDigits=0] - Maximum number of fraction digits
 * @param {boolean} [options.includeSymbol=true] - Whether to include the currency symbol
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (value, options = {}) => {
  const {
    minimumFractionDigits = 0,
    maximumFractionDigits = 0,
    includeSymbol = true,
  } = options;

  return new Intl.NumberFormat("en-US", {
    style: includeSymbol ? "currency" : "decimal",
    currency: "USD",
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(value);
};

/**
 * Format input value for currency field
 * @param {string} value - The raw input value
 * @returns {string} - Formatted input value with commas but no currency symbol
 */
export const formatCurrencyInput = (value) => {
  // Remove any non-numeric characters except decimal point
  let numericValue = value.replaceAll(/[$,]/g, "");

  // Process only if there's a value
  if (numericValue.length > 0) {
    // Parse the value
    const numValue = Number.parseFloat(numericValue);

    // Return the value with commas but no $ (UI already has $ symbol)
    if (!Number.isNaN(numValue)) {
      return numValue.toLocaleString("en-US");
    }
  }

  return "";
};

/**
 * Process and normalize a formatted currency input value
 * @param {string} value - The formatted input value
 * @returns {number} - The numeric value
 */
export const parseCurrencyInput = (value) => {
  // Remove any non-numeric characters for calculation
  const numericValue = value.replaceAll(/[$,]/g, "");
  return Number.parseFloat(numericValue) || 0;
};
