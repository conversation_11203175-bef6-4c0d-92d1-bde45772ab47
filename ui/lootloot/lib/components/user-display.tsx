"use client";

import { shortenSolanaAddress } from "@/lib/utils/string";
import { cx } from "class-variance-authority";
import { CheckIcon, CopyIcon, LucideIcon, UserCircle, Wallet } from "lucide-react";
import { useCallback, useState } from "react";

interface UserDisplayProps {
  kind: "user" | "address";
  value: string | null | undefined;
  isOwn?: boolean;
  showOwnLabel?: boolean;
  className?: string;
}

export function UserDisplay({
  kind,
  value,
  isOwn,
  showOwnLabel = true,
  className,
}: UserDisplayProps) {
  const [Icon, displayValue] = getIconAndValue(kind, value);
  const [copied, setCopied] = useState(false);
  const isAddress = kind === "address";

  const handleCopy = useCallback(() => {
    void navigator.clipboard.writeText(value ?? "").then(() => {
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    });
  }, [value]);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        handleCopy();
      }
    },
    [handleCopy],
  );

  return (
    <div
      className={cx(
        "group flex items-center gap-2",
        isAddress && "cursor-pointer hover:bg-muted/75",
        className,
      )}
      onClick={isAddress ? handleCopy : undefined}
      onKeyDown={isAddress ? handleKeyDown : undefined}
      role={isAddress ? "button" : undefined}
      tabIndex={isAddress ? 0 : undefined}
    >
      <Icon strokeWidth={1} size={16} className="flex-none" />
      <span
        className={cx(
          "break-all",
          kind === "address" && "font-mono",
          isOwn &&
            "drop-shadow-[rgba(137,96,240,0.45)_0_0_4px] dark:drop-shadow-[rgb(184,159,255)_0_0_4px]",
        )}
      >
        {displayValue}
      </span>
      {isOwn && showOwnLabel && (
        <span className="flex-none rounded-full bg-primary px-1.5 pt-0.5 pb-1 text-xs leading-none text-white">
          you
        </span>
      )}
      {isAddress && (
        <span className="inline-flex text-muted-foreground">
          {copied ? (
            <CheckIcon className="h-4 w-4" />
          ) : (
            <CopyIcon className="h-4 w-4 opacity-0 transition-opacity group-hover:opacity-100" />
          )}
        </span>
      )}
    </div>
  );
}

function getIconAndValue(
  kind: "user" | "address",
  value?: string | null,
): [LucideIcon, string] {
  if (value && kind === "address") {
    const address = value.replace(/^[^:]+:/u, "");
    return [Wallet, shortenSolanaAddress(address)];
  }
  return [UserCircle, (value ?? "") || "Anonymous"];
}
