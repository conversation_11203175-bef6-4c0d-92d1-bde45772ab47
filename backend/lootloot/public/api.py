from typing import Literal

from fastapi import APIRouter

from common.psql import DatabaseSession
from lootloot import schemas
from lootloot.heist.api import TimezoneOffset, timezone_offset_to_str
from lootloot.heist.crud import HeistClaimManager
from lootloot.holders.api import get_holders
from lootloot.kinds import HolderSorting
from lootloot.nfts.crud import NFTCollectionManager, NFTManager
from lootloot.utils import pricer

router = APIRouter()


@router.get("/holders", response_model=schemas.HolderListResp)
def get_public_holders(
    db: DatabaseSession,
    nft_collection_uid="",
    sorting=HolderSorting.by_count,
):
    return get_holders(db, nft_collection_uid=nft_collection_uid, sorting=sorting)


@router.get("/nft-collections", response_model=schemas.PublicListNFTCollectionsResp)
def list_all_nft_collections(db: DatabaseSession):
    items = NFTCollectionManager(db).get_all_public()
    return schemas.PublicListNFTCollectionsResp(items=items)


@router.get("/heist/prices", response_model=schemas.HeistPrices)
def get_heist_prices():
    prices_dict = pricer.get_prices(["nana-token", "solana"])
    nana_price = prices_dict.get("nana-token")
    sol_price = prices_dict.get("solana")
    return schemas.HeistPrices(
        nana=nana_price.usd if nana_price else None,
        sol=sol_price.usd if sol_price else None,
    )


@router.get("/heist/stats", response_model=schemas.HeistStatsPublic)
def get_heist_stats(db: DatabaseSession):
    return HeistClaimManager(db).stats_global()


@router.get("/heist/nana-by-day", response_model=schemas.NanaByDayResp)
def list_gained_and_lost_nana_by_day(
    db: DatabaseSession,
    timezone_offset: TimezoneOffset = 0,
):
    timezone = timezone_offset_to_str(timezone_offset)
    items = HeistClaimManager(db).nana_by_day_global(timezone=timezone)
    return schemas.NanaByDayResp(items=items)


@router.get("/heist/wardrobes", response_model=list[schemas.HeistWardrobe])
def get_heist_wardrobes(db: DatabaseSession) -> list[schemas.HeistWardrobe]:
    return NFTManager(db).get_heist_wardrobes()


@router.get("/heist/claims-history", response_model=schemas.TopClaimsResp)
def get_public_top_claims(
    db: DatabaseSession,
    timeframe: Literal["daily", "weekly", "monthly", "all-time"] = "daily",
    sort_by: schemas.TopClaimsSortBy = "total_amount_claimed",
    sort_order: schemas.TopClaimsSortOrder = "desc",
):
    """Get top 100 claims with most NANA claimed for the given timeframe.

    Args:
        db: Database session
        timeframe: Time period to filter claims (daily, weekly, monthly, all-time)
        sort_by: Field to sort by (total_amount_claimed, duration_hours, base_multiplier)
        sort_order: Sort direction (asc, desc)

    Returns:
        Top performers response with ranked claims and NFT details
    """
    items = HeistClaimManager(db).get_top_claims(
        timeframe=timeframe,
        wallet_addresses=None,  # None means get all data
        sort=schemas.TopClaimsSort(by=sort_by, order=sort_order),
    )
    return schemas.TopClaimsResp(items=items)
