#!/bin/bash

# <PERSON><PERSON>t to drop the DB and re-inialize it to a working state.
#   Takes a PG dump as an optional argument to load the DB from.

# Usage: ./scripts/psql_load_database.sh <relative path to PG dump>
#        If not PG dump path is provided, the DB load is skipped.

if [ -z "$(command -v psql)" ]; then
  # Install PSQL client
  apt-get update -y
  apt install gnupg gnupg2 gnupg1 lsb-release wget -y
  sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
  wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -
  apt-get update -y
  apt-get install postgresql-client-14 -y
fi

# Drop and recreate DB
PGPASSWORD=$POSTGRESQL_PASSWORD psql -h $POSTGRESQL_HOST -U $POSTGRESQL_USER -c "drop database temet with (force);"
PGPASSWORD=$POSTGRESQL_PASSWORD psql -h $POSTGRESQL_HOST -U $POSTGRESQL_USER -c "create database temet;"

# Load PG dump
if [ -z "$1" ]; then
  echo "No PG dump provided, skipping DB load"
else
  echo "Loading DB from $1"
  if [ -f "$1" ]; then
    echo "File $1 not found"
  else
    PGPASSWORD=$POSTGRESQL_PASSWORD psql -h $POSTGRESQL_HOST -U $POSTGRESQL_USER -d temet -f $1
  fi
fi

# Run DB migrations
cd alembic || exit 1
alembic upgrade head
cd - || exit 1

# Populate CoinGecko Coins
python market/price_producer.py -skip
