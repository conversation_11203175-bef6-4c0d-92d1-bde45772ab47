import asyncio
import logging
import random
import time

import httpx
import redis.exceptions
from fastapi import BackgroundTasks
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception,
    stop_after_delay,
    wait_exponential_jitter,
)

from common.pricer import schemas
from pricer.cache import Cache
from pricer.coingecko_client import CoingeckoClient, SimplePrice

logger = logging.getLogger(__name__)


class PriceRefresher:
    def __init__(self, cache: Cache, coingecko: CoingeckoClient) -> None:
        self._cache = cache
        self._coingecko = coingecko

    async def refresh(
        self, ids: list[str], background_tasks: BackgroundTasks | None = None
    ) -> list[tuple[str, schemas.Price | None]]:
        resp = await self._coingecko.get_simple_price(ids)
        prices = [(cid, _validate_price(resp.get(cid))) for cid in ids]
        if background_tasks:
            background_tasks.add_task(self._cache.set_prices, prices)
        else:
            await self._cache.set_prices(prices)
        return prices

    def start_requested_prices_task(self):
        # We currently consider prices stale after 1 minute 20 seconds. If we
        # change the below value we should also consider tuning the values on
        # module `pricer.cache`.
        REFRESH_AFTER = 60  # seconds
        LOCK_TIMEOUT = 60  # seconds
        RENEW_LOCK_AFTER = LOCK_TIMEOUT // 3  # seconds
        BATCH_LEN = 100

        lock = self._cache.requested_prices_refresh_lock(timeout=LOCK_TIMEOUT)
        log = logger.getChild("requested_prices_refresh")

        async def keep_alive() -> None:
            while True:
                await asyncio.sleep(RENEW_LOCK_AFTER)
                await lock.reacquire()
                log.debug("Lock renewed")

        @retry(
            retry=retry_if_exception(_can_retry_exception),
            wait=wait_exponential_jitter(max=REFRESH_AFTER // 2),
            stop=stop_after_delay(REFRESH_AFTER * 2),
            before_sleep=before_sleep_log(log, logging.INFO),
        )
        async def acquire_loop() -> None:
            while True:
                acquired = await lock.acquire(blocking=False)
                if not acquired:
                    log.debug("Couldn't acquire lock")
                    # Sleep for a random amount of time to avoid thundering herd
                    await asyncio.sleep(random.uniform(5, 10))
                    continue

                # Lock acquired, this instance is responsible for refreshing
                log.info("Lock acquired")
                try:
                    async with asyncio.TaskGroup() as tg:
                        tg.create_task(keep_alive(), name="requested_prices_refresh_keep_alive")
                        tg.create_task(leader_loop(), name="requested_prices_refresh_leader")
                finally:
                    try:
                        await lock.release()
                        log.info("Lock released")
                    except Exception as e:
                        log.warning("Error releasing lock on cleanup: %s", e)

        async def leader_loop():
            last_refresh = await self._cache.get_last_requested_prices_refresh()
            time_to_wait = last_refresh + REFRESH_AFTER - time.time()
            if time_to_wait > 0:
                log.debug("Waiting %s seconds before refresh", time_to_wait)
                await asyncio.sleep(time_to_wait)

            while True:
                log.debug("Started refreshing requested prices")
                ids = await self._cache.get_all_requested_prices()
                batches = (ids[i : i + BATCH_LEN] for i in range(0, len(ids), BATCH_LEN))
                for ids_batch in batches:
                    log.debug("Refreshing requested prices batch: %s", ids_batch)
                    await self.refresh(ids_batch)
                last_refresh = int(time.time())
                await self._cache.set_last_requested_prices_refresh(last_refresh)
                log.debug("Finished refreshing requested prices")
                await asyncio.sleep(REFRESH_AFTER)

        return asyncio.create_task(acquire_loop(), name="requested_prices_refresh")


def _validate_price(value: SimplePrice | None) -> schemas.Price | None:
    return schemas.Price.model_validate(value) if value else None


EXCEPTIONS_TO_RETRY = (
    httpx.HTTPError,
    redis.exceptions.LockError,
    redis.exceptions.ConnectionError,
    redis.exceptions.TimeoutError,
)


def _can_retry_exception(err: BaseException) -> bool:
    return isinstance(err, EXCEPTIONS_TO_RETRY) or (
        isinstance(err, ExceptionGroup)
        and any(isinstance(e, EXCEPTIONS_TO_RETRY) for e in err.exceptions)
    )
