"use client";

import { toggle } from "@/lib/components/toggle";
import * as RadixToggleGroup from "@radix-ui/react-toggle-group";
import { VariantProps, cx } from "class-variance-authority";
import * as React from "react";

const ToggleGroupContext = React.createContext<VariantProps<typeof toggle>>({
  size: "default",
  variant: "default",
});

export const ToggleGroup = React.forwardRef<
  React.ElementRef<typeof RadixToggleGroup.Root>,
  React.ComponentPropsWithoutRef<typeof RadixToggleGroup.Root> & VariantProps<typeof toggle>
>(function ToggleGroup({ className, variant, size, children, ...props }, ref) {
  return (
    <RadixToggleGroup.Root
      ref={ref}
      className={cx("flex items-center justify-center gap-1", className)}
      {...props}
    >
      <ToggleGroupContext.Provider value={{ variant, size }}>
        {children}
      </ToggleGroupContext.Provider>
    </RadixToggleGroup.Root>
  );
});

export const ToggleGroupItem = React.forwardRef<
  React.ElementRef<typeof RadixToggleGroup.Item>,
  RadixToggleGroup.ToggleGroupItemProps
>(function ToggleGroupItem({ className, children, ...props }, ref) {
  const context = React.useContext(ToggleGroupContext);

  return (
    <RadixToggleGroup.Item
      ref={ref}
      className={cx(
        toggle({
          variant: context.variant,
          size: context.size,
        }),
        className,
      )}
      {...props}
    >
      {children}
    </RadixToggleGroup.Item>
  );
});

export { ToggleGroupItem as Item, ToggleGroup as Root };
