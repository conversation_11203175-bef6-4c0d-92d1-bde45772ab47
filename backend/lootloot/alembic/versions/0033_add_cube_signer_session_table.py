"""add cube signer session table

Revision ID: 0033
Revises: 0032
Create Date: 2024-12-20 12:27:09.001238

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0033"
down_revision = "0032"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "cubesignersession",
        sa.Column("session_name", sa.String(), nullable=False),
        sa.Column("data", postgresql.BYTEA(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("session_name"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("cubesignersession")
    # ### end Alembic commands ###
