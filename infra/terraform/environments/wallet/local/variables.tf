variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  default     = "wallet"
  nullable    = false
}

variable "DOCKER_HOST" {
  description = "<PERSON>kube's docker host passed in using TF_VAR_DOCKER_HOST env var"
  type        = string
  nullable    = false
}

variable "host_path" {
  description = "Host path to mount into containers, for development only"
  type        = string
  default     = "/work/temet/"
  nullable    = false
}

variable "auth_allowed_issuers" {
  description = "Allowed issuers of tokens for authentication. Mapping of issuer to client_id."
  type        = map(string)
  default = {
    "https://accounts.google.com" = "************-4rebtccromkrgj5k4fd3cla6l9inuons.apps.googleusercontent.com"
  }
  nullable = false
}

variable "helius_api_key" {
  description = "Helius API key"
  type        = string
  nullable    = false
  sensitive   = true
  default     = "ecbf7832-326d-453e-8b39-da5923ec3d78"
}
