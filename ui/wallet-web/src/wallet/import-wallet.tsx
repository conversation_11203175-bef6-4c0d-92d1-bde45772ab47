import { useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { Input } from "@/lib/components/input";
import { getErrorMessage } from "@/lib/error";
import { useCreateWalletMutation } from "@/wallet/api";
import { WalletIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface ImportWalletProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export function ImportWallet({ isOpen, setIsOpen }: ImportWalletProps) {
  const [mnemonic, setMnemonic] = useState("");
  const [passphrase, setPassphrase] = useState("");
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const createWalletMutation = useCreateWalletMutation();
  const getPassword = useGetPassword();

  useEffect(() => {
    // Reset state when closed
    if (!isOpen) {
      setMnemonic("");
      setPassphrase("");
      setError("");
      setShowPassword(false);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsOpen(false);
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");

    if (!mnemonic.trim()) {
      setError("Please enter your recovery phrase");
      return;
    }

    try {
      const formData = new FormData(e.currentTarget);
      const password = await getPassword(formData);

      // Process the wallet import with or without passphrase
      const finalMnemonic = passphrase
        ? `${mnemonic.trim()} ${passphrase.trim()}`
        : mnemonic.trim();

      createWalletMutation.mutate(
        { mnemonic: finalMnemonic, password },
        {
          onSuccess: () => {
            setIsOpen(false);
          },
          onError: (error: unknown) => {
            setError(getErrorMessage(error));
          },
        },
      );
    } catch (error: unknown) {
      setError(getErrorMessage(error));
    }
  };

  return (
    <Dialog.Content title="Import Wallet" className="lg:max-w-lg">
      <div className="mb-4 flex items-start gap-3">
        <div className="rounded-lg bg-primary/10 p-2">
          <WalletIcon className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h2 className="text-xl font-bold">Import Wallet</h2>
          <p className="text-sm text-muted-foreground">Enter your secret recovery phrase</p>
        </div>
      </div>

      <form
        onSubmit={(e) => {
          void handleSubmit(e);
        }}
        className="space-y-4"
      >
        <div className="space-y-4">
          <div className="rounded bg-gray-200/80 p-4 dark:bg-slate-800/80">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="text-base font-medium">Secret Recovery Phrase</h3>
            </div>
            <div className="relative">
              <textarea
                value={mnemonic}
                onChange={(e) => {
                  setMnemonic(e.target.value);
                }}
                className="h-20 w-full resize-none rounded border-0 bg-gray-300/50 p-3 font-mono text-sm focus-visible:ring-primary/50 focus-visible:outline-none dark:bg-slate-700/50"
                placeholder="Enter your 12 or 24-word recovery phrase"
              />
            </div>
            <p className="mt-2 text-xs text-gray-500">
              Typically 12 or 24 words separated by spaces
            </p>
          </div>

          <div className="rounded bg-gray-200/80 p-4 dark:bg-slate-800/80">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="text-base font-medium">Passphrase (Optional)</h3>
              <button
                type="button"
                onClick={() => {
                  toggleShowPassword();
                }}
                className="text-xs text-primary"
              >
                {showPassword ? "Hide" : "Show"}
              </button>
            </div>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                value={passphrase}
                onChange={(e) => {
                  setPassphrase(e.target.value);
                }}
                className="w-full border-0 bg-gray-300/50 p-2 font-mono text-sm focus-visible:ring-primary/50 dark:bg-slate-700/50"
                placeholder="Enter passphrase"
              />
            </div>
          </div>

          <div className="rounded bg-gray-200/80 p-4 dark:bg-slate-800/80">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="text-base font-medium">Wallet Password</h3>
            </div>
            <PasswordInput />
          </div>
        </div>

        <div className="flex flex-col items-start gap-2 rounded border border-amber-500/20 bg-amber-500/10 p-3 text-amber-600 dark:text-amber-400">
          <p className="flex items-center gap-2 text-xs">
            <span className="flex-shrink-0">⚠️</span>
            Never share your recovery phrase. Anyone with your recovery phrase can take your
            assets.
          </p>
        </div>

        {error && <AlertError>{error}</AlertError>}

        <div className="flex justify-end space-x-3 pt-2">
          <Button type="button" variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button type="submit" variant="primary">
            Import Wallet
          </Button>
        </div>
      </form>
    </Dialog.Content>
  );
}
