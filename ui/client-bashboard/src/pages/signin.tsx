import { Card } from "@/components/ui/card";
import { Spinner } from "@/components/ui/spinner";
import { newPagesEnabled } from "@/context/env";
import { useAuth } from "@/hooks/useAuth";
import { AutoLayout } from "@/layouts/auto";
import { Container } from "@/layouts/NewLayout";
import LoginPage from "@/pages/login";
import { getErrorDetail } from "@/store/services/helpers";
import { ReactElement, useState } from "react";

const Page = newPagesEnabled ? SignInPage : LoginPage;
export default Page;

function SignInPage() {
  const auth = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isSigningIn, setIsSigningIn] = useState(false);

  const handleSignInWithGoogle = async () => {
    setIsSigningIn(true);
    setError(null);

    try {
      await auth.signInWithGoogle();
    } catch (error) {
      setError(getErrorDetail(error, { fallback: "Failed to sign in with Google" }));
      setIsSigningIn(false);
    }
  };

  const isLoading = auth.loading || isSigningIn;

  // Show loading spinner while authentication is in progress
  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Spinner className="size-10 text-primary" />
      </div>
    );
  }

  return (
    <Container className="mb-8 flex flex-col items-center">
      <Card className="mx-auto mt-8 w-full max-w-xl p-8">
        <div className="flex flex-col items-center gap-6">
          <h1 className="text-2xl font-semibold">Sign In</h1>

          {error && <div className="w-full rounded-md bg-red-50 p-4 text-red-700">{error}</div>}

          <button
            onClick={handleSignInWithGoogle}
            className="flex items-center gap-3 rounded-md border border-gray-300 bg-white px-6 py-3 shadow-xs transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
            disabled={isLoading}
          >
            {/* Google logo */}
            <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
              <path
                fill="#EA4335"
                d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
              />
              <path
                fill="#4285F4"
                d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
              />
              <path
                fill="#FBBC05"
                d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
              />
              <path
                fill="#34A853"
                d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
              />
              <path fill="none" d="M0 0h48v48H0z" />
            </svg>
            <span className="font-medium">Sign in with Google</span>
          </button>
        </div>
      </Card>
    </Container>
  );
}

SignInPage.getLayout = function getLayout(page: ReactElement) {
  return <AutoLayout hiddenNav>{page}</AutoLayout>;
};

SignInPage.getSpinner = function getSpinner() {
  return (
    <AutoLayout hiddenNav>
      <div className="flex h-64 items-center justify-center">
        <Spinner className="size-10 text-primary" />
      </div>
    </AutoLayout>
  );
};

SignInPage.guestGuard = true;
