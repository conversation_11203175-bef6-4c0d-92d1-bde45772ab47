import { useAuth } from "@/lib/auth-ctx";
import * as ToggleGroup from "@/lib/components/toggle-group";
import { Globe, User } from "lucide-react";
import Link from "next/link";
import type { ReadonlyURLSearchParams } from "next/navigation";

export function getView(params: ReadonlyURLSearchParams): "own" | "all" {
  const view = params.get("view");
  return view === "all" ? "all" : "own";
}

export function viewLink(params: ReadonlyURLSearchParams | undefined, view: "own" | "all") {
  const newParams = new URLSearchParams(params);
  if (view === "own") {
    newParams.delete("view");
  } else {
    newParams.set("view", view);
  }
  const queryString = newParams.toString();
  return queryString ? `?${queryString}` : "./";
}

interface ViewToggleProps {
  view: "own" | "all";
  params: ReadonlyURLSearchParams | undefined;
  ownLabel?: string;
  allLabel?: string;
}

export function ViewToggle({
  view,
  params,
  ownLabel = "Your heists",
  allLabel = "All heists",
}: ViewToggleProps) {
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground">Viewing</span>
      <ToggleGroup.Root type="single" value={view}>
        <ToggleGroup.Item value="own" aria-label="Your wallets" asChild>
          <Link href={viewLink(params, "own")}>
            <User />
          </Link>
        </ToggleGroup.Item>
        <ToggleGroup.Item value="all" aria-label="All wallets" asChild>
          <Link href={viewLink(params, "all")}>
            <Globe />
          </Link>
        </ToggleGroup.Item>
      </ToggleGroup.Root>
      <span className="text-sm text-muted-foreground">
        {view === "own" ? ownLabel : allLabel}
      </span>
    </div>
  );
}

interface SignedOutErrorProps {
  params: ReadonlyURLSearchParams | undefined;
  customMessage?: string;
}

export function SignedOutError({ params, customMessage }: SignedOutErrorProps) {
  const auth = useAuth();
  const isSignedOut = auth.initialized && !auth.user;
  const view = params ? getView(params) : "own";
  const isOwn = view === "own";

  if (!isSignedOut || !isOwn) return null;

  return (
    <p className="p-6 text-center leading-7">
      Please{" "}
      <Link className="text-primary underline-offset-2 hover:underline" href="/auth">
        sign in
      </Link>{" "}
      {customMessage ?? "to view your wallets"}, or view the public data for{" "}
      <Link
        className="text-primary underline-offset-2 hover:underline"
        href={viewLink(params, "all")}
      >
        all wallets
      </Link>
      .
    </p>
  );
}
