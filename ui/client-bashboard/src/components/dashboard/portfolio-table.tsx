import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DashboardRow } from "@/helpers/portfolio-calculations";
import { cx } from "cva";
import { ChevronDown, ChevronUp } from "lucide-react";
import { ReactNode, createContext, useContext } from "react";

export type SortBy =
  | "asset"
  | "amount"
  | "interest"
  | "price"
  | "total-usd"
  | "interest-usd"
  | "24h-change";

export type SortOrder = "asc" | "desc";

export const DEFAULT_SORT: readonly [SortBy, SortOrder] = ["total-usd", "desc"];

type SortContextValue = readonly [SortBy, SortOrder];
export const SortContext = createContext<SortContextValue>(DEFAULT_SORT);
SortContext.displayName = "Sort";

interface TableHeadingProps {
  className?: string;
  children: ReactNode;
  itemKey: SortBy;
  align?: "start" | "end";
}

export function TableHeading({
  className,
  children,
  itemKey,
  align = "start",
}: TableHeadingProps) {
  const [sortBy, sortOrder] = useContext(SortContext);
  const sorting = sortBy === itemKey ? sortOrder : null;
  const Icon = sorting === "asc" ? ChevronUp : sorting === "desc" ? ChevronDown : null;
  return (
    <div className={className}>
      {Icon && align === "end" && (
        <span className="relative">
          <Icon className="absolute top-px right-0.5" size={16} />
        </span>
      )}
      {children}
      {Icon && align === "start" && (
        <span className="relative">
          <Icon className="absolute top-px left-0.5" size={16} />
        </span>
      )}
    </div>
  );
}

interface SortableMenuItemProps {
  children: ReactNode;
  itemKey: SortBy;
  onSelect: (sortBy: SortBy) => void;
}

function SortableMenuItem({ children, itemKey, onSelect }: SortableMenuItemProps) {
  const [sortBy, sortOrder] = useContext(SortContext);
  const sorting = sortBy === itemKey ? sortOrder : null;
  const Icon = sorting === "asc" ? ChevronUp : sorting === "desc" ? ChevronDown : null;
  return (
    <DropdownMenuItem onSelect={() => onSelect(itemKey)}>
      {children}
      {Icon && <Icon />}
    </DropdownMenuItem>
  );
}

interface HeadingConfig {
  text: string;
  itemKey: SortBy;
  className?: string;
  showCondition?: boolean;
}

interface SortableTableHeaderProps {
  headings: HeadingConfig[];
  align?: "start" | "end";
  className?: string;
  onSelect: (sortBy: SortBy) => void;
}

export function SortableTableHeader({
  headings,
  align = "start",
  className,
  onSelect,
}: SortableTableHeaderProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={cx(
          "block w-full rounded ring-ring/30 ring-offset-4 focus-visible:ring-2 focus-visible:outline-0",
          align === "end" ? "text-right" : "text-left",
          className,
        )}
      >
        {headings.map(
          (heading, index) =>
            heading.showCondition !== false && (
              <TableHeading
                key={heading.itemKey}
                className={cx(
                  index > 0 ? "mt-px font-medium text-gray-600" : "font-semibold text-gray-700",
                  heading.className,
                )}
                itemKey={heading.itemKey}
                align={align}
              >
                {heading.text}
              </TableHeading>
            ),
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align}>
        <DropdownMenuLabel>Sort by</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {headings.map(
          (heading) =>
            heading.showCondition !== false && (
              <SortableMenuItem
                key={heading.itemKey}
                itemKey={heading.itemKey}
                onSelect={onSelect}
              >
                {heading.text}
              </SortableMenuItem>
            ),
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

/**
 * Sort rows based on the specified sort column and order
 */
export function sortRows(
  rows: readonly DashboardRow[],
  sortBy: SortBy,
  sortOrder: SortOrder,
): readonly DashboardRow[] {
  return [...rows].sort((a, b) => {
    let comparison = 0;

    // Determine which property to sort by
    switch (sortBy) {
      case "asset":
        comparison = a.ticker.localeCompare(b.ticker);
        break;
      case "amount":
        comparison = a.amount - b.amount;
        break;
      case "interest":
        comparison = a.interestAmount - b.interestAmount;
        break;
      case "price":
        comparison = a.price - b.price;
        break;
      case "total-usd":
        comparison = a.dollarValue - b.dollarValue;
        break;
      case "interest-usd":
        comparison = a.interestEarnedDollars - b.interestEarnedDollars;
        break;
      case "24h-change":
        // Handle null values for 24h change
        if (a.usd24hChange == null && b.usd24hChange == null) return 0;
        if (a.usd24hChange == null) return 1;
        if (b.usd24hChange == null) return -1;
        comparison = a.usd24hChange - b.usd24hChange;
        break;
    }

    // Apply sort order
    return sortOrder === "asc" ? comparison : -comparison;
  });
}
