import urllib.parse
from datetime import date
from typing import Generic, TypeVar

import httpx
from pydantic import RootModel
from pydantic_settings import BaseSettings, SettingsConfigDict

from common.pricer import schemas
from common.pricer.settings import SECRET

PricesMapping = RootModel[dict[str, schemas.Price | None]]
CoinsList = RootModel[list[schemas.Coin]]

T = TypeVar("T", httpx.Client, httpx.AsyncClient)


class BasePricerClient(Generic[T]):
    _client: T

    def __init__(
        self,
        client_cls: type[T],
        url: str,
        secret: str,
        timeout: float = 10,
    ) -> None:
        self._client = client_cls(
            base_url=url,
            headers=[("Authorization", f"Bearer {secret}")],
            timeout=timeout,
        )

    @classmethod
    def from_env(cls):
        settings = PricerSettings()
        return cls(settings.pricer_url, SECRET)

    def _get_prices(self, ids: list[str]):
        return self._client.get("/prices", params={"ids": ",".join(ids)})

    def _process_get_prices(self, resp: httpx.Response) -> dict[str, schemas.Price | None]:
        resp.raise_for_status()
        return PricesMapping.model_validate_json(resp.content).root

    def _get_coins(self):
        return self._client.get("/coins")

    def _process_get_coins(self, resp: httpx.Response) -> list[schemas.Coin]:
        resp.raise_for_status()
        return CoinsList.model_validate_json(resp.content).root

    def _get_coin_history(self, coin_id: str, date: date):
        return self._client.get(
            f"/coins/{urllib.parse.quote(coin_id, safe='')}/history",
            params={"date": date.strftime("%d-%m-%Y")},
        )

    def _process_get_coin_history(self, resp: httpx.Response) -> schemas.CoinHistory:
        resp.raise_for_status()
        return schemas.CoinHistory.model_validate_json(resp.content)


class PricerClient(BasePricerClient[httpx.AsyncClient]):
    def __init__(self, url: str, secret: str) -> None:
        super().__init__(httpx.AsyncClient, url, secret)

    async def get_prices(self, ids: list[str]) -> dict[str, schemas.Price | None]:
        resp = await self._get_prices(ids)
        return self._process_get_prices(resp)

    async def get_coins(self) -> list[schemas.Coin]:
        resp = await self._get_coins()
        return self._process_get_coins(resp)

    async def get_coin_history(self, coin_id: str, date: date) -> schemas.CoinHistory:
        resp = await self._get_coin_history(coin_id, date)
        return self._process_get_coin_history(resp)


class PricerClientSync(BasePricerClient[httpx.Client]):
    def __init__(self, url: str, secret: str) -> None:
        super().__init__(httpx.Client, url, secret)

    def get_prices(self, ids: list[str]) -> dict[str, schemas.Price | None]:
        resp = self._get_prices(ids)
        return self._process_get_prices(resp)

    def get_coins(self) -> list[schemas.Coin]:
        resp = self._get_coins()
        return self._process_get_coins(resp)

    def get_coin_history(self, coin_id: str, date: date) -> schemas.CoinHistory:
        resp = self._get_coin_history(coin_id, date)
        return self._process_get_coin_history(resp)


class PricerSettings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    pricer_url: str = "http://pricer.internal/"
