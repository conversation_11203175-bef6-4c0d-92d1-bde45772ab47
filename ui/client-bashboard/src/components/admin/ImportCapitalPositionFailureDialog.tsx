import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";
import { DataGridPro, GridColDef } from "@mui/x-data-grid-pro";
import { useEffect, useMemo, useState } from "react";

import type { ImportFailedRow, ImportResult } from "@/types/schemas";

interface Props {
  readonly data: ImportResult | undefined;
  readonly onClose: () => void;
}

const EMPTY_ARRAY = [] as const;

export default function ImportFailureDialog({ data, onClose }: Props) {
  const [persistedData, setPersistedData] = useState(data);
  useEffect(() => {
    // Persist data to avoid flickering when the dialog is closed (out transition)
    if (data != null) setPersistedData(data);
  }, [data]);

  const open = data != null && !data.ok;
  const lastAvailableData = data ?? persistedData;
  const header = lastAvailableData?.header ?? EMPTY_ARRAY;
  const rows = lastAvailableData?.failedRows ?? EMPTY_ARRAY;

  const columns = useMemo(() => computeColumns(header, rows[0]?.row), [header, rows]);

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <DialogTitle>Failed to import</DialogTitle>
      <DialogContent>
        <DialogContentText gutterBottom>
          We couldn&apos;t find matching transactions for the following Capital Position
          entries. Please review
          <p>
            <strong>Capital Positions were created, do not re-import</strong>.
          </p>
        </DialogContentText>
        <DataGridPro
          sx={{ height: 380 }}
          columns={columns}
          rows={rows}
          getRowId={getRowId}
          pagination
          autoPageSize
        />
      </DialogContent>
      <DialogActions sx={{ justifyContent: "flex-start" }}>
        <Button onClick={() => onClose()}>Close</Button>
      </DialogActions>
    </Dialog>
  );
}

function computeColumns(
  header: readonly string[],
  firstRow?: readonly string[],
): GridColDef<ImportFailedRow, unknown, unknown>[] {
  if (header.length === 0) {
    if (firstRow == null) return [];
    // no header names, but with appropriate number of columns for rows
    header = firstRow.map(() => "") ?? [];
  }
  return [
    {
      field: "reason",
      headerName: "Reason",
      width: 300,
      valueGetter: getReason,
    },
    {
      field: "index",
      headerName: "#",
      width: 70,
      valueGetter: (params) => params.row.index + 1,
    },
    ...header.map(
      (name, i): GridColDef<ImportFailedRow> => ({
        field: `col${i}`,
        headerName: name,
        width: name.includes("Date") ? 180 : 110,
        valueGetter: (params) => params.row.row[i],
      }),
    ),
  ];
}

function getReason({ row }: { readonly row: ImportFailedRow }) {
  switch (row.error) {
    case "unmatched-buy":
      return "Unmatched buy transaction for capital position";
    case "unmatched-sell":
      return "Unmatched sell transaction for capital position";
    default:
      return row.error;
  }
}

function getRowId({ index }: ImportFailedRow) {
  return index;
}
