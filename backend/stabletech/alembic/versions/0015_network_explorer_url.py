"""Network explorer_url

Revision ID: 0015
Revises: 0014
Create Date: 2023-01-31 14:14:30.682582

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0015"
down_revision = "0014"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Network(Base):
    id = Column(Integer, primary_key=True)
    covalent_chain_id: int = Column(Integer)
    explorer_url: str = Column(String)
    name: str = Column(String, nullable=False, index=True, unique=True)
    __tablename__ = "network"


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("network", sa.Column("explorer_url", sa.String(), nullable=True))
    # ### end Alembic commands ###

    session = Session(bind=op.get_bind())
    for network in session.query(Network).all():
        if network.name == "Ethereum":
            network.explorer_url = "https://etherscan.com"
        elif network.name == "Avalanche":
            network.explorer_url = "https://snowtrace.io"
        elif network.name == "BSC":
            network.explorer_url = "https://bscscan.com"
        elif network.name == "Cosmos":
            network.explorer_url = "https://www.mintscan.io/cosmos"
        elif network.name == "Osmosis":
            network.explorer_url = "https://www.mintscan.io/osmosis"
        elif network.name == "Fantom":
            network.explorer_url = "https://ftmscan.com"
        elif network.name == "Moonbeam":
            network.explorer_url = "https://moonscan.io"
        elif network.name == "Elrond":
            network.explorer_url = "https://explorer.multiversx.com"
        elif network.name == "Harmony":
            network.explorer_url = "https://explorer.harmony.one"
        elif network.name == "Solana":
            network.explorer_url = "https://explorer.solana.com"
        elif network.name == "Matic":
            network.explorer_url = "https://polygonscan.com"
        elif network.name == "Aurora":
            network.explorer_url = "https://aurorascan.dev"
        elif network.name == "Moonriver":
            network.explorer_url = "https://moonriver.moonscan.io"
    session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("network", "explorer_url")
    # ### end Alembic commands ###
