# Blockchain Transactions Indexer

The Indexer gathers blockchain transactions from third-party APIs and saves them in a standard
format.

## Covalent

The Covalent Unified API can be used to pull balances, positions and historical granular
transaction data from dozens of blockchain networks.

To use locally, [Signup for a free API key](https://www.covalenthq.com/platform) and add it to
your `backend/.env`

```
COVALENT_API_KEY=<API KEY>
```

## Helius

Making sense of Solana data can be challenging, as it often appears as a series of long, obscure
hashes without context. Helius APIs make it easy to understand and use Solana data by providing
enriched, descriptive context.

To use locally, [Signup for a free API key](https://dev.helius.xyz/dashboard/app) and add it to
your `backend/.env`

```
HELIUS_API_KEY=<API KEY>
```
