import { But<PERSON> } from "@/lib/components/button";
import { cx } from "class-variance-authority";
import { Co<PERSON>, CopyCheck } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface Props extends React.ComponentPropsWithoutRef<typeof Button> {
  text: string | null | undefined;
}

export function CopyButton(props: Props) {
  const { text, ...buttonProps } = props;

  const [copied, setCopied] = useState("");
  const timeoutRef = useRef<number | null>(null);

  const success = copied && copied === text;

  // Clear timeout on unmount
  useEffect(
    () => () => {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }
    },
    [],
  );

  return (
    <Button
      size="icon"
      variant="outline"
      {...buttonProps}
      disabled={!text || props.disabled}
      onClick={(...args) => {
        if (!text) return;
        if (timeoutRef.current) {
          window.clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
        void navigator.clipboard.writeText(text).then(() => {
          setCopied(text);
          timeoutRef.current = window.setTimeout(() => {
            setCopied("");
          }, 1000);
        });
        props.onClick?.(...args);
      }}
    >
      <span className={cx("transition-colors", success && "text-green-500!")}>
        <CopyCheck className={success ? "animate-check" : "hidden"} />
        <span className={success ? "hidden" : ""}>{buttonProps.children ?? <Copy />}</span>
      </span>
    </Button>
  );
}
