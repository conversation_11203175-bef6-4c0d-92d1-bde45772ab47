"""Rename Pool tables

Revision ID: 0013
Revises: 0012
Create Date: 2023-01-02 22:24:55.945242

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0013"
down_revision = "0012"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.rename_table("transaction", "pooltransaction")
    op.rename_table("leg", "poolleg")
    op.drop_index(op.f("ix_transaction_wallet_id"))
    op.create_index(
        op.f("ix_pooltransaction_wallet_id"), "pooltransaction", ["wallet_id"], unique=False
    )
    op.drop_index("ix_transaction_pool_id", table_name="pooltransaction")
    op.create_index(
        op.f("ix_pooltransaction_pool_id"), "pooltransaction", ["pool_id"], unique=False
    )
    op.drop_index("ix_leg_coin_id", table_name="poolleg")
    op.drop_index("ix_leg_transaction_id", table_name="poolleg")
    op.create_index(op.f("ix_poolleg_coin_id"), "poolleg", ["coin_id"], unique=False)
    op.create_index(
        op.f("ix_poolleg_transaction_id"), "poolleg", ["transaction_id"], unique=False
    )


def downgrade() -> None:
    op.rename_table("pooltransaction", "transaction")
    op.rename_table("poolleg", "leg")
