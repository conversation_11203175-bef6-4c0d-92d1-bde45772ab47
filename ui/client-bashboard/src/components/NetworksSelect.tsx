import { Network } from "@/types/schemas";
import { Box, Chip, FormControl, MenuItem, Select } from "@mui/material";
import InputLabel from "@mui/material/InputLabel";
import { Dispatch, useId } from "react";

interface NetworksSelectProps {
  getNetworksMutation: () => { data?: Network[] | undefined };
  selectedNetworks: Network[];
  setSelectedNetworks: Dispatch<Network[]>;
}

export const NetworksSelect = (props: NetworksSelectProps) => {
  const id = useId();
  const selectId = `network-select-${id}`;
  const labelId = `network-select-label-${id}`;

  const { data: networks } = props.getNetworksMutation();

  return (
    <FormControl sx={{ width: 500 }}>
      <InputLabel id={labelId}>Supported Networks</InputLabel>
      <Select
        labelId={labelId}
        id={selectId}
        multiple
        value={props.selectedNetworks.map((network: Network) => network.name)} // Must be a string array
        onChange={(event) => {
          if (networks) {
            props.setSelectedNetworks(
              networks.filter((network: Network) => event.target.value.includes(network.name)),
            );
          }
        }}
        renderValue={(selected) => (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
            {selected.map((value) => (
              <Chip key={value} label={value} />
            ))}
          </Box>
        )}
      >
        {networks &&
          networks.map((network: Network) => (
            <MenuItem key={network.id} value={network.name}>
              {network.name}
            </MenuItem>
          ))}
      </Select>
    </FormControl>
  );
};
