"""Added heist land deed claim

Revision ID: 0022
Revises: 0021
Create Date: 2024-03-15 08:53:27.986078

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0022"
down_revision = "0021"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "heistdeedclaim",
        sa.Column("kind", sa.String(), nullable=False),
        sa.Column("amount", sa.Numeric(), nullable=False),
        sa.Column("at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("wallet_address", sa.String(), nullable=False),
        sa.Column("nft_address", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.<PERSON>KeyConstraint("id"),
    )
    op.drop_index("ix_raw_heist_claim_unprocessed", table_name="rawheistclaim")
    op.create_index(
        "ix_raw_heist_claim_unprocessed",
        "rawheistclaim",
        [sa.text("(processed_id IS NULL)"), "unprocessable"],
        unique=False,
    )
    op.add_column("rawheistdeedclaim", sa.Column("processed_id", sa.Integer(), nullable=True))
    op.add_column(
        "rawheistdeedclaim",
        sa.Column("unprocessable", sa.Boolean(), server_default="false", nullable=False),
    )
    op.create_index(
        "ix_raw_heist_deed_claim_unprocessed",
        "rawheistdeedclaim",
        [sa.text("(processed_id IS NULL)"), "unprocessable"],
        unique=False,
    )
    op.create_unique_constraint(
        "rawheistdeedclaim_processed_id_key", "rawheistdeedclaim", ["processed_id"]
    )
    op.create_foreign_key(
        "rawheistdeedclaim_processed_id_fkey",
        "rawheistdeedclaim",
        "heistdeedclaim",
        ["processed_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "rawheistdeedclaim_processed_id_fkey", "rawheistdeedclaim", type_="foreignkey"
    )
    op.drop_constraint(
        "rawheistdeedclaim_processed_id_key", "rawheistdeedclaim", type_="unique"
    )
    op.drop_index("ix_raw_heist_deed_claim_unprocessed", table_name="rawheistdeedclaim")
    op.drop_column("rawheistdeedclaim", "unprocessable")
    op.drop_column("rawheistdeedclaim", "processed_id")
    op.drop_index("ix_raw_heist_claim_unprocessed", table_name="rawheistclaim")
    op.create_index(
        "ix_raw_heist_claim_unprocessed",
        "rawheistclaim",
        [sa.text("(processed_id IS NULL)"), sa.text("(unprocessable = false)")],
        unique=False,
    )
    op.drop_table("heistdeedclaim")
    # ### end Alembic commands ###
