import { apiQuery } from "@/lib/api";
import { useAuth } from "@/lib/auth-ctx";
import { HolderListResp, PublicListNFTCollectionsResp } from "@/types/schemas";
import { useQuery } from "@tanstack/react-query";

export function useHolderQuery(sorting: string, nftCollection: string) {
  const auth = useAuth();
  const params = {
    sorting: sorting || undefined,
    nft_collection_uid: nftCollection || undefined,
  };
  return useQuery({
    enabled: auth.initialized,
    queryKey: auth.user ? ["holders", params] : ["public", "holders", params],
    queryFn: apiQuery<HolderListResp>,
  });
}

export function useListNFTCollectionsQuery() {
  return useQuery({
    queryKey: ["public", "nft-collections"],
    queryFn: apiQuery<PublicListNFTCollectionsResp>,
  });
}
