import { useAppDispatch, useAppSelector } from "@/hooks/store";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";

import {
  auth,
  createUserWithEmailAndPassword,
  googleAuthProvider,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
} from "@/configs/firebase";
import { redirectLogin } from "@/helpers/redirects";
import { selectLogOut, selectUser, setAuthToken, setLogOut, setUser } from "@/store/apps/auth";
import { api, useAuthLoginMutation, useLazyAuthMeQuery } from "@/store/services/api";
import { User as FirebaseUser } from "firebase/auth";

export function AuthContext() {
  const dispatch = useAppDispatch();
  const logOut = useAppSelector(selectLogOut);
  const user = useAppSelector(selectUser);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [authLogin] = useAuthLoginMutation();
  const [authMe] = useLazyAuthMeQuery();

  const authStateChanged = async (authState: FirebaseUser | null) => {
    const authToken = window.localStorage.getItem("authToken");

    try {
      if (authToken || authState) {
        let userData;
        if (authToken) {
          dispatch(setAuthToken(authToken));
          userData = await authMe().unwrap();
        } else if (authState) {
          // Logged in
          setLoading(true);
          const idToken = await authState.getIdToken();

          // Log user in to our backend using the Google JWT for authentication
          userData = await authLogin(idToken).unwrap();
        }

        if (userData) {
          dispatch(setAuthToken(userData.authToken ?? null));
          dispatch(setUser(userData));

          if (router.pathname === "/signin" || router.pathname === "/login") {
            await router.replace((router.query.returnUrl as string) || "/dashboard");
          }
        }
      }
      setLoading(false);
    } catch {
      logout();
    }
  };

  const signInWithGoogle = async () => {
    setLoading(true);
    try {
      await signInWithPopup(auth, googleAuthProvider);
    } finally {
      setLoading(false);
    }
  };

  /*const signInWithEmailAndPassword = (email: string, password: string) =>
    signInWithEmailAndPassword(email, password)

  const createUserWithEmailAndPassword = (email: string, password: string) =>
    createUserWithEmailAndPassword(email, password)
   */

  const logout = () => {
    dispatch(setAuthToken(null));
    if (user) {
      dispatch(setUser(null));
    }
    signOut(auth).then();

    // Clear rtk-query cache to avoid loading stale data when logging in
    dispatch(api.util.resetApiState());
    setLoading(false);
    redirectLogin(router);
  };

  // listen for Firebase state change
  useEffect(() => {
    return onAuthStateChanged(auth, authStateChanged);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.isReady]);

  // Logout if User is reset from a 401
  useEffect(() => {
    if (logOut === true) {
      dispatch(setLogOut(false));
      logout();
    }
  });

  return {
    loading,
    logout,
    user,
    signInWithGoogle,
    signInWithEmailAndPassword,
    createUserWithEmailAndPassword,
  };
}
