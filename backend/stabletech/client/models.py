from datetime import datetime
from decimal import Decimal

from sqlalchemy import Foreign<PERSON>ey, Numeric, func
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship

from common.models import BaseModel
from stabletech.auth.models import User
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Coin, Network, Transaction


class Base(DeclarativeBase, BaseModel):
    pass


class ClientTransaction(Base):
    # Amount requested by User - remove me when Transactions appear in realtime for the User
    amount: Mapped[Decimal | None] = mapped_column(Numeric(precision=32, scale=18))
    coin: Mapped[Coin] = relationship(Coin)
    coin_id: Mapped[int] = mapped_column(ForeignKey(Coin.id))
    cost_basis_usd: Mapped[Decimal | None] = mapped_column(Numeric(precision=32, scale=18))
    is_deposit: Mapped[bool | None] = mapped_column()
    # Network is set by Clients and only used for making deposit/withdrawal requests
    network_id: Mapped[int | None] = mapped_column(ForeignKey(Network.id), index=True)
    network: Mapped[Network | None] = relationship(foreign_keys=[network_id])
    # Transactions cannot point to more than one ClientTransaction
    transaction_id: Mapped[int | None] = mapped_column(
        ForeignKey(Transaction.id), index=True, unique=True
    )
    transaction: Mapped[Transaction | None] = relationship(
        foreign_keys=[transaction_id], lazy="joined"
    )
    revenue_transaction_id: Mapped[int | None] = mapped_column(
        ForeignKey(Transaction.id), index=True
    )
    revenue_transaction: Mapped[Transaction | None] = relationship(
        foreign_keys=[revenue_transaction_id],
    )
    # User must be None when settings.enable_pooled_fund is enabled
    user_id: Mapped[int | None] = mapped_column(ForeignKey(User.id), index=True)
    user: Mapped[User | None] = relationship(foreign_keys=[user_id])

    created_at: Mapped[datetime] = mapped_column(server_default=func.now())
    reviewed_at: Mapped[datetime | None] = mapped_column()

    @property
    def is_interest_payment(self):
        if self.transaction:
            return (
                self.transaction.get_action_enum() == TransactionAction.spend
                and self.user_id is not None
            )

    @property
    def alias_coin(self):
        if self.coin:
            return self.coin.alias_coin.coin if self.coin.alias_coin else self.coin
        return None
