import { But<PERSON> } from "@/lib/components/button";
import * as Dialog from "@/lib/components/dialog";
import { GenerateSeed } from "@/wallet/generate-seed";
import { ImportWallet } from "@/wallet/import-wallet";
import { KeyIcon, WalletIcon } from "lucide-react";
import { useState } from "react";

/**
 * Navigation bar component for the wallet application
 * Contains the app title and action buttons like "Generate Seed"
 */
export function NavBar() {
  const [isGenerateSeedOpen, setIsGenerateSeedOpen] = useState(false);
  const [isImportWalletOpen, setIsImportWalletOpen] = useState(false);

  return (
    <nav className="border-b border-border bg-secondary/10">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          <div className="flex-shrink-0">
            <span className="text-lg font-semibold">Temet Wallet</span>
          </div>
          <div className="flex items-center space-x-4">
            <Dialog.Root
              open={isGenerateSeedOpen}
              onOpenChange={(isOpen) => {
                setIsGenerateSeedOpen(isOpen);
              }}
            >
              <Dialog.Trigger
                onClick={() => {
                  setIsGenerateSeedOpen(true);
                }}
              >
                <Button variant="primary" size="md" className="flex items-center gap-2">
                  <KeyIcon size={20} /> Generate Seed
                </Button>
              </Dialog.Trigger>
              <GenerateSeed setIsOpen={setIsGenerateSeedOpen} isOpen={isGenerateSeedOpen} />
            </Dialog.Root>
            <Dialog.Root
              open={isImportWalletOpen}
              onOpenChange={(isOpen) => {
                setIsImportWalletOpen(isOpen);
              }}
            >
              <Dialog.Trigger
                onClick={() => {
                  setIsImportWalletOpen(true);
                }}
              >
                <Button variant="primary" size="md" className="flex items-center gap-2">
                  <WalletIcon size={20} /> Import Wallet
                </Button>
              </Dialog.Trigger>
              <ImportWallet setIsOpen={setIsImportWalletOpen} isOpen={isImportWalletOpen} />
            </Dialog.Root>
          </div>
        </div>
      </div>
    </nav>
  );
}
