"""
Price Producer

- Fetches and saves all supported Coins on Pricer.
- Continuously fetches and saves Prices for all Coins that have `has_prices` enabled.

Arguments:
    Pass any argument to only fetch the Coins and return before Price loop.
"""

import asyncio
import sys
from datetime import datetime
from decimal import Decimal

from sqlalchemy import func, select, update
from sqlalchemy.orm import Session

from common.conf import logger
from common.pricer.client import PricerClient
from common.psql import SessionLocal
from stabletech.indexer.models import Coin, Network
from stabletech.market.crud import CoinManager
from stabletech.market.loans_health_check import loans_health_check
from stabletech.market.price_alerts import trigger_price_alerts

CYCLE_SECONDS = 60

pricer = PricerClient.from_env()

# This is a list of coins where the uid differs from the lowercase of the coin name.
# You can add other custom coins to this list to be properly initiatlized at env startup
custom_coins: dict[str, str] = {
    "Avalanche": "avalanche-2",
    "BSC": "binancecoin",
    "Cheqd": "cheqd-network",
    "Elrond": "elrond-erd-2",
    "Fiat": "usd-dollar",
    "Fusion": "fsn",
    "Matic": "matic-network",
    "Terra": "terra-luna-2",
}

# This is a list of networks that have not native_coin_id. Adding to this list will
# exclude the networks from any initiatlization updates
no_native_coin_networks: list[str] = [
    "BlockFills",
]


async def main():
    # Get list of all coins
    coins_list = await pricer.get_coins()
    bulk_updates: list[Coin] = []

    for coin in coins_list:
        if coin.id != Coin.DOLLAR_UID:
            bulk_updates.append(
                Coin(
                    name=coin.name,
                    has_prices=False,
                    ticker=coin.symbol,
                    uid=coin.id,
                    market_cap_usd=(
                        Decimal(str(coin.market_cap_usd))
                        if coin.market_cap_usd
                        else Decimal("0")
                    ),
                )
            )

    if bulk_updates:
        with SessionLocal() as db:
            CoinManager(db).add_coins(bulk_updates)

    await initialize_networks_native_coins()

    if len(sys.argv) == 1:
        while True:
            logger.debug("Fetching prices from Pricer")

            with SessionLocal() as db:
                await update_price_for_coins(
                    db, [coin.uid for coin in CoinManager(db).get_coins_with_prices()]
                )

            with SessionLocal() as db:
                loans_health_check(db)

            with SessionLocal() as db:
                trigger_price_alerts(db)

            await asyncio.sleep(CYCLE_SECONDS)


# Pricer API limits to 100 ids per call
BATCH_SIZE = 100


async def update_price_for_coins(db: Session, coin_uids: list[str]):
    batches = (coin_uids[i : i + BATCH_SIZE] for i in range(0, len(coin_uids), BATCH_SIZE))
    for batch in batches:
        await fetch_coins_price(db, batch)
    db.commit()


async def fetch_coins_price(db: Session, ids: list[str]):
    prices = await pricer.get_prices(ids)
    for coin_uid, price in prices.items():
        if not price:
            continue
        coin = CoinManager(db).get_coin_by_uid(coin_uid)
        last_updated_at = price.last_updated_at
        if not price.last_updated_at:
            continue
        latest_usd_price_updated_at = datetime.fromtimestamp(last_updated_at)
        if (
            coin.latest_usd_price_updated_at is not None
            and coin.latest_usd_price_updated_at >= latest_usd_price_updated_at
        ):
            # Skip if we already have a more or equally recent price
            continue
        coin.latest_usd_price = Decimal(str(price.usd))
        coin.latest_usd_price_updated_at = latest_usd_price_updated_at
        coin.usd_24h_change = price.usd_24h_change


async def initialize_networks_native_coins():
    network_updates: list[Network] = []

    with SessionLocal() as db:
        empty_native_coin_networks = (
            db.execute(
                select(Network.name).where(
                    Network.native_coin_id == None,
                    Network.name.not_in(no_native_coin_networks),
                )
            )
            .scalars()
            .all()
        )
        logger.debug(
            f"Fetched Networks with Empty Native Coin IDs: {empty_native_coin_networks}"
        )

    if empty_native_coin_networks:
        network_updates.append(
            update(Network)
            .where(
                Network.name.not_in(list(custom_coins.keys()) + no_native_coin_networks),
                Network.native_coin_id == None,
            )
            .values(
                native_coin_id=select(Coin.id)
                .where(Coin.uid == func.lower(Network.name))
                .scalar_subquery()
            )
        )

        for network in empty_native_coin_networks:
            if coin_uid := custom_coins.get(network):
                network_updates.append(
                    update(Network)
                    .where(Network.name == network)
                    .values(
                        native_coin_id=select(Coin.id)
                        .where(Coin.uid == coin_uid)
                        .scalar_subquery()
                    )
                )

        with SessionLocal() as db:
            for update_stmt in network_updates:
                db.execute(update_stmt)
            db.commit()


asyncio.run(main())
