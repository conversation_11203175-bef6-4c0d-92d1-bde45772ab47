import {
  createWalletClient,
  formatEther,
  Hex,
  http,
  type HttpTransport,
  publicActions,
  PublicActions,
  toHex,
  TransactionSerializable,
  type WalletClient,
} from "viem";
import { type Chain, mainnet, sepolia } from "viem/chains";

type RPCClient = WalletClient & PublicActions;

const BLOCKCHAINS: Set<string> = new Set<string>(["mainnet", "sepolia"]);

export type EthereumSignFn = (message: TransactionSerializable) => Promise<Hex>;

export interface EthereumTransfer {
  readonly amount: number;
  readonly from: Hex;
  readonly to: Hex;
  readonly sign: EthereumSignFn;
}

export class EthereumRPC {
  private readonly client: RPCClient;

  constructor(chain: Chain, transport: HttpTransport) {
    this.client = createWalletClient({ chain, transport }).extend(publicActions);
  }

  static create(network: string): EthereumRPC {
    if (!BLOCKCHAINS.has(network)) {
      throw new Error(`Unsupported network: ${network}`);
    }
    switch (network) {
      case "mainnet":
        return new EthereumRPC(mainnet, http(import.meta.env.VITE_ETHEREUM_API_URL));
      case "sepolia":
        return new EthereumRPC(sepolia, http(import.meta.env.VITE_SEPOLIA_API_URL));
      default:
        throw new Error(`Missing implementation for network: ${network}`);
    }
  }

  async getBalance(address: string): Promise<string> {
    return formatEther(await this.client.getBalance({ address: toHex(address) }));
  }

  async transfer({ amount, from, to, sign }: EthereumTransfer): Promise<Hex> {
    const request = await this.client.prepareTransactionRequest({
      account: from,
      value: BigInt(amount * 10 ** 18),
      to,
      chain: undefined,
    });
    const serializedTransaction = await sign({
      chainId: request.chainId,
      nonce: request.nonce,
      to: request.to,
      value: request.value,
      gas: 21_000n,
      maxFeePerGas: request.maxFeePerGas,
      maxPriorityFeePerGas: request.maxPriorityFeePerGas,
    });
    return this.client.sendRawTransaction({ serializedTransaction });
  }
}
