"""Lending Protocol

Revision ID: 0016
Revises: 0015
Create Date: 2023-02-06 05:35:55.949616

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0016"
down_revision = "0015"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "lendingprotocol",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_lendingprotocol_name"), "lendingprotocol", ["name"], unique=True)
    op.add_column("account", sa.Column("lending_protocol_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "account", "lendingprotocol", ["lending_protocol_id"], ["id"])
    op.create_index(op.f("ix_bridge_name"), "bridge", ["name"], unique=True)
    op.drop_constraint("exchange_name_key", "exchange", type_="unique")
    op.create_index(op.f("ix_exchange_name"), "exchange", ["name"], unique=True)
    op.drop_constraint("farm_name_key", "farm", type_="unique")
    op.create_index(op.f("ix_farm_name"), "farm", ["name"], unique=True)
    op.add_column("transaction", sa.Column("api_type", sa.String(), nullable=True))
    op.create_index(op.f("ix_wallet_name"), "wallet", ["name"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_wallet_name"), table_name="wallet")
    op.drop_column("transaction", "api_type")
    op.drop_index(op.f("ix_farm_name"), table_name="farm")
    op.create_unique_constraint("farm_name_key", "farm", ["name"])
    op.drop_index(op.f("ix_exchange_name"), table_name="exchange")
    op.create_unique_constraint("exchange_name_key", "exchange", ["name"])
    op.drop_index(op.f("ix_bridge_name"), table_name="bridge")
    op.drop_column("account", "lending_protocol_id")
    op.drop_index(op.f("ix_lendingprotocol_name"), table_name="lendingprotocol")
    op.drop_table("lendingprotocol")
    # ### end Alembic commands ###
