resource "kubernetes_namespace" "namespace" {
  metadata {
    name = var.namespace
  }
}

locals {
  dns_managed_zone = {
    name     = "diwan-digital"
    dns_name = "diwan.digital."
  }
}

module "stabletech" {
  source = "../modules/stabletech"

  api_domain        = "api.diwan.digital"
  env_name          = "diwan"
  managed_zone_name = local.dns_managed_zone.name
  namespace         = var.namespace
  postgresql_db     = "diwan"
  release           = local.release
  secrets           = local.secrets
}

module "dashboard_ui" {
  source = "../modules/ui"

  dns_managed_zone    = local.dns_managed_zone
  namespace           = var.namespace
  project_id          = local.project_id
  website_domain_name = "diwan.digital"
}
