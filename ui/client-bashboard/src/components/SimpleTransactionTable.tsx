import { DataGridTable, TableColumns } from "@/components/DataGridTable";
import { displayTxHash } from "@/context/env";
import {
  addressFormatter,
  capitalizeWord,
  currencyFormatter,
  formatDateTime,
} from "@/helpers/formatters";
import {
  SimpleTransaction,
  SimpleTransfer,
  simplifyTransactions,
} from "@/helpers/transaction-simplifier";
import { Transaction } from "@/types/schemas";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";
import { Link, Stack, Typography } from "@mui/material";
import { GridCellParams } from "@mui/x-data-grid-pro";
import { useMemo } from "react";

function getColumns(rows: readonly SimpleTransactionRow[]) {
  const hasCostBasis = rows.some((row) => row.costBasisUsd != null);

  const items: (TableColumns<SimpleTransactionRow> | false)[] = [
    {
      field: "confirmedAt",
      header: "At",
      renderCell: (params: GridCellParams) => formatDateTime(params.value),
    },

    {
      field: "clientAction",
      header: "Event",
      renderCell: (params: GridCellParams) => (
        <Typography color={"primary"}>{capitalizeWord(params.value)}</Typography>
      ),
    },
    {
      field: "transfers",
      header: "Amount",
      sortable: false,
      renderCell: (params: GridCellParams) => (
        <Stack direction={"row"} spacing={4}>
          {params.value.map((transfer: SimpleTransfer, index: number) => {
            const transferColor =
              transfer.isWithdrawal && params.row.action !== "spend"
                ? "error.main"
                : "success.main";

            return (
              <Stack key={transfer.id} alignItems={"flex-end"} direction={"row"} spacing={1}>
                <Typography color={transferColor}>
                  {currencyFormatter.format(transfer.amount)}{" "}
                </Typography>
                <Typography variant={"caption"} color={transferColor}>
                  {transfer.coin.ticker.toUpperCase()}
                </Typography>

                {index === 0 && params.value.length > 1 && (
                  <ArrowRightAltIcon color={"secondary"} sx={{ pl: 2 }} fontSize={"medium"} />
                )}
              </Stack>
            );
          })}
        </Stack>
      ),
    },
    hasCostBasis && {
      field: "costBasisUsd",
      header: "Cost Basis",
      renderCell: (params: GridCellParams) =>
        params.value != null ? `$ ${currencyFormatter.format(Number(params.value))}` : null,
    },
    displayTxHash && {
      field: "txHash",
      header: "Tx Hash",
      renderCell: ({ row }: GridCellParams) => (
        <Link
          href={`${row.network.explorerUrl}/tx/${row.txHash}`}
          target={"_blank"}
          color={"secondary"}
        >
          {addressFormatter(row.txHash)}
        </Link>
      ),
    },
  ];
  return items.filter((item) => item !== false);
}

type SimpleTransactionRow = SimpleTransaction;

interface TransactionTableProps {
  rows: readonly Transaction[];
}

export const SimpleTransactionTable = (props: TransactionTableProps) => {
  const rows = useMemo(() => props.rows.map(simplifyTransactions), [props.rows]);
  const columns = useMemo(() => getColumns(rows), [rows]);
  return (
    <DataGridTable
      rows={rows}
      sortByField={"confirmedAt"}
      sortDescending
      tableColumns={columns}
    />
  );
};
