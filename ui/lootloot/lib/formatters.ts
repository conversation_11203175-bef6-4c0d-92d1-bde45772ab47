export const currencyFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

export const numberFormatter = new Intl.NumberFormat("en-US");

export const numberFormatter6D = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 6,
  minimumFractionDigits: 6,
});

export const dayMonthFormatter = new Intl.DateTimeFormat("en-US", {
  day: "numeric",
  month: "short",
});

export const dateFormatter = new Intl.DateTimeFormat("en-US", {
  day: "numeric",
  month: "long",
  year: "numeric",
});
