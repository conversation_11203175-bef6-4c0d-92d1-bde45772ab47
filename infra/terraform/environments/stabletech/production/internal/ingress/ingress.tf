locals {
  domain_name = trimsuffix(var.dns_managed_zone.dns_name, ".")
}

# IPv4 is required to work properly inside the local cluster
resource "google_compute_global_address" "ipv4" {
  name       = "internal-${var.env_name}-ipv4"
  ip_version = "IPV4"
}

resource "google_compute_managed_ssl_certificate" "default" {
  name = "internal-${var.env_name}"
  managed {
    domains = [local.domain_name]
  }
}

resource "kubernetes_ingress_v1" "ingress" {
  wait_for_load_balancer = true
  metadata {
    name      = "internal"
    namespace = var.namespace
    annotations = {
      "kubernetes.io/ingress.class"                 = "gce"
      "kubernetes.io/ingress.allow-http"            = "false"
      "ingress.gcp.kubernetes.io/pre-shared-cert"   = google_compute_managed_ssl_certificate.default.name
      "kubernetes.io/ingress.global-static-ip-name" = google_compute_global_address.ipv4.name
    }
  }
  spec {
    rule {
      http {
        path {
          path      = "/*"
          path_type = "ImplementationSpecific"
          backend {
            service {
              name = "pricer"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
  }
}

resource "google_dns_record_set" "ipv4" {
  name         = var.dns_managed_zone.dns_name
  managed_zone = var.dns_managed_zone.name
  type         = "A"
  ttl          = 86400
  rrdatas      = [google_compute_global_address.ipv4.address]
}
