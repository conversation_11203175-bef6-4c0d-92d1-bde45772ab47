from decimal import Decimal
from enum import IntEnum
from typing import NamedTuple

from sqlalchemy import (
    Numeric,
    String,
    bindparam,
    case,
    cast,
    column,
    desc,
    func,
    literal,
    select,
    values,
)
from sqlalchemy.orm import aliased

from lootloot.auth.models import User
from lootloot.base import BaseManager
from lootloot.kinds import HolderSorting
from lootloot.models import NFT, UserWallet


class GroupKind(IntEnum):
    USER = 1
    ADDRESS = 2


class HolderRow(NamedTuple):
    rank: int
    group_kind: GroupKind
    group_id: str
    group_name: str | None
    nft_count: int
    sol_amount: Decimal


class NFTHolderManager(BaseManager):
    def get(
        self,
        nft_collection_sol_prices: dict[str, Decimal],
        *,
        sorting=HolderSorting.by_count,
        user_id=0,
        limit=100,
    ) -> list[HolderRow]:
        order_by = get_holder_sorting(sorting)
        nft_collection_uids = bindparam(
            "nft_collection_uids", [uid for uid in nft_collection_sol_prices], expanding=True
        )

        collection_price = values(
            column("uid", String),
            column("sol_price", Numeric),
            name="collection_price",
        ).data(list(nft_collection_sol_prices.items()))
        collection_price = select(*collection_price.c).cte("collection_price")

        # NFT is "eligible" if it belongs to a relevant collection and it's not rekt.
        eligible_nft = aliased(
            NFT,
            select(NFT)
            .where(NFT.nft_collection_uid.in_(nft_collection_uids), ~NFT.is_rekt_sql())
            .cte("eligible_nft"),
        )

        nft_sol_price = (
            select(collection_price.c.sol_price)
            .where(collection_price.c.uid == eligible_nft.nft_collection_uid)
            .scalar_subquery()
        )

        user_has_display_name = User.display_name != ""

        has_multiple_non_empty_wallets = (
            select(func.count() > 1)
            .select_from(
                select(UserWallet.id)
                .distinct()
                .join(
                    eligible_nft, eligible_nft.owner_address_uri == UserWallet.address_uri_sql()
                )
                .correlate(User)
                .where(UserWallet.user_id == User.id)
                .subquery("user_non_empty_wallets")
            )
            .scalar_subquery()
        )

        # We're calling user "eligible" to mean it should be included in the
        # holder list as an aggregated of their wallets. We're excluding users
        # without a display name and that don't have multiple non-empty wallets.
        # This is to avoid displaying "anonymous" users in the leaderboard for
        # users that only have one non-empty wallet.
        eligible_user = (
            select(
                User.id,
                case((user_has_display_name, User.display_name)).label("display_name"),
            )
            .where(user_has_display_name | has_multiple_non_empty_wallets)
            .cte("eligible_user")
        )

        by_user = (
            select(
                literal(GroupKind.USER.value, literal_execute=True).label("group_kind"),
                cast(eligible_user.c.id, String).label("group_id"),
                eligible_user.c.display_name.label("group_name"),
                nft_sol_price.label("sol_amount"),
            )
            .select_from(eligible_user)
            .join(UserWallet, UserWallet.user_id == eligible_user.c.id)
            .join(eligible_nft, eligible_nft.owner_address_uri == UserWallet.address_uri_sql())
            .cte("by_user")
        )

        by_address = (
            select(
                literal(GroupKind.ADDRESS.value, literal_execute=True).label("group_kind"),
                eligible_nft.owner_address_uri.label("group_id"),
                literal(None, literal_execute=True).label("group_name"),
                nft_sol_price.label("sol_amount"),
            )
            .select_from(eligible_nft)
            .where(
                # Exclude addresses owned by "eligible users" -- this is to avoid
                # having two almost identical rows, one for the user and other for
                # the main wallet address that the user owns.
                eligible_nft.owner_address_uri.not_in(
                    select(UserWallet.address_uri_sql()).join(
                        eligible_user, UserWallet.user_id == eligible_user.c.id
                    )
                )
            )
            .cte("by_address")
        )

        all_ = select(by_user).union_all(select(by_address)).subquery("all")

        holder_list = (
            select(
                all_.c.group_kind,
                all_.c.group_id,
                all_.c.group_name,
                func.count().label("nft_count"),
                func.sum(all_.c.sol_amount).label("sol_amount"),
            )
            .group_by(all_.c.group_kind, all_.c.group_id, all_.c.group_name)
            .order_by(order_by, all_.c.group_kind, all_.c.group_id)
            .subquery()
        )

        holder_list = select(func.row_number().over().label("rank"), *holder_list.columns).cte(
            "holder_list"
        )

        is_within_limit = holder_list.c.rank <= limit

        if user_id:
            is_current_user = (holder_list.c.group_kind == GroupKind.USER.value) & (
                holder_list.c.group_id == str(user_id)
            )
            is_current_user_wallet = (holder_list.c.group_kind == GroupKind.ADDRESS.value) & (
                holder_list.c.group_id.in_(
                    select(UserWallet.address_uri_sql()).where(UserWallet.user_id == user_id)
                )
            )
            where_sql = is_within_limit | is_current_user | is_current_user_wallet
        else:
            where_sql = is_within_limit

        result = self.db.execute(select(holder_list).where(where_sql))
        return [HolderRow(row[0], GroupKind(row[1]), *row[2:]) for row in result]


def get_holder_sorting(sorting: HolderSorting):
    match sorting:
        case HolderSorting.by_value:
            return desc("sol_amount")
        case HolderSorting.by_count:
            return desc("nft_count")
        case _:
            raise ValueError(f"Unsupported sorting: {sorting}")
