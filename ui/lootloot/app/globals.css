@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

@layer base {
  :root {
    --background: oklch(100% 0 0);
    --foreground: oklch(0 0 0);

    --muted: oklch(96.5% 0.007 248);
    --muted-foreground: oklch(55.5% 0.04 257);

    --accent: oklch(96.5% 0.007 248);
    --accent-foreground: oklch(21% 0.04 266);

    --popover: oklch(100% 0 0);
    --popover-foreground: oklch(0 0 0);

    --card: oklch(100% 0 0);
    --card-foreground: oklch(21% 0.04 266);

    --primary: oklch(48% 0.24 292);
    --primary-foreground: oklch(100% 0 0);

    --secondary: oklch(96.5% 0.007 248);
    --secondary-foreground: oklch(21% 0.04 266);

    --destructive: oklch(62.66% 0.257 29.2);
    --destructive-foreground: oklch(98% 0.0042 248);

    --border: oklch(92.4% 0.0076 284);
    --ring: oklch(45% 0.27 274);
  }

  :where(.dark, .dark *) {
    --background: oklch(12.8% 0.027 262);
    --foreground: oklch(92.65% 0.013 253);

    --muted: oklch(24.4% 0.045 284);
    --muted-foreground: oklch(64.2% 0.036 257);

    --accent: oklch(27.6% 0.037 258);
    --accent-foreground: oklch(98% 0.0042 248);

    --popover: oklch(12.8% 0.027 262);
    --popover-foreground: oklch(92.65% 0.013 253);

    --card: oklch(12.8% 0.027 262);
    --card-foreground: oklch(92.65% 0.013 253);

    --primary: oklch(49% 0.24 292);
    --primary-foreground: oklch(100% 0 0);

    --secondary: oklch(20.8% 0.04 266);
    --secondary-foreground: oklch(98% 0.0042 248);

    --destructive: oklch(40% 0.135 25.8);
    --destructive-foreground: oklch(98% 0.0042 248);

    --border: oklch(43.5% 0.036 282);
    --ring: oklch(61.7% 0.217 286);
  }

  body {
    color: var(--foreground);
    background: var(--background);
  }

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border);
  }
}

@theme {
  --animate-check: check 0.32s ease-out;

  @keyframes check {
    0% {
      transform: rotate(0deg) scale(1);
    }
    7% {
      transform: rotate(6deg) scale(1.3);
    }
    100% {
      transform: rotate(0deg) scale(1);
    }
  }
}

@theme inline {
  --color-border: var(--border);
  --color-ring: var(--ring);
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
}

@import "tw-animate-css";
