from fastapi import APIRouter

from common.conf import Environment, settings
from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from stabletech.indexer.internal_sync.client import SUBSIDIARY_ENVS
from stabletech.indexer.internal_sync.sync import sync_missing_transactions

router = APIRouter()


@router.post("/transactions/sync/{company_name}")
async def sync_transactions(
    company_name: str,
    db: DatabaseSession,
) -> int:
    """Sync missing transactions from a subsidiary company."""
    try:
        company = Environment("local" if settings.is_local else company_name)
        if company not in SUBSIDIARY_ENVS:
            raise HTTP400Exception("Invalid company")
    except ValueError:
        raise HTTP400Exception("Invalid company name")
    return sync_missing_transactions(company, db)
