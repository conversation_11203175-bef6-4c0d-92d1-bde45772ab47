import ImportCapitalPositionFailureDialog from "@/components/admin/ImportCapitalPositionFailureDialog";
import ImportFailureDialog from "@/components/admin/ImportFailureDialog";
import TransactionSyncForm from "@/components/admin/TransactionSyncForm";
import { isLocal, isStableTech } from "@/context/env";
import { useTimedBool } from "@/hooks/time";
import {
  useAccountingDeleteCapitalPositionsMutation,
  useIndexerImportCointrackingMutation,
  useLazyIndexerJobQuery,
  useLazyIndexerSyncBlockFillsQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { ImportRequest } from "@/types/indexer";
import { ImportResult } from "@/types/schemas";
import { CancelRounded, CheckCircle } from "@mui/icons-material";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  <PERSON>,
  Button,
  Card,
  CardActions,
  CardContent,
  CircularProgress,
  Fade,
  Grid,
  Grow,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { MouseEvent, useCallback, useEffect, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";

export default function Import() {
  return (
    <Grid container>
      <Grid item xs>
        <Typography variant="h3" gutterBottom>
          Import
        </Typography>
        <Stack gap={6}>
          <Importer provider="cointracking" title="CoinTracking">
            <Typography variant="body1" gutterBottom>
              <strong>Important:</strong> when exporting data on CoinTracking, only use “full
              export” for CSV option.
            </Typography>
          </Importer>
          <Importer provider="bitcointax" title="Bitcoin.tax">
            <DeleteCapitalPositionsForm />
          </Importer>
          <Grid container spacing={6}>
            <Grid item xs={12} md={6}>
              <BlockFillsSyncForm />
            </Grid>
            {(isLocal || isStableTech) && (
              <Grid item xs={12} md={6}>
                <TransactionSyncForm />
              </Grid>
            )}
          </Grid>
        </Stack>
      </Grid>
    </Grid>
  );
}

const acceptFileTypes = { "text/csv": [".csv"] };

function Importer({
  provider,
  title,
  children,
}: {
  provider: ImportRequest["provider"];
  title: string;
  children?: React.ReactNode;
}) {
  const [file, setFile] = useState<File | undefined>();
  const [doImport, { isLoading, error, data }, reset] = useImport();
  const [didUpload, setDidUpload] = useTimedBool(false);
  const [didCancel, setDidCancel] = useTimedBool(false);

  const onDrop = useCallback(
    (acceptedFiles: readonly File[]) => {
      setFile(acceptedFiles[0]);
      reset();
      setDidUpload(false, -1);
      setDidCancel(false, -1);
    },
    [reset, setDidUpload, setDidCancel],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptFileTypes,
    disabled: isLoading,
    onDrop: onDrop,
  });

  const inputProps = { ...getInputProps(), id: "admin-import-cointracking" };

  const doUploadHelper = (params: Omit<ImportRequest, "file">) => {
    if (file == null) return;
    doImport({ ...params, file }).then((importResult) => {
      if (importResult == null || !importResult.ok) return;
      setFile(undefined);
      reset();
      setDidUpload(true, 3000);
    });
  };

  const handleUpload = (event: MouseEvent) => {
    event.stopPropagation();
    doUploadHelper({ provider });
  };

  // TODO: fix Card `elevation` prop, instead of using custom shadow
  const cardStyle = {
    boxShadow: isDragActive ? "0px 5px 13px 4px rgba(19, 17, 32, 0.3)" : undefined,
  };

  const cardContent = (
    <Card style={cardStyle} {...getRootProps()}>
      <input {...inputProps} />
      <CardContent>
        <Typography variant="h4" gutterBottom>
          {title}
        </Typography>
        {children}
        <Typography variant="body2">
          {file != null ? (
            <>
              Selected: <em>{file.name}</em>
            </>
          ) : isDragActive ? (
            <em>Drop...</em>
          ) : (
            "Drag 'n' drop a file here, or click to select it"
          )}
        </Typography>
        {error != null && (
          <Typography variant="body2" color="error" marginTop={2}>
            Something went wrong: {error}
          </Typography>
        )}
      </CardContent>
      <CardActions style={{ paddingLeft: 8 }}>
        <Button
          disabled={isLoading}
          component="label"
          htmlFor={inputProps.id}
          color={isDragActive ? "info" : "primary"}
        >
          Select file
        </Button>
        <Button disabled={isLoading || file == null} onClick={handleUpload} color="success">
          Upload file
        </Button>
        {isLoading && <CircularProgress size={22} style={{ marginLeft: 8 }} />}
        <Grow in={didUpload || didCancel} timeout={400}>
          {didUpload ? <CheckCircle color="success" /> : <CancelRounded color="warning" />}
        </Grow>
      </CardActions>
    </Card>
  );

  const dialog =
    provider == "cointracking" ? (
      <ImportFailureDialog
        data={data}
        onClose={({ skipAlreadyInDb = false, mergeInFileDuplicates = false } = {}) => {
          if (skipAlreadyInDb || mergeInFileDuplicates) {
            doUploadHelper({ provider, skipAlreadyInDb, mergeInFileDuplicates });
          } else {
            setFile(undefined);
            reset();
            setDidCancel(true, 3000);
          }
        }}
      />
    ) : (
      <ImportCapitalPositionFailureDialog
        data={data}
        onClose={() => {
          setFile(undefined);
          reset();
          setDidCancel(true, 3000);
        }}
      />
    );

  return (
    <>
      {cardContent}
      {dialog}
    </>
  );
}

type ImportFn = (params: ImportRequest) => Promise<ImportResult | undefined>;

type ResetFn = () => void;

interface ImportState {
  readonly error?: string;
  readonly isLoading: boolean;
  readonly data?: ImportResult;
}

const INITIAL_IMPORT_STATE: ImportState = { isLoading: false };

/**
 * Upload and wait import job to complete helper.
 */
function useImport(): [ImportFn, ImportState, ResetFn] {
  const [doUpload] = useIndexerImportCointrackingMutation();
  const [fetchJob] = useLazyIndexerJobQuery();
  const [state, setState] = useState(INITIAL_IMPORT_STATE);
  const abortControllerRef = useRef<AbortController>();

  const uploadAndWaitJob = useCallback(
    async (params: ImportRequest): Promise<ImportResult | undefined> => {
      abortControllerRef.current?.abort();
      abortControllerRef.current = new AbortController();
      const { signal } = abortControllerRef.current;

      setState({ isLoading: true });

      try {
        const result = await doUpload(params);
        if (signal.aborted) return;
        if ("error" in result) {
          setState((state) => ({ ...state, error: getErrorDetail(result.error) }));
          return;
        }

        let attempt = 1;
        while (true) {
          let sleepTime = Math.pow(1.5, attempt) * 1000;
          sleepTime = Math.min(sleepTime, 5000); // max 5s
          await new Promise((resolve) => setTimeout(resolve, sleepTime));
          if (signal.aborted) return;

          const { error, data } = await fetchJob(result.data.id);
          if (signal.aborted) return;

          if (error || data == null) {
            const msg = getErrorDetail(error, { fallback: "Unexpected response fetching job" });
            setState((state) => ({ ...state, error: msg }));
            return;
          }

          if (data.status === "failed") {
            const error = data.error ?? "Unknown error";
            setState((state) => ({ ...state, error }));
            return;
          }

          if (data.result == null) {
            attempt++;
            continue;
          }

          const importResult = data.result as ImportResult;
          setState((state) => ({ ...state, data: importResult }));
          return importResult;
        }
      } catch (error) {
        const msg = getErrorDetail(error, { fallback: "Unexpected error" });
        setState((state) => ({ ...state, error: msg }));
      } finally {
        setState((state) => ({ ...state, isLoading: false }));
      }
    },
    [doUpload, fetchJob],
  );

  const reset: ResetFn = useCallback(() => {
    abortControllerRef.current?.abort();
    setState(INITIAL_IMPORT_STATE);
  }, []);

  // Abort any pending request on unmount
  useEffect(() => {
    return () => abortControllerRef.current?.abort();
  }, []);

  return [uploadAndWaitJob, state, reset];
}

function BlockFillsSyncForm() {
  const [syncBlockFills, { isLoading, isError, error }] = useLazyIndexerSyncBlockFillsQuery();

  return (
    <Card>
      <CardContent>
        <Typography variant="h4" gutterBottom>
          Block Fills
        </Typography>
        <Typography variant="body1">Fetch newest swaps executed on Block Fills.</Typography>
        {isError && (
          <Typography variant="body2" color="error" mt={4}>
            {getErrorDetail(error, { fallback: "Unexpected error" })}
          </Typography>
        )}
      </CardContent>
      <CardActions style={{ paddingLeft: 8 }}>
        {/* eslint-disable-next-line jsx-a11y/label-has-associated-control, jsx-a11y/click-events-have-key-events, jsx-a11y/no-noninteractive-element-interactions */}
        <LoadingButton loading={isLoading} component="label" onClick={() => syncBlockFills()}>
          Sync
        </LoadingButton>
      </CardActions>
    </Card>
  );
}

function DeleteCapitalPositionsForm() {
  const [deleteCapitalPositions, { isLoading, isError, error }] =
    useAccountingDeleteCapitalPositionsMutation();
  const [didDelete, setDidDelete] = useTimedBool(false);

  const [year, setYear] = useState("");
  const [attemptedSubmit, setAttemptedSubmit] = useState(false);

  const parsedYear = Number.parseFloat(year ?? "");
  const isValidYear = Number.isInteger(parsedYear);
  const yearError =
    (year || attemptedSubmit) && !isValidYear ? "Please enter a valid year" : undefined;

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-noninteractive-element-interactions
    <Box
      component="form"
      mt={4}
      mb={8}
      onClick={(event) => event.stopPropagation()}
      onSubmit={(event) => {
        event.preventDefault();
        setAttemptedSubmit(true);
        if (Number.isInteger(parsedYear)) {
          deleteCapitalPositions({ year: parsedYear })
            .unwrap()
            .then(() => {
              setDidDelete(true, 5000);
              setYear("");
              setAttemptedSubmit(false);
            });
        }
      }}
    >
      <Typography variant="body1">Delete capital positions for a specific year.</Typography>
      <Stack direction="row" gap={4} alignItems="flex-start" marginTop={3}>
        <TextField
          size="small"
          label="Year"
          name="year"
          value={year}
          onChange={(event) => setYear(event.target.value)}
          type="number"
          helperText={yearError}
          error={Boolean(yearError)}
        />
        <Button
          sx={{ height: 40 }}
          variant="outlined"
          color="error"
          type="submit"
          disabled={isLoading}
        >
          Delete
        </Button>
        <Box sx={{ height: 40 }} display="flex" alignItems="center">
          {isLoading && <CircularProgress size={22} />}
          <Fade in={didDelete} timeout={{ enter: 100, exit: 1000 }}>
            <CheckCircle color="success" />
          </Fade>
        </Box>
      </Stack>
      {isError && (
        <Typography variant="body2" color="error" mt={2}>
          Something went wrong: {getErrorDetail(error, { fallback: "Unexpected error" })}
        </Typography>
      )}
    </Box>
  );
}
