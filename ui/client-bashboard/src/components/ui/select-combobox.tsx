"use client";

import { button } from "@/components/ui/button";
import * as Command from "@/components/ui/command";
import * as Popover from "@/components/ui/popover";
import { defaultFilter } from "cmdk";
import { cx } from "cva";
import { Check, ChevronDown } from "lucide-react";
import { forwardRef, useMemo, useState } from "react";

interface Option {
  readonly value: string;
  readonly label: string;
  readonly keywords?: string[] | undefined;
}

type CommandFilter = typeof defaultFilter;

const EMPTY_ARRAY: [] = [];

export interface Props {
  options: readonly Option[];
  placeholder: string;
  value: string;
  onValueChange: (value: string) => void;
  id?: string | undefined;
  emptyText?: string | undefined;
  searchText?: string | undefined;
  disabled?: boolean | undefined;
  onBlur?: (() => void) | undefined;
}

export const SelectCombobox = forwardRef<HTMLButtonElement, Props>(
  function SelectCombobox(props, ref) {
    const {
      options,
      placeholder,
      value,
      onValueChange,
      id,
      emptyText = "No option found.",
      searchText = "Search option...",
      disabled = false,
      onBlur,
    } = props;
    const [open, setOpen] = useState(false);

    const handleSelect = (selectedValue: string) => {
      onValueChange(selectedValue === value ? "" : selectedValue);
      setOpen(false);
    };

    let label = placeholder;

    const items = options.map((option) => {
      const isSelected = value === option.value;
      if (isSelected) {
        label = option.label;
      }
      return (
        <Command.Item
          key={option.value}
          value={option.value}
          onSelect={handleSelect}
          keywords={option.keywords ?? EMPTY_ARRAY}
          disabled={disabled}
        >
          <Check className={cx("mr-2 h-4 w-4", isSelected ? "visible" : "invisible")} />
          {option.label}
        </Command.Item>
      );
    });

    /**
     * This allows users to search based on the human-readable labels rather than the
     * internal values.
     */
    const filterFn = useMemo((): CommandFilter => {
      const labelsMap = new Map<string, string>();
      for (const opt of options) {
        labelsMap.set(opt.value, opt.label);
      }
      return (value, search, keywords?) =>
        defaultFilter(labelsMap.get(value) ?? value, search, keywords);
    }, [options]);

    return (
      <Popover.Root open={open} onOpenChange={setOpen}>
        <Popover.Trigger
          ref={ref}
          className={button({
            variant: "outline",
            className: "justify-between! rounded-md! px-3! font-normal!",
          })}
          id={id}
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          onBlur={onBlur}
        >
          <span className="overflow-hidden text-ellipsis">{label}</span>
          <ChevronDown strokeWidth={1} className="-mr-1.5 h-6 w-6 shrink-0 opacity-60" />
        </Popover.Trigger>
        <Popover.Content className="flex max-w-screen flex-col p-0!" align="start">
          <Command.Root filter={filterFn}>
            <Command.Input placeholder={searchText} disabled={disabled} />
            <Command.List>
              <Command.Empty>{emptyText}</Command.Empty>
              <Command.Group className="overflow-y-auto!">{items}</Command.Group>
            </Command.List>
          </Command.Root>
        </Popover.Content>
      </Popover.Root>
    );
  },
);
