"use client";

import { button } from "@/lib/components/button";
import { Label } from "@/lib/components/form";
import * as Popover from "@/lib/components/popover";
import * as Select from "@/lib/components/select";
import { SelectCombobox } from "@/lib/components/select-combobox";
import { useRouter, useSearchParams } from "@/lib/navigation";
import { NFTListItem } from "@/types/schemas";
import { cx } from "class-variance-authority";
import { Filter } from "lucide-react";
import { Fragment, useCallback, useId, useMemo } from "react";

export type NftFiltering = ReturnType<typeof useNftFiltering>;

export const NFT_FILTERING_STUB: NftFiltering = {
  options: [],
  state: new Map(),
  setFilter: () => {
    // empty
  },
  clearFilters: () => {
    // empty
  },
  nfts: [],
};

export function useNftFiltering(allNfts: readonly NFTListItem[]) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const options = useMemo(() => getOptions(allNfts), [allNfts]);

  const state: ReadonlyMap<string, string> = useMemo(() => {
    const map = new Map<string, string>();
    for (const [name] of options) {
      const value = searchParams?.get(name);
      if (value) {
        map.set(name, value);
      }
    }
    return map;
  }, [options, searchParams]);

  const setFilter = useCallback(
    (name: string, value: string): void => {
      if (!router) return;
      const newParams = new URLSearchParams(location.search);
      newParams.set(name, value);
      router.replace(`?${newParams.toString()}`);
    },
    [router],
  );

  const clearFilters = useCallback(
    (names: readonly string[]) => {
      if (!router) return;
      const newParams = new URLSearchParams(location.search);
      for (const name of names) {
        newParams.delete(name);
      }
      router.replace(`?${newParams.toString()}`);
    },
    [router],
  );

  const nfts: readonly NFTListItem[] = useMemo(() => {
    const filteredNfts = [];
    for (const nft of allNfts) {
      let include = true;
      for (const [name, value] of state) {
        const attribute = nft.attributes_data.find((att) => att.trait_type === name);
        if (attribute == null || attribute.value !== value) {
          include = false;
          break;
        }
      }
      if (include) {
        filteredNfts.push(nft);
      }
    }
    return filteredNfts;
  }, [allNfts, state]);

  return { options, state, setFilter, clearFilters, nfts } as const;
}

/**
 * Array of tuples of filter name and possible values.
 */
type Options = readonly (readonly [string, readonly string[]])[];

export function FilterButton({
  options,
  state,
  setFilter,
}: {
  options: Options;
  state: ReadonlyMap<string, string>;
  setFilter: (name: string, value: string) => void;
}) {
  const id = useId();

  return (
    <Popover.Root>
      <Popover.Trigger className={cx(button(), "gap-2")}>
        <Filter size={16} />
        Filter
      </Popover.Trigger>
      <Popover.Content className="w-80" align="start">
        {options.length === 0 && (
          <div className="grid h-16 place-items-center">
            <span>No filters available</span>
          </div>
        )}
        <div className="grid grid-cols-[auto_1fr] items-center gap-2">
          {options.map(([name, values]) => {
            const currentId = `${id}filter-${name}`;

            const selectedValue = state.get(name) ?? "";
            const handleSelect = (value: string): void => {
              setFilter(name, value);
            };

            const selector =
              values.length < 9 ? (
                <Select.Root value={selectedValue} onValueChange={handleSelect}>
                  <Select.Trigger id={currentId} />
                  <Select.Content>
                    {values.map((option) => (
                      <Select.Item key={option} value={option}>
                        {option}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Root>
              ) : (
                <SelectCombobox
                  options={values.map((value) => ({ label: value, value }))}
                  placeholder=""
                  value={selectedValue}
                  onValueChange={handleSelect}
                  id={currentId}
                  emptyText="No value found."
                  searchText="Search value..."
                />
              );

            return (
              <Fragment key={name}>
                <Label htmlFor={currentId} className="text-right">
                  {name}
                </Label>
                {selector}
              </Fragment>
            );
          })}
        </div>
      </Popover.Content>
    </Popover.Root>
  );
}

function getOptions(nfts: readonly NFTListItem[]): Options {
  const options = new Map<string, Set<string>>();
  for (const nft of nfts) {
    for (const att of nft.attributes_data) {
      let optionsSet = options.get(att.trait_type);
      if (!optionsSet) {
        optionsSet = new Set();
        options.set(att.trait_type, optionsSet);
      }
      optionsSet.add(att.value);
    }
  }

  const optionsArray: [string, string[]][] = [];
  for (const [trait, values] of options) {
    optionsArray.push([trait, [...values]]);
  }

  return optionsArray;
}
