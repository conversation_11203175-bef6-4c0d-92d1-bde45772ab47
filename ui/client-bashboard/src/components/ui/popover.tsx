"use client";

import * as RadixPopover from "@radix-ui/react-popover";
import { cx } from "cva";
import { forwardRef } from "react";

export * from "@radix-ui/react-popover";

export const PopoverContent = forwardRef<
  React.ElementRef<typeof RadixPopover.Content>,
  React.ComponentPropsWithoutRef<typeof RadixPopover.Content>
>(function PopoverContent({ className, align = "center", sideOffset = 4, ...props }, ref) {
  return (
    <RadixPopover.Portal>
      <RadixPopover.Content
        ref={ref}
        align={align}
        sideOffset={sideOffset}
        className={cx(
          "max-h-[var(--radix-popper-available-height)] overflow-auto rounded-md border border-border bg-popover p-4 text-popover-foreground shadow-md outline-hidden data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
          className,
        )}
        {...props}
      />
    </RadixPopover.Portal>
  );
});

export function PopoverArrow(props: React.ComponentPropsWithoutRef<typeof RadixPopover.Arrow>) {
  return (
    <RadixPopover.Arrow
      strokeWidth={2}
      stroke="currentColor"
      {...props}
      className={cx("fill-border text-border", props.className)}
    />
  );
}

export { PopoverArrow as Arrow, PopoverContent as Content };
