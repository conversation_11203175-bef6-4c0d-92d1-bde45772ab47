import codecs
from typing import Literal

import psycopg.errors
from fastapi import APIRouter, UploadFile
from sqlalchemy.exc import IntegrityError, NoResultFound

from common.exceptions import HTTP400Exception, HTTPNotFoundException
from common.psql import DatabaseSession
from stabletech import schemas
from stabletech.client.crud import ClientTransactionManager
from stabletech.indexer import crud, jobs, models
from stabletech.indexer.block_fills.external import sync_block_fills_executions
from stabletech.indexer.covalent.queries import save_transaction_by_hash
from stabletech.indexer.enums import JobKind
from stabletech.indexer.internal_sync.sync_to_main import sync_transaction_to_main
from stabletech.utils.fastapi import CurrentUser

router = APIRouter()


@router.get("/account", response_model=schemas.AddAccount | None)
def get_accounts(address: str, network_id: int, db: DatabaseSession):
    try:
        return crud.AccountManager(db).get_by_address_network_id(address, network_id)
    except NoResultFound:
        return None


@router.get("/asset-holdings", response_model=schemas.AssetHoldingsResponse)
def get_asset_holdings(db: DatabaseSession):
    """Get all asset holdings with their current values and 24h changes."""
    return crud.TransactionManager(db).get_asset_holdings()


@router.get("/asset-holdings/{coin_id}/transactions", response_model=list[schemas.Transaction])
def get_asset_transactions(coin_id: int, db: DatabaseSession):
    """Get all classified transactions involving a specific coin."""
    return crud.TransactionManager(db).get_asset_transactions(coin_id)


@router.post("/bridge/add", response_model=schemas.Bridge)
def add_bridge(
    bridge_schema: schemas.Bridge,
    db: DatabaseSession,
):
    try:
        return crud.BridgeManager(db).add(bridge_schema)
    except IntegrityError:
        raise HTTP400Exception(f"Bridge {bridge_schema.name} already exists")


@router.get("/bridges", response_model=list[schemas.Bridge])
def get_bridges(db: DatabaseSession):
    return crud.BridgeManager(db).get_all()


@router.get("/client-transactions", response_model=list[schemas.AdminClientTransaction])
def get_user_client_transactions(db: DatabaseSession, user_id: int, only_pending: bool = False):
    return ClientTransactionManager(db).get_user_client_transactions(
        user_id, filter_option="only_pending" if only_pending else None
    )


@router.post("/client-transaction/review", response_model=schemas.AdminClientTransaction)
def client_transaction_edit(client_transaction_id: int, db: DatabaseSession):
    return ClientTransactionManager(db).review(client_transaction_id)


@router.delete("/client-transaction/delete")
def client_transaction_delete(client_transaction_id: int, db: DatabaseSession):
    ClientTransactionManager(db).delete(client_transaction_id)


@router.get("/liabilities", response_model=list[schemas.UserLiability])
def get_liabilities(db: DatabaseSession):
    return ClientTransactionManager(db).get_liabilities()


@router.post("/exchange/add", response_model=schemas.Exchange)
def add_exchange(
    exchange_schema: schemas.AddExchange,
    db: DatabaseSession,
):
    try:
        return crud.ExchangeManager(db).add(exchange_schema)
    except IntegrityError:
        raise HTTP400Exception(f"Exchange {exchange_schema.name} already exists")


@router.get("/exchanges", response_model=list[schemas.Exchange])
def get_exchanges(db: DatabaseSession):
    return crud.ExchangeManager(db).get_all()


@router.get(
    "/exchanges/with-account-address", response_model=list[schemas.ExchangeWithAccountAddress]
)
def get_exchanges_with_account_address(db: DatabaseSession):
    return [
        schemas.ExchangeWithAccountAddress(
            **dict(schemas.Exchange.model_validate(exchange)),
            account_address=account_address,
            network=network,
        )
        for exchange, account_address, network in crud.ExchangeManager(db)
        .get_all_with_account_address()
        .tuples()
    ]


@router.post("/farm/add", response_model=schemas.Farm)
def add_pool(
    farm_schema: schemas.AddFarm,
    db: DatabaseSession,
):
    try:
        return crud.FarmManager(db).add(farm_schema)
    except IntegrityError:
        raise HTTP400Exception(f"Farm {farm_schema.name} already exists")


@router.get("/farms", response_model=list[schemas.Farm])
def get_farms(db: DatabaseSession):
    return crud.FarmManager(db).get_all()


@router.get("/farms/with-account-address", response_model=list[schemas.FarmWithAccountAddress])
def get_farms_with_account_address(db: DatabaseSession):
    return [
        schemas.FarmWithAccountAddress(
            **dict(schemas.Farm.model_validate(farm)),
            account_address=account_address,
            network=network,
        )
        for farm, account_address, network in crud.FarmManager(db)
        .get_all_with_account_address()
        .tuples()
    ]


@router.post("/network/add", response_model=schemas.NetworkWithNativeCoin)
def add_network(
    network_schema: schemas.AddNetwork,
    db: DatabaseSession,
):
    try:
        return crud.NetworkManager(db).add(network_schema)
    except IntegrityError:
        raise HTTP400Exception(f"Network {network_schema.name} already exists")


@router.post("/network/update", response_model=schemas.NetworkWithNativeCoin)
def update_network(network_schema: schemas.Network, db: DatabaseSession):
    # Changing a Network's native Coin is not supported and needs to be done manually for now
    return crud.NetworkManager(db).update(network_schema)


@router.get("/networks", response_model=list[schemas.NetworkWithNativeCoin])
def get_networks(db: DatabaseSession):
    return crud.NetworkManager(db).get_all()


@router.get("/networks/with-admin-wallets", response_model=list[schemas.NetworkWithNativeCoin])
def get_networks_with_admin_wallets(db: DatabaseSession):
    return crud.NetworkManager(db).get_all_with_admin_wallets()


@router.get("/pools", response_model=list[schemas.AccountPool])
def get_pools(db: DatabaseSession):
    account_manager = crud.AccountManager(db)
    return account_manager.get_pools()


@router.get("/pools/{account_id}", response_model=schemas.AccountPoolFull)
def get_pool(account_id: int, db: DatabaseSession):
    account_manager = crud.AccountManager(db)

    if not account_manager.is_exchange(account_id):
        raise HTTP400Exception("Invalid account")

    pool = account_manager.get_pool(account_id)
    if not pool:
        raise HTTPNotFoundException("Not found")
    return pool


@router.get("/pools/{account_id}/price-alerts", response_model=list[schemas.PoolPriceAlert])
def get_pool_price_alerts(account_id: int, db: DatabaseSession):
    return crud.PoolPriceAlertManager(db).get_all_from_account(account_id)


@router.delete("/pools/{account_id}/price-alerts/{coin_id}")
def delete_pool_price_alert(account_id: int, coin_id: int, db: DatabaseSession):
    crud.PoolPriceAlertManager(db).delete(account_id, coin_id)
    db.commit()


@router.put("/pools/{account_id}/price-alerts/{coin_id}")
def create_or_replace_pool_price_alert(
    account_id: int,
    coin_id: int,
    alert_range: schemas.PoolPriceAlertRange,
    db: DatabaseSession,
):
    if alert_range.min_price_usd >= alert_range.max_price_usd:
        raise HTTP400Exception("minimum range value must be less than maximum")

    crud.PoolPriceAlertManager(db).create_or_update(
        account_id=account_id,
        coin_id=coin_id,
        min_price_usd=alert_range.min_price_usd,
        max_price_usd=alert_range.max_price_usd,
    )
    db.commit()


@router.get("/loans", response_model=list[schemas.AccountLoan])
def get_loans(db: DatabaseSession, p2p: bool = False):
    account_manager = crud.AccountManager(db)
    return account_manager.get_loans(p2p=p2p)


@router.get("/loans/{account_id}", response_model=schemas.AccountLoanFull)
def get_loan(account_id: int, db: DatabaseSession, p2p: bool = False):
    """Returns all loan Transactions from the same Network and LendingProtocol as the queried Account."""
    account_manager = crud.AccountManager(db)
    try:
        account = account_manager.get_by_id(account_id)
    except NoResultFound:
        raise HTTPNotFoundException("Invalid Account")
    loan = account_manager.get_loan(account, p2p=p2p)
    if not loan:
        raise HTTPNotFoundException("Not found")
    return loan


@router.put("/loans/{account_id}")
def update_lending_protocol_liquidation_threshold(
    account_id: int,
    lending_protocol_schema: schemas.LendingProtocolLiquidationThreshold,
    db: DatabaseSession,
):
    account_manager = crud.AccountManager(db)
    try:
        account = account_manager.get_by_id(account_id)
    except NoResultFound:
        raise HTTPNotFoundException(f"Could not find loan with Account id {account_id}")

    return crud.LendingProtocolManager(db).update_liquidation_threshold(
        account.lending_protocol,
        lending_protocol_schema,
    )


@router.post("/lending-protocol/add", response_model=schemas.LendingProtocol)
def add_lending_protocol(
    lending_protocol_schema: schemas.AddLendingProtocol,
    db: DatabaseSession,
):
    try:
        return crud.LendingProtocolManager(db).add(lending_protocol_schema)
    except IntegrityError:
        raise HTTP400Exception(
            f"Lending protocol {lending_protocol_schema.name} already exists"
        )


@router.get("/lending-protocols", response_model=list[schemas.LendingProtocol])
def get_lending_protocols(db: DatabaseSession):
    return crud.LendingProtocolManager(db).get_all()


@router.get(
    "/lending-protocols/with-account-address",
    response_model=list[schemas.LendingProtocolWithAccountAddress],
)
def get_lending_protocols_with_account_address(db: DatabaseSession):
    return [
        schemas.LendingProtocolWithAccountAddress(
            **dict(schemas.LendingProtocol.model_validate(lending_protocol)),
            account_address=account_address,
            network=network,
        )
        for lending_protocol, account_address, network in crud.LendingProtocolManager(db)
        .get_all_with_account_address()
        .tuples()
    ]


@router.put("/transaction/classify")
def classify_transaction(
    db: DatabaseSession,
    transaction_schema: schemas.TransactionClassify,
    user: CurrentUser,
    client_transaction_id: int | None = None,
):
    client_transaction = None
    if client_transaction_id:
        try:
            client_transaction = crud.ClientTransactionManager(db).get_by_id(
                client_transaction_id
            )
        except NoResultFound:
            raise HTTPNotFoundException("Client transaction not found")

    try:
        crud.TransactionManager(db).classify(
            transaction_schema, client_transaction=client_transaction
        )
    except IntegrityError as e:
        if isinstance(e.orig, psycopg.errors.UniqueViolation):
            raise HTTP400Exception(str(e.orig).strip()) from e
        raise


@router.post("/transaction/add", response_model=schemas.Transaction)
def add_transaction(
    db: DatabaseSession,
    add_transaction_schema: schemas.AddTransaction,
    client_transaction_id: int | None = None,
):
    resp = crud.TransactionManager(db).add_manually(
        add_transaction_schema,
        client_transaction_id=client_transaction_id,
    )
    return resp


@router.post("/transaction/replace", response_model=schemas.Transaction)
def replace_transaction(
    db: DatabaseSession,
    replace_transaction_schema: schemas.ReplaceTransaction,
):
    transaction_manager = crud.TransactionManager(db)
    client_transaction_manager = crud.ClientTransactionManager(db)

    # If is_for_pooled_fund is disabled and there's a client transaction, delete it
    # Otherwise just reset it as it will get handled in classify
    if not replace_transaction_schema.add_transaction.is_for_pooled_fund:
        client_transaction_manager.delete_by_transaction_id(
            replace_transaction_schema.transaction_id, commit=False
        )
    else:
        client_transaction_manager.reset_by_transaction_id(
            replace_transaction_schema.transaction_id
        )
    old_transaction_id = replace_transaction_schema.transaction_id
    transaction_manager.delete(old_transaction_id, commit=False)

    # Add the new transaction that replaces the former
    resp = transaction_manager.add_manually(
        replace_transaction_schema.add_transaction,
        client_transaction_id=replace_transaction_schema.client_transaction_id,
        commit_and_sync=False,
    )

    db.commit()

    # Sync the replacement Transaction
    sync_transaction_to_main(resp, db, replace_transaction_id=old_transaction_id)

    return resp


@router.get("/transaction/query-covalent")
def transaction_from_covalent(
    network_id: int, wallet_id: int, tx_hash: str, db: DatabaseSession
):
    """Query Covalent for a transaction by hash and save it."""
    transaction_manager = crud.TransactionManager(db)
    if transaction_manager.tx_hash_exists(network_id, tx_hash):
        raise HTTP400Exception("Transaction already exists")
    try:
        network = crud.NetworkManager(db).get_by_id(network_id)
        wallet = crud.WalletManager(db).get_by_id(wallet_id)
    except NoResultFound:
        raise HTTPNotFoundException("Network or wallet not found")

    if not save_transaction_by_hash(db, network, wallet, tx_hash):
        raise HTTP400Exception("Transaction not found on Covalent")


@router.post("/transaction/reset")
def reset_transaction(transaction_id: int, db: DatabaseSession):
    crud.TransactionManager(db).reset(transaction_id)


@router.delete("/transaction")
def delete_transaction(transaction_id: int, db: DatabaseSession):
    """Delete a Transaction by ID."""
    crud.TransactionManager(db).delete(transaction_id)


def get_paginated_transactions(
    db: DatabaseSession,
    from_id: int,
    limit: int,
    page: Literal["prev", "next"],
    search_query: str,
    time_query: str,
    classified: bool,
):
    if limit > 100:
        raise HTTP400Exception("Limit cannot be greater than 100")

    desc = page == "next"
    list_result = crud.TransactionManager(db).get_paginated_transactions(
        classified=classified,
        from_id=from_id,
        desc=desc,
        limit=limit,
        search_query=search_query,
        time_query=time_query,
    )

    transactions = list_result.items
    if desc:
        next_ = list_result.next
        prev = list_result.prev
    else:
        # Endpoint always returns transactions in desc order
        transactions.reverse()
        next_ = list_result.prev
        prev = list_result.next

    # At this point transactions are always in desc order
    if next_ and len(transactions) < limit:
        # This prevents returning a not full page when going backwards. It
        # can happen when newer transactions are added to the DB, for example.
        # So, if there are more results and we didn't get enough results to
        # fill the page, then we proceed to fill the page.
        last_transaction_id = transactions[-1].id
        missing_len = limit - len(transactions)
        list_result = crud.TransactionManager(db).get_paginated_transactions(
            classified=classified,
            from_id=last_transaction_id,
            desc=True,
            limit=missing_len,
            search_query=search_query,
            time_query=time_query,
        )
        transactions = transactions + list_result.items
        next_ = list_result.next

    return schemas.PaginatedResult(next=next_, prev=prev, items=transactions)


@router.get("/transactions", response_model=schemas.PaginatedResult[schemas.Transaction])
def get_transactions(
    db: DatabaseSession,
    from_id: int = 0,
    limit: int = 50,
    page: Literal["prev", "next"] = "next",
    search_query: str = "",
    time_query: str = "",
):
    paginated_results = get_paginated_transactions(
        db, from_id, limit, page, search_query, time_query, True
    )
    client_transaction_ids = ClientTransactionManager(db).get_ids_by_transaction_ids(
        [transaction.id for transaction in paginated_results.items]
    )
    transaction_schemas = []
    for transaction in paginated_results.items:
        transaction_schema = schemas.Transaction.model_validate(transaction)
        transaction_schema.client_transaction_id = client_transaction_ids.get(
            transaction.id
        )  # Set client_transaction_id directly
        transaction_schemas.append(transaction_schema)
    paginated_results.items = transaction_schemas
    return paginated_results


@router.get(
    "/transactions/unclassified",
    response_model=schemas.PaginatedResult[schemas.TransactionUnclassified],
)
def get_unclassified_transactions(
    db: DatabaseSession,
    from_id: int = 0,
    limit: int = 50,
    page: Literal["prev", "next"] = "next",
    search_query: str = "",
    time_query: str = "",
):
    return get_paginated_transactions(db, from_id, limit, page, search_query, time_query, False)


# Important: must come after other /transactions/* routes, to not match
# "*" as the "{transaction_id}".
@router.get("/transactions/{transaction_id}", response_model=schemas.Transaction)
def get_transaction(transaction_id: int, db: DatabaseSession):
    try:
        return crud.TransactionManager(db).get_by_id(transaction_id, classified=True)
    except NoResultFound:
        raise HTTPNotFoundException("Transaction not found")


@router.get(
    "/transactions/{transaction_id}/client-transaction",
    response_model=schemas.ClientTransactionWithUser | None,
)
def get_transaction_client_transaction(transaction_id: int, db: DatabaseSession):
    return ClientTransactionManager(db).get_by_transaction_id(transaction_id)


@router.get(
    "/transactions/{transaction_id}/possible-actions",
    response_model=list[schemas.TransactionAction],
)
def get_transaction_possible_actions(transaction_id: int, db: DatabaseSession):
    try:
        return crud.TransactionManager(db).get_possible_actions(transaction_id)
    except NoResultFound:
        raise HTTPNotFoundException("Transaction not found")


@router.delete("/transfers/{transfer_id}")
def delete_transfer(transfer_id: int, db: DatabaseSession):
    """Delete a Transfer by ID. The transfer's transaction must be unclassified."""
    transfer_manager = crud.TransferManager(db)
    try:
        transfer = transfer_manager.get_by_id(transfer_id)
    except NoResultFound:
        raise HTTPNotFoundException("Transfer not found")
    if transfer.transaction.action:
        raise HTTP400Exception("Cannot delete transfer from already classified transaction")
    db.delete(transfer)
    db.commit()


@router.post("/wallet/add", response_model=schemas.Wallet)
def add_wallet(wallet_schema: schemas.AddWallet, db: DatabaseSession):
    wallet = crud.WalletManager(db).add_wallet(wallet_schema, None, commit=False)
    job = jobs.create_wallet_sync(wallet.id, initial=True)
    db.add(job)
    db.commit()
    return wallet


@router.post("/wallet/sync", response_model=schemas.JobResponse)
def sync_wallet(wallet_id: int, db: DatabaseSession) -> models.Job:
    wallet_manager = crud.WalletManager(db)

    if wallet_id > 0:
        wallet = wallet_manager.get_by_id(wallet_id)
        if not wallet.is_active:
            raise HTTP400Exception("Wallet must be active")
    elif wallet_id == -1:
        pass
    else:
        raise HTTP400Exception(f"Invalid wallet_id '{wallet_id}'")

    job = jobs.create_wallet_sync(wallet_id)
    db.add(job)
    db.commit()

    return job


@router.post("/wallet/edit", response_model=schemas.Wallet)
def edit_wallet(wallet_schema: schemas.EditWallet, db: DatabaseSession):
    return crud.WalletManager(db).edit(wallet_schema, None)


@router.get("/wallets", response_model=list[schemas.Wallet])
def get_wallets(db: DatabaseSession, network_id: int | None = None, user_id: int | None = None):
    network = crud.NetworkManager(db).get_by_id(network_id) if network_id else None
    return crud.WalletManager(db).get_all_active(network, user_id=user_id)


MAX_FILE_SIZE = 2 * 1024 * 1024


@router.post("/import/{provider}", response_model=schemas.JobResponse)
def import_from_provider(
    provider: Literal["cointracking", "bitcointax"],
    db: DatabaseSession,
    file: UploadFile,
    user: CurrentUser,
    skip_already_in_db: bool = False,
    merge_in_file_duplicates: bool = False,
):
    if file.size and file.size > MAX_FILE_SIZE:
        raise HTTP400Exception(f"File is too large, max size is ${MAX_FILE_SIZE} bytes")

    # We need to read as text, but UploadFile is read as binary.
    # So we use a codecs.StreamReader to transparently decode as it's read.
    text_stream = codecs.getreader("utf_8")(file.file)

    csv_text = text_stream.read()
    job_params = schemas.ImportJobParams(
        merge_in_file_duplicates=merge_in_file_duplicates,
        skip_already_in_db=skip_already_in_db,
        user_id=user.id,
    )

    kind: JobKind
    match provider:
        case "cointracking":
            kind = "import_cointracking_csv"
        case "bitcointax":
            if job_params.merge_in_file_duplicates:
                raise HTTP400Exception("merge_in_file_duplicates not supported for bitcointax")
            if job_params.skip_already_in_db:
                raise HTTP400Exception("skip_already_in_db not supported for bitcointax")
            kind = "import_bitcointax_csv"
        case _:
            raise NotImplementedError(f"Provider '{provider}' not implemented")

    job = models.Job.create(
        kind=kind,
        data=job_params.model_dump(mode="json", round_trip=True),
        data_text=csv_text,
    )

    db.add(job)
    db.commit()
    return job


@router.get("/jobs/{job_id}", response_model=schemas.JobResponse)
def get_job(job_id: int, db: DatabaseSession):
    job = db.query(models.Job).filter_by(id=job_id).one_or_none()
    if not job:
        raise HTTPNotFoundException("Job not found")
    return job


@router.get("/expenses", response_model=schemas.TransactionsOverview)
def get_expenses(db: DatabaseSession) -> schemas.TransactionsOverview:
    return crud.TransactionManager(db).get_expenses()


@router.get("/income", response_model=schemas.TransactionsOverview)
def get_income(db: DatabaseSession) -> schemas.TransactionsOverview:
    return crud.TransactionManager(db).get_income()


@router.get("/stake", response_model=list[schemas.Stake])
def get_stake(db: DatabaseSession) -> list[schemas.Stake]:
    return crud.TransactionManager(db).get_stakes()


@router.get("/stake/{farm_id}", response_model=schemas.StakeDetails)
def get_coin_stake(
    farm_id: int, network_id: int, coin_id: int, db: DatabaseSession
) -> schemas.StakeDetails:
    return crud.TransactionManager(db).get_coin_stake(farm_id, coin_id, network_id)


@router.get("/sync/block-fills")
def sync_block_fills(db: DatabaseSession):
    sync_block_fills_executions(db)
