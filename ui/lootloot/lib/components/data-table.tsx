"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/lib/components/table";
import {
  ColumnDef,
  HeaderContext,
  SortingState,
  Table as TableType,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { cva } from "class-variance-authority";
import { ArrowDownWideNarrow, ArrowUpDown, ArrowUpNarrowWide } from "lucide-react";
import React, { useState } from "react";

interface DataTableProps<TData, TValue> {
  readonly columns: ColumnDef<TData, TValue>[];
  readonly data: TData[];
}

/**
 * A simple table to display data.
 *
 * Prefer composing `useReactTable` + `renderTable` instead of adding more
 * features to this component.
 */
export function DataTable<TData, TValue>({ columns, data }: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  return <div className="rounded-md border">{renderTable(columns, table)}</div>;
}

export function renderTable<TData, TValue>(
  columns: readonly ColumnDef<TData, TValue>[],
  table: TableType<TData>,
) {
  const rowModel = table.getRowModel();
  return (
    <Table>
      <TableHeader>
        {table.getHeaderGroups().map((headerGroup) => (
          <TableRow key={headerGroup.id}>
            {headerGroup.headers.map((header) => {
              return (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              );
            })}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {rowModel.rows.length ? (
          rowModel.rows.map((row) => (
            <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={columns.length} className="h-24 text-center">
              No results.
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}

export const headerButton = cva(
  "-m-1 inline-flex items-center rounded-xs p-1 hover:bg-accent focus-visible:bg-accent focus-visible:ring-1 focus-visible:ring-ring/50 focus-visible:outline-hidden dark:focus-visible:ring-ring/80",
);

export function sortableHeader<TData>(
  header: string,
): ({ column }: HeaderContext<TData, unknown>) => React.JSX.Element {
  return function renderSortableHeader({ column }) {
    const sorting = column.getIsSorted();
    const Icon = sorting
      ? sorting === "asc"
        ? ArrowUpNarrowWide
        : ArrowDownWideNarrow
      : ArrowUpDown;
    return (
      <button
        className={headerButton()}
        onClick={() => {
          column.toggleSorting();
        }}
      >
        {header}
        <Icon className="ml-2 h-4 w-4" />
      </button>
    );
  };
}
