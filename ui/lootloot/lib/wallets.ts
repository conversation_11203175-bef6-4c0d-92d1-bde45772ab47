"use client";

import { apiMutation, apiQuery } from "@/lib/api";
import { AddPhantomWallet, ListUserWalletsResp, UserWallet } from "@/types/schemas";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const LIST_WALLETS_KEY = ["wallets"] as const;

export function useWalletsQuery() {
  return useQuery({
    queryKey: LIST_WALLETS_KEY,
    queryFn: apiQuery<ListUserWalletsResp>,
  });
}

async function addPhantomWallet(params: AddPhantomWallet) {
  return apiMutation<UserWallet>({
    method: "POST",
    resource: "wallets/phantom",
    body: params,
  });
}

export function useAddPhantomWallet() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addPhantomWallet,
    onSuccess() {
      void queryClient.invalidateQueries({ queryKey: LIST_WALLETS_KEY });
    },
  });
}

type NetworkGroup = UserWallet["network_group"];

interface DeleteWalletParams {
  readonly networkGroup: NetworkGroup;
  readonly address: string;
}

async function deleteWallet({ networkGroup, address }: DeleteWalletParams) {
  return apiMutation<undefined>({
    method: "DELETE",
    resource: `wallets/${networkGroup}/${address}`,
  });
}

export function useDeleteWallet() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteWallet,
    onSuccess() {
      void queryClient.invalidateQueries({ queryKey: LIST_WALLETS_KEY });
    },
  });
}
