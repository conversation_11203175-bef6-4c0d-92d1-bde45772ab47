"""CapitalPosition add transaction, csv_row and external_transaction

Revision ID: 0048
Revises: 0047
Create Date: 2024-03-19 01:15:54.895816

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0048"
down_revision = "0047"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("capitalposition", sa.Column("user_id", sa.Integer(), nullable=True))
    op.add_column(
        "capitalposition", sa.Column("buy_transaction_id", sa.Integer(), nullable=True)
    )
    op.add_column(
        "capitalposition", sa.Column("external_buy_transaction_id", sa.String(), nullable=True)
    )
    op.add_column(
        "capitalposition", sa.Column("sell_transaction_id", sa.Integer(), nullable=True)
    )
    op.add_column(
        "capitalposition", sa.Column("external_sell_transaction_id", sa.String(), nullable=True)
    )
    op.add_column("capitalposition", sa.Column("csv_row", sa.Integer(), nullable=True))
    op.create_index(
        op.f("ix_capitalposition_user_id"), "capitalposition", ["user_id"], unique=False
    )
    op.create_foreign_key(
        None, "capitalposition", "transaction", ["sell_transaction_id"], ["id"]
    )
    op.create_foreign_key(None, "capitalposition", "user", ["user_id"], ["id"])
    op.create_foreign_key(
        None, "capitalposition", "transaction", ["buy_transaction_id"], ["id"]
    )
    op.drop_column("capitalposition", "is_imported")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "capitalposition",
        sa.Column("is_imported", sa.BOOLEAN(), autoincrement=False, nullable=False),
    )
    op.drop_constraint(
        "capitalposition_buy_transaction_id_fkey", "capitalposition", type_="foreignkey"
    )
    op.drop_constraint("capitalposition_user_id_fkey", "capitalposition", type_="foreignkey")
    op.drop_constraint(
        "capitalposition_sell_transaction_id_fkey", "capitalposition", type_="foreignkey"
    )
    op.drop_index(op.f("ix_capitalposition_user_id"), table_name="capitalposition")
    op.drop_column("capitalposition", "csv_row")
    op.drop_column("capitalposition", "external_sell_transaction_id")
    op.drop_column("capitalposition", "sell_transaction_id")
    op.drop_column("capitalposition", "external_buy_transaction_id")
    op.drop_column("capitalposition", "buy_transaction_id")
    op.drop_column("capitalposition", "user_id")
    # ### end Alembic commands ###
