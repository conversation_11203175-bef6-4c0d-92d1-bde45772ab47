# Resources related to the Wallet API

# Service account that will be used by the API pods
resource "google_service_account" "wallet_api_pods" {
  for_each = local.wallet_envs

  account_id   = "wallet-${each.value}-api-pods"
  display_name = "API pods on wallet-${each.value}"
}

# Allow service account for pods to access the secret for cubist sessions
resource "google_secret_manager_secret_iam_member" "wallet_api_pods" {
  for_each = google_service_account.wallet_api_pods

  secret_id = "wallet-${each.key}-cubist-session-secret-box-key"
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${each.value.email}"
}

# Bind pods to their respective service account
resource "google_service_account_iam_binding" "wallet_api_pods" {
  for_each = google_service_account.wallet_api_pods

  service_account_id = each.value.id
  role               = "roles/iam.workloadIdentityUser"
  members = [
    "serviceAccount:${local.project_id}.svc.id.goog[wallet-${each.key}/api-pods]"
  ]
}
