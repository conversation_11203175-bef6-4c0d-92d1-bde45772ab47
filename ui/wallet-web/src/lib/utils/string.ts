export function capitalize<T extends string>(str: T): Capitalize<T> {
  return (str.charAt(0).toUpperCase() + str.slice(1)) as Capitalize<T>;
}

export function shortenAddress(address: string): string {
  if (address.length < 10) {
    return address;
  }
  if (address.startsWith("0x")) {
    return `${address.slice(0, 6)}…${address.slice(-4)}`;
  }
  return `${address.slice(0, 4)}…${address.slice(-4)}`;
}
