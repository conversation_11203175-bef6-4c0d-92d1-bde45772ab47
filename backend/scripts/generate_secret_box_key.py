#!/usr/bin/env python3
"""
This script generates a NaCl SecretBox key and uploads it to Google Cloud Secret Manager.

Generates a NaCl SecretBox key, creates a secret in Google Cloud Secret Manager, and
uploads the key as a secret version.

Usage:
    Edit the secret_id and project_id, then run this script directly to generate and
    upload the key.
"""

import base64

import nacl.utils
from google.cloud import secretmanager
from nacl.secret import SecretBox


def generate_and_upload_key():
    """Generate a NaCl SecretBox key and upload it to Google Cloud Secret Manager."""
    # Create the secret manager client
    client = secretmanager.SecretManagerServiceClient()

    # The secret ID/name
    secret_id = "wallet-stage-cubist-session-secret-box-key"

    # Get the project ID
    project_id = "temet-359508"

    # Create the parent resource name
    parent = f"projects/{project_id}"

    # Create the secret
    secret = client.create_secret(
        parent=parent,
        secret_id=secret_id,
        secret=secretmanager.Secret(
            replication=secretmanager.Replication(
                automatic=secretmanager.Replication.Automatic(),
            )
        ),
    )

    # Generate a new SecretBox key
    key = nacl.utils.random(SecretBox.KEY_SIZE)

    # Encode the key in base64
    encoded_key = base64.b64encode(key)

    # Add the secret version
    version = client.add_secret_version(
        parent=secret.name,
        payload=secretmanager.SecretPayload(data=encoded_key),
    )

    print(f"Successfully created and uploaded secret: {secret_id}")
    print(f"Secret version: {version.name}")


if __name__ == "__main__":
    generate_and_upload_key()
