"""Add cubist_user_id column to WalletUser

Revision ID: 0031
Revises: 0030
Create Date: 2024-11-22 18:21:42.586058

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0031"
down_revision = "0030"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("walletuser", sa.Column("cubist_user_id", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_walletuser_cubist_user_id"), "walletuser", ["cubist_user_id"], unique=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_walletuser_cubist_user_id"), table_name="walletuser")
    op.drop_column("walletuser", "cubist_user_id")
    # ### end Alembic commands ###
