import logging
import urllib.parse
from datetime import datetime
from typing import Any

import httpx
import pydantic

from common.conf import settings

logger = logging.getLogger(__name__)

client = httpx.Client(
    base_url="https://apis.mintscan.io",
    headers=(("Accept", "application/json"),),
    timeout=30,
)
if settings.mintscan_api_key:
    client.headers["Authorization"] = f"Bearer {settings.mintscan_api_key}"
else:
    logger.warning("Mintscan API key is not set")


class Pagination(pydantic.BaseModel):
    totalCount: int
    searchAfter: str | None = None


class Attribute(pydantic.BaseModel):
    index: bool | None = None
    key: str
    value: str | list[str]


class Event(pydantic.BaseModel):
    attributes: list[Attribute]
    type: str


class Log(pydantic.BaseModel):
    events: list[Event]
    msg_index: int | None


class Transaction(pydantic.BaseModel):
    chain_id: str
    code: int
    codespace: str
    data: str | None = pydantic.Field(default=None, repr=False)
    gas_used: str
    gas_wanted: str
    height: str
    info: str
    logs: list[Log] = pydantic.Field(repr=False)
    timestamp: str
    tx: dict[str, Any] = pydantic.Field(repr=False)
    txhash: str


class AccountTransactions(pydantic.BaseModel):
    transactions: list[Transaction]
    pagination: Pagination


def get_account_transactions(
    blockchain: str,
    address: str,
    *,
    from_date_time: datetime | None = None,
    to_date_time: datetime | None = None,
    search_after: str = "",
) -> AccountTransactions:
    blockchain = urllib.parse.quote(blockchain, safe="")
    address = urllib.parse.quote(address, safe="")

    params = tuple(
        param
        for param in (
            ("fromDateTime", from_date_time.isoformat()) if from_date_time else None,
            ("toDateTime", to_date_time.isoformat()) if to_date_time else None,
            ("searchAfter", search_after) if search_after else None,
        )
        if param is not None
    )

    resp = client.get(f"/v1/{blockchain}/accounts/{address}/transactions", params=params)
    resp.raise_for_status()

    return AccountTransactions.model_validate_json(resp.content)


class Asset(pydantic.BaseModel):
    denom: str
    type: str
    symbol: str
    decimals: int
    coingecko_id: str | None = pydantic.Field(default=None, validation_alias="coinGeckoId")


class AssetList(pydantic.BaseModel):
    assets: list[Asset]


def get_asset(blockchain: str, denom: str) -> Asset | None:
    blockchain = urllib.parse.quote(blockchain, safe="")
    denom = urllib.parse.quote(denom, safe="")

    resp = client.get(f"/v1/{blockchain}/assets/{denom}")
    resp.raise_for_status()

    assets = AssetList.model_validate_json(resp.content).assets
    return assets[0] if assets else None


def get_asset_list(blockchain: str) -> AssetList:
    blockchain = urllib.parse.quote(blockchain, safe="")

    resp = client.get(f"/v1/{blockchain}/assets")
    resp.raise_for_status()

    return AssetList.model_validate_json(resp.content)


class DenomUnit(pydantic.BaseModel):
    denom: str
    exponent: int
    aliases: list[str] | None = None


class CosmosAsset(pydantic.BaseModel):
    address: str | None = None
    base: str
    coingecko_id: str | None = None
    denom_units: list[DenomUnit]
    description: str | None = None
    display: str
    keywords: list[str] | None = None
    logo_URIs: dict[str, str] | None = None
    name: str
    symbol: str
    traces: list | None = None
    type_asset: str | None = None


class CosmosAssetList(pydantic.BaseModel):
    chain_name: str
    assets: list[CosmosAsset]


gh_client = httpx.Client(
    base_url="https://raw.githubusercontent.com",
    timeout=10,
)


def get_cosmos_asset_list(blockchain: str) -> CosmosAssetList:
    if blockchain == "cosmos":
        blockchain = "cosmoshub"
    blockchain = urllib.parse.quote(blockchain, safe="")
    resp = gh_client.get(f"/cosmos/chain-registry/master/{blockchain}/assetlist.json")
    resp.raise_for_status()
    return CosmosAssetList.model_validate_json(resp.content)
