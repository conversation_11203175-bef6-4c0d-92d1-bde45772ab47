import { Button, ButtonProps, LinkProps, Link as MuiLink } from "@mui/material";
import NextLink, { LinkProps as NextLinkProps } from "next/link";

interface Props extends Omit<LinkProps, "href"> {
  readonly href: NextLinkProps["href"];
}

export default function Link({ href, ...props }: Props) {
  return (
    <NextLink href={href} passHref legacyBehavior>
      <MuiLink {...props} />
    </NextLink>
  );
}

interface ButtonLinkProps extends Omit<ButtonProps, "href"> {
  readonly href: NextLinkProps["href"];
}

/** A link that looks like a button */
export function ButtonLink({ href, ...props }: ButtonLinkProps) {
  return (
    <NextLink href={href} passHref legacyBehavior>
      <Button {...props} />
    </NextLink>
  );
}
