import logging
import re
from datetime import datetime
from decimal import Decimal
from typing import NamedTuple

import httpx
from pydantic import ValidationError
from sqlalchemy.exc import DatabaseError, NoResultFound
from sqlalchemy.orm import Session

import stabletech.indexer.mintscan.external as mintscan
from stabletech.indexer.crud import A<PERSON>unt<PERSON><PERSON><PERSON>, TransactionManager
from stabletech.indexer.models import Network, Transaction, Transfer, Wallet
from stabletech.market.crud import AliasCoinManager, CoinManager
from stabletech.schemas import WalletSyncResult

logger = logging.getLogger(__name__)

AMOUNT_AND_TOKEN_REGEX = re.compile(r"^(\d+)([^,]+)$")


class RawTransfer(NamedTuple):
    sender: str
    recipient: str
    amount: int
    token: str


def filter_existing_transactions(
    transaction_manager: TransactionManager,
    network_id: int,
    raw_tx_list: list[mintscan.Transaction],
) -> list[mintscan.Transaction]:
    existing_tx_hashes = set(
        transaction_manager.filter_existing_tx_hashes(
            network_id, [tx.txhash for tx in raw_tx_list]
        )
    )
    return [tx for tx in raw_tx_list if tx.txhash not in existing_tx_hashes]


def filter_fee_transfer(
    raw_transfer_list: list[RawTransfer], sender: str, raw_fee: tuple[int, str]
) -> list[RawTransfer]:
    """Filter out the first transfer from the raw transfer list that matches the fee.

    If found, returns a new list of raw transfers without the fee transfer. Otherwise,
    returns the original list.
    """
    fee_amount, fee_token = raw_fee
    for i, raw_transfer in enumerate(raw_transfer_list):
        if (
            raw_transfer.amount == fee_amount
            and raw_transfer.token == fee_token
            and raw_transfer.sender == sender
        ):
            return raw_transfer_list[:i] + raw_transfer_list[i + 1 :]
    return raw_transfer_list


def get_address_raw_transfers(raw_tx: mintscan.Transaction, address: str) -> list[RawTransfer]:
    """Get the raw transfers for a given address in a transaction.

    Returns a list of RawTransfer named tuples.
    """
    signer = get_signer_address(raw_tx)
    is_signer = signer == address

    transfer_events = (
        event
        for log in raw_tx.logs
        for event in log.events
        if is_transfer_event(address, is_signer, event)
    )

    transfers: list[RawTransfer] = []
    for event in transfer_events:
        sender: str | None = None
        recipient: str | None = None
        amount: int | None = None
        token: str | None = None

        for attr in event.attributes:
            assert isinstance(
                attr.value, str
            ), f"Unexpected transfer event attribute: {attr.key} -> {attr.value}"
            match (attr.key):
                case "sender":
                    sender = attr.value
                case "recipient" | "validator":
                    # "validator" is for the "delegate" events, where the value is the
                    # validator address receiving the tokens.
                    recipient = attr.value
                case "amount":
                    amount, token = parse_amount_and_token(attr.value)
                case _:
                    pass

        if amount is None or token is None or recipient is None:
            raise ValueError(f"Unexpected transfer event: {event}")

        if sender is None:
            # The sender might be omitted in some cases, for example, on "multi send"
            # transactions. In these cases, the sender is the signer of the transaction.
            sender = signer

        if address not in (sender, recipient):
            continue  # irrelevant transfer
        if sender == recipient:
            continue  # self-transfers doesn't affect anything

        transfers.append(RawTransfer(sender, recipient, amount, token))

    return transfers


def simplify_raw_transfers(address: str, transfers: list[RawTransfer]) -> list[RawTransfer]:
    """Simplify the raw transfers for a given address without modifying its balance
    changes.

    This will remove transfers that result in no balance change for the address, and
    aggregate transfers between the same addresses and using the same token.
    """
    # Address balances, token -> balance
    balances: dict[str, int] = {}
    for sender, recipient, amount, token in transfers:
        balances[token] = balances.get(token, 0) + (amount if recipient == address else -amount)

    tokens_with_zero_balance = [token for token, balance in balances.items() if balance == 0]

    # Address-relative aggregated transfers, (counterparty, token) -> amount
    agg_transfers: dict[tuple[str, str], int] = {}
    for sender, recipient, amount, token in transfers:
        if token in tokens_with_zero_balance:
            continue  # ignore tokens with zero balance
        is_recipient = recipient == address
        counterparty = sender if is_recipient else recipient
        key = (counterparty, token)
        agg_transfers[key] = agg_transfers.get(key, 0) + (amount if is_recipient else -amount)

    final_transfers = [
        (
            RawTransfer(address, counterparty, -amount, token)
            if amount < 0
            else RawTransfer(counterparty, address, amount, token)
        )
        for (counterparty, token), amount in agg_transfers.items()
        if amount != 0
    ]
    final_transfers.sort(key=get_raw_transfer_sort_key)

    return final_transfers


def get_raw_transaction_fee(raw_tx: mintscan.Transaction) -> tuple[int, str] | None:
    """Get the fee for a given transaction.

    Returns a tuple of (amount, token) or None if no fee is found.
    """
    fees = [
        parse_amount_and_token(attr.value)
        for log in raw_tx.logs
        if log.msg_index == None
        for event in log.events
        if event.type == "tx"
        for attr in event.attributes
        if attr.key == "fee" and isinstance(attr.value, str)
    ]
    assert len(fees) <= 1 or len(set(fees)) == 1, "Multiple fees found"
    return fees[0] if fees else None


def get_raw_transfer_sort_key(raw_transfer: RawTransfer):
    "Sort by token, amount (largest first), recipient, sender"
    sender, recipient, amount, token = raw_transfer
    return (token, -amount, recipient, sender)


def get_signer_address(raw_tx: mintscan.Transaction) -> str:
    """Get the signer address of a raw transaction.

    The signer of the transaction is the sender of the message(s), which can be
    found as the first event of each log with a defined `msg_index`.
    """
    senders: list[str] = []
    for log in raw_tx.logs:
        if not log.events:
            continue
        first_event = log.events[0]
        for attr in first_event.attributes:
            if attr.key in ("receiver", "sender", "spender") and isinstance(attr.value, str):
                senders.append(attr.value)
                break  # we must take only the first, some events have multiple

    count = len(set(senders))
    assert count == 1, f"Not single sender found on raw transaction ({count} found)"
    return senders[0]


def is_transfer_event(address: str, is_signer: bool, event: mintscan.Event):
    """Return whether it is an event transferring tokens to/from the address."""
    match event.type:
        case "transfer":
            sender = recipient = amount = None
            for attr in event.attributes:
                match attr.key:
                    case "sender":
                        sender = attr.value
                    case "recipient":
                        recipient = attr.value
                    case "amount":
                        amount = attr.value

            # Found a transaction with amount empty in an transfer event: https://www.mintscan.io/osmosis/tx/EA8478BF2E7CBC427ADEA37E6CAE3498E93A0DEC1088516AEB9985A81BA671E7
            # but ignoring this event works fine, the final transaction and transfers are
            # correct. That was the motivation to add this check.
            # Note that sender can be None (i.e omitted)
            if not amount or not recipient or not (sender or sender is None):
                logger.warning("Unusual transfer event: %s", event)
                return False

            if not (isinstance(amount, str) and AMOUNT_AND_TOKEN_REGEX.match(amount)):
                logger.error(
                    "Skipped transfer event due to unsupported amount format: %s",
                    amount,
                    stack_info=True,
                    extra={"address": address, "event": event},
                )
                return False

            return (
                # If the address is either sender or recipient of the transfer
                (address in (sender, recipient))
                # If the address is the signer of the transaction and sender was omitted
                or (is_signer and sender is None)
            )

        case "delegate":
            # On delegate events, only if we're the signer, i.e we're delegating
            return is_signer

    return False


def parse_amount_and_token(amount_and_token: str) -> tuple[int, str]:
    """Parse an "amount and token" string.

    Returns a tuple of (amount, token).
    """
    match = AMOUNT_AND_TOKEN_REGEX.match(amount_and_token)
    if not match:
        raise ValueError(f"Invalid amount and token string: {amount_and_token}")
    return int(match.group(1)), match.group(2)


class Processor:
    account_manager: AccountManager
    alias_coin_manager: AliasCoinManager
    coin_manager: CoinManager
    db: Session
    network_id: int
    blockchain: str

    _assets_cosmos: dict[str, mintscan.CosmosAsset] | None = None
    _assets_mintscan: dict[str, mintscan.Asset] | None = None

    def __init__(self, db: Session, network_id: int, blockchain: str):
        self.account_manager = AccountManager(db)
        self.alias_coin_manager = AliasCoinManager(db)
        self.coin_manager = CoinManager(db)
        self.db = db
        self.network_id = network_id
        self.blockchain = blockchain

    @property
    def assets_cosmos(self) -> dict[str, mintscan.CosmosAsset]:
        if self._assets_cosmos is None:
            all_assets = mintscan.get_cosmos_asset_list(self.blockchain).assets
            self._assets_cosmos = {asset.base: asset for asset in all_assets}
        return self._assets_cosmos

    @property
    def assets_mintscan(self) -> dict[str, mintscan.Asset]:
        if self._assets_mintscan is None:
            all_assets = mintscan.get_asset_list(self.blockchain).assets
            self._assets_mintscan = {asset.denom: asset for asset in all_assets}
        return self._assets_mintscan

    def process_amount(self, amount: int, token: str):
        """Process amount and token into a Decimal and asset account."""
        amount_decimal = None
        coingecko_id = None
        ticker_symbol = None

        # As of June 2024, mintscan API is missing some tokens. So we will be using
        # the assets from Cosmos chain registry with more precedence, and fallback
        # to mintscan assets just in case.
        if asset := self.assets_cosmos.get(token):
            decimals = next(
                unit.exponent for unit in asset.denom_units if unit.denom == asset.display
            )
            ticker_symbol = asset.symbol
            amount_decimal = Decimal(amount).scaleb(-decimals)
            coingecko_id = asset.coingecko_id

        if asset := self.assets_mintscan.get(token):
            if not ticker_symbol or amount_decimal is None:
                ticker_symbol = asset.symbol
                amount_decimal = Decimal(amount).scaleb(-asset.decimals)
            if not coingecko_id:
                coingecko_id = asset.coingecko_id

        if not ticker_symbol or amount_decimal is None:
            raise ValueError(f"Unknown token: {token}")

        asset_account = self.account_manager.get_or_create_with_network_id(
            token, self.network_id, ticker_symbol=ticker_symbol
        )
        if not asset_account.coin:
            coin = None
            if coingecko_id:
                try:
                    coin = self.coin_manager.get_coin_by_uid(coingecko_id)
                except NoResultFound:
                    pass

            if not coin:
                coin = self.coin_manager.get_coin_by_ticker(
                    ticker_symbol.casefold(), priced=True
                )

            if coin:
                if alias_coin := self.alias_coin_manager.get_by_aliased_coin_id(coin.id):
                    coin = alias_coin.coin
                asset_account.coin = coin
                self.db.commit()

        return amount_decimal, asset_account

    def process_fee(self, fee: tuple[int, str]):
        """Process raw fee into a Decimal and asset account."""
        amount, token = fee
        if amount == 0:
            return None
        return self.process_amount(amount, token)

    def process_signer_account(self, raw_tx: mintscan.Transaction):
        """Get the signer account from a raw transaction."""
        signer = get_signer_address(raw_tx)
        return self.account_manager.get_or_create_with_network_id(signer, self.network_id)

    def process_transfer(self, raw_transfer: RawTransfer) -> Transfer:
        """Process raw transfer into a Transfer model."""
        sender, recipient, amount, token = raw_transfer

        from_account = self.account_manager.get_or_create_with_network_id(
            sender, self.network_id
        )
        to_account = self.account_manager.get_or_create_with_network_id(
            recipient, self.network_id
        )

        amount_decimal, asset_account = self.process_amount(amount, token)

        return Transfer(
            asset_account=asset_account,
            amount=amount_decimal,
            from_account=from_account,
            to_account=to_account,
        )

    def process_transaction(
        self, wallet: Wallet, raw_tx: mintscan.Transaction
    ) -> Transaction | None:
        """Process a raw transaction into a Transaction model and its transfers.

        Returns a tuple of (transaction, transfer_list) or None if the transaction
        isn't relevant to the wallet.
        """
        logger.debug("Processing transaction %s (%s)", raw_tx.txhash, self.blockchain)

        raw_transfer_list = get_address_raw_transfers(raw_tx, wallet.address)
        if not raw_transfer_list:
            logger.info(
                "No transfers found in transaction %s (%s)", raw_tx.txhash, self.blockchain
            )
            return None

        signer_account = self.process_signer_account(raw_tx)

        transaction = Transaction(
            confirmed_at=datetime.fromisoformat(raw_tx.timestamp),
            json_data=raw_tx.model_dump_json(round_trip=True),
            network_id=self.network_id,
            signer_account=signer_account,
            tx_hash=raw_tx.txhash,
            wallet=wallet,
        )
        raw_fee = get_raw_transaction_fee(raw_tx)

        if raw_fee and (fee := self.process_fee(raw_fee)):
            fee_amount, asset_account = fee
            transaction.fees_paid = fee_amount
            transaction.fees_asset_account = asset_account

        # The fee is accounted on the transaction.fees_paid, so we must filter it out
        # from the transfers list, so it isn't accounted twice.
        if raw_fee:
            raw_transfer_list = filter_fee_transfer(raw_transfer_list, wallet.address, raw_fee)
        else:
            logger.warning(
                "No fee found in transaction %s (%s)", raw_tx.txhash, self.blockchain
            )

        raw_transfer_list = simplify_raw_transfers(wallet.address, raw_transfer_list)

        transaction.transfers = [
            self.process_transfer(raw_transfer) for raw_transfer in raw_transfer_list
        ]

        return transaction


def sync_transactions(db: Session, wallet: Wallet) -> list[WalletSyncResult]:
    """Fetch and save all newer transactions for a wallet."""
    results_list: list[WalletSyncResult] = []
    networks_list = wallet.networks
    for network in networks_list:
        if result := sync_transactions_for_network(db, wallet, network):
            results_list.append(result)
    return results_list


def sync_transactions_for_network(db: Session, wallet: Wallet, network: Network):
    """Fetch and save newer transactions for a wallet and network.

    Returns `None` if the network is not supported. Otherwise, returns a
    `WalletSyncResult`.
    """
    # Ensure wallet account exists
    AccountManager(db).get_or_create_with_network_id(wallet.address, network.id, wallet=wallet)

    blockchain = network.name.lower()

    transaction_manager = TransactionManager(db)
    latest_transaction = transaction_manager.get_latest(network, wallet)
    latest_transaction_time = latest_transaction.confirmed_at if latest_transaction else None

    result = WalletSyncResult(
        network_name=network.name,
        provider="mintscan",
        provider_network=blockchain,
        processed_transactions=0,
        errors=[],
    )

    has_more = True
    search_after = ""
    raw_tx_list: list[mintscan.Transaction] = []
    while has_more:
        try:
            resp = mintscan.get_account_transactions(
                blockchain,
                wallet.address,
                search_after=search_after,
                from_date_time=latest_transaction_time,
            )
        except httpx.HTTPError as e:
            detail = e.response.text if isinstance(e, httpx.HTTPStatusError) else str(e)
            message = f"Failed to fetch transactions from Mintscan: {detail}"
            logger.error(message, exc_info=True)
            result.errors.append(message)
            return result

        search_after = resp.pagination.searchAfter or ""
        has_more = bool(search_after)
        raw_tx_list.extend(resp.transactions)

    raw_tx_list = filter_existing_transactions(transaction_manager, network.id, raw_tx_list)

    processor = Processor(db, network.id, blockchain)

    while raw_tx_list:
        # Transactions comes ordered from newest to oldest, so we consume from
        # the end of the list to process in chronological order.
        raw_tx = raw_tx_list.pop()

        try:
            transaction = processor.process_transaction(wallet, raw_tx)
            if transaction:
                transaction_manager.add_with_transfers(
                    wallet, {transaction: transaction.transfers}
                )
                result.processed_transactions += 1
        except (
            AssertionError,
            DatabaseError,
            ValidationError,
            ValueError,
            httpx.HTTPError,
        ) as e:
            db.rollback()
            detail = e.response.text if isinstance(e, httpx.HTTPStatusError) else str(e)
            message = f"{type(e).__name__}: {detail}"
            logger.error(
                "Error processing transaction %s (%s): %s",
                raw_tx.txhash,
                blockchain,
                message,
                exc_info=True,
            )
            result.errors.append(message)

    return result
