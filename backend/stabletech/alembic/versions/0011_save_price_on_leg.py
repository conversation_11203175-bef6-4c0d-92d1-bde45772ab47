"""Save Price on Leg

Revision ID: 0011
Revises: 0010
Create Date: 2022-12-23 10:57:17.225348

"""

import datetime
from decimal import Decimal

import sqlalchemy as sa
from alembic import op
from sqlalchemy import Column, DateTime, ForeignKey, Integer, Numeric, String
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.orm.session import Session
from sqlalchemy.sql import func

# revision identifiers, used by Alembic.
revision = "0011"
down_revision = "0010"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Coin(Base):
    id = Column(Integer, primary_key=True)
    latest_price_id: int = Column(Integer, ForeignKey("price.id"))
    latest_price = relationship("Price", foreign_keys=[latest_price_id])
    ticker: str = Column(String, nullable=False)
    uid: str = Column(String, index=True, unique=True)
    __tablename__ = "coin"


class Price(Base):
    id = Column(Integer, primary_key=True)
    at: datetime = Column(DateTime, nullable=False, index=True, server_default=func.now())
    coin_id: int = Column(Integer, ForeignKey(Coin.id), nullable=False, index=True)
    coin = relationship(Coin, foreign_keys=[coin_id])
    dollar_price: Decimal = Column(Numeric(precision=32, scale=18), nullable=False)
    last_updated_at: datetime = Column(
        DateTime, nullable=False
    )  # When CoinGecko last updated the price
    market_cap: Decimal = Column(Numeric(precision=32, scale=18), nullable=False)
    price_change_last_day: Decimal = Column(Numeric(precision=32, scale=18), nullable=False)
    volume_last_day: Decimal = Column(Numeric(precision=32, scale=18), nullable=False)
    __tablename__ = "price"


class Leg(Base):
    id = Column(Integer, primary_key=True)
    coin_id: int = Column(Integer, ForeignKey(Coin.id), nullable=False, index=True)
    coin = relationship(Coin, foreign_keys=[coin_id])
    price_id: int = Column(Integer, ForeignKey("price.id"), nullable=False)
    price = relationship("Price", foreign_keys=[price_id])
    __tablename__ = "leg"


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("leg", sa.Column("price_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "leg", "price", ["price_id"], ["id"])
    # ### end Alembic commands ###

    # Set Price for existing Legs
    session = Session(bind=op.get_bind())

    # Add Price for US Dollar
    usd_coin = session.query(Coin).filter_by(uid="dollar").one()
    now = datetime.datetime.now()
    usd_coin.ticker = usd_coin.ticker.lower()
    price = Price(
        at=now,
        coin=usd_coin,
        coin_id=usd_coin.id,
        dollar_price=Decimal("1"),
        last_updated_at=now,
        market_cap=Decimal(0),
        price_change_last_day=Decimal(0),
        volume_last_day=Decimal(0),
    )
    session.add(price)
    session.commit()
    session.refresh(price)
    usd_coin.latest_price_id = price.id
    session.commit()

    for leg in session.query(Leg).all():
        leg.price_id = leg.coin.latest_price_id

    session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("leg", "price_id")
    # ### end Alembic commands ###
