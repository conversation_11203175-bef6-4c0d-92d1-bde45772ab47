"""User agreement

Revision ID: 0053
Revises: 0052
Create Date: 2024-06-03 16:03:17.875429

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0053"
down_revision = "0052"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user", sa.Column("agreement_accepted_at", sa.DateTime(timezone=True), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "agreement_accepted_at")
    # ### end Alembic commands ###
