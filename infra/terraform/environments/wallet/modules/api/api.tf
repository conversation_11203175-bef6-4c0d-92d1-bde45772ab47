resource "random_id" "neg_suffix" {
  byte_length = 4
}

locals {
  api_container_args = compact([
    "uvicorn",
    "wallet.api:app",
    "--forwarded-allow-ips=*",
    "--host=0.0.0.0",
    "--port=80",
    var.auto_reload ? "--reload" : ""
  ])

  neg_name = var.create_neg ? "${var.namespace}-api-${random_id.neg_suffix.hex}" : ""
}

resource "kubernetes_service" "api" {
  metadata {
    name      = "api"
    namespace = var.namespace
    annotations = {
      "cloud.google.com/neg" = (local.neg_name != "" ?
        jsonencode({ exposed_ports = { "80" = { name = local.neg_name } } })
      : null)
    }
  }
  spec {
    selector = {
      app = "api"
    }
    port {
      port        = 80
      target_port = 80
    }
  }
  lifecycle {
    ignore_changes = [metadata[0].annotations["cloud.google.com/neg-status"]]
  }
}

resource "kubernetes_service_account_v1" "api" {
  metadata {
    name      = "api-pods"
    namespace = var.namespace
    annotations = {
      "iam.gke.io/gcp-service-account" : var.pods_service_account
    }
  }
}

resource "kubernetes_deployment" "api" {
  metadata {
    name      = "api"
    namespace = var.namespace
    labels = {
      app = "api"
    }
  }

  spec {
    replicas = var.replicas
    selector {
      match_labels = {
        app = "api"
      }
    }
    template {
      metadata {
        labels = {
          app = "api"
        }
      }
      spec {
        service_account_name = kubernetes_service_account_v1.api.metadata.0.name

        container {
          name              = "api"
          image             = var.python_runner_image
          image_pull_policy = "IfNotPresent"
          args              = local.api_container_args

          port {
            container_port = 80
          }

          dynamic "env_from" {
            for_each = var.config_maps
            iterator = config_map
            content {
              config_map_ref {
                name = config_map.value
              }
            }
          }
          dynamic "env_from" {
            for_each = var.secrets
            iterator = secret
            content {
              secret_ref {
                name = secret.value
              }
            }
          }

          resources {
            requests = {
              cpu    = "60m"
              memory = "128Mi"
            }
          }

          readiness_probe {
            http_get {
              path = "/"
              port = 80
            }
            initial_delay_seconds = 5
            period_seconds        = 5
          }

          dynamic "liveness_probe" {
            for_each = var.disable_liveness_probe ? [] : [1]
            content {
              http_get {
                path = "/"
                port = 80
              }
              initial_delay_seconds = 30
              period_seconds        = 5
            }
          }

          dynamic "volume_mount" {
            for_each = var.host_volumes
            content {
              name       = volume_mount.value.name
              mount_path = volume_mount.value.mount_path
            }
          }
        }

        dynamic "volume" {
          for_each = var.host_volumes
          content {
            name = volume.value.name
            host_path {
              path = volume.value.host_path
            }
          }
        }
      }
    }
  }
}

output "neg_name" {
  value      = local.neg_name
  depends_on = [kubernetes_service.api, kubernetes_deployment.api]
}
