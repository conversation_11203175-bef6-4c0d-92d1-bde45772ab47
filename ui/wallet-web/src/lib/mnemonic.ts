import { importK<PERSON> } from "@/lib/secret-store";
import * as ed25519 from "@noble/ed25519";
import * as secp256k1 from "@noble/secp256k1";
import { HDKey } from "@scure/bip32";
import {
  generateMnemonic as genMnemonic,
  mnemonicToSeed,
  validateMnemonic,
} from "@scure/bip39";
import { wordlist } from "@scure/bip39/wordlists/english";

interface NetworkConfig {
  derivationPath: string;
  algorithm: string;
}

export type Network = "ethereum" | "solana";

// Combined network configuration mapping
const NETWORK_CONFIGS: Record<string, NetworkConfig> = {
  solana: {
    derivationPath: "m/44'/501'/0'/0'",
    algorithm: "ed25519",
  },

  // evm
  ethereum: {
    derivationPath: "m/44'/60'/0'/0/0",
    algorithm: "secp256k1",
  },
};

const DEFAULT_DERIVATION_NETWORKS: Network[] = ["ethereum", "solana"];

function getNetworkConfig(network: string): NetworkConfig {
  const config = NETWORK_CONFIGS[network];
  if (!config) {
    throw new WalletError(`Unsupported network: ${network}`);
  }
  return config;
}

/**
 * Base error class for wallet creation errors
 */
export class WalletError extends Error {
  constructor(message: string) {
    super(message);
    this.name = this.constructor.name;
  }
}

/**
 * Thrown when master key creation fails
 */
export class MasterKeyError extends WalletError {
  constructor(message: string) {
    super(`Master key error: ${message}`);
  }
}

/**
 * Thrown when key derivation fails
 */
export class DerivationError extends WalletError {
  constructor(message: string) {
    super(`Derivation error: ${message}`);
  }
}

/**
 * Thrown when there are issues with private/public key operations
 */
export class KeyError extends WalletError {
  constructor(message: string) {
    super(`Key error: ${message}`);
  }
}

/**
 * Generate a new mnemonic phrase
 * @returns A string containing the mnemonic words
 */
export type MnemonicStrength = 128 | 256; // 128 bits = 12 words, 256 bits = 24 words

export function generateMnemonic(strength: MnemonicStrength = 256): string {
  return genMnemonic(wordlist, strength);
}

/**
 * Validate a mnemonic phrase
 * @param mnemonic The mnemonic phrase to validate
 * @returns true if valid, false otherwise
 */
export function isValidMnemonic(mnemonic: string): boolean {
  return validateMnemonic(mnemonic, wordlist);
}

function validatePrivateKey(privateKey: unknown) {
  if (!privateKey) {
    throw new KeyError("Key has no private key");
  }

  if (!(privateKey instanceof Uint8Array)) {
    throw new KeyError("Invalid private key type - expected Uint8Array");
  }
  return privateKey;
}

async function getPublicKeyByNetwork(key: Uint8Array, network: string): Promise<Uint8Array> {
  // Generate public key based on network's cryptographic algorithm
  switch (network) {
    case "ethereum":
      return secp256k1.getPublicKey(key, false);
    case "solana":
      return await ed25519.getPublicKeyAsync(key);
    default:
      throw new WalletError(`Unsupported network: ${network}`);
  }
}

export interface SeedAccount {
  keyId: string;
  derivedKeyIds: Set<string>;
}

async function deriveKey(
  masterKeyId: string,
  key: HDKey,
  password: CryptoKey,
  network: Network = "solana",
): Promise<string> {
  // Determine the cryptographic algorithm based on the network
  const networkConfig = getNetworkConfig(network);
  const path = networkConfig.derivationPath;
  const cryptoAlgorithm = networkConfig.algorithm;

  const derived = key.derive(path);
  if (!(derived instanceof HDKey)) {
    throw new DerivationError("Invalid derived key type");
  }

  const derivedPrivateKey = validatePrivateKey(derived.privateKey);

  // Generate public key based on network's cryptographic algorithm
  const derivedPublicKey = await getPublicKeyByNetwork(derivedPrivateKey, network);

  // Store derived key pair with metadata
  const derivedKeyId = await importKey({
    password,
    publicKey: derivedPublicKey,
    secretKey: derivedPrivateKey,
    validate: cryptoAlgorithm === "ed25519", // Only validate ed25519 keys
    metadata: {
      isMasterKey: false,
      network: network,
      masterKeyId: masterKeyId,
    },
  });

  return derivedKeyId;
}

/**
 * Derives both Solana and Ethereum wallets from a single mnemonic phrase.
 * This allows users to have wallets on both networks derived from the same seed.
 *
 * @param mnemonic The mnemonic phrase to use
 * @param password The password to encrypt the keys with
 * @returns Object containing keyId (master key) and derivedKeyIds for both networks
 */
export async function deriveMultiChainWallets(
  mnemonic: string,
  password: CryptoKey,
): Promise<SeedAccount> {
  if (!isValidMnemonic(mnemonic)) {
    throw new WalletError("Invalid mnemonic phrase");
  }
  try {
    // Step 1: Convert to seed
    const seed = await mnemonicToSeed(mnemonic);

    // Step 2: Create master key
    const masterKey = HDKey.fromMasterSeed(seed);
    if (!(masterKey instanceof HDKey)) {
      throw new MasterKeyError("Invalid master key type");
    }

    const masterPrivateKey = masterKey.privateKey;
    if (!masterPrivateKey) {
      throw new KeyError("Master key has no private key");
    }

    if (!(masterPrivateKey instanceof Uint8Array)) {
      throw new KeyError("Invalid master private key type - expected Uint8Array");
    }

    // Generate public key for master key (using ed25519 for storage)
    const masterPublicKey = await ed25519.getPublicKeyAsync(masterPrivateKey);

    // Step 3: Store master key pair with metadata
    const keyId = await importKey({
      password,
      publicKey: masterPublicKey,
      secretKey: masterPrivateKey,
      validate: true, // Validate the master key
      metadata: {
        isMasterKey: true,
        network: "solana", // Default to Solana for the master key
      },
    });

    const derivedKeyIds = new Set<string>();
    // Directly enumerate the networks we support
    for (const network of DEFAULT_DERIVATION_NETWORKS) {
      // Derive key for each network
      const derivedKeyId = await deriveKey(keyId, masterKey, password, network);
      derivedKeyIds.add(derivedKeyId);
    }
    return {
      keyId,
      derivedKeyIds,
    };
  } catch (error) {
    // Re-throw wallet errors directly
    if (error instanceof WalletError) {
      throw error;
    }
    // Otherwise wrap in Error object
    throw new WalletError("Failed to create multi-chain wallets");
  }
}

/**
 * Creates a new wallet from an imported mnemonic phrase and stores it securely
 * Also derives sub-wallets for each available network (Solana and Ethereum)
 *
 * @param mnemonic The mnemonic phrase to import
 * @param password The password to encrypt the keys with
 * @returns Object containing keyId (master key) and derivedKeyIds for both networks
 */
export async function importWalletFromMnemonic(
  mnemonic: string,
  password: CryptoKey,
): Promise<SeedAccount> {
  if (!isValidMnemonic(mnemonic)) {
    throw new WalletError("Invalid mnemonic phrase");
  }

  // Use the existing deriveMultiChainWallets function to create the master key
  // and derive sub-wallets for each network
  return await deriveMultiChainWallets(mnemonic, password);
}
