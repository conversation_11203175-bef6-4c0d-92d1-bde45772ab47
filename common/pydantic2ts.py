"""Generate TypeScript types from backend pydantic models.

Example usage:
```bash
source ./backend/.venv/bin/activate
pip install -r ./backend/common/requirements.txt
python ./common/pydantic2ts.py
```
"""

import argparse
import dataclasses
import json
import os
import subprocess
import sys
from pathlib import Path

parser = argparse.ArgumentParser()
parser.add_argument("-v", "--verbose", action="store_true")
args = parser.parse_args()
verbose: bool = args.verbose


@dataclasses.dataclass
class Input:
    module: str
    output: str


input_list = [
    Input(module="stabletech.schemas", output="ui/client-bashboard/src/types/schemas.ts"),
    Input(module="lootloot.schemas", output="ui/lootloot/types/schemas.ts"),
    Input(module="wallet.schemas", output="ui/wallet-web/src/types/schemas.ts"),
]


def run(cmd: list[str], **kwargs):
    kwargs.setdefault("text", True)
    if verbose:
        print("$", " ".join(cmd))
        kwargs.setdefault("stdout", sys.stdout)
        kwargs.setdefault("stderr", sys.stdout)
    else:
        kwargs.setdefault("stdout", subprocess.PIPE)
        kwargs.setdefault("stderr", subprocess.STDOUT)
    return subprocess.run(cmd, **kwargs)


def maybe_fail(result: subprocess.CompletedProcess, *values):
    if result.returncode == 0:
        return
    print("Command:", result.args)
    print(result.stdout)
    print(*values)
    sys.exit(1)


# Check if pydantic2ts is available and install if necessary
result = run(["pip", "list", "--local", "--format=freeze"], stdout=subprocess.PIPE)
maybe_fail(result, "Unexpected error listing installed packages.")
if "pydantic-to-typescript==2.0.0\n" not in result.stdout:
    print("Installing pydantic-to-typescript with pip...", flush=True)
    result = run(
        [
            "pip",
            "install",
            "pydantic-to-typescript==2.0.0",
        ]
    )
    maybe_fail(result, "Failed installing pydantic-to-typescript package.")

# Check if npm is available
result = run(["which", "npm"])
maybe_fail(result, "npm not found, do you have https://nodejs.org/ installed?")
json2ts_cmd = "npm exec --package=json-schema-to-typescript@^15.0.4 -y -- json2ts"

cwd = os.getcwd()
root_path_abs = Path(__file__).parent.parent.resolve()
root_path = Path(os.path.relpath(root_path_abs, cwd))
python_path = root_path / "backend"

our_banner = """/**
 * This file was automatically generated by running common/pydantic2ts.py.
 * Do not modify it by hand - just update the pydantic models and then re-run the script
 */

 """
output_list: list[str] = []

for i in input_list:
    print(f"Processing {i.module} module...", flush=True)
    output = str(root_path / i.output)
    result = run(
        [
            "pydantic2ts",
            "--json2ts-cmd",
            json2ts_cmd,
            "--module",
            i.module,
            "--exclude",
            "BaseSchema",
            "--output",
            output,
        ],
        env={**os.environ, "PYTHONPATH": python_path},
    )
    maybe_fail(result, "Unexpected error running pydantic2ts.")

    # Replace default banner with ours
    with open(output) as fp:
        output_content = fp.read()
    banner_end_marker = "\n*/\n\n"
    banner_end_index = output_content.find(banner_end_marker) + len(banner_end_marker)
    output_content = our_banner + output_content[banner_end_index:]
    with open(output, "w") as fp:
        fp.write(output_content)

    output_list.append(output)

print("Formatting outputs...", flush=True)
with open(root_path / "package.json") as fp:
    prettier_version = json.load(fp)["devDependencies"]["prettier"]
run(
    [
        "npm",
        "exec",
        f"--package=prettier@{prettier_version}",
        "-y",
        "--",
        "prettier",
        "--write",
        *output_list,
    ]
)

print("Completed successfully.")
