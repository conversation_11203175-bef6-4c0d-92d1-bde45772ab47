resource "google_service_account" "terraform_cloud" {
  account_id                   = "terraform-cloud"
  create_ignore_already_exists = null
  description                  = "Terraform Cloud deployments"
  disabled                     = false
  display_name                 = "terraform-cloud"
}

resource "google_project_iam_member" "terraform_cloud_storage_admin_minus_read_write_objects" {
  member  = "serviceAccount:${google_service_account.terraform_cloud.email}"
  project = local.project_id
  role    = google_project_iam_custom_role.storage_admin_minus_read_write_objects.id
}

resource "google_project_iam_member" "terraform_cloud_view_service_accounts_excluding_keys" {
  member  = "serviceAccount:${google_service_account.terraform_cloud.email}"
  project = local.project_id
  role    = google_project_iam_custom_role.view_service_accounts_excluding_keys.id
}

resource "google_project_iam_member" "terraform_cloud_container_admin" {
  member  = "serviceAccount:${google_service_account.terraform_cloud.email}"
  project = local.project_id
  role    = "roles/container.admin"
}

resource "google_project_iam_member" "terraform_cloud_compute_admin" {
  member  = "serviceAccount:${google_service_account.terraform_cloud.email}"
  project = local.project_id
  role    = "roles/compute.admin"
}

resource "google_project_iam_member" "terraform_cloud_dns_admin" {
  member  = "serviceAccount:${google_service_account.terraform_cloud.email}"
  project = local.project_id
  role    = "roles/dns.admin"
}

resource "google_project_iam_member" "terraform_cloud_cloudsql_admin" {
  member  = "serviceAccount:${google_service_account.terraform_cloud.email}"
  project = local.project_id
  role    = "roles/cloudsql.admin"
}

resource "google_project_iam_member" "terraform_cloud_bucket_iam_manager" {
  member  = "serviceAccount:${google_service_account.terraform_cloud.email}"
  project = local.project_id
  role    = google_project_iam_custom_role.storage_bucket_iam_manager.id
  condition {
    title       = "domain_restricted"
    description = "Only allow to manage permissions of allowed buckets"
    # Resource names will follow the format: projects/_/buckets/<name>
    expression = <<EOT
      resource.name.endsWith('/stabletech.capital') ||
      resource.name.endsWith('.stabletech.capital') ||
      resource.name.endsWith('/lootloot.farm') ||
      resource.name.endsWith('.lootloot.farm') ||
      resource.name.endsWith('/temet.tech') ||
      resource.name.endsWith('.temet.tech')
    EOT
  }
}

# Allow terraform cloud workspaces to impersonate terraform-cloud
locals {
  terraform_principals = {
    for name, id in {
      "lootloot-production"   = "ws-VqVzGAPbhhKwTj2g"
      "stabletech-apollo"     = "ws-yr8QMmrZQZa9Ymx9"
      "stabletech-diwan"      = "ws-7kHihs3KhNETaBNZ"
      "stabletech-lp"         = "ws-1WWxdAHSxnLwE9zS"
      "stabletech-production" = "ws-u9V3sYixLcP9bpAD"
      "stabletech-stage"      = "ws-6cH7JSqkFq5PrxyV"
      "wallet-stage"          = "ws-HPuVaYYUXQTZAkxh"
    } :
    name => "principal://iam.googleapis.com/projects/${data.google_project.this.number}/locations/global/workloadIdentityPools/terraform-cloud/subject/${id}"
  }
}

resource "google_service_account_iam_member" "terraform_cloud_workload_identity_user" {
  for_each           = local.terraform_principals
  service_account_id = google_service_account.terraform_cloud.name
  role               = "roles/iam.workloadIdentityUser"
  member             = each.value
}
