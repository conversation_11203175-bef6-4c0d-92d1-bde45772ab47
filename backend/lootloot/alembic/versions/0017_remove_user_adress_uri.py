"""Remove User.adress_uri

Revision ID: 0017
Revises: 0016
Create Date: 2024-03-04 20:54:25.459094

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0017"
down_revision = "0016"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("user_address_uri_key", "user", type_="unique")
    op.drop_column("user", "address_uri")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user", sa.Column("address_uri", sa.VARCHAR(), autoincrement=False, nullable=True)
    )
    op.create_unique_constraint("user_address_uri_key", "user", ["address_uri"])
    # ### end Alembic commands ###
