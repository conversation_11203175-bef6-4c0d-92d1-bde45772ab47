"""Revenue share

Revision ID: 0066
Revises: 0065
Create Date: 2025-05-27 19:33:59.173821

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0066"
down_revision = "0065"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "clienttransaction", sa.<PERSON>umn("revenue_transaction_id", sa.Integer(), nullable=True)
    )
    op.create_index(
        op.f("ix_clienttransaction_revenue_transaction_id"),
        "clienttransaction",
        ["revenue_transaction_id"],
        unique=False,
    )
    op.create_foreign_key(
        None, "clienttransaction", "transaction", ["revenue_transaction_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "clienttransaction", type_="foreignkey")
    op.drop_index(
        op.f("ix_clienttransaction_revenue_transaction_id"), table_name="clienttransaction"
    )
    op.drop_column("clienttransaction", "revenue_transaction_id")
    # ### end Alembic commands ###
