import { Network } from "@/lib/mnemonic";
import { randomId } from "@/lib/utils/random";
import { xchacha20poly1305 } from "@noble/ciphers/chacha";
import { managedNonce } from "@noble/ciphers/webcrypto";
import * as ed25519 from "@noble/ed25519";
import base58 from "bs58";
import { Buffer } from "buffer";
import { del, get, set, update } from "idb-keyval";
import { Hex, TransactionSerializable } from "viem";
import { privateKeyToAccount } from "viem/accounts";

const PREFIX = "wallet:ss1";
const KEYS_LIST_KEY = `${PREFIX}:keys`;
const CREDENTIAL_KEY = `${PREFIX}:credential`;

const xchacha20poly1305WithManagedNonce = managedNonce(xchacha20poly1305);

interface KeyData {
  readonly salt: Uint8Array;
  readonly iterations: number;
  readonly publicKey: Uint8Array;
  readonly wrappedSecretKey: Uint8Array;
  readonly credentialWrapped?: Uint8Array | undefined;
  readonly metadata: KeyMetadata;
}

export interface KeyMetadata {
  readonly network: Network;
  readonly masterKeyId?: string;
  readonly isMasterKey: boolean;
}

export interface Key {
  readonly id: string;
  readonly publicKey: Uint8Array;
  readonly metadata: KeyMetadata;
}

interface CredentialData {
  readonly id: string;
}

export async function createKey(password: CryptoKey, metadata?: KeyMetadata): Promise<string> {
  const secretKey = ed25519.utils.randomPrivateKey();
  const publicKey = await ed25519.getPublicKeyAsync(secretKey);

  return await saveKey({ password, publicKey, secretKey, metadata });
}

export async function getKeyList(): Promise<Key[]> {
  return (await get<Key[]>(KEYS_LIST_KEY)) ?? [];
}

export async function importKey({
  password,
  publicKey,
  secretKey,
  validate = true,
  metadata,
}: {
  password: CryptoKey;
  publicKey: Uint8Array;
  secretKey: Uint8Array;
  validate?: boolean;
  metadata?: KeyMetadata | undefined;
}): Promise<string> {
  if (validate) {
    const data = new TextEncoder().encode("keep cool, never freeze");
    const signature = await ed25519.signAsync(data, secretKey);
    const valid = await ed25519.verifyAsync(signature, data, publicKey);
    if (!valid) {
      throw new Error("Key validation failed");
    }
  }

  return await saveKey({ password, publicKey, secretKey, metadata });
}

async function saveKey({
  password,
  publicKey,
  secretKey,
  metadata = { network: "solana", isMasterKey: false },
}: {
  password: CryptoKey;
  publicKey: Uint8Array;
  secretKey: Uint8Array;
  metadata: KeyMetadata | undefined;
}): Promise<string> {
  const { wrappingKey, salt, iterations } = await deriveWrappingKey(password);

  const wrappingKeyData = await crypto.subtle.exportKey("raw", wrappingKey);
  const cypher = xchacha20poly1305WithManagedNonce(new Uint8Array(wrappingKeyData));
  const wrappedSecretKey = cypher.encrypt(secretKey);

  // Avoid importing the same key twice
  const keys = await getKeyList();
  if (keys.some((key) => buffersAreEqual(key.publicKey, publicKey))) {
    const address = base58.encode(publicKey);
    const message = `Key already exists for address ${address}`;
    throw new Error(message);
  }

  const keyId = randomId(16);

  await set(`${PREFIX}:${keyId}`, {
    iterations,
    salt,
    publicKey,
    wrappedSecretKey,
    metadata,
  } satisfies KeyData);

  await update<Key[]>(KEYS_LIST_KEY, (keys = []) => [
    ...keys,
    { id: keyId, publicKey, metadata },
  ]);

  return keyId;
}

export type KeyDecoder =
  | { kind: "password"; password: CryptoKey }
  | { kind: "credential"; key: CryptoKey };

async function getSecretKey({
  keyId,
  decoder,
}: {
  keyId: string;
  decoder: KeyDecoder;
}): Promise<Uint8Array> {
  const keyData = await get<KeyData>(`${PREFIX}:${keyId}`);
  if (!keyData) {
    throw new Error("Key not found");
  }

  const cipher = await createCipher(decoder, keyData);

  const wrappedKey =
    decoder.kind === "password" ? keyData.wrappedSecretKey : keyData.credentialWrapped;
  if (!wrappedKey) {
    throw new Error("Unsupported key type");
  }

  let secretKey: Uint8Array;
  try {
    secretKey = cipher.decrypt(wrappedKey);
  } catch (error) {
    let message = "Failed to unwrap key";
    if (error instanceof Error && error.message === "invalid tag") {
      message = "Invalid password";
    }
    throw new Error(message, { cause: error });
  }
  return secretKey;
}

async function createCipher(decoder: KeyDecoder, keyData: KeyData) {
  let wrappingKey;
  if (decoder.kind === "password") {
    const derivedKey = await deriveWrappingKey(
      decoder.password,
      keyData.salt,
      keyData.iterations,
    );
    wrappingKey = derivedKey.wrappingKey;
  } else {
    wrappingKey = decoder.key;
  }
  const wrappingKeyData = await crypto.subtle.exportKey("raw", wrappingKey);
  return xchacha20poly1305WithManagedNonce(new Uint8Array(wrappingKeyData));
}

export async function signSolanaWithKey({
  keyId,
  message,
  decoder,
}: {
  keyId: string;
  message: Uint8Array;
  decoder: KeyDecoder;
}): Promise<Uint8Array> {
  const secretKey = await getSecretKey({ keyId, decoder });
  return await ed25519.signAsync(message, secretKey);
}

export async function signEthereumWithKey({
  keyId,
  message,
  decoder,
}: {
  keyId: string;
  message: TransactionSerializable;
  decoder: KeyDecoder;
}): Promise<Hex> {
  const secretKey = await getSecretKey({ keyId, decoder });
  const hexPrivateKey = Buffer.from(secretKey).toString("hex");
  const account = privateKeyToAccount(`0x${hexPrivateKey}`);
  return await account.signTransaction(message);
}

export async function getPasswordKey(password: string): Promise<CryptoKey> {
  return await crypto.subtle.importKey(
    "raw",
    new TextEncoder().encode(password),
    "PBKDF2",
    false,
    ["deriveKey"],
  );
}

export async function deriveWrappingKey(
  password: CryptoKey,
  salt?: Uint8Array,
  iterations?: number,
) {
  salt ??= crypto.getRandomValues(new Uint8Array(16));
  iterations ??= 700_000; // https://cheatsheetseries.owasp.org/cheatsheets/Password_Storage_Cheat_Sheet.html#pbkdf2
  const wrappingKey = await crypto.subtle.deriveKey(
    { name: "PBKDF2", salt, iterations, hash: "SHA-256" },
    password,
    { name: "AES-KW", length: 256 },
    true, // change to false when external libs are not required
    ["wrapKey", "unwrapKey"],
  );

  return { wrappingKey, salt, iterations };
}

function buffersAreEqual(a: Uint8Array, b: Uint8Array): boolean {
  if (a.byteLength !== b.byteLength) {
    return false;
  }
  // eslint-disable-next-line unicorn/no-for-loop
  for (let i = 0; i < a.length; i++) {
    if (a[i] !== b[i]) {
      return false;
    }
  }
  return true;
}

/**
 * Wrap all keys with a credential wrapping key.
 */
export async function credentialWrapAll(
  credentialId: string,
  wrappingKey: Uint8Array,
  password: CryptoKey,
): Promise<void> {
  const credentialData = await get<CredentialData>(CREDENTIAL_KEY);
  if (!credentialData) {
    throw new Error("No credential registered");
  }
  if (credentialData.id !== credentialId) {
    throw new Error("Credential mismatch");
  }

  const keys = await getKeyList();
  if (keys.length === 0) {
    return;
  }

  for (const { id: keyId } of keys) {
    await credentialWrapKey(keyId, wrappingKey, password);
  }
}

/**
 * Wrap a key with a credential wrapping key.
 */
export async function credentialWrapKey(
  keyId: string,
  wrappingKey: Uint8Array,
  password: CryptoKey,
) {
  const keyData = await get<KeyData>(`${PREFIX}:${keyId}`);
  if (!keyData) {
    return;
  }

  const { wrappingKey: unwrappingKey } = await deriveWrappingKey(
    password,
    keyData.salt,
    keyData.iterations,
  );
  const unwrappingKeyData = await crypto.subtle.exportKey("raw", unwrappingKey);
  const unwrappingCypher = xchacha20poly1305WithManagedNonce(new Uint8Array(unwrappingKeyData));

  const wrappingCipher = xchacha20poly1305WithManagedNonce(wrappingKey);

  let wrappedKey: Uint8Array;
  {
    const secretKey = unwrappingCypher.decrypt(keyData.wrappedSecretKey);
    wrappedKey = wrappingCipher.encrypt(secretKey);
  }

  const newKeyData = { ...keyData, credentialWrapped: wrappedKey } satisfies KeyData;
  await set(`${PREFIX}:${keyId}`, newKeyData);
}

/**
 * Remove credential wrapping from a key.
 */
export async function credentialWrapKeyRemove(keyId: string): Promise<void> {
  const keyData = await get<KeyData>(`${PREFIX}:${keyId}`);
  if (!keyData) {
    return;
  }

  if (keyData.credentialWrapped) {
    const newKeyData = { ...keyData, credentialWrapped: undefined } satisfies KeyData;
    await set(`${PREFIX}:${keyId}`, newKeyData);
  }
}

/**
 * Attempt to register a credential to be used for unwrapping keys
 */
export async function registerCredential(credentialId: string): Promise<boolean> {
  let succeeded = false;
  await update<CredentialData>(CREDENTIAL_KEY, (value) => {
    if (value) {
      if (value.id === credentialId) succeeded = true;
      return value;
    }
    succeeded = true;
    return { id: credentialId };
  });
  return succeeded;
}

/**
 * Unregister credential.
 *
 * This will remove all encrypted keys from the storage.
 */
export async function unregisterCredential(credentialId: string) {
  const credentialData = await get<CredentialData>(CREDENTIAL_KEY);
  if (!credentialData) {
    return;
  }
  if (credentialData.id !== credentialId) {
    throw new Error("Credential mismatch");
  }

  const keys = await getKeyList();
  if (keys.length === 0) {
    return;
  }

  for (const { id: keyId } of keys) {
    const keyData = await get<KeyData>(`${PREFIX}:${keyId}`);
    if (!keyData) {
      continue;
    }

    if (keyData.credentialWrapped) {
      const newKeyData = { ...keyData, credentialWrapped: undefined } satisfies KeyData;
      await set(`${PREFIX}:${keyId}`, newKeyData);
    }
  }

  await del(CREDENTIAL_KEY);
}

export async function getCredentialRegistered(): Promise<CredentialData | null> {
  return (await get<CredentialData>(CREDENTIAL_KEY)) ?? null;
}

export interface KeyWithCredentialStatus {
  key: Key;
  isCredentialWrapped: boolean;
}

/**
 * Get all keys with their credential wrapping status
 */
export async function getKeysWithCredentialStatus(): Promise<KeyWithCredentialStatus[]> {
  const keys = await getKeyList();
  const result: KeyWithCredentialStatus[] = [];

  for (const key of keys) {
    const keyData = await get<KeyData>(`${PREFIX}:${key.id}`);
    if (keyData) {
      result.push({
        key,
        isCredentialWrapped: keyData.credentialWrapped !== undefined,
      });
    }
  }

  return result;
}

/**
 * Get whether a key has been wrapped by a credential.
 */
export async function isKeyCredentialWrapped(keyId: string): Promise<boolean> {
  const keyData = await get<KeyData>(`${PREFIX}:${keyId}`);
  return keyData?.credentialWrapped !== undefined;
}
