import { AbilityBuilder, createMongoAbility, MongoAbility } from "@casl/ability";

type Subjects = string;
type Actions = "manage" | "create" | "read" | "update" | "delete";

export type AppAbility = MongoAbility<[Actions, Subjects]>;

export interface ACLObj {
  action: Actions;
  subject: string;
}

export const buildAbilityFor = (role: string, subject: string): AppAbility => {
  const { can, build } = new AbilityBuilder<AppAbility>(createMongoAbility);

  //can('manage', 'all')
  //return rules
  if (role === "admin") {
    can("manage", "all");
  } else if (role === "client") {
    can(["read"], subject);
  } else {
    can(["read", "create", "update", "delete"], subject);
  }

  return build();
};

export const defaultACLObj: ACLObj = {
  action: "manage",
  subject: "all",
};
