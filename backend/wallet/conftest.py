"""Test configuration and fixtures for the wallet project."""

import json
import uuid
from collections.abc import Generator
from typing import Any

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import <PERSON><PERSON><PERSON>, LargeBinary, create_engine, event
from sqlalchemy.dialects.postgresql import ARRAY, BYTEA, JSONB
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.types import TypeDecorator

from common.psql import get_db
from wallet.api import app
from wallet.indexer.db import Base as BaseIndexer
from wallet.indexer.db import get_subql_db
from wallet.models import Base, User, UserSession


class SQLiteARRAY(TypeDecorator):
    """ARRAY type for SQLite."""

    impl = JSON
    cache_ok = True

    def __init__(self, item_type: Any) -> None:
        super().__init__()
        self.item_type = item_type

    def process_bind_param(self, value: list[Any] | None, dialect: Any) -> str | None:
        if value is None:
            return None
        return json.dumps(value)

    def process_result_value(self, value: str | None, dialect: Any) -> list[Any] | None:
        if value is None:
            return None
        return json.loads(value)


@pytest.fixture(scope="session", autouse=True)
def _register_sqlite_types() -> None:
    """Register SQLite type adapters and functions for all models."""
    for table in Base.metadata.tables.values():
        for column in table.columns:
            if isinstance(column.type, JSONB):
                column.type = JSON()
            elif isinstance(column.type, BYTEA):
                column.type = LargeBinary()
            elif isinstance(column.type, ARRAY):
                column.type = SQLiteARRAY(column.type.item_type)


def _add_sqlite_functions(dbapi_connection, connection_record):
    """Add PostgreSQL-like functions to SQLite for testing."""
    dbapi_connection.create_function("gen_random_uuid", 0, lambda: str(uuid.uuid4()))


@pytest.fixture(scope="session")
def session() -> Generator[Session, None, None]:
    """Create a test session factory with all tables."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    event.listen(engine, "connect", _add_sqlite_functions)
    Base.metadata.create_all(bind=engine)
    BaseIndexer.metadata.create_all(bind=engine)  # Sharing same engine for simplicity
    TestSessionLocal = sessionmaker(bind=engine, expire_on_commit=False)
    session = TestSessionLocal()
    yield session
    session.close()
    engine.dispose()


@pytest.fixture(scope="session")
def client(session: Session) -> Generator[TestClient, None, None]:
    """Create a test client with authentication and database overrides."""

    def get_test_db() -> Generator[Session, None, None]:
        """Get test database session with transaction management."""
        try:
            yield session
            session.commit()
        finally:
            session.rollback()

    app.dependency_overrides[get_db] = get_test_db
    app.dependency_overrides[get_subql_db] = get_test_db

    # Create test user and token
    test_email = "<EMAIL>"
    test_user = session.query(User).filter_by(email=test_email).first()

    if not test_user:
        test_user = User(email=test_email, first_name="John", last_name="Doe")
        session.add(test_user)
        session.commit()
        user_session = None
    else:
        user_session = session.query(UserSession).filter_by(user_id=test_user.id).first()

    if not user_session:
        user_session = UserSession.create(
            user=test_user, user_agent="test", ip_address="127.0.0.1"
        )
        session.add(user_session)
        session.commit()

    with TestClient(
        app, headers={"Authorization": f"Bearer {user_session.token}"}
    ) as test_client:
        yield test_client
