/**
 * This file was automatically generated by running common/pydantic2ts.py.
 * Do not modify it by hand - just update the pydantic models and then re-run the script
 */

export type AuctionHouse = "magic_eden";
export type WalletOrigin = "phantom" | "matrica";

export interface AddNFT {
  address_uri: string;
  nft_collection_uid: string;
}
export interface AddNFTListing {
  auction_house: AuctionHouse;
  nft_address_uri: string;
  sol_price: string;
  at?: string | null;
  nft_collection_uid: string;
}
export interface AddPhantomWallet {
  name: string;
  address: string;
  message: string;
  signature: string;
}
export interface AddRaffle {
  entries_count: number;
  secret_word: string;
  winners_count: number;
}
export interface AddUserWallet {
  name: string;
  origin: WalletOrigin;
  address: string;
  network_group: "evm" | "solana";
}
export interface HeistPrices {
  sol: number | null;
  nana: number | null;
}
export interface HeistStats {
  claimed: string;
  fumbled: string;
  completed: number;
  rekt: number;
  lost_to_ambush: string;
  gained_from_ambush: string;
}
export interface HeistStatsPublic {
  claimed: string;
  fumbled: string;
  completed: number;
  rekt: number;
}
export interface HeistWardrobe {
  species: string;
  background: string[];
  clothing: string[];
  fur: string[];
  head: string[];
  mouth: string[];
}
export interface HolderItem {
  rank: number;
  kind: "user" | "address";
  value: string;
  nft_count: number;
  sol_amount: string;
  own: boolean;
}
export interface HolderListResp {
  items: HolderItem[];
  sol_usd_price: string;
}
export interface ListNFTCollectionsResp {
  items: NFTCollectionListItem[];
}
export interface NFTCollectionListItem {
  uid: string;
  name: string;
  nft_count: number;
  image_url: string;
}
export interface ListNFTResp {
  items: NFTListItem[];
}
export interface NFTListItem {
  address_uri: string;
  uid: string;
  image_url: string;
  is_burnt: boolean;
  is_frozen: boolean;
  attributes_data: NFTAttribute[];
}
export interface NFTAttribute {
  trait_type: string;
  value: string;
}
export interface ListUserWalletsResp {
  items: UserWallet[];
}
export interface UserWallet {
  name: string;
  address: string;
  network_group: "evm" | "solana";
}
export interface ListUsersResp {
  items: User[];
}
export interface User {
  email: string | null;
  first_name: string | null;
  last_name: string | null;
  picture_url: string | null;
  display_name: string | null;
  matrica_username: string | null;
  is_admin: boolean;
}
export interface LoginResponse {
  token: string;
  user: User;
}
export interface MatricaLogin {
  code: string;
  redirect_uri: string;
  code_verifier: string;
}
export interface NFTClaimHistoryItem {
  total_amount_claimed: string;
  duration_hours: number;
  total_amount_emitted: string;
  base_multiplier: number;
  ended_at: string;
  event_id: number;
  location_name: string;
}
export interface NFTClaimsHistoryResp {
  nft_name: string;
  nft_image_url: string;
  claims: NFTClaimHistoryItem[];
}
export interface NanaByDayResp {
  items: NanaInDay[];
}
export interface NanaInDay {
  at: string;
  lost: string;
  gained: string;
}
export interface NonPaginatedRespHolderItem {
  items: HolderItem[];
}
export interface NonPaginatedRespNFTCollectionListItem {
  items: NFTCollectionListItem[];
}
export interface NonPaginatedRespNFTListItem {
  items: NFTListItem[];
}
export interface NonPaginatedRespNanaInDay {
  items: NanaInDay[];
}
export interface NonPaginatedRespPublicNFTCollectionListItem {
  items: PublicNFTCollectionListItem[];
}
export interface PublicNFTCollectionListItem {
  name: string;
  uid: string;
  nft_count: number;
}
export interface NonPaginatedRespTopClaimItem {
  items: TopClaimItem[];
}
export interface TopClaimItem {
  nft_image_url: string | null;
  total_amount_claimed: string;
  duration_hours: number;
  total_amount_emitted: string;
  base_multiplier: number;
  ended_at: string;
  address: string;
  wallet_kind: "user" | "address";
  wallet_value?: string | null;
  location_name: string;
  event_id: number;
  is_ambush?: boolean;
}
export interface NonPaginatedRespUserWallet {
  items: UserWallet[];
}
export interface NonPaginatedRespUser {
  items: User[];
}
export interface PhantomLogin {
  message: string;
  signature: string;
  address: string;
}
export interface PublicListNFTCollectionsResp {
  items: PublicNFTCollectionListItem[];
}
export interface RaffleResp {
  pending_prizes_count: number;
  user_deposits: RaffleUserDepositCount[];
  withdrawals_count: number;
}
export interface RaffleUserDepositCount {
  count: number;
  display_name: string;
  rank: number;
}
export interface TopClaimsFilters {
  species?: ("Chimp" | "Orangutan" | "Gorilla")[] | null;
  event_id?: number[] | null;
  location_id?: number[] | null;
}
export interface TopClaimsResp {
  items: TopClaimItem[];
}
export interface TopClaimsSort {
  by?: "total_amount_claimed" | "duration_hours" | "base_multiplier" | "ended_at";
  order?: "asc" | "desc";
}
export interface UpdateUserInfo {
  first_name?: string | null;
  last_name?: string | null;
  display_name?: string | null;
}
export interface WalletAuthMessage {
  message: string;
}
