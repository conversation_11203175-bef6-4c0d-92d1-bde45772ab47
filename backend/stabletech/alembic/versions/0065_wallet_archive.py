"""Wallet archive

Revision ID: 0065
Revises: 0064
Create Date: 2025-05-16 16:47:08.218479

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0065"
down_revision = "0064"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "wallet", sa.<PERSON>umn("is_archived", sa.<PERSON>(), server_default="false", nullable=False)
    )
    op.create_index(op.f("ix_wallet_is_archived"), "wallet", ["is_archived"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_wallet_is_archived"), table_name="wallet")
    op.drop_column("wallet", "is_archived")
    # ### end Alembic commands ###
