"use client";

import { Label } from "@/lib/components/form";
import { SelectCombobox } from "@/lib/components/select-combobox";
import { Spinner } from "@/lib/components/spinner";
import { useGetPublicHeistWardrobesQuery } from "@/lib/heist";
import { mergeImages } from "@/lib/utils/merge-images";
import { capitalize } from "@/lib/utils/string";
import { useQuery } from "@tanstack/react-query";
import { cx } from "class-variance-authority";
import Image from "next/image";
import { Fragment, useId, useState } from "react";

const INITIAL_SELECT_STATE = {
  background: "",
  clothing: "",
  fur: "",
  head: "",
  mouth: "",
  species: "",
};

const PIECES_KEYS = ["background", "fur", "clothing", "mouth", "head"] as const;

type SelectState = typeof INITIAL_SELECT_STATE;

export default function WardrobePage() {
  const id = useId();

  const wardrobesQuery = useGetPublicHeistWardrobesQuery();
  const wardrobeList = wardrobesQuery.data ?? [];

  const [selectState, setSelectState] = useState(INITIAL_SELECT_STATE);
  const selectStateSetter = (key: keyof SelectState) => (value: string) => {
    setSelectState((prev) => ({ ...prev, [key]: value }));
  };
  const wardrobe = wardrobeList.find(({ species }) => species === selectState.species);

  const mergedImageQuery = useQuery({
    queryKey: ["heistWardrobeMergeImages", selectState],
    queryFn: async () => {
      const species = encodeURIComponent(selectState.species);
      if (!species) return "";

      const pieces: string[] = [];
      for (const key of PIECES_KEYS) {
        let value = selectState[key];
        if (!value) {
          if (key === "fur") break;
          continue;
        }
        const name = encodeURIComponent(capitalize(key));
        value = encodeURIComponent(value);
        pieces.push(
          `https://d1emnl92bfxoe0.cloudfront.net/layer-assets/${species}/${name}/${value}.png`,
        );
      }

      if (pieces.length === 0) return "";

      return await mergeImages(pieces);
    },
    placeholderData: (prev: string | undefined) => prev,
    staleTime: Infinity,
  });

  return (
    <main className="grid max-w-5xl gap-6 px-2 py-4 sm:px-4 md:grid-cols-2">
      <div className="col-span-full flex items-center">
        <h1 className="text-2xl">Wardrobe</h1>
        <Spinner
          className={cx("ml-3", "invisible", wardrobesQuery.isLoading && "visible!")}
          size={30}
        />
      </div>
      <div className="mx-auto grid w-full max-w-md grid-cols-[auto_1fr] content-center items-center gap-5">
        <Label htmlFor={`species${id}`} className="text-right">
          Species
        </Label>
        <SelectCombobox
          options={wardrobeList.map(({ species }) => ({ label: species, value: species }))}
          placeholder=""
          value={selectState.species}
          onValueChange={(value: string) => {
            setSelectState({ ...INITIAL_SELECT_STATE, species: value });
          }}
          id={`species${id}`}
          emptyText="No species found."
          searchText="Search species..."
        />
        {PIECES_KEYS.map((key) => {
          const name = capitalize(key);
          const currentId = `${key}${id}`;
          return (
            <Fragment key={key}>
              <Label htmlFor={currentId} className="text-right">
                {name}
              </Label>
              <SelectCombobox
                key={key}
                options={wardrobe?.[key].map((value) => ({ label: value, value })) ?? []}
                placeholder=""
                value={selectState[key]}
                onValueChange={selectStateSetter(key)}
                id={currentId}
                emptyText={selectState.species ? `No ${key} found.` : "Select a species first."}
                searchText={`Search ${key}...`}
              />
            </Fragment>
          );
        })}
      </div>
      <div className="relative mx-auto w-full max-w-md">
        {mergedImageQuery.data ? (
          <Image
            alt="NFT image preview"
            className={cx(
              "aspect-square h-auto w-full rounded-lg transition",
              mergedImageQuery.isFetching ? "blur-xs" : "blur-0",
            )}
            src={mergedImageQuery.data}
            width={0}
            height={0}
            sizes="100vw"
            referrerPolicy="no-referrer"
            unoptimized
          />
        ) : (
          <div className="grid aspect-square h-auto w-full place-items-center rounded-lg bg-muted p-6">
            <p className="text-center text-lg text-muted-foreground/50">
              Select species and others to preview the NFT.
            </p>
          </div>
        )}
        <div
          className={cx(
            "absolute inset-0 grid place-items-center rounded-lg transition",
            mergedImageQuery.isFetching ? "bg-muted/25" : "bg-muted/0",
          )}
        >
          <Spinner
            className={cx(
              "transition",
              mergedImageQuery.isFetching ? "opacity-100" : "opacity-0",
            )}
            size={56}
          />
        </div>
      </div>
    </main>
  );
}
