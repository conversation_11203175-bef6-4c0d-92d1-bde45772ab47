import json
import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Any

import httpx
import mmh3
import sqlalchemy.exc
from tenacity import (
    Retrying,
    before_sleep_log,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential_jitter,
)

from common.psql import SessionLocal
from lootloot.external.heist import HeistClient
from lootloot.heist.crud import HeistDeedClaimManager, RawHeistDeedClaimManager
from lootloot.nfts.crud import NFTManager

logger = logging.getLogger(__name__)

default_retry = Retrying(
    retry=retry_if_exception_type((httpx.HTTPError, sqlalchemy.exc.OperationalError)),
    wait=wait_exponential_jitter(max=600),
    stop=stop_after_attempt(60),
    before_sleep=before_sleep_log(logger, logging.INFO),
)

LOG_PERIOD = 60
REQUEST_MIN_TIME = 0.2  # throttle 5 requests per second


def consume_deeds_history(client: HeistClient):
    logger.info("Consuming heist land deeds history")

    with SessionLocal.begin() as db:
        addresses = [
            uri.removeprefix("solana:")
            for uri in NFTManager(db).get_address_uris_from_collection("the_heist_land")
        ]
    logger.info("Got %d nft addresses to consume...", len(addresses))

    start_time = time.time()
    done_count = 0
    prev_log_time = start_time
    prev_log_done_count = 0
    request_start_time = 0

    for address in addresses:
        if log_consume_deeds_history_progress(
            len(addresses), done_count, prev_log_done_count, prev_log_time
        ):
            prev_log_time = time.time()
            prev_log_done_count = done_count

        # Throttle by enforcing min time between requests
        if (sleep_time := request_start_time + REQUEST_MIN_TIME - time.time()) > 0:
            time.sleep(sleep_time)

        request_start_time = time.time()
        raw_claims = raw_claims_from_address(client, address)
        if raw_claims:
            persist_raw_claims(raw_claims)

        done_count += 1

    logger.info(
        "Finished consuming deeds history of %d nft addresses in %s",
        done_count,
        timedelta(seconds=time.time() - start_time),
    )


@default_retry.wraps
def persist_raw_claims(raw_claims: list[dict[str, Any]]) -> None:
    with SessionLocal.begin() as db:
        RawHeistDeedClaimManager(db).create_if_not_exists(raw_claims)


def raw_claims_from_address(client: HeistClient, address: str) -> list[dict[str, Any]]:
    requested_at = datetime.now(timezone.utc)
    resp = client.get_deed_history_raw(address)
    deed_history = resp.json()

    raw_claims: list[dict[str, Any]] = []

    if not isinstance(deed_history, list):
        logger.error(
            "Unexpected deed history type",
            extra={"address": address, "data": deed_history, "type": type(deed_history)},
        )
        return raw_claims

    for sublist in deed_history:
        if not isinstance(sublist, list):
            logger.error(
                "Unexpected deed history sublist type",
                extra={"address": address, "data": sublist, "type": type(sublist)},
            )
            continue

        for claim in sublist:
            if not isinstance(claim, dict):
                logger.error(
                    "Unexpected deed history claim type",
                    extra={"address": address, "data": claim, "type": type(claim)},
                )
                continue

            data_hash = calc_hash(claim)
            raw_claims.append(
                {
                    "address": address,
                    "data_hash": data_hash,
                    "requested_at": requested_at,
                    "data": claim,
                }
            )

    return raw_claims


def calc_hash(claim: dict) -> bytes:
    """Calculate a hash of the claim data.

    We cannot use the whole claim data as it contains things that will change over
    time, like `wallet.lastLogin`, `wallet.heistsCompleted` and others.

    We don't include the NFT address (`deed.id`) as the unique constraint should be
    `(NFT address, hash)`. So this hash will be unique by NFT only.
    """
    kind = claim.get("type")
    total_amount = claim.get("totalAmount")
    created_at = claim.get("createdAt")

    wallet = claim.get("wallet")
    wallet_id = wallet.get("id") if isinstance(wallet, dict) else None

    return mmh3.hash_bytes(
        json.dumps(
            (kind, created_at, total_amount, wallet_id),
            check_circular=False,
            separators=(",", ":"),
            sort_keys=True,
        )
    )


def log_consume_deeds_history_progress(
    total: int,
    count: int,
    prev_count: int,
    prev_time: float,
) -> bool:
    elapsed = time.time() - prev_time
    if elapsed < LOG_PERIOD:
        return False

    done_in_period = count - prev_count
    speed_per_minute = done_in_period * 60 / elapsed
    remaining_count = total - count
    remaining_minutes = remaining_count / speed_per_minute
    logger.info(
        "Consumed %d of %d nft addresses, processing %.1f addresses per minute, estimated %.1f minutes remaining",
        count,
        total,
        speed_per_minute,
        remaining_minutes,
    )
    return True


def process_raw_deed_claims() -> None:
    logger.info("Processing raw deed claims...")
    processed_count = 0
    log_time = time.time()
    while True:
        batch_size = process_batch_raw_deed_claims()
        if batch_size == 0:
            break
        processed_count += batch_size
        if time.time() - log_time > LOG_PERIOD:
            logger.info("%d processed...", processed_count)
            log_time = time.time()
    logger.info("Finished processing a total of %d raw deed claims", processed_count)


@default_retry.wraps
def process_batch_raw_deed_claims() -> int:
    with SessionLocal.begin() as db:
        claims_to_process = RawHeistDeedClaimManager(db).get_unprocessed(limit=200)
        claim_manager = HeistDeedClaimManager(db)
        for raw_claim in claims_to_process:
            claim_manager.process_raw(raw_claim)
        return len(claims_to_process)


def main():
    # Silence httpx logs as we're going to do a lot of requests
    httpx_logger = logging.getLogger("httpx")
    httpx_logger.setLevel(logging.WARNING)

    with HeistClient(retry=default_retry) as client:
        client.authenticate_with_keypairs_file()
        consume_deeds_history(client)

    process_raw_deed_claims()


main()
