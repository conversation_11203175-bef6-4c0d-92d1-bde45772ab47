// ** React Imports
import { ReactNode } from "react";

// ** MUI Imports
import CssBaseline from "@mui/material/CssBaseline";
import GlobalStyles from "@mui/material/GlobalStyles";
import { Theme, ThemeProvider, createTheme, responsiveFontSizes } from "@mui/material/styles";
import { deepmerge } from "@mui/utils";

// ** Type Imports
import { Settings } from "@/@core/context/settingsContext";

// ** Theme Config
import themeConfig from "@/configs/themeConfig";

// ** Direction component for LTR or RTL
import Direction from "@/layouts/components/Direction";

// ** Theme Override Imports
import overrides from "@/@core/theme/overrides";
import typography from "@/@core/theme/typography";

// ** Theme
import themeOptions from "@/@core/theme/ThemeOptions";
import UserThemeOptions from "@/layouts/UserThemeOptions";

// ** Global Styles
import GlobalStyling from "@/@core/theme/globalStyles";

interface Props {
  settings: Settings;
  children: ReactNode;
}

// ** Deep Merge Component overrides of core and user
const mergeComponentOverrides = (theme: Theme, settings: Settings) =>
  deepmerge({ ...overrides(theme, settings) }, UserThemeOptions()?.components);

// ** Deep Merge Typography of core and user
const mergeTypography = (theme: Theme) =>
  deepmerge(typography(theme), UserThemeOptions()?.typography);

const ThemeComponent = (props: Props) => {
  // ** Props
  const { settings, children } = props;

  // ** Merged ThemeOptions of Core and User
  const coreThemeConfig = themeOptions(settings);

  // ** Pass ThemeOptions to CreateTheme Function to create partial theme without component overrides
  let theme = createTheme(coreThemeConfig);

  // ** Continue theme creation and pass merged component overrides to CreateTheme function
  theme = createTheme(theme, {
    components: { ...mergeComponentOverrides(theme, settings) },
    typography: { ...mergeTypography(theme) },
  });

  // ** Set responsive font sizes to true
  if (themeConfig.responsiveFontSizes) {
    theme = responsiveFontSizes(theme);
  }

  return (
    <ThemeProvider theme={theme}>
      <Direction direction={settings.direction}>
        <CssBaseline />
        <GlobalStyles styles={() => GlobalStyling(theme, settings)} />
        {children}
      </Direction>
    </ThemeProvider>
  );
};

export default ThemeComponent;
