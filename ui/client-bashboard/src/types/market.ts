import { Coin, User } from "@/types/schemas";
import { createUserWithEmailAndPassword, signInWithEmailAndPassword } from "firebase/auth";

export interface AuthValuesType {
  user: User | null;
  loading: boolean;
  logout: () => void | Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithEmailAndPassword: typeof signInWithEmailAndPassword;
  createUserWithEmailAndPassword: typeof createUserWithEmailAndPassword;
}

export const coinFmt = (coin: Coin) => {
  return `${coin.name} (${coin.ticker.toUpperCase()})`;
};
