from datetime import datetime
from decimal import Decimal

from common.conf import settings
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Transaction
from stabletech.utils.pydantic import BaseSchema


class InternalAccount(BaseSchema):
    address: str
    coin_uid: str | None = None
    is_pool_token: bool = False


class InternalTransfer(BaseSchema):
    amount: Decimal
    asset_account: InternalAccount
    from_account: InternalAccount
    to_account: InternalAccount


class InternalTransaction(BaseSchema):
    action: TransactionAction
    internal_id: str
    comment: str | None
    confirmed_at: datetime
    fees_paid: Decimal
    is_collateral: bool
    is_deposit: bool | None
    network_name: str
    transfers: list[InternalTransfer]
    tx_hash: str
    wallet_address: str
    wallet_name: str

    @classmethod
    def from_transaction(
        cls,
        transaction: Transaction,
    ):
        """Removes IDs specific to this environment before sending it across."""
        transaction_data = {
            "action": transaction.action,
            "internal_id": f"{settings.env_name}:{transaction.id}",
            "comment": transaction.comment,
            "confirmed_at": transaction.confirmed_at,
            "is_collateral": transaction.is_collateral,
            "is_deposit": transaction.is_deposit,
            "fees_paid": transaction.fees_paid or Decimal(0),
            "network_name": transaction.network.name,
            "transfers": [
                {
                    "amount": transfer.amount,
                    "asset_account": {
                        "address": transfer.asset_account.address,
                        "coin_uid": (
                            transfer.asset_account.alias_coin.uid
                            if transfer.asset_account.alias_coin
                            else None
                        ),
                        # OR condition to support transactions created before is_pool_token was added
                        "is_pool_token": transfer.asset_account.is_pool_token
                        or not transfer.asset_account.coin_id,
                    },
                    "from_account": {
                        "address": transfer.from_account.address,
                        "is_pool_token": transfer.from_account.is_pool_token,
                    },
                    "to_account": {
                        "address": transfer.to_account.address,
                        "is_pool_token": transfer.to_account.is_pool_token,
                    },
                }
                for transfer in transaction.transfers
            ],
            "tx_hash": transaction.tx_hash,
            "wallet_address": transaction.wallet.address,
            "wallet_name": transaction.wallet.name,
        }
        return cls.model_validate(transaction_data)
