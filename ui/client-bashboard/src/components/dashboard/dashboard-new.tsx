import { useDashboardQuery, getDashboardTransactions, isSahabaDashboard } from "@/api/dashboard";
import { useUsersQuery } from "@/api/users";
import { TransactionForClient } from "@/types/schemas";
import { PortfolioOverviewNew } from "@/components/dashboard/PortfolioOverviewNew";
import { PortfolioSummaryNew } from "@/components/dashboard/PortfolioSummaryNew";
import { PortfolioLineChartSahaba } from "@/components/dashboard/sahaba/PortfolioLineChartSahaba";
import { PortfolioSummarySahaba } from "@/components/dashboard/sahaba/PortfolioSummarySahaba";
import { TransactionsSahaba } from "@/components/dashboard/sahaba/TransactionsSahaba";
import { TransactionsNew } from "@/components/dashboard/TransactionsNew";
import { Alert } from "@/components/ui/alert";
import { Card } from "@/components/ui/card";
import { SelectCombobox } from "@/components/ui/select-combobox";
import { Spinner } from "@/components/ui/spinner";
import { hasEarnProduct, isSahaba } from "@/context/env";
import {
  DashboardRow,
  processDashboard,
  useAverageCost,
} from "@/helpers/portfolio-calculations";
import { useTransactionsFiltering } from "@/helpers/transactions-filtering";
import { useAppSelector } from "@/hooks/store";
import { AutoLayout } from "@/layouts/auto";
import { Container } from "@/layouts/NewLayout";
import { selectUser } from "@/store/apps/auth";
import { getErrorDetail } from "@/store/services/helpers";
import { User } from "@/types/schemas";
import { ReactElement, useCallback, useMemo, useRef, useState } from "react";

const EMPTY_USERS = [] as const;

export function DashboardNew() {
  const currentUser = useAppSelector(selectUser);
  const adminEnabled = currentUser?.role === "admin";
  const usersQuery = useUsersQuery({ enabled: adminEnabled });
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  const users = usersQuery.data ?? EMPTY_USERS;

  const displayUser = useMemo(() => {
    const userFound = selectedUserId ? users.find((user) => user.id === selectedUserId) : null;
    if (userFound) return userFound;
    if (users.length > 0) return users[0];
    return currentUser;
  }, [currentUser, selectedUserId, users]);

  const adminUserOptions = useMemo(
    () =>
      users.map((user) => {
        const name = [user.firstName, user.lastName].filter(Boolean).join(" ");
        return {
          value: user.id.toString(),
          label: `${name || "<no name>"} - ${user.email}`,
        };
      }),
    [users],
  );

  if (!displayUser || (adminEnabled && usersQuery.isLoading)) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Spinner className="size-10 text-primary" />
      </div>
    );
  }

  return (
    <Container className="mb-8 flex flex-col gap-4">
      {adminEnabled && (
        <Card
          variant="outlined"
          className="flex flex-col items-start gap-2 sm:flex-row sm:items-center"
        >
          <h2>User dashboard</h2>
          <div className="w-full *:w-full sm:w-sm">
            <SelectCombobox
              options={adminUserOptions}
              placeholder="Select user"
              value={displayUser.id.toString()}
              onValueChange={(value) => {
                if (!value) return;
                setSelectedUserId(Number.parseInt(value));
              }}
              emptyText="No users found"
              searchText="Search users..."
            />
          </div>
        </Card>
      )}
      <DashboardContent selectedUser={displayUser} />
    </Container>
  );
}

interface DashboardContentProps {
  selectedUser: User;
}

function DashboardContent({ selectedUser }: DashboardContentProps) {
  const query = useDashboardQuery(selectedUser);
  const dashboard = query.data;
  const transactionsRef = useRef<HTMLDivElement>(null);

  // Create a normalized dashboard object for the filtering hook
  const normalizedDashboard = useMemo(() => {
    if (!dashboard) return null;
    if (isSahabaDashboard(dashboard)) {
      // For sahaba, convert client transactions to TransactionForClient format
      const convertedTransactions = getDashboardTransactions(dashboard);
      return {
        coins: dashboard.coins,
        transactions: convertedTransactions,
      };
    }
    return dashboard;
  }, [dashboard]);

  const transactionsFiltering = useTransactionsFiltering(normalizedDashboard);

  // For sahaba, we need to work with TransactionForClient directly
  const sahabaTransactions = useMemo(() => {
    if (dashboard && isSahabaDashboard(dashboard)) {
      return getDashboardTransactions(dashboard);
    }
    return [];
  }, [dashboard]);

  const averageCost = useAverageCost(
    dashboard && isSahabaDashboard(dashboard)
      ? sahabaTransactions
      : (transactionsFiltering.transactions as TransactionForClient[]),
    transactionsFiltering.selectedCoinId,
  );

  const showInterest = hasEarnProduct && selectedUser.earnsInterest;
  const processedData = useMemo(() => {
    if (!normalizedDashboard) return null;
    const interestRate = showInterest ? Number.parseFloat(selectedUser.interestRate) : 0;
    return processDashboard(normalizedDashboard, interestRate);
  }, [normalizedDashboard, selectedUser.interestRate, showInterest]);

  const { handleSelectCoin, selectedCoinId } = transactionsFiltering;

  const handleRowClick = useCallback(
    (row: DashboardRow): void => {
      handleSelectCoin(row);
      if (row.id !== selectedCoinId && transactionsRef.current) {
        transactionsRef.current.scrollIntoView({ behavior: "smooth" });
      }
    },
    [handleSelectCoin, selectedCoinId],
  );

  if (!processedData) {
    return (
      <div className="flex h-64 items-center justify-center">
        {query.isFetching ? (
          <Spinner className="size-10 text-primary" />
        ) : (
          <Alert variant="error">
            {getErrorDetail(query.error, { fallback: "Something went wrong unexpectedly" })}
          </Alert>
        )}
      </div>
    );
  }

  if (isSahaba) {
    return (
      <>
        <PortfolioSummarySahaba
          portfolioDollars={processedData.portfolioDollars}
          interestEarnedDollars={processedData.lifetimeInterestEarned}
          showInterest={showInterest}
        />

        <PortfolioLineChartSahaba transactions={sahabaTransactions} />

        <div ref={transactionsRef}>
          <TransactionsSahaba
            transactions={sahabaTransactions}
            selectedCoin={transactionsFiltering.selectedCoin as any}
            averageCost={averageCost}
            onClearSelectedCoin={transactionsFiltering.clearSelectedCoin}
          />
        </div>
      </>
    );
  }
  return (
    <>
      <PortfolioSummaryNew
        portfolioDollars={processedData.portfolioDollars}
        interestEarnedDollars={processedData.interestEarnedDollars}
        showInterest={showInterest}
      />

      <PortfolioOverviewNew
        rows={processedData.rows}
        showInterest={showInterest}
        selectedRowId={selectedCoinId}
        onRowClick={handleRowClick}
      />

      <div ref={transactionsRef}>
        <TransactionsNew
          transactions={transactionsFiltering.transactions as TransactionForClient[]}
          selectedCoin={transactionsFiltering.selectedCoin as any}
          averageCost={averageCost}
          onClearSelectedCoin={transactionsFiltering.clearSelectedCoin}
        />
      </div>
    </>
  );
}

DashboardNew.acl = {
  action: "read",
  subject: "Position",
};

DashboardNew.getLayout = function getLayout(page: ReactElement) {
  return <AutoLayout>{page}</AutoLayout>;
};

DashboardNew.getSpinner = function getSpinner() {
  return (
    <AutoLayout>
      <div className="flex h-64 items-center justify-center">
        <Spinner className="size-10 text-primary" />
      </div>
    </AutoLayout>
  );
};
