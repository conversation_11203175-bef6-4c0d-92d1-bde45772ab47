variable "name" {
  description = "Kubernetes name to use for the resource"
  type        = string
  nullable    = false
}

variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "config_maps" {
  description = "ConfigMap names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "secrets" {
  description = "Secret names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "host_volumes" {
  description = "Host paths to mount into containers, for development only"
  type = list(object({
    name       = string
    host_path  = string
    mount_path = string
  }))
  default  = []
  nullable = false
}

variable "subql_runner_image" {
  description = "subql-runner fully qualified image name"
  type        = string
  nullable    = false
}

variable "subql_command" {
  description = "Command for the subql-runner"
  type        = string
  nullable    = false
}
