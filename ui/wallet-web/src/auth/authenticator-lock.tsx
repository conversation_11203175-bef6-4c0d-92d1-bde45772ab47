import { PasswordContext, useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import {
  createWrapKeyAuthOptions,
  RequestOptions,
  useAddCredentialFlow,
  useListCredentials,
  verifyCredential,
} from "@/lib/auth";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import {
  credentialWrapAll,
  getCredentialRegistered,
  registerCredential,
} from "@/lib/secret-store";
import { formatDate } from "@/lib/utils/date";
import { base64URLStringToBuffer, startAuthentication } from "@simplewebauthn/browser";
import { useMutation } from "@tanstack/react-query";
import { ArrowRight } from "lucide-react";
import { use, useState } from "react";

interface AddAuthenticatorLockProps {
  onDone: () => void;
  onEditLock: () => void;
}

export function AddAuthenticatorLock({ onDone, onEditLock }: AddAuthenticatorLockProps) {
  const getPassword = useGetPassword();
  const addCredentialFlow = useAddCredentialFlow();
  const listCredentials = useListCredentials();

  const addWrapKeyCredential = useMutation({
    async mutationFn() {
      if (await getCredentialRegistered()) {
        throw new Error("Another wallet authenticator is already registered");
      }
      const credentialId = await addCredentialFlow("wrap-key");
      const registered = await registerCredential(credentialId);
      if (!registered) {
        throw new Error("Another wallet authenticator got registered in the meantime");
      }
      return credentialId;
    },
  });

  const activateCredential = useMutation({
    async mutationFn(credentialId: string) {
      const registered = await registerCredential(credentialId);
      if (!registered) {
        throw new Error("Another wallet authenticator got registered in the meantime");
      }
      return credentialId;
    },
  });

  const protectAll = useMutation({
    async mutationFn(
      params:
        | { credentialId: string; password: CryptoKey; formData?: never }
        | { credentialId: string; formData: FormData; password?: never },
    ) {
      const { credentialId, formData } = params;
      const password = formData
        ? await getPassword(formData, { testPassword: true })
        : params.password;
      await wrapAll({ credentialId, password });
    },
  });

  const [showProtectAllForm, setShowProtectAllForm] = useState(false);

  if (protectAll.isSuccess) {
    return (
      <section className="grid gap-4 *:min-w-0">
        <h2 className="text-lg">Wallet authenticator protection</h2>
        <p>All your current wallets are now protected with your authenticator.</p>
        <Button variant="primary" onClick={onDone}>
          OK
        </Button>
      </section>
    );
  }

  const activatedCredentialId = addWrapKeyCredential.data ?? activateCredential.data;

  if (activatedCredentialId) {
    const { password } = use(PasswordContext);

    return (
      <section className="grid gap-4 *:min-w-0">
        <h2 className="text-lg">Wallet authenticator activated</h2>
        {showProtectAllForm ? (
          <>
            <p>Enter your password to protect all local wallets with your authenticator.</p>
            <form
              className="grid gap-4 *:min-w-0"
              onSubmit={(e) => {
                e.preventDefault();
                protectAll.mutate({
                  credentialId: activatedCredentialId,
                  formData: new FormData(e.currentTarget),
                });
              }}
            >
              <PasswordInput disabled={protectAll.isPending} />
              <div className="grid grid-cols-2 gap-4">
                <Button
                  type="button"
                  disabled={protectAll.isPending}
                  variant="secondary"
                  onClick={() => {
                    setShowProtectAllForm(false);
                  }}
                >
                  Back
                </Button>
                <Button type="submit" disabled={protectAll.isPending}>
                  Proceed
                </Button>
              </div>
            </form>
          </>
        ) : (
          <>
            <p>The authenticator has been activated on this device.</p>
            <Button
              variant="outline"
              disabled={protectAll.isPending}
              onClick={() => {
                if (password) {
                  protectAll.mutate({ credentialId: activatedCredentialId, password });
                } else {
                  setShowProtectAllForm(true);
                }
              }}
            >
              Protect all local wallets
            </Button>
            <Button variant="outline" disabled={protectAll.isPending} onClick={onEditLock}>
              Select wallets to protect
            </Button>
            <Button variant="outline" disabled={protectAll.isPending} onClick={onDone}>
              Done for now
            </Button>
          </>
        )}
        {protectAll.isError && (
          <AlertError onClear={protectAll.reset}>
            {getErrorMessage(protectAll.error)}
          </AlertError>
        )}
      </section>
    );
  }

  const credentialList = listCredentials.data?.filter((c) => c.use_for === "wrap-key") ?? [];

  return (
    <section className="grid gap-4 *:min-w-0">
      <h2 className="text-lg">Activate wallet authenticator</h2>
      <p>
        Select an authenticator or add a new one. After this step, you will be able to use the
        authenticator to protect your wallets.
      </p>
      {addWrapKeyCredential.isError && (
        <AlertError onClear={addWrapKeyCredential.reset}>
          {getErrorMessage(addWrapKeyCredential.error)}
        </AlertError>
      )}
      {activateCredential.isError && (
        <AlertError onClear={activateCredential.reset}>
          {getErrorMessage(activateCredential.error)}
        </AlertError>
      )}
      {listCredentials.isSuccess ? (
        credentialList.length === 0 ? (
          <div className="place-items-center">
            <p className="text-sm text-foreground/70">
              No wallet authenticator found, try adding a new one.
            </p>
          </div>
        ) : (
          credentialList.map((credential) => (
            <button
              key={credential.id}
              className="group grid grid-cols-[1fr_auto] items-center gap-1 rounded border px-4 py-2 text-left *:min-w-0 hover:bg-accent/50"
              onClick={() => {
                activateCredential.mutate(credential.id);
              }}
            >
              <div className="grid gap-0.5 *:min-w-0">
                <span>{credential.name || "Unnamed"}</span>
                <span className="text-sm opacity-80">
                  Created at {formatDate(credential.created_at)}
                </span>
                <span className="text-sm opacity-80">
                  Latest use at {formatDate(credential.latest_use_at)}
                </span>
              </div>
              <span className="rounded-full bg-primary p-1 text-primary-foreground group-hover:bg-primary/90">
                <ArrowRight size={24} />
              </span>
            </button>
          ))
        )
      ) : listCredentials.isError ? (
        <AlertError>
          Error listing credentials: {getErrorMessage(listCredentials.error)}
        </AlertError>
      ) : (
        <div className="place-items-center">
          <Spinner size={32} />
        </div>
      )}
      <div className="mt-2 grid grid-cols-2 gap-4">
        <Button
          disabled={addWrapKeyCredential.isPending || activateCredential.isPending}
          variant="secondary"
          onClick={onDone}
        >
          Back
        </Button>
        <Button
          disabled={addWrapKeyCredential.isPending || activateCredential.isPending}
          onClick={() => {
            addWrapKeyCredential.mutate();
          }}
        >
          Add new
        </Button>
      </div>
    </section>
  );
}

interface LockActivationParams {
  credentialId: string;
  password: CryptoKey;
}

async function wrapAll({ credentialId, password }: LockActivationParams) {
  const optionsResp = await createWrapKeyAuthOptions(credentialId);
  const authResp = await startAuthentication({
    optionsJSON: optionsResp.auth_options as RequestOptions,
  });
  const verificationResp = await verifyCredential(authResp);
  if (verificationResp.kind !== "wrap-key") {
    throw new Error("Unexpected authenticator verification response");
  }

  const secretKey = base64URLStringToBuffer(verificationResp.secret_key);
  await credentialWrapAll(credentialId, new Uint8Array(secretKey), password);
}
