import { Settings } from "@/@core/context/settingsContext";
import React, { ReactNode } from "react";

export type Layout = "vertical" | "horizontal" | "blank" | "blankWithAppBar";

export type Skin = "default" | "bordered" | "semi-dark";

export type ContentWidth = "full" | "boxed";

export type AppBar = "fixed" | "static" | "hidden";

export type Footer = "fixed" | "static" | "hidden";

export type ThemeColor = "primary" | "secondary" | "error" | "warning" | "info" | "success";

export type VerticalNavToggle = "accordion" | "collapse";

export type HorizontalMenuToggle = "hover" | "click";

export interface NavLink {
  icon?: React.ComponentType;
  path?: string;
  title: string;
  action?: string;
  subject?: string;
  disabled?: boolean;
  badgeContent?: string;
  externalLink?: boolean;
  openInNewTab?: boolean;
  badgeColor?: "default" | "primary" | "secondary" | "success" | "error" | "warning" | "info";
}

export interface NavGroup {
  icon?: React.ComponentType;
  title: string;
  action?: string;
  subject?: string;
  badgeContent?: string;
  children?: (NavGroup | NavLink)[];
  badgeColor?: "default" | "primary" | "secondary" | "success" | "error" | "warning" | "info";
}

export interface NavSectionTitle {
  action?: string;
  subject?: string;
  sectionTitle: string;
}

export type VerticalNavItemsType = (NavLink | NavGroup | NavSectionTitle)[];
export type HorizontalNavItemsType = (NavLink | NavGroup)[];

export interface LayoutProps {
  hidden?: boolean;
  settings: Settings;
  children?: ReactNode;
  menuLockedIcon?: ReactNode;
  menuUnlockedIcon?: ReactNode;
  verticalNavItems?: VerticalNavItemsType;
  scrollToTop?: (props?: LayoutProps) => ReactNode;
  saveSettings: (values: Settings) => void;
  footerContent?: (props?: LayoutProps) => ReactNode;
  horizontalNavItems?: HorizontalNavItemsType;
  toggleNavVisibility?: () => void;
  verticalAppBarContent?: (props?: LayoutProps) => ReactNode;
  verticalNavMenuContent?: (props?: LayoutProps) => ReactNode;
  verticalNavMenuBranding?: (props?: LayoutProps) => ReactNode;
  horizontalAppBarContent?: (props?: LayoutProps) => ReactNode;
  horizontalAppBarBranding?: (props?: LayoutProps) => ReactNode;
  horizontalNavMenuContent?: (props?: LayoutProps) => ReactNode;
  afterVerticalNavMenuContent?: (props?: LayoutProps) => ReactNode;
  beforeVerticalNavMenuContent?: (props?: LayoutProps) => ReactNode;
}

export interface BlankLayoutProps {
  children: ReactNode;
}

export interface BlankLayoutWithAppBarProps {
  children: ReactNode;
}
