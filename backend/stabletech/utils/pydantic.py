from datetime import datetime

from pydantic import BaseModel as CoreBaseModel
from pydantic import ConfigDict


def convert_datetime_to_iso_8601_with_z_suffix(dt: datetime) -> str:
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


def to_camel(snake_case: str) -> str:
    string_split = snake_case.split("_")
    camel_case = string_split[0] + "".join(word.capitalize() for word in string_split[1:])
    return camel_case


class BaseSchema(CoreBaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
        json_encoders={
            # Custom output conversion for datetime
            # TODO[pydantic]: The following keys were removed: `json_encoders`.
            # Check https://docs.pydantic.dev/dev-v2/migration/#changes-to-config for more information.
            datetime: convert_datetime_to_iso_8601_with_z_suffix
        },
    )
