import Spinner from "@/@core/components/spinner";
import { CoinTotalsCard } from "@/components/admin/CoinTotalsCard";
import FilteredByCoinDisplay from "@/components/admin/FilteredByCoinDisplay";
import TransactionsTable, {
  transfersMapperCombined,
} from "@/components/admin/TransactionsTable";
import {
  processTransactionsOverview,
  simpleCostBasis,
  useSimpleAverageCost,
} from "@/helpers/transactions";
import { useTransactionsFiltering } from "@/helpers/transactions-filtering";
import { useIndexerIncomeQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Alert, Card, CardContent, CardHeader, Grid, Stack } from "@mui/material";
import { useMemo } from "react";

export default function IncomePage() {
  const { data: rawData, error } = useIndexerIncomeQuery();

  const data = useMemo(
    () => (rawData != null ? processTransactionsOverview(rawData) : null),
    [rawData],
  );

  const transactionsFiltering = useTransactionsFiltering(data);
  const averageCost = useSimpleAverageCost(
    transactionsFiltering.selectedCoinId,
    transactionsFiltering.transactions,
  );

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed loading income page" })}
      </Alert>
    );
  }

  if (data == null) {
    return <Spinner />;
  }

  return (
    <Grid container gap={6}>
      <Grid item xs={12}>
        <CoinTotalsCard
          title="Income Overview"
          rows={data.coinTotals}
          onRowClick={transactionsFiltering.handleSelectCoin}
        />
      </Grid>
      <Grid item xs={12}>
        <Card>
          <CardHeader
            title={
              <Stack direction="row" gap={4} alignItems="flex-end">
                <span>Transactions</span>
                <FilteredByCoinDisplay
                  selectedCoin={transactionsFiltering.selectedCoin}
                  averageCost={averageCost}
                  onClearSelectedCoin={transactionsFiltering.clearSelectedCoin}
                />
              </Stack>
            }
          />
          <CardContent>
            <TransactionsTable
              rows={transactionsFiltering.transactions}
              transfersMapper={transfersMapperCombined}
              costBasisCalculator={simpleCostBasis}
              pagination={{
                pageSize: 50,
              }}
            />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
