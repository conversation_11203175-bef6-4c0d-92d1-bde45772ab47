import { apiMutation, apiQuery } from "@/api/api";
import {
  AddClientTransaction,
  AddWallet,
  ClientTransaction,
  EditWallet,
  Network,
  Wallet,
} from "@/types/schemas";
import {
  useMutation,
  UseMutationResult,
  useQuery,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";

export function useWalletsQuery(networkId?: number): UseQueryResult<Wallet[]> {
  return useQuery({
    queryKey: ["client", "wallets", { network_id: networkId }],
    queryFn: apiQuery<Wallet[]>,
  });
}

export function useAddWalletMutation(): UseMutationResult<Wallet, Error, AddWallet> {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (wallet: AddWallet) =>
      apiMutation<Wallet>({
        method: "POST",
        resource: "client/wallet/add",
        body: wallet,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client", "wallets"] });
    },
  });
}

export function useEditWalletMutation(): UseMutationResult<Wallet, Error, EditWallet> {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (wallet: EditWallet) =>
      apiMutation<Wallet>({
        method: "POST",
        resource: "client/wallet/edit",
        body: wallet,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client", "wallets"] });
    },
  });
}

export function useArchiveWalletMutation(): UseMutationResult<Wallet, Error, number> {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (walletId: number) =>
      apiMutation<Wallet>({
        method: "POST",
        resource: `client/wallet/archive?wallet_id=${walletId}`,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client", "wallets"] });
    },
  });
}

export function useNetworksQuery(): UseQueryResult<Network[]> {
  return useQuery({
    queryKey: ["client", "networks"],
    queryFn: apiQuery<Network[]>,
  });
}

export function usePendingTransactionsQuery(): UseQueryResult<ClientTransaction[]> {
  return useQuery({
    queryKey: ["client", "client-transactions", "pending"],
    queryFn: apiQuery<ClientTransaction[]>,
  });
}

export function useAddTransactionMutation(): UseMutationResult<
  ClientTransaction,
  Error,
  AddClientTransaction
> {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (transaction: AddClientTransaction) =>
      apiMutation<ClientTransaction>({
        method: "POST",
        resource: "client/client-transaction/add",
        body: transaction,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["client", "client-transactions", "pending"] });
    },
  });
}

export function useDepositAddressQuery(networkId?: number): UseQueryResult<string> {
  return useQuery({
    queryKey: ["client", "deposit-address", { network_id: networkId }],
    queryFn: apiQuery<string>,
    enabled: Boolean(networkId),
  });
}
