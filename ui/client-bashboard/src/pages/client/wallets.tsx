import { DataGridTable, TableColumns } from "@/components/DataGridTable";
import { NetworksSelect } from "@/components/NetworksSelect";
import { SnackSuccess } from "@/components/SnackSuccess";
import { CancelButton } from "@/components/button/CancelButton";
import { addressFormatter } from "@/helpers/formatters";
import {
  useClientNetworksQuery,
  useClientWalletAddMutation,
  useClientWalletEditMutation,
  useClientWalletsQuery,
  useIndexerNetworksQuery,
  useIndexerWalletAddMutation,
  useIndexerWalletEditMutation,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AddWallet, Network, User, Wallet } from "@/types/schemas";
import { CloseOutlined } from "@mui/icons-material";
import {
  Al<PERSON>,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  Chip,
  FormGroup,
  Grid,
  Slide,
  Stack,
  <PERSON>Field,
  Toolt<PERSON>,
  Typography,
} from "@mui/material";
import FormControlLabel from "@mui/material/FormControlLabel";
import { GridCellParams } from "@mui/x-data-grid-pro";
import { skipToken } from "@reduxjs/toolkit/query";
import React, { useState } from "react";

interface WalletsProps {
  isAdmin: boolean;
  selectedUser: User | null;
  walletsData?: readonly object[];
}

const emptySubmitData = {
  address: "",
  name: "",
  networkIds: [],
  isUsableByClients: null,
};

export const Wallets = (props: WalletsProps) => {
  const isAdminWithUser = props.isAdmin && !props.selectedUser;
  const tableColumns: TableColumns[] = [
    { field: "id", header: "ID" },
    { field: "name", header: "Name" },
    {
      field: "address",
      header: "Address",
      renderCell: (params: GridCellParams) => (
        <Tooltip title={params.value}>
          <Typography>{addressFormatter(params.value)}</Typography>
        </Tooltip>
      ),
    },
    {
      field: "networks",
      header: "Networks",
      sortable: false,
      renderCell: (params: GridCellParams) =>
        params.value.map((network: Network) => (
          <Box key={network.name} pl={2}>
            <Chip label={network.name} />
          </Box>
        )),
    },
    ...(isAdminWithUser
      ? [
          {
            field: "isUsableByClients",
            header: "Usable By Clients",
            type: "boolean",
          },
        ]
      : []),
  ];

  const [addWallet] = (
    props.isAdmin ? useIndexerWalletAddMutation : useClientWalletAddMutation
  )();
  const [editWallet] = (
    props.isAdmin ? useIndexerWalletEditMutation : useClientWalletEditMutation
  )();
  const { data: clientWalletsData } = useClientWalletsQuery(
    props.isAdmin ? skipToken : undefined,
  );

  const [selectedEditWallet, setSelectedEditWallet] = useState<Wallet | null>(null);
  const [openWalletAdd, setOpenWalletAdd] = useState(false);
  const [selectedNetworks, setSelectedNetworks] = useState<Network[]>([]);
  const [submitData, setSubmitData] = useState<AddWallet>(emptySubmitData);

  const [errorAlert, setErrorAlert] = useState<string | null>(null);
  const [showSuccessSnack, setShowSuccessSnack] = useState(false);

  const clearData = () => {
    setErrorAlert("");
    setSelectedEditWallet(null);
    setOpenWalletAdd(false);
    setSelectedNetworks([]);
    setSubmitData(emptySubmitData);
  };
  const dataAdded = () => {
    clearData();
    setShowSuccessSnack(true);
  };

  const cancelButton = (
    <React.Fragment>
      <CancelButton onClick={clearData} />
    </React.Fragment>
  );

  return (
    <Grid container spacing={6}>
      <Grid item xs>
        <Card>
          <CardContent>
            <SnackSuccess
              showSuccessSnack={showSuccessSnack}
              setShowSuccessSnack={setShowSuccessSnack}
            />

            <Grid container spacing={6}>
              <Grid item xs={4}>
                <Typography variant={"h4"}> Wallets </Typography>
              </Grid>

              <Grid item xs>
                <Stack direction={"row"} justifyContent={"end"} spacing={4}>
                  <Button
                    onClick={() => {
                      clearData();
                      setOpenWalletAdd(!openWalletAdd);
                    }}
                  >
                    + Wallet
                  </Button>
                </Stack>
              </Grid>

              <Grid item xs={12}>
                <DataGridTable
                  hideIdColumn
                  onRowClick={(gridCell) => {
                    const wallet = gridCell.row;
                    setSelectedNetworks(wallet.networks);
                    setSubmitData({
                      address: wallet.address,
                      name: wallet.name,
                      isUsableByClients: wallet.isUsableByClients,
                      networkIds: [],
                    });
                    setSelectedEditWallet(wallet);
                  }}
                  rows={props.walletsData || clientWalletsData || []}
                  sortByField={"name"}
                  tableColumns={tableColumns}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={5} hidden={!openWalletAdd && !selectedEditWallet}>
        <Grid container spacing={6}>
          <Slide
            direction={"left"}
            mountOnEnter
            unmountOnExit
            in={openWalletAdd || (!!selectedEditWallet && !!selectedNetworks)}
          >
            <Grid item xs>
              <Card>
                <CardContent>
                  <CardHeader
                    title={`${(selectedEditWallet && "Edit") || "Add"} a Wallet`}
                    action={
                      <Button color={"error"}>
                        <CloseOutlined
                          onClick={() => {
                            setOpenWalletAdd(false);
                            setSelectedEditWallet(null);
                          }}
                        />
                      </Button>
                    }
                  />
                  <Stack spacing={6}>
                    {errorAlert && <Alert severity={"error"}>{errorAlert}</Alert>}

                    <Stack direction={"row"} spacing={4}>
                      <NetworksSelect
                        getNetworksMutation={
                          props.isAdmin ? useIndexerNetworksQuery : useClientNetworksQuery
                        }
                        selectedNetworks={selectedNetworks}
                        setSelectedNetworks={setSelectedNetworks}
                      />

                      <Tooltip title={"Address cannot be changed once added"}>
                        <TextField
                          fullWidth
                          label={"Address"}
                          disabled={selectedEditWallet != null}
                          onChange={(event) => {
                            setSubmitData({ ...submitData, address: event.target.value });
                          }}
                          value={submitData.address}
                        />
                      </Tooltip>
                    </Stack>

                    <TextField
                      label={"Name"}
                      onChange={(event) => {
                        setSubmitData({ ...submitData, name: event.target.value });
                      }}
                      value={submitData.name}
                    />

                    {isAdminWithUser && (
                      <FormGroup>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={submitData.isUsableByClients || false}
                              onChange={(event) =>
                                setSubmitData({
                                  ...submitData,
                                  isUsableByClients: event.target.checked,
                                })
                              }
                            />
                          }
                          label="Is Usable By Clients"
                        />
                      </FormGroup>
                    )}

                    <Stack direction={"row"} spacing={4}>
                      <Button
                        disabled={
                          !submitData.address ||
                          !submitData.name ||
                          selectedNetworks.length === 0
                        }
                        color={"success"}
                        fullWidth={true}
                        variant={"outlined"}
                        onClick={() => {
                          const networkIds = selectedNetworks.map(
                            (network: Network) => network.id,
                          );
                          const call = selectedEditWallet
                            ? editWallet({
                                id: selectedEditWallet.id,
                                isUsableByClients: submitData.isUsableByClients,
                                name: submitData.name,
                                networkIds: networkIds,
                              })
                            : addWallet({ ...submitData, networkIds: networkIds });
                          call
                            .unwrap()
                            .then(dataAdded)
                            .catch((error) => setErrorAlert(getErrorDetail(error)));
                        }}
                      >
                        {(selectedEditWallet && "Save") || "Add"}
                      </Button>
                      {cancelButton}
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Slide>
        </Grid>
      </Grid>
    </Grid>
  );
};

Wallets.acl = {
  action: "read",
  subject: "Position",
};

export default Wallets;
