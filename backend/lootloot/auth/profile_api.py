from fastapi import APIRouter

from common.exceptions import HTTP400Exception
from lootloot import schemas
from lootloot.auth.fastapi import CurrentUser

router = APIRouter()


@router.post("", response_model=schemas.User)
def update_user_info_post(user: CurrentUser, update_user_info: schemas.UpdateUserInfo):
    for field in update_user_info.model_fields_set:
        value = getattr(update_user_info, field)
        match value:
            case None:
                continue
            case str():
                setattr(user, field, value.strip())
            case _:
                raise HTTP400Exception(f"Unexpected value for field '{field}': {value!r}")

    user.db.commit()
    return user
