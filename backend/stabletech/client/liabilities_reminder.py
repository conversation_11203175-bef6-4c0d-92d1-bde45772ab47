from datetime import datetime, timedelta, timezone

from common.psql import SessionLocal
from stabletech import schemas
from stabletech.client.crud import ClientTransactionManager
from stabletech.utils import slack


def get_client_name(liability: schemas.UserLiability):
    client_name = " ".join(
        name for name in (liability.user.first_name, liability.user.last_name) if name
    )
    if not client_name:
        if liability.user.email:
            client_name = liability.user.email
        else:
            client_name = f"<User id={liability.user.id}>"
    return client_name


db = SessionLocal()
liabilities = ClientTransactionManager(db).get_liabilities()

today = datetime.now(timezone.utc).date()
date_to_remind = today + timedelta(days=14)

liabilities_to_remind = [
    liability
    for liability in liabilities
    if liability.due_at.astimezone(timezone.utc).date() == date_to_remind
]

for liability in liabilities_to_remind:
    date_str = liability.due_at.astimezone(timezone.utc).date().strftime("%B %d, %Y")
    client_name = get_client_name(liability)
    slack.post_message(
        slack.stable_tech_channel(),
        f"{client_name} has an interest payment of $ {liability.total_usd_debt:.2f} due on {date_str}.",
    )

db.close()
