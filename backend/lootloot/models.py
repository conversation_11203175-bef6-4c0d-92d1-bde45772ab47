from datetime import datetime
from decimal import Decimal
from typing import Any

from sqlalchemy import (
    ColumnElement,
    DateTime,
    ForeignKey,
    Index,
    UniqueConstraint,
    func,
    literal,
)
from sqlalchemy.dialects.postgresql import BYTEA, JSON, JSONB, JSONPATH
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    MappedAsDataclass,
    foreign,
    mapped_column,
    relationship,
)

from common.models import BaseModel
from lootloot.auth.models import User
from lootloot.external.heist import HEIST_EVENTS
from lootloot.kinds import NetworkGroup


class Base(MappedAsDataclass, DeclarativeBase, BaseModel, kw_only=True):
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, init=False)


class UserWallet(Base):
    user_id: Mapped[int] = mapped_column(ForeignKey(User.id))
    user: Mapped[User] = relationship(init=False, repr=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), init=False, server_default=func.now()
    )
    name: Mapped[str]
    origin: Mapped[str]
    address: Mapped[str]
    network_group: Mapped[NetworkGroup]
    sequence_number: Mapped[int] = mapped_column(repr=False)

    @staticmethod
    def address_uri_sql() -> ColumnElement[str]:
        """SQL expression for address_uri"""
        return (
            UserWallet.network_group + literal(":", literal_execute=True) + UserWallet.address
        )

    @property
    def address_uri(self) -> str:
        return f"{self.network_group}:{self.address}"

    __table_args__ = (
        UniqueConstraint("user_id", "name"),
        UniqueConstraint("network_group", "address"),
    )


class NFTCollection(Base):
    name: Mapped[str]
    # Magic eden's collection uid
    uid: Mapped[str] = mapped_column(unique=True)
    # Collection address <network>:<address>
    address_uri: Mapped[str] = mapped_column(unique=True)
    royalty_percent: Mapped[Decimal] = mapped_column(repr=False)
    description: Mapped[str] = mapped_column(repr=False)

    @property
    def address(self):
        return self.address_uri.split(":")[1]


class NFT(Base):
    address_uri: Mapped[str] = mapped_column(unique=True)
    attributes_data: Mapped[list] = mapped_column(JSONB, repr=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    image_url: Mapped[str] = mapped_column(repr=False)
    is_burnt: Mapped[bool] = mapped_column(repr=False)
    is_frozen: Mapped[bool] = mapped_column(repr=False)
    listings: Mapped[list["NFTListing"]] = relationship(
        uselist=True,
        back_populates="nft",
        repr=False,
        primaryjoin=lambda: NFTListing.nft_address_uri == foreign(NFT.address_uri),
    )
    nft_collection_uid: Mapped[str] = mapped_column(ForeignKey(NFTCollection.uid), index=True)
    nft_collection: Mapped[NFTCollection] = relationship(
        foreign_keys=[nft_collection_uid], repr=False
    )
    off_chain_data: Mapped[dict[str, Any]] = mapped_column(
        JSONB, deferred=True, repr=False, server_default="{}"
    )
    # Owner's address <network>:<address>
    owner_address_uri: Mapped[str] = mapped_column(index=True, repr=False)
    uid: Mapped[str]

    @staticmethod
    def is_rekt_sql() -> ColumnElement[bool]:
        """SQL expression for ensuring NFT has a set "rekt" flag on its off chain data.

        Useful for heist NFTs.
        """
        return NFT.off_chain_data.path_match(
            literal("'$.rekt == true'", JSONPATH, literal_execute=True)
        )


class NFTListing(Base):
    auction_house: Mapped[str]
    nft_address_uri: Mapped[str] = mapped_column(index=True)
    nft: Mapped[NFT | None] = relationship(
        back_populates="listings",
        repr=False,
        primaryjoin=lambda: NFTListing.nft_address_uri == foreign(NFT.address_uri),
    )
    nft_collection_uid: Mapped[str] = mapped_column(ForeignKey(NFTCollection.uid), index=True)
    nft_collection: Mapped[NFTCollection] = relationship(
        foreign_keys=[nft_collection_uid], repr=False
    )
    sol_price: Mapped[Decimal]
    at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())


class RawHeistClaim(Base):
    requested_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    data: Mapped[dict[str, Any]] = mapped_column(JSON, repr=False)
    processed_id: Mapped[int | None] = mapped_column(ForeignKey("heistclaim.id"), default=None)
    unprocessable: Mapped[bool] = mapped_column(default=False, server_default="false")

    __table_args__ = (
        Index("ix_raw_heist_claim_requested_at", requested_at.desc()),
        Index("ix_raw_heist_claim_unprocessed", (processed_id == None), unprocessable),
    )


class HeistLocation(Base):
    game_id: Mapped[int]
    name: Mapped[str] = mapped_column(unique=True)
    location_type: Mapped[str]
    risk: Mapped[str]
    district_id: Mapped[int | None] = mapped_column(repr=False)
    district_name: Mapped[str | None]
    active_chimp_count: Mapped[int]
    active_orangutan_count: Mapped[int]
    active_gorilla_count: Mapped[int]


class HeistClaim(Base):
    game_id: Mapped[int] = mapped_column()
    started_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    ended_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    total_amount_emitted: Mapped[Decimal]
    total_amount_claimed: Mapped[Decimal]
    nft_address: Mapped[str]
    wallet_address: Mapped[str] = mapped_column(index=True)
    location_id: Mapped[int] = mapped_column(ForeignKey(HeistLocation.id))
    location: Mapped[HeistLocation] = relationship(foreign_keys=[location_id], repr=False)
    event_id: Mapped[int]
    arresting_officer_address: Mapped[str | None]
    arresting_officer_wallet_address: Mapped[str | None]
    is_bonus_roll: Mapped[bool]
    secondary_event: Mapped[str | None]
    opposing_nft_address: Mapped[str | None]
    opposing_wallet_address: Mapped[str | None] = mapped_column(index=True)

    @property
    def event(self):
        return HEIST_EVENTS[self.event_id]

    __table_args__ = (
        Index("ix_heist_claim_ended_at_game_id", ended_at.desc(), game_id, unique=True),
    )

    @staticmethod
    def nft_address_uri_sql() -> ColumnElement[str]:
        """SQL expression for nft_address_uri"""
        return literal("solana:", literal_execute=True) + HeistClaim.nft_address


class HeistDeedClaim(Base):
    kind: Mapped[str] = mapped_column()
    amount: Mapped[Decimal] = mapped_column()
    at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    wallet_address: Mapped[str] = mapped_column()
    nft_address: Mapped[str] = mapped_column()


class RawHeistDeedClaim(Base):
    address: Mapped[str] = mapped_column()
    data_hash: Mapped[bytes] = mapped_column(BYTEA, repr=False)
    requested_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    data: Mapped[dict[str, Any]] = mapped_column(JSON, repr=False)
    processed_id: Mapped[int | None] = mapped_column(
        ForeignKey(HeistDeedClaim.id), default=None, unique=True
    )
    unprocessable: Mapped[bool] = mapped_column(default=False, server_default="false")

    __table_args__ = (
        UniqueConstraint(address, data_hash),
        Index("ix_raw_heist_deed_claim_unprocessed", (processed_id == None), unprocessable),
    )
