variable "enable_liveness_probe" {
  description = "Enable pod liveness probes"
  default     = true
}

variable "enable_update_prices" {
  description = "Enable update prices cronjob"
  default     = true
}

variable "enable_calculate_cost_basis" {
  description = "Enable calculate cost basis cronjob"
  default     = true
}

variable "enable_dev_mode" {
  description = "Enable development mode features like auto-reloading. Not for production use!"
  default     = false
}

variable "host_volumes" {
  type = list(object({
    name       = string
    host_path  = string
    mount_path = string
  }))

  description = "Host paths to mount into containers for ease of development"
  default     = []
}
variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "replicas" {
  description = "Number of Pod replicas to run"
  default     = 1
}

variable "python_runner_image" {
  type        = string
  description = "python-runner fully qualified image name"
  nullable    = false
}

variable "requests" {
  description = "CPU and Memory requests for the containers"
  type = object({
    cpu    = string
    memory = string
  })
  default = {
    cpu    = "60m"
    memory = "128Mi"
  }
  nullable = false
}

variable "config_maps" {
  description = "ConfigMap names to mount into all containers"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "secrets" {
  description = "Secret names to mount into all containers"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "api_secrets" {
  description = "Secret names to mount into the api container only"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "jobs_consumer_secrets" {
  description = "Secret names to mount into the jobs consumer container only"
  type        = list(string)
  nullable    = false
  default     = []
}
