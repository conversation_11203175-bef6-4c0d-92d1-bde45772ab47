import { store } from "@/store";
import { selectAuthToken, selectUser, setLogOut } from "@/store/apps/auth";
import { QueryFunctionContext } from "@tanstack/react-query";
import * as v from "valibot";

const API_URL = process.env.NEXT_PUBLIC_API_URL?.endsWith("/")
  ? process.env.NEXT_PUBLIC_API_URL.slice(0, -1)
  : (process.env.NEXT_PUBLIC_API_URL ?? "/api");

interface AuthConfig {
  readonly token: string;
  readonly onInvalidToken: () => void;
}

const defaultAuthConfig = {
  get token() {
    return selectAuthToken(store.getState()) ?? "";
  },
  onInvalidToken: () => {
    const state = store.getState();
    const token = selectAuthToken(state);
    const user = selectUser(state);
    if (token || user) store.dispatch(setLogOut(true));
  },
};

/**
 * Use as `queryFn` for `useQuery` to fetch data from the API.
 *
 * @example
 * // GET /items?active=true&limit=10
 * const itemListQuery = useQuery({
 *   queryKey: ["items", { active: true, limit: 10 }],
 *   queryFn: apiQuery<ItemListResponse>,
 * })
 *
 * @example
 * // GET /items/123
 * const itemQuery = useQuery({
 *  queryKey: ["items", 123],
 *  queryFn: apiQuery<ItemResponse>,
 * })
 */
export async function apiQuery<T>(ctx: QueryFunctionContext<ApiQueryKey>): Promise<T> {
  return executeApiQuery<T>(defaultAuthConfig, ctx);
}

type ApiQueryKey =
  | readonly [string, ...(string | number)[]]
  | readonly [string, ...(string | number)[], Record<string, UrlSearchValue>];

async function executeApiQuery<T>(
  authConfig: AuthConfig,
  ctx: QueryFunctionContext<ApiQueryKey>,
): Promise<T> {
  const keysExceptLast = ctx.queryKey.slice(0, -1) as (string | number)[];
  let url = [API_URL, ...keysExceptLast].join("/");
  const lastKey = ctx.queryKey.at(-1);
  url +=
    typeof lastKey !== "object"
      ? `/${lastKey}`
      : getUrlSearch(Object.entries(lastKey).sort(compareRecordValues));
  const headers = new Headers({ Accept: "application/json" });
  if (authConfig.token) headers.set("Authorization", `Bearer ${authConfig.token}`);
  const res = await fetch(url, { headers });
  if (res.status === 401) {
    authConfig.onInvalidToken();
  }
  if (!res.ok) {
    throw await ApiError.fromResponse(res);
  }
  return res.json() as Promise<T>;
}

interface MutationParams {
  readonly method: "POST" | "PUT" | "DELETE" | "PATCH";
  readonly resource: string;
  readonly search?: readonly NameValuePair[];
  readonly body?: unknown;
  readonly auth?: boolean;
  readonly authConfig?: AuthConfig;
}

/**
 * Use in `mutationFn` for `useMutation` to perform a mutation on the API.
 *
 * @example
 * // POST /items
 * const createItem = useMutation({
 *   mutationFn: async (body: CreateItem) => {
 *     return apiMutation({
 *       method: "POST",
 *       resource: "items",
 *       body,
 *     });
 *   },
 * });
 */
export async function apiMutation<T>({
  method,
  resource,
  search,
  body,
  auth = true,
  authConfig = defaultAuthConfig,
}: MutationParams): Promise<T> {
  const headers = new Headers({ Accept: "application/json" });
  if (auth) headers.set("Authorization", `Bearer ${authConfig.token}`);
  if (body) headers.set("Content-Type", "application/json");
  const searchString = search ? getUrlSearch(search) : "";
  const res = await fetch(`${API_URL}/${resource}${searchString}`, {
    method,
    headers,
    body: JSON.stringify(body),
  });
  if (auth && res.status === 401) {
    authConfig.onInvalidToken();
  }
  if (!res.ok) {
    throw await ApiError.fromResponse(res, true);
  }
  return res.json() as Promise<T>;
}

const ApiErrorDetailItemSchema = v.object({
  type: v.string(),
  msg: v.string(),
  loc: v.array(v.string()),
  input: v.unknown(),
  ctx: v.object({
    max_length: v.optional(v.number()),
    min_length: v.optional(v.number()),
  }),
});

const ApiErrorBodySchema = v.object({
  detail: v.pipe(v.union([v.string(), v.array(ApiErrorDetailItemSchema)]), v.nonEmpty()),
});

type ApiErrorBody = v.InferInput<typeof ApiErrorBodySchema>;

/** Error thrown by default when the API returns a >=400 response. */
export class ApiError extends Error {
  readonly response: Response;
  readonly body: ApiErrorBody | null;

  constructor(response: Response, body: ApiErrorBody | null, message: string) {
    super(message);
    this.name = "ApiError";
    this.response = response;
    this.body = body;
  }

  static async fromResponse(response: Response, mutation = false): Promise<ApiError> {
    let rawBody: unknown;
    if (response.headers.get("Content-Type")?.startsWith("application/json")) {
      try {
        rawBody = await response.json();
      } catch {
        // Ignore error
      }
    }
    const body = v.is(ApiErrorBodySchema, rawBody) ? rawBody : null;

    const message =
      (mutation ? "Failed mutation" : "Failed to fetch data") +
      ` (${response.status} ${response.statusText})`;

    return new ApiError(response, body, message);
  }
}

type UrlSearchValue = string | number | boolean | null | undefined;

type NameValuePair = readonly [string, UrlSearchValue];

/**
 * @param params Parameters names and their respective values. The
 * parameters with values of `null` or `undefined` are omitted.
 * @returns URL search string with a leading `?` or empty string
 */
export function getUrlSearch(params: readonly NameValuePair[]): string {
  const searchParams = new URLSearchParams();
  for (const [name, value] of params) {
    if (value != null) searchParams.append(name, `${value}`);
  }

  const search = searchParams.toString();
  if (search.length === 0) return "";

  return `?${search}`;
}

function compareRecordValues([keyA]: [string, unknown], [keyB]: [string, unknown]): number {
  return keyA < keyB ? -1 : keyA > keyB ? 1 : 0;
}
