# Stable Tech

## Simulate another environment

During local development it could be useful to simulate being in another environment, for
example, on LP (`lp.stabletech.capital`) to test some features unique to this environment. To do
that we can provide a `ENV_OVERRIDE` environment variable, for example, by putting it on a
`.env` file sibling to this readme:

```
ENV_OVERRIDE=lp
```

> ⚠️ The UI configuration is independent of this setting. This will only change the API server.
> For consistent testing, make sure to also update the UI configuration to run it as the desired
> environment (see
> [ui/client-bashboard/README.md](../ui/client-bashboard/README.md#simulate-another-environment)).

## DB Helpers

### Reset DB

Run `./scripts/psql_load_database.sh` to drop the `temet` DB and recreate it, applying
migrations and fetching CoinGecko Coins. Takes an optional argument to specify a dump file to
load into the DB.

### Load DB dump during init

Locally, when a Postgres dump exists at `../tmp/load.db.dump`, the `init` container will delete
the current `temet` database and load in the contents of that dump instead when executing
`python-runner.start.sh`. _Note: all data producers need to be disabled to avoid contamination_

```
kubectl config use-context minikube
kubectl delete deploy market-price-producer
```

## iPython snippets

### Postgres

Add a new admin User:

```python
email = '<EMAIL>'
password = "t"

from common.psql import SessionLocal
from stabletech.auth import schemas
from stabletech.auth.crud import UserManager

session = SessionLocal()

user = UserManager(db).add_with_password(
    schemas.LoginPassword(email=email, password=password)
)
user.set_admin_role()
session.commit()
```

# Lootloot

## Set user as admin

Currently admin users are any user with an `@stabletech.capital` email address. There is no
database flag to set a user as admin.

# Wallet

## CubeSigner sessions

We must initialize once for each environment the key for the "secret box", except for local
development environments which will use a pre-defined key. When creating a new environment use
`backend/scripts/generate_secret_box_key.py` to easily achieve that, remember to edit the secret
name to match `${namespace}-cubist-session-secret-box-key` before running.

Once we have the secret setup, to create or update a CubeSigner session use the provided script:

> 💡 Before you begin, identify the Kubernetes cluster context, namespace and pod name you're
> going to use, the pod must be a wallet API pod.

Then run the script with the required parameters:

```shell
./wallet/scripts/create_cubesigner_session.py \
    --context gke_temet-359508_us-west1-a_temet-359508-gke \
    --namespace wallet-stage \
    --pod-name api-8554d7cf6c-2df2p \
    --purpose dev \
    --refresh-lifetime 2700000 \
    --session-name default
```

The script will:

1. Create a CubeSigner session using the CLI
2. Upload the session file to the pod
3. Save the encrypted session to the database
4. Automatically clean up session files from both the pod and local machine

> ℹ️ This script can be used for setting up both production and local development environments,
> just adjust the Kubernetes context (e.g. `minikube`), namespace and pod name accordingly.
