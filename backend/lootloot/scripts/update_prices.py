import logging

import httpx
import sentry_sdk
from sqlalchemy.orm import Session
from tenacity import retry, retry_if_exception_type, stop_after_delay, wait_exponential_jitter

from common.psql import SessionLocal
from lootloot import schemas
from lootloot.external.magic_eden import CollectionListing, MagicEdenClient
from lootloot.kinds import AuctionHouse
from lootloot.nfts.crud import NFTListingManager, NFTManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

HEIST_UID = "theheist"
HEIST_GENERATIONS_UID = "the_heist_generations"
HEIST_LAND_UID = "the_heist_land"

magic_eden = MagicEdenClient(timeout=15)

get_collection_listings_with_retry = retry(
    retry=retry_if_exception_type(httpx.HTTPError),
    wait=wait_exponential_jitter(jitter=2),
    stop=stop_after_delay(30),
)(magic_eden.collection_listings)


def update_heist_nfts_floor_prices(db: Session) -> None:
    cheapest_listings: list[CollectionListing] = []

    for species in ("Chimp", "Gorilla", "Orangutan"):
        resp = get_collection_listings_with_retry(
            HEIST_UID, attributes=[[{"traitType": "Species", "value": species}]], limit=1
        )
        if resp:
            cheapest_listings.append(resp[0])

    for species in ("Chimp", "Orangutan"):
        resp = get_collection_listings_with_retry(
            HEIST_GENERATIONS_UID,
            attributes=[[{"traitType": "Species", "value": species}]],
            limit=1,
        )
        if resp:
            cheapest_listings.append(resp[0])

    for size in ("Small", "Medium", "Large"):
        resp = get_collection_listings_with_retry(
            HEIST_LAND_UID, attributes=[[{"traitType": "Plot Size", "value": size}]], limit=1
        )
        if resp:
            cheapest_listings.append(resp[0])

    nft_manager = NFTManager(db)
    nft_listing_manager = NFTListingManager(db)
    for listing in cheapest_listings:
        with db.begin():
            nft = nft_manager.get(address_uri=f"solana:{listing.tokenMint}")
            if not nft:
                logger.warning(
                    "Skipped update for not found NFT 'solana:%s'", listing.tokenMint
                )
                continue
            nft_listing_manager.create(
                schemas.AddNFTListing(
                    auction_house=AuctionHouse.magic_eden,
                    nft_address_uri=nft.address_uri,
                    sol_price=listing.price,
                    nft_collection_uid=nft.nft_collection_uid,
                )
            )


def main() -> None:
    tasks_list = [update_heist_nfts_floor_prices]
    with SessionLocal() as db:
        for task in tasks_list:
            with sentry_sdk.isolation_scope() as scope:
                task_name = task.__name__
                scope.set_context("task", {"name": task_name})
                logger.info("Starting task %s", task_name)
                try:
                    task(db)
                    logger.info("Succeeded task %s", task_name)
                except Exception as e:
                    sentry_sdk.capture_exception(e)
                    db.rollback()
                    logger.exception(e)
                    logger.info("Failed task %s", task_name)


main()
