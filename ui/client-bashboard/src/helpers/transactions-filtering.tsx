import * as schema from "@/types/schemas";
import { useCallback, useMemo, useState } from "react";

type Coin = Pick<schema.Coin, "id">;

interface Transaction {
  readonly action: schema.Transaction["action"];
  readonly transfers: readonly schema.Transfer[];
}

interface Params<T extends Transaction, C extends Coin> {
  readonly coinTotals?: readonly { readonly coin: C }[];
  readonly coins?: readonly C[];
  readonly transactions: readonly T[];
}

export function useTransactionsFiltering<T extends Transaction, C extends Coin>(
  params: Params<T, C> | undefined | null,
) {
  const [selectedCoinId, setSelectedCoinId] = useState(0);

  // Toggle off if selecting same coin
  const handleSelectCoin = useCallback((value: Coin) => {
    setSelectedCoinId((id) => (value.id !== id ? value.id : 0));
  }, []);

  const clearSelectedCoin = useCallback(() => {
    setSelectedCoinId(0);
  }, []);

  const transactions = useMemo(() => {
    if (params == null) return [];
    if (selectedCoinId === 0) return params.transactions;
    return params.transactions.filter((tx) => isCoinTransaction(selectedCoinId, tx));
  }, [selectedCoinId, params]);

  const selectedCoin = useMemo(() => {
    if (params == null) return null;
    if (params.coins) return params.coins.find((coin) => coin.id === selectedCoinId) ?? null;
    if (params.coinTotals == null) return null;
    return params.coinTotals.find(({ coin }) => coin.id === selectedCoinId)?.coin ?? null;
  }, [selectedCoinId, params]);

  return {
    selectedCoinId,
    selectedCoin,
    transactions,
    handleSelectCoin,
    clearSelectedCoin,
  } as const;
}

export function isCoinTransaction(coinId: number, transaction: Transaction): boolean {
  if (transaction.action === "swap") {
    // Filter for the deposit (buy) side only
    return transaction.transfers.some(
      (transfer) => !transfer.isWithdrawal && transfer.assetAccount.coin?.id === coinId,
    );
  }
  return transaction.transfers.some((transfer) => transfer.assetAccount.coin?.id === coinId);
}
