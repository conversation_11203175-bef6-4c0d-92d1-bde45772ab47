import { ConfirmationDialog, useConfirmationDialog } from "@/components/ConfirmationDialog";
import { SnackSuccess } from "@/components/SnackSuccess";
import { TransactionCard } from "@/components/TransactionCard";
import TransactionsSearch from "@/components/TransactionSearch";
import { WalletSync } from "@/components/admin/WalletSync";
import { NetworkSearch } from "@/components/autocomplete/NetworkSearch";
import { WalletSearch } from "@/components/autocomplete/WalletSearch";
import { TransactionAdd } from "@/components/transaction/TransactionAdd";
import { TransactionClassify } from "@/components/transaction/TransactionClassify";
import {
  useIndexerTransactionDeleteMutation,
  useIndexerTransactionsUnclassifiedQuery,
  useLazyIndexerTransactionQueryCovalentQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Network, TransactionUnclassified, Wallet } from "@/types/schemas";
import { Add } from "@mui/icons-material";
import DeleteIcon from "@mui/icons-material/Delete";
import { Alert, Box } from "@mui/material";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import React, { useState } from "react";

const TransactionsNew = () => {
  const [errorAlert, setErrorAlert] = useState<string | null>(null);
  const [selectedNetwork, setSelectedNetwork] = useState<Network | null>(null);
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null);
  const [showSuccessSnack, setShowSuccessSnack] = useState(false);
  const [toShow, setToShow] = useState<"add" | "search" | null>(null);

  const [deleteTransaction, transactionDeletion] = useIndexerTransactionDeleteMutation();
  const [queryCovalent] = useLazyIndexerTransactionQueryCovalentQuery();

  const [confirmationDialogProps, confirm] = useConfirmationDialog();

  const mainContent = (
    <TransactionsSearch
      header={
        <SnackSuccess
          showSuccessSnack={showSuccessSnack}
          setShowSuccessSnack={setShowSuccessSnack}
        />
      }
      searchBarExtra={
        <>
          <IconButton
            sx={{ flex: 0 }}
            size={"large"}
            onClick={() => setToShow(toShow === "add" ? null : "add")}
          >
            <Add />
          </IconButton>
          <Stack sx={{ flex: 1 }} direction="row" alignItems="center" gap={1}>
            <WalletSync />
          </Stack>
        </>
      }
      body={
        toShow === "add" && (
          <Grid item xs={12}>
            <TransactionAdd onSuccess={() => setShowSuccessSnack(true)} />
          </Grid>
        )
      }
      renderEmptyResults={(searchQuery, refetch) =>
        toShow !== "add" && (
          <Grid item>
            <Stack spacing={4}>
              <Typography variant={"h5"}>No transactions found</Typography>
              <Button onClick={() => setToShow("search")}>Query Covalent</Button>
              {toShow === "search" && (
                <>
                  <NetworkSearch
                    isAdmin
                    selectedNetwork={selectedNetwork}
                    setSelectedNetwork={setSelectedNetwork}
                  />

                  {selectedNetwork && (
                    <WalletSearch
                      isAdmin
                      network={selectedNetwork}
                      selectedWallet={selectedWallet}
                      setSelectedWallet={setSelectedWallet}
                    />
                  )}
                </>
              )}
              {selectedNetwork && selectedWallet && (
                <Button
                  onClick={() => {
                    setErrorAlert(null);
                    queryCovalent([selectedNetwork.id, selectedWallet.id, searchQuery])
                      .unwrap()
                      .then(() => {
                        setSelectedNetwork(null);
                        setSelectedWallet(null);
                        setToShow(null);
                        refetch();
                      })
                      .catch((error) => setErrorAlert(getErrorDetail(error)));
                  }}
                >
                  Search
                </Button>
              )}
              {errorAlert && <Alert severity={"error"}>{errorAlert}</Alert>}
            </Stack>
          </Grid>
        )
      }
      transactionHook={useIndexerTransactionsUnclassifiedQuery}
      displayTransactions={toShow !== "add"}
      onSearchChange={(event: React.ChangeEvent<HTMLInputElement>) => {
        const query = event.target.value;
        if (query) {
          setToShow(null);
        } else {
          setSelectedNetwork(null);
          setSelectedWallet(null);
          setToShow(null);
        }
      }}
      renderTransaction={(transaction, isFetching) => {
        return (
          <Grid item key={transaction.id} xs={12}>
            <TransactionCard
              transaction={transaction}
              isLoading={isFetching}
              showTransferDelete
              endElement={
                <Grid item xs={7}>
                  <Stack direction={"row"} alignItems="center" gap={2}>
                    <Box flex={1} mx={2}>
                      <TransactionClassify
                        transaction={transaction as TransactionUnclassified}
                      />
                    </Box>
                    <Button
                      aria-label="Delete transaction"
                      title="Delete transaction"
                      color="error"
                      disabled={isFetching || transactionDeletion.isLoading}
                      onClick={() => {
                        confirm({
                          action: () => deleteTransaction(transaction.id).unwrap(),
                          title: "Delete Transaction",
                          message: (
                            <Stack gap={4}>
                              <Typography>
                                Are you sure you want to delete this transaction?
                              </Typography>
                              <Typography
                                bgcolor="background.default"
                                borderRadius={1}
                                fontFamily="monospace"
                                px={2}
                                py={1}
                                sx={{ wordBreak: "break-all" }}
                              >
                                {transaction.txHash}
                              </Typography>
                              <Typography fontSize="small">
                                Network: {transaction.network.name}
                              </Typography>
                              <Typography fontSize="small">
                                Confirmed at {new Date(transaction.confirmedAt).toString()}
                              </Typography>
                              <Typography color="error">
                                This action cannot be undone.
                              </Typography>
                            </Stack>
                          ),
                        });
                      }}
                      size="small"
                      sx={{ flexShrink: "0", p: 1, m: -1, minWidth: 0, minHeight: 0 }}
                    >
                      <DeleteIcon />
                    </Button>
                  </Stack>
                </Grid>
              }
            />
          </Grid>
        );
      }}
    />
  );

  return (
    <>
      {mainContent}
      <ConfirmationDialog {...confirmationDialogProps} />
    </>
  );
};

TransactionsNew.acl = {
  action: "manage",
  subject: "Transaction",
};

export default TransactionsNew;
