import json

from fastapi import APIRouter, HTTPException, Response

from common.conf import settings
from common.psql import DatabaseSession
from wallet import schemas
from wallet.auth.fastapi import CurrentUser
from wallet.cubist.cubist_client import get_cubist_management_client

router = APIRouter()


@router.post("/register-oidc-user", response_model=schemas.CubistRegisterOidcUserResp)
async def register_oidc_user(
    db: DatabaseSession,
    user: CurrentUser,
    identity_proof: schemas.CubistIdentityProof,
):
    """Register a new Cubist user using their OIDC identity."""
    if user.email != identity_proof.email:
        raise HTTPException(
            status_code=400, detail="email of user doesn't match identity proof"
        )

    cubist_management_client = await get_cubist_management_client()

    resp = await cubist_management_client.verify_identity(
        org_id=settings.cubist_org_id,
        identity_proof=identity_proof,
    )

    if not resp:
        raise HTTPException(status_code=400, detail="identity verification failed")

    if identity_proof.user_info:
        raise HTTPException(status_code=400, detail="user info exists")

    user_name = " ".join(name for name in (user.first_name, user.last_name) if name)

    registration_response = await cubist_management_client.add_user_to_org(
        org_id=settings.cubist_org_id,
        email=identity_proof.email,
        identity=identity_proof.identity,
        name=user_name,
        mfa_policy=None,  # TODO: need to define the mfa policy, and understand if it can be set with the user initially
    )

    user.cubist_user_id = registration_response.user_id
    db.commit()
    return registration_response


@router.post("/create/key")
async def create_key(user: CurrentUser, data: schemas.CubistCreateKeyRequest):
    """Create a new Cubist signing key for the user."""
    if not user.cubist_user_id:
        raise HTTPException(status_code=400, detail="User not registered with Cubist")

    cubist_management_client = await get_cubist_management_client()

    key_response = await cubist_management_client.create_key(data.key_type, user.cubist_user_id)
    return Response(status_code=200, content=json.dumps(key_response))


@router.post("/users")
async def associate_user_with_cubist(
    db: DatabaseSession, user: CurrentUser, cubist_user_id: str
):
    """Associate an existing user with a Cubist user ID."""
    if not user.cubist_user_id:
        user.cubist_user_id = cubist_user_id
        db.commit()
    elif user.cubist_user_id != cubist_user_id:
        raise HTTPException(
            status_code=400, detail="User already associated with another Cubist user"
        )
