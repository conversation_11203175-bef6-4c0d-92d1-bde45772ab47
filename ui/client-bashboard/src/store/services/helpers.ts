import { ApiError } from "@/api/api";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

const PROPS_TO_CHECK_FOR_MESSAGE = ["message", "detail", "error"] as const;

/**
 * Get error detail string or fallback.
 *
 * @returns a string detailing the error as best-effort, or `fallback` which
 * defaults to empty string.
 */
export function getErrorDetail(
  error: unknown,
  { fallback = "" }: { fallback?: string } = {},
): string {
  if (error == null) {
    return fallback;
  }
  if (error instanceof ApiError) {
    return getApiErrorDetail(error);
  }
  if (isFetchError(error)) {
    if (isDataWithDetailString(error.data) && error.data.detail != "") {
      return error.data.detail;
    }
    if ("error" in error && error.error != "") {
      return error.error;
    }
  }
  if (typeof error === "object") {
    for (const prop of PROPS_TO_CHECK_FOR_MESSAGE) {
      const value = (error as Record<string, unknown>)[prop];
      if (typeof value === "string" && value.length > 0) {
        return value;
      }
    }
  } else if (typeof error === "string" && error.length > 0) {
    return error;
  }
  return fallback;
}

function isFetchError(error: object): error is FetchBaseQueryError {
  return (
    "status" in error &&
    ("data" in error || ("error" in error && typeof error.error === "string"))
  );
}

function isDataWithDetailString(data: unknown): data is { detail: string } {
  return (
    typeof data === "object" &&
    data != null &&
    "detail" in data &&
    typeof data.detail === "string"
  );
}

function getApiErrorDetail(error: ApiError) {
  if (!error.body?.detail) {
    return error.message;
  }
  if (typeof error.body.detail === "string") {
    return error.body.detail;
  }
  return error.body.detail
    .map((e) => `${e.loc.slice(1).map(snakeToTitle).join(" » ")}: ${e.msg}`)
    .join("\u2002•\u2002"); // U+2002 En Space
}

/** Convert snake_case to Title Case */
function snakeToTitle(snake: string): string {
  return snake.replaceAll("_", " ").replaceAll(/\b\w/gu, (c) => c.toUpperCase());
}
