"use client";

import "@/app/heists/page.css";
import { useAuth } from "@/lib/auth-ctx";
import { AlertError } from "@/lib/components/alert";
import solanaLogo from "@/lib/components/assets/solana-logo.svg";
import { SignedOutError, ViewToggle, getView } from "@/lib/components/view-toggle";
import { getErrorMessage } from "@/lib/error";
import {
  currencyFormatter,
  dateFormatter,
  dayMonthFormatter,
  numberFormatter,
  numberFormatter6D,
} from "@/lib/formatters";
import {
  useGetHeistNanaByDayQuery,
  useGetHeistStatsQuery,
  useGetPublicHeistNanaByDayQuery,
  useGetPublicHeistPrices,
  useGetPublicHeistStatsQuery,
} from "@/lib/heist";
import { NavigationProvider, useSearchParams } from "@/lib/navigation";
import { HeistStats, HeistStatsPublic, NanaInDay } from "@/types/schemas";
import { cx } from "class-variance-authority";
import Image from "next/image";
import React, { Suspense, useMemo } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function HeistsWrapper() {
  return (
    <Suspense fallback={<Heists />}>
      <NavigationProvider>
        <Heists />
      </NavigationProvider>
    </Suspense>
  );
}

function Heists() {
  const params = useSearchParams();
  const auth = useAuth();

  const isSignedOut = auth.initialized && !auth.user;
  const view = params ? getView(params) : "own";
  const isOwn = view === "own";

  const prices = useGetPublicHeistPrices();

  const ownStats = useGetHeistStatsQuery({ enabled: isOwn && !isSignedOut });
  const allStats = useGetPublicHeistStatsQuery({ enabled: !isOwn });

  const timezoneOffset = new Date().getTimezoneOffset();
  const ownNanaByDay = useGetHeistNanaByDayQuery({
    enabled: isOwn && !isSignedOut,
    timezoneOffset,
  });
  const allNanaByDay = useGetPublicHeistNanaByDayQuery({ enabled: !isOwn, timezoneOffset });

  const stats = isOwn ? ownStats : allStats;
  const statsData = stats.data ? processStats(stats.data) : undefined;

  const nanaByDay = isOwn ? ownNanaByDay : allNanaByDay;
  const nanaByDayData = useMemo(
    () => (nanaByDay.data?.items ?? []).map(processNanaInDay),
    [nanaByDay.data],
  );

  if (isOwn && isSignedOut) {
    return (
      <main className="grid gap-6 px-2 py-4 md:px-4">
        <div className="grid grid-cols-[1fr_auto]">
          <h1 className="text-2xl">Heists</h1>
          <ViewToggle view={view} params={params} />
        </div>
        <SignedOutError params={params} />
      </main>
    );
  }

  const errorContent = stats.error ? (
    <AlertError className="text-center leading-7">{stats.error.message}</AlertError>
  ) : null;

  const pulseLoader = (
    <span className="inline-block w-24 animate-pulse rounded-md bg-foreground/10">&nbsp;</span>
  );

  const renderPrice = (price: number | null | undefined, formatter = currencyFormatter) =>
    prices.isLoading ? (
      pulseLoader
    ) : price != null ? (
      <span>${formatter.format(price)}</span>
    ) : (
      "—"
    );

  const renderStats = (value: number | undefined, units: "NANA" | "apes" | "heists") =>
    stats.isLoading ? (
      pulseLoader
    ) : value != null ? (
      <span>
        {numberFormatter.format(units === "NANA" ? Math.floor(value) : value)}{" "}
        {<span className="font-light">{units}</span>}
      </span>
    ) : (
      "—"
    );

  return (
    <main className="grid gap-6 px-2 py-4 md:px-4">
      <div className="grid grid-cols-[1fr_auto]">
        <h1 className="text-2xl">Heists</h1>
        <ViewToggle view={view} params={params} />
      </div>
      <SignedOutError params={params} />
      {errorContent}
      {!errorContent && (
        <>
          <Dl className="relative grid-cols-2">
            <DlValue>
              <dt className="flex items-baseline gap-1">
                <Image className="inline" height={11} src={solanaLogo} alt="" />
                SOL
              </dt>
              <dd>{renderPrice(prices.data?.sol)}</dd>
            </DlValue>
            <DlValue>
              <dt>🍌 NANA</dt>
              <dd>{renderPrice(prices.data?.nana, numberFormatter6D)}</dd>
            </DlValue>
            {prices.error && (
              <div className="absolute inset-0 grid place-items-center rounded-sm bg-background/65">
                <AlertError className="w-fit!">{getErrorMessage(prices.error)}</AlertError>
              </div>
            )}
          </Dl>
          <Dl className={cx("grid-cols-2", isOwn && "xl:grid-cols-4")}>
            <DlValue>
              <dt>💰 Claimed</dt>
              <dd>{renderStats(statsData?.claimed, "NANA")}</dd>
            </DlValue>
            <DlValue>
              <dt>💸 Fumbled</dt>
              <dd>{renderStats(statsData?.fumbled, "NANA")}</dd>
            </DlValue>
            {isOwn && (
              <>
                <DlValue>
                  <dt>🗡️ Gained from Ambush</dt>
                  <dd>{renderStats(statsData?.gainedFromAmbush, "NANA")}</dd>
                </DlValue>
                <DlValue>
                  <dt>💥 Lost to Ambush</dt>
                  <dd>{renderStats(statsData?.lostToAmbush, "NANA")}</dd>
                </DlValue>
              </>
            )}
          </Dl>
          <div>
            <Dl className="grid-cols-3">
              <DlValue>
                <dt>✅ Completed</dt>
                <dd>{renderStats(statsData?.completed, "heists")}</dd>
              </DlValue>
              <DlValue>
                <dt>🔥 Rekt</dt>
                <dd>{renderStats(statsData?.rekt, "apes")}</dd>
              </DlValue>
              <DlValue>
                <dt>📈 NANA per heist</dt>
                <dd>
                  {renderStats(
                    statsData && statsData.completed > 0
                      ? (statsData.claimed + (statsData.gainedFromAmbush ?? 0)) /
                          statsData.completed
                      : undefined,
                    "NANA",
                  )}
                </dd>
              </DlValue>
              <DlValue className="col-span-full justify-stretch!">
                <dt>📊 NANA by day</dt>
                <dd className="mt-0.5 h-64 w-full">
                  {nanaByDayData.length > 0 ? (
                    renderNanaByDayBarChart(nanaByDayData)
                  ) : (
                    <p className="grid h-full place-items-center font-light">
                      No data available
                    </p>
                  )}
                </dd>
              </DlValue>
            </Dl>
          </div>
        </>
      )}
    </main>
  );
}

function Dl({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <dl className={cx("grid gap-1.5 text-center text-sm sm:gap-2 sm:text-base", className)}>
      {children}
    </dl>
  );
}

function DlValue({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <div
      className={cx(
        "grid justify-center justify-items-center gap-1 rounded-sm bg-primary/5 px-1 py-2 shadow-xs dark:bg-primary/35",
        className,
      )}
    >
      {children}
    </div>
  );
}

function processStats(stats: HeistStats | HeistStatsPublic) {
  return {
    claimed: Number.parseFloat(stats.claimed),
    fumbled: Number.parseFloat(stats.fumbled),
    completed: stats.completed,
    rekt: stats.rekt,
    lostToAmbush:
      "lost_to_ambush" in stats ? Number.parseFloat(stats.lost_to_ambush) : undefined,
    gainedFromAmbush:
      "gained_from_ambush" in stats ? Number.parseFloat(stats.gained_from_ambush) : undefined,
  };
}

function processNanaInDay(data: NanaInDay) {
  return {
    at: new Date(data.at),
    lost: Math.floor(Number.parseFloat(data.lost)),
    gained: Math.floor(Number.parseFloat(data.gained)),
  };
}

type NanaInDayProcessed = ReturnType<typeof processNanaInDay>;

function renderNanaByDayBarChart(nanaByDayData: NanaInDayProcessed[]) {
  return (
    <ResponsiveContainer>
      <BarChart
        data={nanaByDayData}
        margin={{ top: 5, right: 5, left: 5, bottom: 0 }}
        barGap={0}
      >
        <CartesianGrid vertical={false} stroke="hsl(var(--border))" />
        <XAxis
          dataKey="at"
          tickFormatter={(value: Date) => dayMonthFormatter.format(value)}
          fontSize="0.625rem"
          fontWeight={300}
          stroke="hsl(var(--foreground) / 0.6)"
        />
        <YAxis
          tickFormatter={(value: number) => numberFormatter.format(value)}
          fontSize="0.625rem"
          fontWeight={300}
          stroke="hsl(var(--foreground) / 0.6)"
        />
        <Tooltip
          cursor={{ fill: "hsl(var(--foreground))", opacity: 0.15 }}
          formatter={(value: number) => numberFormatter.format(value)}
          labelFormatter={(value: Date) => dateFormatter.format(value)}
          contentStyle={{
            fontSize: "0.875rem",
            color: "hsl(var(--foreground))",
            backgroundColor: "hsl(var(--background))",
            borderColor: "hsl(var(--border))",
          }}
        />
        <Bar dataKey="gained" fill="hsl(var(--gained))" />
        <Bar dataKey="lost" fill="hsl(var(--lost))" />
      </BarChart>
    </ResponsiveContainer>
  );
}
