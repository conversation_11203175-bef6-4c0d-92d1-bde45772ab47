"""Client Transaction
Revision ID: 0027
Revises: 0026
Create Date: 2023-07-02 02:25:54.855888
"""

from datetime import datetime
from decimal import Decimal

import sqlalchemy as sa
from alembic import op
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Table,
    UniqueConstraint,
    exists,
    func,
    not_,
)
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0027"
down_revision = "0026"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


network_wallet_association_table = Table(
    "network_wallet_association",
    Base.metadata,
    Column("network_id", ForeignKey("network.id"), primary_key=True),
    Column("wallet_id", ForeignKey("wallet.id"), primary_key=True),
    UniqueConstraint("network_id", "wallet_id"),
)


class Network(Base):
    id: int = Column(Integer, primary_key=True, index=True)
    name: str = Column(String, nullable=False, unique=True, index=True)
    __tablename__ = "network"


class Wallet(Base):
    id: int = Column(Integer, primary_key=True, index=True)
    address: str = Column(String, nullable=False, index=True, unique=True)
    is_active = Column(Boolean, default=True, index=True)
    name: str = Column(String, nullable=False, unique=True, index=True)
    networks = relationship(Network, secondary=network_wallet_association_table)
    __tablename__ = "wallet"


class Coin(Base):
    id: int = Column(Integer, primary_key=True, index=True)
    has_prices: bool = Column(Boolean, default=False)
    name: str = Column(String, nullable=False)
    ticker: str = Column(String, nullable=False)
    uid: str = Column(String, index=True, unique=True)
    __tablename__ = "coin"


class Account(Base):
    id: int = Column(Integer, primary_key=True, index=True)
    address: str = Column(String, nullable=False, index=True)
    network_id: int = Column(Integer)
    ticker_symbol: str = Column(String, index=True)
    wallet_id: int = Column(Integer, ForeignKey(Wallet.id), index=True)
    __tablename__ = "account"

    @property
    def is_wallet(self):
        return self.wallet_id is not None


class Transaction(Base):
    id: int = Column(Integer, primary_key=True, index=True)
    action: str = Column(String)  # TransactionAction
    api_type: str = Column(String)
    confirmed_at: datetime = Column(DateTime, nullable=False)
    json_data: str = Column(String)
    fees_paid: Decimal = Column(Numeric(precision=32, scale=18))  # in Network's `native_coin`
    # Transaction spent gas on an Approval
    has_approval: bool = Column(Boolean, nullable=False, server_default="false")
    is_deposit: bool = Column(Boolean)
    native_coin_dollar_price: Decimal = Column(Numeric(precision=32, scale=18))
    network_id: int = Column(Integer, ForeignKey(Network.id), nullable=False, index=True)
    network = relationship(Network, foreign_keys=[network_id])
    signer_account_id: int = Column(Integer)
    transfers: list = relationship("Transfer", back_populates="transaction")
    tx_hash: str = Column(String)
    wallet_id: int = Column(Integer, ForeignKey(Wallet.id), nullable=False, index=True)
    wallet = relationship(Wallet, foreign_keys=[wallet_id])
    __tablename__ = "transaction"


class Transfer(Base):
    """
    Movement of an amount of an Asset between two other Accounts
    """

    id: int = Column(Integer, primary_key=True, index=True)
    asset_account_id: int = Column(Integer, ForeignKey(Account.id), nullable=False, index=True)
    asset_account = relationship(Account, foreign_keys=[asset_account_id])
    amount: Decimal = Column(Numeric(precision=48, scale=18), nullable=False)
    nft_id: int = Column(Numeric(precision=32, scale=0))

    from_account_id: int = Column(Integer, ForeignKey(Account.id), nullable=False, index=True)
    from_account = relationship(Account, foreign_keys=[from_account_id])
    to_account_id: int = Column(Integer, ForeignKey(Account.id), nullable=False, index=True)
    to_account = relationship(Account, foreign_keys=[to_account_id])
    transaction = relationship(Transaction, back_populates="transfers")
    transaction_id: int = Column(
        Integer, ForeignKey(Transaction.id), nullable=False, index=True
    )
    __tablename__ = "transfer"

    @property
    def is_deposit(self):
        return self.to_account.is_wallet


class Position(Base):
    id: int = Column(Integer, primary_key=True, index=True)
    amount: Decimal = Column(Numeric(precision=32, scale=18), nullable=False)
    coin_id: str = Column(Integer, ForeignKey(Coin.id), nullable=False)
    coin = relationship(Coin)
    cost_basis_usd: Decimal = Column(Numeric(precision=32, scale=18))
    created_at: datetime = Column(
        DateTime, nullable=False, index=True, server_default=func.now()
    )
    is_active = Column(Boolean, default=True, index=True)
    # Original Position this one was swapped from
    swap_position_id: int = Column(Integer, ForeignKey("position.id"), index=True)
    swap_transaction_id: int = Column(Integer)
    user_id: int = Column(Integer)

    ended_at: datetime = Column(DateTime)
    started_at: datetime = Column(DateTime, nullable=False)
    __tablename__ = "position"


class ClientTransaction(Base):
    id: int = Column(Integer, primary_key=True, index=True)
    amount: Decimal = Column(Numeric(precision=32, scale=18))
    coin_id: int = Column(Integer)
    cost_basis_usd: Decimal = Column(Numeric(precision=32, scale=18))
    transaction_id: int = Column(Integer)
    user_id: int = Column(Integer)

    reviewed_at: datetime = Column(DateTime, nullable=True)
    created_at: datetime = Column(DateTime, nullable=False, server_default=func.now())
    __tablename__ = "clienttransaction"


class TransactionManager:
    def __init__(self, db):
        self.db = db

    def add_model(self, model_instance):
        self.db.add(model_instance)
        self.db.commit()
        self.db.refresh(model_instance)
        return model_instance

    def add_with_transfers(
        self, wallet: Wallet, transactions_to_transfers: dict[Transaction : list[Transfer]]
    ):
        transfers_to_add = []
        for transaction, transfers in transactions_to_transfers.items():
            if (
                not transfers
                and transaction.signer_account.address.casefold() != wallet.address.casefold()
                or self.tx_hash_exists(transaction.network, transaction.tx_hash)
            ):
                # Transaction does not involve a saved Wallet
                continue

            coin_amounts = {}
            transaction = self.add_model(transaction)
            transaction_transfers = []
            for transfer in transfers:
                # Combine transfer amounts if they have the same asset and accounts
                for existing_transfer in transaction_transfers:
                    if existing_transfer.asset_account == transfer.asset_account and {
                        existing_transfer.from_account,
                        existing_transfer.to_account,
                    } == {transfer.from_account, transfer.to_account}:
                        existing_transfer.amount += transfer.amount * (
                            1 if transfer.is_deposit else -1
                        )
                        break
                else:
                    transfer.amount *= 1 if transfer.is_deposit else -1
                    transfer.transaction_id = transaction.id
                    transaction_transfers.append(transfer)

            for transfer in transaction_transfers:
                # Check for 0-sum Coin amount
                address = transfer.asset_account.address
                if address not in coin_amounts and transfer.amount:
                    coin_amounts[address] = transfer.amount
                else:
                    coin_amounts[address] += transfer.amount

            transfers_to_add += list(
                transfer
                for transfer in transaction_transfers
                if coin_amounts[transfer.asset_account.address].normalize() != Decimal(0)
            )

        for transfer in transfers_to_add:
            amount = transfer.amount
            if amount > 1 and not transfer.to_account.is_wallet:
                to_account = transfer.to_account
                transfer.to_account = transfer.from_account
                transfer.from_account = to_account
            elif amount < 0 and not transfer.from_account.is_wallet:
                from_account = transfer.from_account
                transfer.from_account = transfer.to_account
                transfer.to_account = from_account
            transfer.amount = abs(amount)

        self.db.add_all(transfers_to_add)
        self.db.commit()

    def tx_hash_exists(self, network, tx_hash) -> bool:
        return self.db.query(
            exists().where(Transaction.network_id == network.id, Transaction.tx_hash == tx_hash)
        ).scalar()


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "clienttransaction",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("amount", sa.Numeric(precision=32, scale=18), nullable=True),
        sa.Column("coin_id", sa.Integer(), nullable=True),
        sa.Column("cost_basis_usd", sa.Numeric(precision=32, scale=18), nullable=True),
        sa.Column("transaction_id", sa.Integer(), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("reviewed_at", sa.DateTime(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("transacted_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"]),
        sa.ForeignKeyConstraint(["transaction_id"], ["transaction.id"]),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_clienttransaction_id"), "clienttransaction", ["id"], unique=False)
    op.create_index(
        op.f("ix_clienttransaction_transaction_id"),
        "clienttransaction",
        ["transaction_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_clienttransaction_user_id"), "clienttransaction", ["user_id"], unique=False
    )
    op.alter_column("transaction", "tx_hash", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###

    session = Session(bind=op.get_bind())

    # Fix Solana Account address
    solana_account = (
        session.query(Account)
        .filter(Account.address == "2cs7tzxrbhqcx3ncxmsgqcwupnaqebqpaqneavu1yrbo")
        .first()
    )
    if solana_account:
        solana_account.address = "2cS7tZxRBhqcx3ncxmSGqCWuPnAqEbQpAqNEaVU1yRBo"
        session.commit()

    cheqd_network = Network(name="Cheqd")
    session.add(cheqd_network)
    chia_network = Network(name="Chia")
    session.add(chia_network)
    fiat_network = Network(name="Fiat")
    session.add(fiat_network)
    session.commit()
    session.refresh(fiat_network)
    fiat_wallet = Wallet(address="chasebank", name="Chase Bank", networks=[fiat_network])
    session.add(fiat_wallet)
    session.commit()
    session.refresh(fiat_wallet)
    transaction_manager = TransactionManager(session)

    btc_network = session.query(Network).filter(Network.name == "Bitcoin").one()
    canto_network = session.query(Network).filter(Network.name == "Canto").one()
    cheqd_network = session.query(Network).filter(Network.name == "Cheqd").one()
    chia_network = session.query(Network).filter(Network.name == "Chia").one()
    cosmos_network = session.query(Network).filter(Network.name == "Cosmos").one()
    eth_network = session.query(Network).filter(Network.name == "Ethereum").one()
    osmosis_network = session.query(Network).filter(Network.name == "Osmosis").one()
    polkadot_network = session.query(Network).filter(Network.name == "Polkadot").one()
    polygon_network = session.query(Network).filter(Network.name == "Matic").one()
    solana_network = session.query(Network).filter(Network.name == "Solana").one()
    tezos_network = session.query(Network).filter(Network.name == "Tezos").one()

    # Copy position to client_transaction
    positions = session.query(Position).filter(Position.is_active == True).all()
    tx_hash_counter = 0
    for position in positions:
        # Create Transaction and Transfer
        network = None
        ticker = position.coin.ticker.casefold()
        if position.coin.uid.lower() == "dollar":
            network = fiat_network
        elif ticker in ("aleph", "dfyn", "eth", "route", "usdc", "wbtc"):
            network = eth_network
        elif ticker == "btc":
            network = btc_network
        elif ticker == "canto":
            network = canto_network
        elif ticker == "cheq":
            network = cheqd_network
        elif ticker == "xch":
            network = chia_network
        elif ticker == "atom":
            network = cosmos_network
        elif ticker == "osmo":
            network = osmosis_network
        elif ticker == "dot":
            network = polkadot_network
        elif ticker == "matic":
            network = polygon_network
        elif ticker == "route":
            network = polygon_network
        elif ticker == "sol":
            network = solana_network
        elif ticker == "xtz":
            network = tezos_network

        if network is None:
            raise Exception(f"No network with ticker {ticker} found")

        wallet = (
            session.query(Wallet)
            .filter(not_(Wallet.name.contains("Chainge")), Wallet.networks.contains(network))
            .first()
        )

        if not wallet:
            wallet = Wallet(
                address=f"{ticker}_client_deposit_change_me",
                name=f"{network.name} - Update Me",
                networks=[network],
            )
            session.add(wallet)
            session.commit()
            session.refresh(wallet)
        wallet_account = (
            session.query(Account)
            .filter(Account.address == wallet.address, Account.network_id == network.id)
            .first()
        )
        if not wallet_account:
            wallet_account = Account(
                address=wallet.address, network_id=network.id, wallet_id=wallet.id
            )
            session.add(wallet_account)
            session.commit()
            session.refresh(wallet_account)

        transaction_id = position.swap_transaction_id or None
        if not transaction_id:
            tx_hash = f"temp-client-deposit-{tx_hash_counter}"
            transaction = Transaction(
                action="loan",
                confirmed_at=position.started_at,
                is_deposit=True,
                network=network,
                signer_account_id=wallet_account.id,
                tx_hash=tx_hash,
                wallet_id=wallet.id,
            )
            tx_hash_counter += 1
            asset_account = (
                session.query(Account)
                .filter(
                    func.lower(Account.ticker_symbol) == ticker,
                    Account.network_id == network.id,
                )
                .first()
            )
            if not asset_account:
                asset_account = Account(
                    address="-1", network_id=network.id, ticker_symbol=position.coin.ticker
                )
                session.add(asset_account)
                session.commit()

            from_account = (
                session.query(Account)
                .filter(Account.address == tx_hash, Account.network_id == network.id)
                .first()
            )
            if not from_account:
                from_account = Account(address=tx_hash, network_id=network.id)
                session.add(from_account)
                session.commit()
                session.refresh(from_account)

            transfer = Transfer(
                asset_account=asset_account,
                amount=position.amount,
                from_account=from_account,
                to_account=wallet_account,
            )

            transaction_manager.add_with_transfers(wallet, {transaction: [transfer]})
            session.refresh(transaction)
            transaction_id = transaction.id

        if not position.swap_position_id:
            # Skip adding the swap Positions as they are running totals that will be calculated dynamically
            client_transaction = ClientTransaction(
                amount=position.amount,
                coin_id=int(position.coin_id),
                cost_basis_usd=position.cost_basis_usd,
                reviewed_at=position.started_at,
                created_at=position.started_at,
                transaction_id=transaction_id,
                user_id=position.user_id,
            )
            session.add(client_transaction)
    session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_clienttransaction_user_id"), table_name="clienttransaction")
    op.drop_index(op.f("ix_clienttransaction_transaction_id"), table_name="clienttransaction")
    op.drop_index(op.f("ix_clienttransaction_id"), table_name="clienttransaction")
    op.drop_table("clienttransaction")
    # ### end Alembic commands ###
