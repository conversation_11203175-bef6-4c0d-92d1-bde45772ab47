"""Go through client transactions calculating their cost basis."""

import logging
from decimal import Decimal

from sqlalchemy.sql.expression import select

from common.psql import SessionLocal
from stabletech.client.models import ClientTransaction
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Account, Transaction, Transfer
from stabletech.utils.get_prices import get_coingecko_prices

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def calculate_swap_cost_basis(transaction: Transaction) -> Decimal | None:
    """Return cost basis in USD for a swap or None in case of failure."""
    if transaction.action != TransactionAction.swap:
        logger.warning("Expected swap transaction, got %r", transaction.action)
        return None

    accounts_set: set[Account] = set()
    from_transfer = None
    from_asset_amount = Decimal(0)
    to_asset_amount = Decimal(0)

    for transfer in transaction.transfers:
        asset_account = transfer.asset_account
        accounts_set.add(asset_account)
        if transfer.is_deposit:
            to_asset_amount += transfer.amount
        elif transfer.is_withdrawal:
            from_transfer = transfer
            from_asset_amount += transfer.amount

    if len(accounts_set) != 2:
        logger.warning(
            "Expected only 2 assets for swap transaction %d, got %d",
            transaction.id,
            len(accounts_set),
        )
        return None
    if not from_transfer:
        logger.warning("Missing from_transfer for swap transaction: %r", transaction)
        return None
    if (price := get_coingecko_prices(from_transfer, transaction.confirmed_at)) is not None:
        from_transfer.asset_price_usd = price
    else:
        return None

    cost_basis_usd = from_asset_amount * from_transfer.asset_price_usd / to_asset_amount
    return cost_basis_usd


def main():
    total_processed_count = 0

    db = SessionLocal()

    while True:
        exists_unpriced_transfer = (
            select(1)
            .where(Transfer.transaction_id == Transaction.id, Transfer.asset_price_usd == None)
            .exists()
        )

        missing_cost_basis_batch: list[ClientTransaction] = (
            db.query(ClientTransaction)
            .join(Transaction, Transaction.id == ClientTransaction.transaction_id)
            .where(
                ClientTransaction.cost_basis_usd == None,
                Transaction.action == TransactionAction.swap,  # Only swaps have cost basis
                ~exists_unpriced_transfer,  # Need to price transfers first
            )
            .order_by(Transaction.confirmed_at.desc())
            .limit(10)
            .all()
        )
        if len(missing_cost_basis_batch) == 0:
            logger.info("Processed %d client transactions cost basis", total_processed_count)
            logger.info("No more client transactions cost basis to process, exiting")
            break

        iteration_processed_count = 0
        for client_transaction in missing_cost_basis_batch:
            transaction = client_transaction.transaction
            cost_basis_usd = calculate_swap_cost_basis(transaction)
            if cost_basis_usd is None:
                logger.error("Failed to calculate cost basis for transaction: %r", transaction)
                continue
            client_transaction.cost_basis_usd = cost_basis_usd
            iteration_processed_count += 1

        db.commit()
        total_processed_count += iteration_processed_count
        if iteration_processed_count == 0:
            logger.info("Unable to process client transactions cost basis, exiting")
            exit(1)


if __name__ == "__main__":
    main()
