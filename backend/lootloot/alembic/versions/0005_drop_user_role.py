"""Drop user role

Revision ID: 0005
Revises: 0004
Create Date: 2023-12-16 09:18:35.436015

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0005"
down_revision = "0004"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "role")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user",
        sa.Column(
            "role",
            sa.VARCHAR(length=5),
            server_default=sa.text("'user'::character varying"),
            autoincrement=False,
            nullable=False,
        ),
    )
    # ### end Alembic commands ###
