locals {
  # We have to use dashes instead of dots in the access log bucket, because that bucket is not a website
  website_domain_name_dashed = replace(var.website_domain_name, ".", "-")
  access_log_kms_keys        = var.access_logs_kms_key_name == "" ? [] : [var.access_logs_kms_key_name]
  website_kms_keys           = var.website_kms_key_name == "" ? [] : [var.website_kms_key_name]
}

# --------------------------------------------------------------------------------------
# CREATE THE WEBSITE BUCKET
# --------------------------------------------------------------------------------------

resource "google_storage_bucket" "ui" {
  name          = var.website_domain_name
  location      = var.website_location
  storage_class = var.website_storage_class

  uniform_bucket_level_access = true

  versioning {
    enabled = var.enable_versioning
  }

  website {
    main_page_suffix = var.index_page
    not_found_page   = var.not_found_page
  }

  force_destroy = var.force_destroy_website

  dynamic "encryption" {
    for_each = local.website_kms_keys
    content {
      default_kms_key_name = encryption.value
    }
  }

  labels = var.custom_labels
  logging {
    log_bucket        = google_storage_bucket.access_logs.name
    log_object_prefix = var.access_log_prefix != "" ? var.access_log_prefix : local.website_domain_name_dashed
  }
}

# make bucket public
resource "google_storage_bucket_iam_member" "default" {
  bucket = google_storage_bucket.ui.name
  role   = "roles/storage.objectViewer"
  member = "allUsers"
}

# --------------------------------------------------------------------------------------
# CREATE A SEPARATE BUCKET TO STORE ACCESS LOGS
# --------------------------------------------------------------------------------------

resource "google_storage_bucket" "access_logs" {
  # Use the dashed domain name
  name          = "${local.website_domain_name_dashed}-logs"
  location      = var.website_location
  storage_class = var.website_storage_class

  force_destroy = var.force_destroy_access_logs_bucket

  dynamic "encryption" {
    for_each = local.access_log_kms_keys
    content {
      default_kms_key_name = encryption.value
    }
  }

  lifecycle_rule {
    action {
      type = "Delete"
    }

    condition {
      age = var.access_logs_expiration_time_in_days
    }
  }
  labels = var.custom_labels
}

# --------------------------------------------------------------------------------------
# GRANT WRITER ACCESS TO GOOGLE ANALYTICS
# --------------------------------------------------------------------------------------

resource "google_storage_bucket_acl" "analytics_write" {
  bucket = google_storage_bucket.access_logs.name

  # The actual identity is '<EMAIL>', but
  # we're required to prefix that with the type of identity
  role_entity = ["WRITER:<EMAIL>"]
}

# --------------------------------------------------------------------------------------
# OUTPUTS
# --------------------------------------------------------------------------------------

output "bucket" {
  description = "The bucket created for the static website"
  value       = google_storage_bucket.ui
}
