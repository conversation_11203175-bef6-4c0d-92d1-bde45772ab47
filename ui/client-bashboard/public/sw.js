/* global self */
// Originally from: https://github.com/NekR/self-destroying-sw
// Service Worker that only unregisters itself and reloads the page.
// Current code doesn't registers any service worker. This is solely for
// uninstalling the old service worker, so it doesn't prevent the new
// app versions from being used by users that already had the old
// service worker.

self.addEventListener("install", function () {
  self.skipWaiting();
});

self.addEventListener("activate", function () {
  self.registration
    .unregister()
    .then(function () {
      return self.clients.matchAll();
    })
    .then(function (clients) {
      for (const client of clients) client.navigate(client.url);
    });
});
