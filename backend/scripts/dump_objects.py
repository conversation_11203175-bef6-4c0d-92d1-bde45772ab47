"""Dump objects from the database with their dependencies.

Example usage:
    # Edit USER_ID and object_list query (container)
    vim scripts/dump_objects.py

    # Dump objects to dump.json (container)
    python scripts/dump_objects.py dump.json

    # Download dump.json from container (local)
    kubectl cp default/api-7946b9d79b-7r3mz:dump.json dump.json

    # Copy dump.json to the container of your local cluster (local)
    kubectl config use-context minikube
    kubectl cp dump.json default/api-59d987d6dc-g94vs:dump.json

    # Load dump.json to the database (container)
    python scripts/load_objects.py dump.json
"""

import argparse
import dataclasses
import inspect
import json
import sys
from datetime import datetime
from decimal import Decimal
from typing import Any, Literal, TextIO

import sentry_sdk
from sqlalchemy import Column

import stabletech.indexer.models
from common.models import BaseModel
from common.psql import SessionLocal
from stabletech.indexer.models import (
    Account,
    Bridge,
    Coin,
    Exchange,
    Farm,
    LendingProtocol,
    Network,
    Transaction,
    Wallet,
)

# Disable sentry
sentry_sdk.init(None)

db = SessionLocal()

# User that will get assigned to created resources
USER_ID = 1


def is_model(value: Any) -> bool:
    return inspect.isclass(value) and issubclass(value, BaseModel)


models_by_table_name = {
    model.__tablename__: model
    for _, model in inspect.getmembers(stabletech.indexer.models, is_model)
}


@dataclasses.dataclass(kw_only=True)
class Value:
    kind: Literal["Value"] = "Value"
    module: str
    constructor: str
    method: str | None = None
    args: list[Any] | None = None
    kwargs: dict[str, Any] | None = None


@dataclasses.dataclass(kw_only=True)
class Ref:
    kind: Literal["Ref"] = "Ref"
    module: str
    model: str
    filter_by: "dict[str, int | str| Ref]"


# (table name, id) -> Value
ValuesDict = dict[tuple[str, int], Value]
AnySerializable = Ref | Value | int | str | bool | float | None


def add_obj(dump: ValuesDict, value: BaseModel) -> tuple[ValuesDict, Value]:
    model = type(value)
    columns = model.__table__.columns

    val = dump.get((model.__tablename__, value.id))
    if val is not None:
        return dump, val

    kwargs: dict[str, Any] = {}
    for c in columns.values():
        if c.name == "id":
            continue
        dump, kwargs[c.name] = add_column_value(dump, c, getattr(value, c.name))

    val = Value(
        module=model.__module__,
        constructor=model.__qualname__,
        kwargs=kwargs,
    )
    dump = {**dump, (model.__tablename__, value.id): val}
    return dump, val


def add_column_value(
    dump: ValuesDict, column: Column, value: Any
) -> tuple[ValuesDict, AnySerializable]:
    if value is not None and column.foreign_keys:
        return add_fk(dump, column.foreign_keys, value)
    return dump, get_simple_value(value)


def add_fk(dump: ValuesDict, foreign_keys: set, fk_id: int) -> tuple[ValuesDict, Ref | int]:
    if len(foreign_keys) > 1:
        raise NotImplementedError("Cannot handle multiple foreign keys")
    for fk in foreign_keys:
        break
    table = fk.column.table
    if table.name == "user":
        return dump, USER_ID
    model = models_by_table_name[table.name]
    obj = db.query(model).filter_by(id=fk_id).one()
    return add_obj_ref(dump, obj)


def add_obj_ref(dump: ValuesDict, obj) -> tuple[ValuesDict, Ref]:
    model = type(obj)
    if (model.__tablename__, obj.id) not in dump:
        dump, _ = add_obj(dump, obj)
    dump, filter_by = get_obj_filter_by(dump, obj)
    return dump, Ref(
        module=model.__module__,
        model=model.__qualname__,
        filter_by=filter_by,
    )


def get_obj_filter_by(dump: ValuesDict, obj) -> tuple[ValuesDict, dict[str, int | str | Ref]]:
    match obj:
        case Coin():
            return dump, {"uid": obj.uid}
        case Network() | Exchange() | Bridge() | Farm() | LendingProtocol():
            return dump, {"name": obj.name}
        case Wallet():
            user_id = None if obj.user_id is None else USER_ID
            return dump, {"address": obj.address, "user_id": user_id}
        case Account():
            dump, network_ref = add_obj_ref(dump, obj.network)
            return dump, {"address": obj.address, "network_id": network_ref}
        case Transaction():
            dump, network_ref = add_obj_ref(dump, obj.network)
            return dump, {"network_id": network_ref, "tx_hash": obj.tx_hash}
        case _:
            raise NotImplementedError(f"Cannot get filter for {obj!r}")


def get_simple_value(x) -> Value | int | str | bool | float | None:
    if x is None:
        return None

    constructor = type(x)
    if constructor in (int, str, bool, float):
        return x

    val = Value(
        module=constructor.__module__,
        constructor=constructor.__qualname__,
    )
    match x:
        case datetime():
            val.method = "fromisoformat"
            val.args = [x.isoformat()]
        case Decimal():
            val.args = [str(x)]
        case dict():
            val.kwargs = x
        case _:
            raise NotImplementedError(f"Cannot get value for {x!r}")
    return val


class EnhancedJSONEncoder(json.JSONEncoder):
    @staticmethod
    def dict_factory(o):
        return {k: v for (k, v) in o if v is not None}

    def default(self, o):
        if dataclasses.is_dataclass(o):
            return dataclasses.asdict(o, dict_factory=EnhancedJSONEncoder.dict_factory)
        return super().default(o)


def dump_objects(file: TextIO):
    # Objects that are going to be dumped
    object_list: list = (
        db.query(stabletech.indexer.models.Transfer)
        .where(
            stabletech.indexer.models.Transfer.transaction_id
            == stabletech.indexer.models.Transaction.id,
            stabletech.indexer.models.Transaction.action
            == stabletech.indexer.models.TransactionAction.spend.value,
        )
        .all()
    )

    dump: ValuesDict = {}
    for obj in object_list:
        dump, _ = add_obj(dump, obj)

    json.dump(list(dump.values()), file, cls=EnhancedJSONEncoder)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "file",
        type=argparse.FileType("w", encoding="utf_8"),
        nargs="?",
        default=sys.stdout,
        help="Path to output file, defaults to stdout",
    )
    args = parser.parse_args()
    dump_objects(args.file)
