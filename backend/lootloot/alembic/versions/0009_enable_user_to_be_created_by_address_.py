"""Enable User to be created by address, allow user to be created without email

Revision ID: 0009
Revises: 0008
Create Date: 2024-01-25 02:32:46.388987

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0009"
down_revision = "0008"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user", sa.Column("origin", sa.String(), nullable=False))
    op.add_column("user", sa.Column("address_uri", sa.String(), nullable=True))
    op.alter_column("user", "email", existing_type=sa.VARCHAR(), nullable=True)
    op.drop_index("ix_user_email", table_name="user")
    # For some reason for the new unique constraints, the constraint name is not being auto generated, so
    # manually generated following the pattern
    op.create_unique_constraint("user_email_key", "user", ["email"])
    op.create_unique_constraint("user_address_uri_key", "user", ["address_uri"])
    op.drop_constraint("userwallet_user_id_name_key", "userwallet", type_="unique")
    op.create_unique_constraint(
        "userwallet_network_group_address_key", "userwallet", ["network_group", "address"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("userwallet_network_group_address_key", "userwallet", type_="unique")
    op.create_unique_constraint(
        "userwallet_user_id_name_key", "userwallet", ["user_id", "name"]
    )
    op.drop_constraint("user_email_key", "user", type_="unique")
    op.drop_constraint("user_address_uri_key", "user", type_="unique")
    op.create_index("ix_user_email", "user", ["email"], unique=False)
    op.alter_column("user", "email", existing_type=sa.VARCHAR(), nullable=False)
    op.drop_column("user", "address_uri")
    op.drop_column("user", "origin")
    # ### end Alembic commands ###
