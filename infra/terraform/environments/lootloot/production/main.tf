resource "google_dns_managed_zone" "public" {
  name     = "lootloot-farm"
  dns_name = "lootloot.farm."

  dnssec_config {
    state = "on"
  }
}

module "lootloot" {
  source = "../modules/lootloot"

  namespace        = var.namespace
  env_name         = "production"
  dns_managed_zone = google_dns_managed_zone.public
  api_replicas     = 2
  pg_disk_size     = 15
  pg_tier          = "db-custom-1-3840"

  auth_allowed_issuers = {
    "https://accounts.google.com" = "************-4rebtccromkrgj5k4fd3cla6l9inuons.apps.googleusercontent.com"
  }
  secrets = local.secrets

  release = local.release
}

resource "google_storage_bucket_iam_member" "github_actions_deployer" {
  bucket = module.lootloot.webapp_bucket_name
  role   = "projects/${local.project_id}/roles/StorageGitHubWebDeployer"
  member = "principal://iam.googleapis.com/projects/${data.google_project.this.number}/locations/global/workloadIdentityPools/github-actions/subject/repo:Stable-Tech/temet:environment:${var.namespace}"
}
