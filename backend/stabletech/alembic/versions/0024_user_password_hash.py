"""User password_hash

Revision ID: 0024
Revises: 0023
Create Date: 2023-05-27 07:53:15.032093

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0024"
down_revision = "0023"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("transaction", "tx_hash", existing_type=sa.VARCHAR(), nullable=True)
    op.add_column("user", sa.Column("password_hash", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "password_hash")
    op.alter_column("transaction", "tx_hash", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###
