import { UnifiedAccount } from "@/auth/accounts";
import { useGetPassword } from "@/auth/password";
import { PasswordInput } from "@/auth/password-input";
import { unifiedSign } from "@/auth/unified-wallet/sign";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { getErrorMessage } from "@/lib/error";
import WalletKitClient, { WalletKitTypes } from "@reown/walletkit";
import { Transaction } from "@solana/web3.js";
import { useMutation } from "@tanstack/react-query";
import { getSdkError } from "@walletconnect/utils";
import base58 from "bs58";
import { useCallback, useEffect, useState } from "react";

const EMPTY_REQUESTS: readonly WalletKitTypes.SessionRequest[] = [];

function useSessionRequests(getWalletKit: () => Promise<WalletKitClient>) {
  const [requests, setRequests] = useState(EMPTY_REQUESTS);

  const removeRequest = useCallback((id: number) => {
    setRequests((prev) => prev.filter((request) => request.id !== id));
  }, []);

  useEffect(() => {
    function handleSessionRequest(request: WalletKitTypes.SessionRequest) {
      setRequests((prev) => [...prev, request]);
    }

    async function setupRequestHandler() {
      const walletKit = await getWalletKit();
      walletKit.on("session_request", handleSessionRequest);
      return function cleanup() {
        walletKit.removeListener("session_request", handleSessionRequest);
      };
    }

    const setupPromise = setupRequestHandler();
    return () => {
      void setupPromise.then((cleanup) => {
        cleanup();
      });
    };
  });

  return [requests, removeRequest] as const;
}

interface AcceptanceParams {
  password: Promise<CryptoKey> | undefined;
  request: WalletKitTypes.SessionRequest;
}

interface Props {
  account: UnifiedAccount | undefined;
  getWalletKit: () => Promise<WalletKitClient>;
}

export function SessionRequest(props: Props) {
  const { account, getWalletKit } = props;
  const [requests, removeRequest] = useSessionRequests(getWalletKit);

  const acceptance = useMutation({
    async mutationFn(params: AcceptanceParams) {
      const password = await params.password;
      const { request } = params;
      if (!account) throw new Error("No account selected");
      const transaction = extractTransaction(request.params.request);
      const decoder = password ? { kind: "password" as const, password } : undefined;
      const signature = await unifiedSign(account, transaction.serializeMessage(), decoder);
      if (!(signature instanceof Uint8Array)) throw new Error("Invalid signature");

      const walletKit = await getWalletKit();
      await walletKit.respondSessionRequest({
        topic: request.topic,
        response: {
          jsonrpc: "2.0",
          id: request.id,
          result: {
            signature: base58.encode(signature),
          },
        },
      });
      removeRequest(request.id);
    },
  });

  const rejection = useMutation({
    async mutationFn(request: WalletKitTypes.SessionRequest) {
      const walletKit = await getWalletKit();
      await walletKit.respondSessionRequest({
        topic: request.topic,
        response: {
          jsonrpc: "2.0",
          id: request.id,
          error: getSdkError("USER_REJECTED"),
        },
      });
    },
    onSettled(_data, _error, request) {
      removeRequest(request.id);
    },
  });

  const request = requests[0];
  const isPending = acceptance.isPending || rejection.isPending;

  const getPassword = useGetPassword();

  let content;

  if (request) {
    const { origin, validation, isScam } = request.verifyContext.verified;
    const { method } = request.params.request;

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      const formData = new FormData(e.currentTarget);
      const password = getPassword(formData);
      acceptance.mutate({ request, password });
    };

    content = (
      <>
        <div className="grid grid-cols-[auto_auto] gap-x-2 gap-y-1">
          <span className="text-right">Origin:</span>
          <span>{origin}</span>
          <span className="text-right">Validation:</span>
          <span>{validation}</span>
          <span className="text-right">Scam:</span>
          <span>{isScam ? "Yes" : "No"}</span>
          <span className="text-right">Method:</span>
          <code className="text-primary">{method}</code>
        </div>
        <form className="mt-2 grid w-full justify-items-center gap-4" onSubmit={handleSubmit}>
          {account?.provider === "local" && <PasswordInput disabled={isPending} />}
          {acceptance.error && <AlertError>{getErrorMessage(acceptance.error)}</AlertError>}
          <div className="grid grid-cols-2 gap-4">
            <Button
              disabled={isPending}
              variant="outline"
              onClick={() => {
                rejection.mutate(request);
              }}
            >
              Reject
            </Button>
            <Button type="submit" disabled={isPending} variant="outline">
              Accept
            </Button>
          </div>
        </form>
      </>
    );
  } else {
    content = <p className="text-sm opacity-50">No pending request</p>;
  }

  return (
    <section className="my-4 grid w-full max-w-md min-w-0 justify-items-center gap-2">
      <h3 className="text-lg">Requests</h3>
      {content}
    </section>
  );
}

interface BaseRequest {
  method: string;
  params: unknown;
  expiryTimestamp?: number;
}

interface RequestSolanaSignTransaction extends BaseRequest {
  method: "solana_signTransaction";
  params: { transaction: string };
}

function extractTransaction(request: BaseRequest) {
  if (request.method !== "solana_signTransaction")
    throw new Error(`Unsupported method ${request.method}`);

  const base64EncodedTx = (request as RequestSolanaSignTransaction).params.transaction;

  // eslint-disable-next-line unicorn/prefer-code-point
  const transactionBytes = Uint8Array.from(atob(base64EncodedTx), (c) => c.charCodeAt(0));
  return Transaction.from(transactionBytes);
}
