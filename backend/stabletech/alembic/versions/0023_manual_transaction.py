"""Manual Transaction

Revision ID: 0023
Revises: 0022
Create Date: 2023-05-09 01:03:56.606567

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0023"
down_revision = "0022"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("transaction", "json_data", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("transaction", "json_data", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###
