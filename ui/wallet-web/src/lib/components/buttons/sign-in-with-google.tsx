import googleLogo from "@/lib/components/assets/google-g.svg";

type ButtonProps = React.ComponentPropsWithoutRef<"button">;

/**
 * Google Sign-In button following Google's branding guidelines
 * https://developers.google.com/identity/branding-guidelines#custom-button
 */
export function SignInWithGoogle(props: Omit<ButtonProps, "children">) {
  return (
    <button
      className="flex items-center gap-[10px] rounded-full border border-[#747775] bg-white px-[11px] py-[9px] hover:bg-gray-100 focus:ring-3 focus:ring-gray-300 focus:ring-offset-1 focus:outline-hidden dark:border-[#8E918F] dark:bg-[#131314] dark:ring-offset-gray-900 dark:hover:bg-gray-800 dark:focus:ring-gray-600"
      {...props}
    >
      <img src={googleLogo} alt="" width="20" height="20" />
      <span className="text-sm leading-none font-medium text-[#1F1F1F] dark:text-[#E3E3E3]">
        Sign in with Google
      </span>
    </button>
  );
}
