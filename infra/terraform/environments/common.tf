# Common configurations shared by all cloud environments

data "google_secret_manager_secret_version" "secret" {
  secret = var.namespace
}

locals {
  project_id = "${var.project_name}-${var.project_uid}"

  # Parse JSON secret from Google Secret Manager into locals
  secrets = jsondecode(data.google_secret_manager_secret_version.secret.secret_data)

  release = substr(var.TFC_CONFIGURATION_VERSION_GIT_COMMIT_SHA, 0, 7)
}

provider "google" {
  project = local.project_id
  region  = var.region
  zone    = "${var.region}-${var.zone}"
}

data "google_client_config" "default" {}

data "google_container_cluster" "this" {
  name     = "${data.google_client_config.default.project}-gke"
  location = data.google_client_config.default.zone
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.this.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.this.master_auth[0].cluster_ca_certificate)
}

data "google_project" "this" {}
