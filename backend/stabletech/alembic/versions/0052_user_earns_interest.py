"""User earns interest

Revision ID: 0052
Revises: 0051
Create Date: 2024-06-03 15:51:48.740231

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0052"
down_revision = "0051"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user", sa.<PERSON>("earns_interest", sa.<PERSON>(), server_default="true", nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "earns_interest")
    # ### end Alembic commands ###
