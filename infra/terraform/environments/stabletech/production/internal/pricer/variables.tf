variable "namespace" {
  description = "Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "python_runner_image" {
  description = "python-runner fully qualified image name"
  type        = string
  nullable    = false
}

variable "secrets" {
  type = object({
    coingecko_api_key   = string
    postgresql_host     = string
    postgresql_user     = string
    postgresql_password = string
  })
  nullable  = false
  sensitive = true
}

variable "replicas" {
  description = "Number of Pods to run"
  type        = number
  default     = 2
  nullable    = false
}
