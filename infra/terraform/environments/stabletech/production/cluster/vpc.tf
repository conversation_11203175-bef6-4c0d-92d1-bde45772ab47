provider "google" {
  project = local.project_id
  region  = var.region
}

# VPC
resource "google_compute_network" "vpc" {
  name                    = "${local.project_id}-vpc"
  auto_create_subnetworks = "false"
}

# Subnet
resource "google_compute_subnetwork" "subnet" {
  name          = "${local.project_id}-subnet"
  region        = var.region
  network       = google_compute_network.vpc.name
  ip_cidr_range = "10.0.0.0/16"

  secondary_ip_range {
    range_name    = "services-range"
    ip_cidr_range = "********/16"
  }

  secondary_ip_range {
    range_name    = "pod-ranges"
    ip_cidr_range = "********/16"
  }
}
