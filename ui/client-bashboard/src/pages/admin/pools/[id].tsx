import Spinner from "@/@core/components/spinner";
import { ButtonCopy } from "@/components/ButtonCopy";
import Link from "@/components/Link";
import { SnackSuccess } from "@/components/SnackSuccess";
import { Toast, Toaster, useToast } from "@/components/Toast";
import TransactionsTable, {
  useTransfersMapperTickers,
} from "@/components/admin/TransactionsTable";
import {
  currencyFormatter,
  diffCurrencyFormatter,
  ellipsisDecimals,
  numberFormatterMax20D,
} from "@/helpers/formatters";
import { usePathId } from "@/helpers/routing";
import { useModal } from "@/hooks/modal";
import {
  useIndexerPoolPriceAlertDeleteMutation,
  useIndexerPoolPriceAlertUpsertMutation,
  useIndexerPoolPriceAlertsQuery,
  useIndexerPoolQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AccountPoolFull, PoolCoinTotal, PoolPriceAlert, Transaction } from "@/types/schemas";
import {
  ExpandMore,
  Notifications,
  NotificationsActive,
  NotificationsOff,
} from "@mui/icons-material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  ClickAwayListener,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  FormGroup,
  Grid,
  IconButton,
  Stack,
  Switch,
  SxProps,
  TextField,
  Typography,
} from "@mui/material";
import { skipToken } from "@reduxjs/toolkit/query";
import { Equal } from "mdi-material-ui";
import { useRouter } from "next/router";
import { useId, useLayoutEffect, useMemo, useState } from "react";

const POSITION_RELATIVE = { position: "relative" };

interface PriceAlertsModalState {
  readonly open: boolean;
  readonly coin: {
    readonly id: number;
    readonly name: string;
    readonly ticker: string;
    readonly latestUsdPrice: string;
  };
  readonly enabled: boolean;
  readonly min: string;
  readonly max: string;
}

const INITIAL_PRICE_ALERTS_MODAL_STATE: PriceAlertsModalState = {
  open: false,
  coin: { id: 0, name: "", ticker: "", latestUsdPrice: "" },
  enabled: false,
  min: "",
  max: "",
};

function Pool() {
  const router = useRouter();
  const [showSuccessSnack, setShowSuccessSnack] = useState(false);

  const accountId = usePathId();
  const { data: poolDataRaw } = useIndexerPoolQuery(accountId != null ? accountId : skipToken, {
    // If the page is not yet generated, router.isFallback will be true
    // initially until getStaticProps() finishes running
    skip: router.isFallback,
  });

  const poolData = useMemo(
    () => (poolDataRaw != null ? processPoolData(poolDataRaw) : null),
    [poolDataRaw],
  );

  const tickersMapper = useTransfersMapperTickers(poolData?.tickers ?? []);

  const priceAlertsQuery = useIndexerPoolPriceAlertsQuery(accountId ?? skipToken);
  const priceAlertsByCoinId = useMemo((): ReadonlyMap<number, PoolPriceAlert> => {
    const mapping = new Map<number, PoolPriceAlert>();
    if (priceAlertsQuery.data != null) {
      for (const priceAlert of priceAlertsQuery.data) {
        mapping.set(priceAlert.coinId, priceAlert);
      }
    }
    return mapping;
  }, [priceAlertsQuery.data]);

  const priceAlertsModal = useModal(INITIAL_PRICE_ALERTS_MODAL_STATE);

  const [toastProps, toast] = useToast();

  if (accountId == null)
    return (
      <Stack direction="row" alignItems="center" gap={4}>
        <Link href="/admin/pools" display="flex" color="text.primary">
          <ArrowBackIcon fontSize="large" />
        </Link>
        <Alert severity="error">Invalid account ID</Alert>
      </Stack>
    );

  if (poolData == null) return <Spinner />;

  return (
    <Card>
      <CardContent>
        <SnackSuccess
          showSuccessSnack={showSuccessSnack}
          setShowSuccessSnack={setShowSuccessSnack}
        />

        <Grid container gap={6} pt={2}>
          <Grid container item xs={"auto"} alignItems={"center"}>
            <Link href="/admin/pools" display="flex" color="text.primary">
              <ArrowBackIcon fontSize={"large"} />
            </Link>

            {poolData && (
              <Button href={poolData.exchange?.url ?? ""} target="_blank">
                <Typography variant={"h4"} color={"primary"}>
                  {poolData.name}
                </Typography>
              </Button>
            )}
          </Grid>

          <Grid
            container
            item
            xs="auto"
            gap={10}
            display={poolData.kRatio ? undefined : "none"}
            alignItems="center"
            justifyContent="center"
          >
            <Grid item>
              {poolData && poolData.tvl != 0 && (
                <Stack alignItems={"center"}>
                  <Typography color={"primary"}>TVL</Typography>
                  <ButtonCopy textToCopy={poolData.tvl}>
                    <Typography>
                      $ {poolData.tvl && currencyFormatter.format(poolData.tvl)}
                    </Typography>
                  </ButtonCopy>
                </Stack>
              )}
            </Grid>
            <Grid item>
              <Grid item>
                {poolData && poolData.impermanentLossPercent && (
                  <Stack alignItems={"center"}>
                    <Typography color={"primary"}>Imp. Loss</Typography>
                    <ButtonCopy textToCopy={poolData.kRatio}>
                      <Typography>
                        {currencyFormatter.format(poolData.impermanentLossPercent)} %
                      </Typography>
                    </ButtonCopy>
                  </Stack>
                )}
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs position="relative" minWidth="270px" minHeight="56px" mt="-9px">
            <LpTokenWidget
              depositLp={poolData.depositLp}
              sx={{ position: "absolute", right: 0, maxWidth: "270px", zIndex: 1 }}
            />
          </Grid>

          <Grid container item justifyContent={"center"} mt={4}>
            <Stack direction="row" alignItems={"center"} spacing={6} pl={4}>
              <Grid container item xs={"auto"}>
                <Stack alignItems={"center"} spacing={6}>
                  <Typography>
                    {poolData.kRatio ? "Deposit Balance" : "Excess After Withdrawal"}
                  </Typography>
                  <Stack direction={"row"} alignItems={"center"} spacing={6} pl={4}>
                    {poolData.coinTotals.map((coinTotal) => {
                      // Flip signs when the pool is closed to show gain/loss from user's perspective
                      const amountTotal = coinTotal.amountTotal * (poolData.kRatio ? 1 : -1);
                      const priceAlert = priceAlertsByCoinId.get(coinTotal.coin.id);
                      const hasPriceAlert = priceAlert != null;
                      return (
                        <Card
                          key={coinTotal.coin.id}
                          variant={"outlined"}
                          sx={POSITION_RELATIVE}
                        >
                          <CardContent>
                            <Stack alignItems={"center"}>
                              <Typography color={"primary"} fontSize={12} mt={-3} pb={0.5}>
                                <ButtonCopy textToCopy={coinTotal.dollarPrice}>
                                  $ {currencyFormatter.format(coinTotal.dollarPrice)}
                                </ButtonCopy>
                              </Typography>
                              <ButtonCopy textToCopy={amountTotal}>
                                <Typography fontSize={18}>
                                  {currencyFormatter.format(amountTotal)}{" "}
                                  {coinTotal.coin.ticker.toUpperCase()}
                                </Typography>
                              </ButtonCopy>
                              <Typography color={"primary"} fontSize={12} mb={-3} pt={0.5}>
                                <ButtonCopy textToCopy={amountTotal * coinTotal.dollarPrice}>
                                  = ${" "}
                                  {currencyFormatter.format(
                                    amountTotal * coinTotal.dollarPrice,
                                  )}
                                </ButtonCopy>
                              </Typography>
                            </Stack>
                          </CardContent>
                          <Box position="absolute" right={3} top={3}>
                            <IconButton
                              aria-label="Configure price alerts"
                              title="Configure price alerts"
                              size="small"
                              disabled={priceAlertsQuery.isFetching}
                              sx={POSITION_RELATIVE}
                              onClick={() => {
                                priceAlertsModal.setState({
                                  open: true,
                                  coin: coinTotal.coin,
                                  enabled: hasPriceAlert,
                                  min: hasPriceAlert ? priceAlert.minPriceUsd : "",
                                  max: hasPriceAlert ? priceAlert.maxPriceUsd : "",
                                });
                              }}
                            >
                              {priceAlertsQuery.isFetching ? (
                                <Notifications />
                              ) : hasPriceAlert ? (
                                <NotificationsActive color="primary" />
                              ) : (
                                <NotificationsOff color="secondary" />
                              )}
                            </IconButton>
                          </Box>
                        </Card>
                      );
                    })}
                  </Stack>
                </Stack>
              </Grid>

              <Grid container item display={{ xs: poolData.kRatio ? "auto" : "none" }}>
                <Stack alignItems={"center"} spacing={6}>
                  <Typography>Current Balance</Typography>

                  <Stack direction={"row"} alignItems={"center"} spacing={6}>
                    <Equal />

                    {poolData.coinTotals.map((coinTotal) => {
                      const coinDifference = coinTotal.amountCurrent - coinTotal.amountTotal;
                      return (
                        <Card key={coinTotal.coin.id} variant={"outlined"}>
                          <CardContent>
                            <Stack alignItems={"center"}>
                              <Stack direction={"row"} alignItems={"center"}>
                                <ButtonCopy
                                  textToCopy={coinDifference}
                                  color={coinDifference > 0 ? "success" : "error"}
                                  size={"small"}
                                >
                                  {diffCurrencyFormatter.format(coinDifference)}{" "}
                                  {coinTotal.coin.ticker.toUpperCase()}
                                </ButtonCopy>
                              </Stack>

                              <ButtonCopy textToCopy={coinTotal.amountCurrent}>
                                <Typography fontSize={18}>
                                  {currencyFormatter.format(coinTotal.amountCurrent)}{" "}
                                  {coinTotal.coin.ticker.toUpperCase()}
                                </Typography>
                              </ButtonCopy>

                              <Typography color={"primary"} fontSize={12} mb={-3} pt={0.5}>
                                <ButtonCopy
                                  textToCopy={coinTotal.amountCurrent * coinTotal.dollarPrice}
                                >
                                  = ${" "}
                                  {currencyFormatter.format(
                                    coinTotal.amountCurrent * coinTotal.dollarPrice,
                                  )}
                                </ButtonCopy>
                              </Typography>
                            </Stack>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </Stack>
                </Stack>
              </Grid>
            </Stack>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardHeader title={"Transactions"} />
              <CardContent>
                <TransactionsTable
                  transfersMapper={tickersMapper}
                  rows={poolData.transactions}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </CardContent>

      <PriceAlertsModal
        accountId={accountId}
        onClose={priceAlertsModal.close}
        onToast={toast}
        poolName={poolData.name}
        state={priceAlertsModal.state}
      />

      <Toast {...toastProps} />
    </Card>
  );
}

Pool.acl = {
  action: "manage",
  subject: "Pools",
};

export default Pool;

function LpTokenWidget({ depositLp, sx }: { depositLp: number; sx?: SxProps }) {
  const [expanded, setExpanded] = useState(false);

  const [currentLpRaw, setCurrentLpRaw] = useState("");
  const currentLp = Number.parseFloat(currentLpRaw.replaceAll(",", ""));
  const currentLpIsError = currentLpRaw !== "" && !Number.isFinite(currentLp);
  const difference = currentLp - depositLp;

  return (
    <ClickAwayListener
      onClickAway={() => {
        setExpanded(false);
      }}
    >
      <Accordion
        expanded={expanded}
        onChange={(_, value) => {
          setExpanded(value);
        }}
        variant="outlined"
        disableGutters
        sx={sx}
      >
        <AccordionSummary
          expandIcon={<ExpandMore />}
          aria-controls="admin-pools-deposit-lp-panel-content"
          id="admin-pools-deposit-lp-panel-header"
        >
          <Stack gap={1}>
            <Typography color="primary">Deposit LP</Typography>
            <ButtonCopy textToCopy={depositLp} minimal>
              <Typography>
                {ellipsisDecimals(7, numberFormatterMax20D.format(depositLp))}
              </Typography>
            </ButtonCopy>
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          <Stack gap={1}>
            <Typography
              component="label"
              color="primary"
              display="flex"
              flexDirection="column"
              mt={2}
              gap={1}
            >
              Current LP
              <TextField
                size="small"
                variant="standard"
                onChange={(e) => setCurrentLpRaw(e.target.value)}
                value={currentLpRaw}
                helperText={currentLpIsError ? "Invalid number" : "Current amount of LP tokens"}
                error={currentLpIsError}
              />
            </Typography>
            <Typography color="primary" mt={2}>
              Difference
            </Typography>
            <ButtonCopy textToCopy={difference} minimal>
              <Typography>
                {currentLpIsError || currentLpRaw === ""
                  ? "—"
                  : ellipsisDecimals(7, numberFormatterMax20D.format(difference))}
              </Typography>
            </ButtonCopy>
          </Stack>
        </AccordionDetails>
      </Accordion>
    </ClickAwayListener>
  );
}

function PriceAlertsModal({
  accountId,
  onClose,
  onToast,
  poolName,
  state,
}: {
  accountId: number;
  onClose: () => void;
  onToast: Toaster;
  poolName: string;
  state: PriceAlertsModalState;
}) {
  const titleId = `title${useId()}`;

  const [editedEnabled, setEditedEnabled] = useState<boolean | null>(null);
  const enabled = editedEnabled ?? state.enabled;

  const [doDelete, deleteState] = useIndexerPoolPriceAlertDeleteMutation();
  const [doUpsert, upsertState] = useIndexerPoolPriceAlertUpsertMutation();
  const isLoading = deleteState.isLoading || upsertState.isLoading;

  useLayoutEffect(() => {
    if (state.open) {
      // Reset the form on open
      setEditedEnabled(null);
    }
  }, [state.open]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isLoading) return;

    if (!enabled) {
      doDelete({ accountId, coinId: state.coin.id })
        .unwrap()
        .then(() => {
          onClose();
        })
        .catch((error: unknown) => {
          onToast({
            message: `Failed to disable alert: ${getErrorDetail(error)}`,
            severity: "error",
          });
        });
      return;
    }

    const data = new FormData(e.currentTarget);
    const [min, max] = [data.get("min"), data.get("max")].map((value) =>
      typeof value === "string" ? value.trim() : null,
    );
    if (!min || !max) {
      onToast({
        message: "Both min and max values must be set when enabled",
        severity: "error",
      });
      return;
    }

    doUpsert({
      accountId,
      coinId: state.coin.id,
      minPriceUsd: min,
      maxPriceUsd: max,
    })
      .unwrap()
      .then(() => {
        onClose();
      })
      .catch((error: unknown) => {
        onToast({
          message: `Failed to save alert: ${getErrorDetail(error)}`,
          severity: "error",
        });
      });
  };

  return (
    <Dialog
      aria-labelledby={titleId}
      open={state.open}
      onClose={(_, reason) => {
        if (reason === "backdropClick") {
          return; // Do nothing
        }
        onClose();
      }}
      PaperProps={{ component: "form", onSubmit: handleSubmit }}
    >
      <DialogTitle id={titleId}>Price Alert</DialogTitle>
      <DialogContent>
        <Typography gutterBottom>
          <strong>Pool:</strong> {poolName}
        </Typography>
        <Typography gutterBottom>
          <strong>Coin:</strong> {state.coin.name} ({state.coin.ticker.toUpperCase()})
        </Typography>
        <Typography gutterBottom>
          <strong>Coin price:</strong> $ {state.coin.latestUsdPrice}
        </Typography>
        <Typography gutterBottom>
          <strong>Current range:</strong>{" "}
          {state.enabled ? `(${state.min}, ${state.max})` : <em>disabled</em>}
        </Typography>
        <Typography gutterBottom mt={6}>
          <strong>Configure</strong>
        </Typography>
        <FormGroup sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={enabled}
                disabled={isLoading}
                onChange={(_, checked) => {
                  setEditedEnabled(checked);
                }}
              />
            }
            label="Enabled"
          />
        </FormGroup>
        <TextField
          name="min"
          label="MIN"
          disabled={isLoading || !enabled}
          required={enabled}
          defaultValue={state.min}
          size="small"
        />{" "}
        <TextField
          name="max"
          label="MAX"
          disabled={isLoading || !enabled}
          required={enabled}
          defaultValue={state.max}
          size="small"
        />
      </DialogContent>
      <DialogActions>
        <Button color="secondary" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit" color="primary" disabled={isLoading}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function processPoolData(raw: AccountPoolFull) {
  const coinTotals = raw.coinTotals
    .map(processCoinTotal)
    .sort((a, b) => a.coin.ticker.localeCompare(b.coin.ticker));

  // eslint-disable-next-line unicorn/no-array-reduce
  const depositLp = raw.transactions.reduce(reduceDepositLp, 0);

  return {
    ...raw,
    coinTotals,
    depositLp,
    impermanentLossPercent:
      raw.impermanentLossPercent != null
        ? Number.parseFloat(raw.impermanentLossPercent)
        : undefined,
    kRatio: Number.parseFloat(raw.kRatio),
    tvl: Number.parseFloat(raw.tvl),
  };
}

function processCoinTotal(raw: PoolCoinTotal) {
  return {
    ...raw,
    dollarPrice: Number.parseFloat(raw.dollarPrice),
    amountAdded: Number.parseFloat(raw.amountAdded),
    amountCurrent: Number.parseFloat(raw.amountCurrent),
    amountTotal: Number.parseFloat(raw.amountTotal),
  };
}

function reduceDepositLp(value: number, transaction: Transaction): number {
  for (const transfer of transaction.transfers) {
    if (transaction.isDeposit) {
      // Pool deposit, skip transfers withdrawing from our wallets
      if (transfer.isWithdrawal) continue;
    } else {
      // Pool withdrawal, skip transfers depositing into our wallets
      if (!transfer.isWithdrawal) continue;
    }
    const amount = Number.parseFloat(transfer.amount);
    if (!Number.isFinite(amount)) continue;
    if (transaction.isDeposit) {
      value += amount;
    } else {
      value -= amount;
    }
  }
  return value;
}
