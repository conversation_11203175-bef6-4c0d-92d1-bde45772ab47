variable "name" {
  description = "The name of the instance"
  type        = string
  nullable    = false
}

variable "database_version" {
  description = "Instance version"
  type        = string
  nullable    = false
}

variable "disk_size" {
  description = "The disk size of the database"
  type        = number
  default     = 10
  nullable    = false
}

variable "private_network" {
  description = "The VPC network from which the Cloud SQL instance is accessible for private IP"
  type        = string
  nullable    = false
}

variable "tier" {
  description = "The DB tier"
  type        = string
  default     = "db-f1-micro"
  nullable    = false
}

resource "google_sql_database_instance" "pg" {
  name                = var.name
  database_version    = var.database_version
  deletion_protection = true

  settings {
    edition                     = "ENTERPRISE"
    tier                        = var.tier
    disk_size                   = var.disk_size
    disk_autoresize             = false
    deletion_protection_enabled = true

    ip_configuration {
      enable_private_path_for_google_cloud_services = true
      ssl_mode                                      = "ENCRYPTED_ONLY"

      ipv4_enabled    = true
      private_network = var.private_network

      # Allow access from Metabase
      authorized_networks {
        name  = "Metabase1"
        value = "*************"
      }
      authorized_networks {
        name  = "Metabase2"
        value = "************"
      }
      authorized_networks {
        name  = "Metabase3"
        value = "*************"
      }
    }

    backup_configuration {
      enabled    = true
      start_time = "12:00" # UTC
      location   = "us"
      backup_retention_settings {
        retained_backups = 7
      }
      point_in_time_recovery_enabled = true
      transaction_log_retention_days = 7
    }

    password_validation_policy {
      enable_password_policy      = true
      disallow_username_substring = true
      min_length                  = 16
    }
  }
}

resource "google_sql_database" "pg" {
  name     = "temet"
  instance = google_sql_database_instance.pg.name
}

resource "random_password" "pg" {
  length           = 21
  override_special = "$-_+!*()" # avoid problematic symbols on postgres URIs
}

resource "google_sql_user" "pg" {
  name     = "temet"
  instance = google_sql_database_instance.pg.name
  password = random_password.pg.result
}

output "db" {
  value = google_sql_database.pg.name
}

output "host" {
  value = google_sql_database_instance.pg.private_ip_address
}

output "user" {
  value = google_sql_user.pg.name
}

output "password" {
  value     = google_sql_user.pg.password
  sensitive = true
}
