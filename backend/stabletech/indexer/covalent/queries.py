import json
from decimal import Decimal

import httpx
from dateutil import parser
from sqlalchemy.exc import DatabaseError
from sqlalchemy.orm import Session

from common.conf import logger, settings
from stabletech.indexer.crud import (
    AccountManager,
    CovalentSyncManager,
    NetworkManager,
    TransactionManager,
)
from stabletech.indexer.models import Account, Network, Transaction, Transfer, Wallet
from stabletech.schemas import WalletSyncResult

NATIVE_VALUE_CONVERSION = 10**18

COVALENT_CHAINS = {
    # Network.name to Covalent Chain IDs mapping
    "Arbitrum": "arbitrum-mainnet",
    "Aurora": "aurora-mainnet",
    "Avalanche": "avalanche-mainnet",
    # TODO: enable when BTC when fully supported on Covalent - historical queries are still not working
    # "Bitcoin": "btc-mainnet",
    "Base": "base-mainnet",
    "BSC": "bsc-mainnet",
    "Berachain": "berachain-mainnet",
    "Canto": "canto-mainnet",
    "Ethereum": "eth-mainnet",
    "Fantom": "fantom-mainnet",
    "Matic": "matic-mainnet",
    "Moonbeam": "moonbeam-mainnet",
    "Optimism": "optimism-mainnet",
}


client = httpx.Client(
    base_url="https://api.covalenthq.com/v1/",
    auth=httpx.BasicAuth(settings.covalent_api_key, "") if settings.covalent_api_key else None,
    headers={"accept": "application/json"},
    follow_redirects=True,
    timeout=10,
)


def add_native_transfer(
    data,
    transactions_to_transfers,
    transaction,
    native_coin_account,
    from_account,
    to_account,
    nft_id=None,
):
    return transactions_to_transfers[transaction].append(
        Transfer(
            amount=Decimal(data["value"]) / NATIVE_VALUE_CONVERSION,
            asset_account=native_coin_account,
            nft_id=nft_id,
            from_account=from_account,
            to_account=to_account,
        )
    )


def process_transaction_data(
    db: Session, network: Network, wallet: Wallet, transactions_data: list
):
    account_manager = AccountManager(db)
    account_manager.get_or_create(wallet.address, network, api_name=wallet.name)
    native_coin = network.native_coin
    native_coin_account = account_manager.get_or_create(
        Account.NATIVE_TOKEN_ADDRESS,
        network,
        api_name=native_coin.name,
        ticker_symbol=native_coin.ticker.upper(),
    )
    transaction_manager = TransactionManager(db)
    transactions_to_transfers = {}

    for transaction_data in transactions_data:
        is_bridge = False
        from_account = account_manager.get_or_create(transaction_data["from_address"], network)
        to_account_address = transaction_data["to_address"]
        to_account = (
            account_manager.get_or_create(to_account_address, network)
            if to_account_address
            else None
        )
        native_coin_dollar_price = transaction_data["gas_quote_rate"]
        fees_paid = transaction_data["fees_paid"]
        if fees_paid:
            fees_paid = Decimal(fees_paid) / NATIVE_VALUE_CONVERSION
        transaction = Transaction(
            confirmed_at=parser.parse(transaction_data["block_signed_at"]),
            fees_paid=fees_paid,
            json_data=json.dumps(transaction_data),
            native_coin_dollar_price=native_coin_dollar_price,
            network=network,
            signer_account=from_account,
            tx_hash=transaction_data["tx_hash"],
            wallet=wallet,
        )
        transactions_to_transfers[transaction] = []

        log_events = transaction_data.get("log_events")
        if not log_events:
            if Decimal(transaction_data["value"]) != 0:
                # Transfer of Native Coin
                add_native_transfer(
                    transaction_data,
                    transactions_to_transfers,
                    transaction,
                    native_coin_account,
                    from_account,
                    to_account,
                )
                log_events = log_events or []
            else:
                continue

        # Transaction's Transfers
        for log_event in log_events:
            decoded_data = log_event.get("decoded")

            if decoded_data is None:
                # Transfer of Native Coin
                if len(log_events) == 1 or is_bridge:
                    add_native_transfer(
                        transaction_data,
                        transactions_to_transfers,
                        transaction,
                        native_coin_account,
                        from_account,
                        to_account,
                    )
                    is_bridge = False
                continue

            params = decoded_data.get("params")
            decoded_name = decoded_data["name"]

            # Process decoded log events
            amount = 0
            asset_account = None
            asset_address = from_address = to_address = ""
            asset_name = log_event["sender_name"] or None
            asset_ticker_symbol = log_event["sender_contract_ticker_symbol"] or None
            if asset_ticker_symbol:
                asset_ticker_symbol = asset_ticker_symbol.split(" ")[0]
            nft_id = None

            if decoded_name in ("Approval", "ApprovalForAll", "Created") or params is None:
                if not transaction.has_approval:
                    transaction.has_approval = True
                continue

            elif decoded_name == "Deposit":
                asset_address = log_event["sender_address"]
                from_address = transaction_data["from_address"]
                for param in params:
                    param_name = param["name"]
                    if param_name == "dst":
                        to_address = param["value"]
                    elif param_name == "wad":
                        amount = Decimal(param["value"]) / NATIVE_VALUE_CONVERSION

            elif decoded_name in ("Deposited", "TransitSwapped"):
                # Bridge transaction
                from_address = transaction_data["from_address"]
                to_address = transaction_data["to_address"]

                if decoded_name == "Deposited":
                    add_native_transfer(
                        transaction_data,
                        transactions_to_transfers,
                        transaction,
                        native_coin_account,
                        from_account,
                        to_account,
                        nft_id=nft_id,
                    )
                    continue
                else:
                    for param in params:
                        if param["name"] == "srcToken":
                            if param["value"] == Account.EVM_BURN_ADDRESS:
                                # Eth transfer
                                asset_account = native_coin_account
                            else:
                                asset_address = param["value"]
                                asset_account = None
                for param in params:
                    if param["name"] == "fee":
                        amount = Decimal(param["value"])
                        if amount:
                            if log_event["sender_contract_decimals"]:
                                amount /= 10 ** log_event["sender_contract_decimals"]
                            else:
                                amount /= NATIVE_VALUE_CONVERSION

            elif decoded_name == "InboxMessageDelivered":
                # Bridge transaction that has another log_event with no decoded data picked up above
                is_bridge = True
                continue

            elif decoded_name in ("ExitedEther", "LockedEther"):
                # Bridge ETH
                asset_account = native_coin_account
                from_address = transaction_data["to_address"]
                for param in params:
                    param_name = param["name"]
                    if param_name == "amount":
                        amount = Decimal(param["value"]) / NATIVE_VALUE_CONVERSION
                    elif param_name in ("depositReceiver", "exitor"):
                        to_address = param["value"]

            elif decoded_name == "Swap":
                # Handled by a Transfer event in the transaction
                continue

            elif decoded_name == "Transfer":
                # ERC20 transfer
                asset_address = log_event["sender_address"]
                for param in params:
                    param_name = param["name"]
                    if param_name == "tokenId":
                        # NFT transfer
                        amount = Decimal(1)
                        nft_id = int(param["value"])
                    if param_name == "from":
                        from_address = param["value"]
                    elif param_name == "to":
                        to_address = param["value"]
                    elif param_name == "value":
                        amount = Decimal(param["value"])
                        if log_event["sender_contract_decimals"]:
                            amount /= 10 ** log_event["sender_contract_decimals"]
                        else:
                            amount /= NATIVE_VALUE_CONVERSION

            elif decoded_name == "TransferSingle":
                # NFT transfer
                amount = Decimal(1)
                asset_address = log_event["sender_address"]
                for param in params:
                    if param["name"] == "_operator":
                        from_address = param["value"]
                    elif param["name"] == "_to":
                        to_address = param["value"]
                    if param["name"] == "_id":
                        nft_id = param["value"]

            elif decoded_name == "Withdrawal":
                # Unwrapping WETH and sending it to transaction's signer
                asset_address = log_event["sender_address"]
                from_address = transaction_data["to_address"]
                to_address = transaction_data["from_address"]
                for param in params:
                    if param["name"] == "wad":
                        amount = Decimal(param["value"]) / NATIVE_VALUE_CONVERSION

            elif decoded_name == "Wrap":
                # NFT bridged with a fee paid
                add_native_transfer(
                    transaction_data,
                    transactions_to_transfers,
                    transaction,
                    native_coin_account,
                    from_account,
                    to_account,
                    nft_id=nft_id,
                )
                continue

            if (
                from_address
                and to_address
                and wallet.address.casefold()
                in (from_address.casefold(), to_address.casefold())
                and (asset_address or asset_account)
            ):
                if asset_address:
                    assert not asset_account, "Should use either the Address or the Account"
                    asset_account = account_manager.get_or_create(
                        asset_address, network, asset_name, asset_ticker_symbol
                    )
                from_account = account_manager.get_or_create(from_address, network)
                to_account = account_manager.get_or_create(to_address, network)
                if amount:
                    if amount > Decimal(10**30):
                        # Spam amounts sent by scammers
                        continue
                    if nft_id and Decimal(str(nft_id)) > Decimal(10**32):
                        # Spam NFTs
                        continue

                    transactions_to_transfers[transaction].append(
                        Transfer(
                            amount=amount,
                            asset_account=asset_account,
                            nft_id=nft_id,
                            from_account=from_account,
                            to_account=to_account,
                        )
                    )

    if transactions_to_transfers:
        transaction_manager.add_with_transfers(wallet, transactions_to_transfers)


def fetch_covalent_query(covalent_chain_name: str, url_path_with_args: str) -> dict | None:
    try:
        response = client.get(f"{covalent_chain_name}/{url_path_with_args}")
        response.raise_for_status()
        response_json: dict = response.json()
    except (httpx.HTTPError, json.JSONDecodeError) as error:
        logger.info(f"Covalent query failed: {error}")
        return None

    if response_json["error"]:
        logger.info(f"Error: {response_json['error_message']}")
        return None

    return response_json


def save_transaction_history_for_address(
    db: Session, wallet: Wallet, networks: list[Network] = None
):
    """
    Fetches the entire transaction history for a Wallet and saves it.
    TODO: needs to be called asynchronously when adding a new Fund Wallet.

    https://www.covalenthq.com/docs/api/transactions/get-paginated-transactions-for-address-v3/
    """
    network_manager = NetworkManager(db)

    if not networks:
        network_to_covalent_chains = {
            network_manager.get_by_name(network_name): covalent_chain
            for network_name, covalent_chain in COVALENT_CHAINS.items()
        }
    else:
        network_to_covalent_chains = {
            network: COVALENT_CHAINS[network.name] for network in networks
        }

    for network, covalent_chain in network_to_covalent_chains.items():
        has_more: bool = True
        if network not in wallet.networks:
            # Wallet does not have this network enabled
            continue

        page_number: int = 0
        transactions_data: list = []

        while has_more:
            response_json = fetch_covalent_query(
                covalent_chain,
                f"address/{wallet.address}/transactions_v3/?page-number={page_number}",
            )
            if not response_json:
                break

            transactions_data += response_json["data"]["items"]
            has_more = response_json["data"]["links"]["next"] is not None
            page_number += 1

        if not has_more:
            process_transaction_data(db, network, wallet, transactions_data)


def save_transaction_by_hash(
    db: Session, network: Network, wallet: Wallet, tx_hash: str
) -> bool:
    """
    Fetches and saves a transaction by hash.

    Returns True if the transaction was found and saved, False otherwise.

    https://www.covalenthq.com/docs/api/transactions/get-transaction-by-hash-v3/
    """
    covalent_chain_name = COVALENT_CHAINS.get(network.name)
    if not covalent_chain_name:
        # Unsupported Network
        return False

    response_json = fetch_covalent_query(covalent_chain_name, f"transaction_v2/{tx_hash}/")
    if not response_json:
        return False

    process_transaction_data(db, network, wallet, response_json["data"]["items"])
    return True


def sync_transactions(db: Session, wallet: Wallet):
    """Fetch and save all newer transactions since the latest sync."""
    results_list: list[WalletSyncResult] = []
    networks_list = wallet.networks
    for network in networks_list:
        if result := sync_transactions_for_network(db, wallet, network):
            results_list.append(result)
    return results_list


def sync_transactions_for_network(db: Session, wallet: Wallet, network: Network):
    """Fetch and save newer transactions since the latest sync for a network.

    Returns `None` if the network is not supported. Otherwise, returns a
    `WalletSyncResult`.
    """
    covalent_chain = COVALENT_CHAINS.get(network.name)
    if not covalent_chain:
        return None

    address = wallet.address

    covalent_sync = CovalentSyncManager(db).get_or_create(covalent_chain, address)
    page = covalent_sync.latest_page
    db.commit()

    processed_count = 0
    errors: list[str] = []
    has_more = True
    while has_more:
        try:
            response = client.get(
                f"{covalent_chain}/address/{address}/transactions_v3/page/{page}/",
                params=(("block-signed-at-asc", "true"),),
            )
            response.raise_for_status()

            response_json: dict = response.json()
            if response_json["error"]:
                raise CovalentError(
                    response_json.get("error_message") or "Unexpected covalent error",
                    response=response,
                )

            new_transactions = _filter_new_transactions(
                db, network.id, response_json["data"]["items"]
            )
            process_transaction_data(db, network, wallet, new_transactions)
            db.commit()
        except (httpx.HTTPError, json.JSONDecodeError, CovalentError, DatabaseError) as e:
            db.rollback()
            msg = e.response.text if isinstance(e, httpx.HTTPStatusError) else str(e)
            errors.append(f"{type(e).__name__}: {msg}")
            break

        processed_count += len(new_transactions)
        has_more = response_json["data"]["links"]["next"] is not None
        if has_more:
            page += 1
            with db.begin():
                covalent_sync.latest_page = page

    return WalletSyncResult(
        network_name=network.name,
        provider="covalent",
        provider_network=covalent_chain,
        processed_transactions=processed_count,
        errors=errors,
    )


def _filter_new_transactions(db: Session, network_id: int, transactions: list[dict]):
    all_tx_hashes = [tx["tx_hash"] for tx in transactions]
    existing_tx_hashes = set(
        TransactionManager(db).filter_existing_tx_hashes(network_id, all_tx_hashes)
    )
    return [
        transaction
        for transaction in transactions
        if transaction["tx_hash"] not in existing_tx_hashes
    ]


class CovalentError(Exception):
    """Covalent responded with an error."""

    def __init__(self, message: str, *, response: httpx.Response) -> None:
        super().__init__(message)
        self.response = response
