// ** React Imports
import { ReactNode } from "react";

// ** Types
import { ThemeColor } from "@/@core/layouts/types";

export interface CardStatsHorizontalProps {
  title: string;
  stats: string;
  icon: ReactNode;
  color?: ThemeColor;
  trendNumber: string;
  trend?: "positive" | "negative";
}

export interface CardStatsVerticalProps {
  title: string;
  stats: string;
  icon: ReactNode;
  subtitle: string;
  color?: ThemeColor;
  trendNumber: string;
  trend?: "positive" | "negative";
}

export interface CardStatsCharacterProps {
  src: string;
  title: string;
  stats: string;
  chipText: string;
  trendNumber: string;
  chipColor?: ThemeColor;
  trend?: "positive" | "negative";
}
