variable "host_volumes" {
  type = list(object({
    name       = string
    host_path  = string
    mount_path = string
  }))

  description = "Host paths to mount into containers for ease of development"
  default     = []
}

variable "namespace" {
  description = "Kubernetes namespace to use"
}

variable "python_runner_image" {
  type        = string
  description = "python-runner fully qualified image name"
  nullable    = false
}

variable "config_maps" {
  description = "ConfigMap names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}

variable "secrets" {
  description = "Secret names to mount into the container"
  type        = list(string)
  nullable    = false
  default     = []
}
