import asyncio
import logging
import os
import signal
import time
from contextlib import asynccontextmanager

import httpx
import redis.exceptions
import sentry_sdk
from fastapi import APIRouter, BackgroundTasks, FastAPI, HTTPException, Response
from fastapi.responses import J<PERSON>NResponse

from common.auth import Secret<PERSON><PERSON>A<PERSON>
from common.pricer import schemas
from common.pricer.settings import SECRET
from pricer.cache import Cache, CacheState
from pricer.coingecko_client import CoingeckoClient, CoingeckoError, CoingeckoUnavailableError
from pricer.historical.price_collector import PriceCollector
from pricer.kinds import HistoryDate
from pricer.price_refresher import PriceRefresher
from pricer.settings import Settings

settings = Settings()

sentry_sdk.init(dsn=settings.sentry_dsn, environment=settings.env_name)

logging.basicConfig(level=settings.log_level)
logger = logging.getLogger(__name__)

coingecko = CoingeckoClient(settings.coingecko_api_key)

cache = Cache(
    prefix="pricer",
    host=settings.redis_host,
    port=settings.redis_port,
    db=settings.redis_db,
    password=settings.redis_password,
)

price_refresher = PriceRefresher(cache, coingecko)
price_collector = PriceCollector(coingecko)


def shutdown_on_task_done(task: asyncio.Task):
    if task.cancelled():
        logger.info("Task cancelled: %s", task)
    else:
        exc = task.exception()
        if exc:
            logger.error("Task failed: %s", task, exc_info=exc)
    os.kill(os.getpid(), signal.SIGTERM)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    # startup
    price_refresher.start_requested_prices_task().add_done_callback(shutdown_on_task_done)
    price_collector.start_price_collection_task().add_done_callback(shutdown_on_task_done)
    yield
    # shutdown


app = FastAPI(lifespan=lifespan)


@app.middleware("http")
async def handle_common_errors(request, call_next):
    try:
        return await call_next(request)
    except CoingeckoError as e:
        logger.warning("Coingecko error: %s", e)
        return JSONResponse(status_code=e.response.status_code, content={"detail": str(e)})
    except CoingeckoUnavailableError as e:
        logger.warning("Coingecko unavailable: %s", e)
        detail = "Too many requests" if e.code == 429 else "Service unavailable"
        retry_after = max(1, int(e.until - time.time()))
        return JSONResponse(
            status_code=e.code,
            headers={"Retry-After": str(retry_after)},
            content={"detail": detail},
        )
    except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError) as e:
        logger.warning("Redis unavailable: %s", e)
        return JSONResponse(status_code=503, content={"detail": "Service unavailable"})
    except httpx.TimeoutException as e:
        logger.warning("HTTP request timeout: %s %s", e.request.method, e.request.url)
        return JSONResponse(status_code=504, content={"detail": "Gateway timeout"})


@app.get("/")
async def health_check():
    return Response(status_code=200)  # empty response


auth = SecretTokenAuth(SECRET)

private = APIRouter()


@private.get("/prices", response_model=dict[str, schemas.Price | None])
async def get_prices(ids: str, background_tasks: BackgroundTasks):
    ids_list = ids.split(",")
    if len(ids_list) > 100:
        raise HTTPException(
            status_code=400,
            detail=f"Too many ids: got {len(ids_list)}, max 100",
        )

    cached_prices = await cache.get_prices(ids_list)

    missing = [cid for state, cid, _ in cached_prices if state == CacheState.MISSING]
    stale = [cid for state, cid, _ in cached_prices if state == CacheState.STALE]
    prices = {cid: price for _, cid, price in cached_prices}

    if missing:
        logger.debug("Prices missing: %s", missing)
        fresh_prices = await price_refresher.refresh(missing, background_tasks)
        prices.update(fresh_prices)

    if stale:
        logger.debug("Prices stale: %s", stale)
        background_tasks.add_task(price_refresher.refresh, stale)

    background_tasks.add_task(cache.set_requested_prices, ids_list)

    return prices


@private.get("/coins", response_model=list[schemas.Coin])
async def get_coins(background_tasks: BackgroundTasks):
    state, data = await cache.get_coins()
    if not data or state == CacheState.MISSING:
        logger.debug("Coins list missing")
        data = await refresh_coins(background_tasks)
    elif state == CacheState.STALE:
        logger.debug("Coins list stale")
        background_tasks.add_task(refresh_coins)
    return data


async def refresh_coins(background_tasks: BackgroundTasks | None = None) -> list[schemas.Coin]:
    resp = await coingecko.get_coins_list()
    resp = [
        schemas.Coin(
            id=coin["id"],
            symbol=coin["symbol"],
            name=coin["name"],
            market_cap_usd=coin["market_cap"],
        )
        for coin in resp
    ]
    if background_tasks:
        background_tasks.add_task(cache.set_coins, resp)
    else:
        await cache.set_coins(resp)
    return resp


@private.get(
    "/coins/{coin_id}/history",
    response_model=schemas.CoinHistory,
    responses={404: {"model": schemas.Error}},
)
async def get_coin_history(coin_id: str, date: HistoryDate, background_tasks: BackgroundTasks):
    state, data = await cache.get_coin_history(coin_id, date)

    match state, data:
        case (CacheState.MISSING, _) | (CacheState.STALE, None):
            # Refresh if missing on cache or stale and not found
            logger.debug("Coin history %s: %s %s", state.name, coin_id, date)
            data = await refresh_coin_history(coin_id, date, background_tasks)
        case CacheState.STALE, _:
            # Refresh in background if stale but found (stale while revalidate)
            logger.debug("Coin history stale: %s %s", coin_id, date)
            background_tasks.add_task(refresh_coin_history, coin_id, date)

    if not data:
        # Cannot raise HTTPException because it would not schedule background tasks
        return JSONResponse(status_code=404, content={"detail": "Not found"})

    return data


async def refresh_coin_history(
    coin_id: str, date: str, background_tasks: BackgroundTasks | None = None
) -> schemas.CoinHistory | None:
    resp = await coingecko.get_coin_history(coin_id, date)
    resp = schemas.CoinHistory.model_validate(resp) if resp else None
    if background_tasks:
        background_tasks.add_task(cache.set_coin_history, coin_id, date, resp)
    else:
        await cache.set_coin_history(coin_id, date, resp)
    return resp


app.include_router(private, dependencies=[auth.depends_authentication])


class AccessLoggerFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        method = record.args[1]  # type: ignore
        route = record.args[2]  # type: ignore
        # filter-out health checks
        return not (method == "GET" and route == "/")


logging.getLogger("uvicorn.access").addFilter(AccessLoggerFilter())

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "pricer.api:app", host="localhost", port=int(os.getenv("PORT", 8080)), reload=True
    )
