"""
Creates pool Transactions for testing.
Currently, assumes the database has been reset.
TODO: get_or_create objects instead of assuming they don't exist.
"""

from datetime import datetime
from decimal import Decimal

from common.conf import settings
from common.psql import SessionLocal
from stabletech.auth.models import User
from stabletech.indexer import models

session = SessionLocal()

admin_user = session.query(User).filter_by(email=settings.admin_email).one()

# Get Coins
btc_coin = session.query(models.Coin).filter_by(ticker="btc").first()
eth_coin = session.query(models.Coin).filter_by(ticker="eth").first()
sol_coin = session.query(models.Coin).filter_by(ticker="sol").first()

# Create Exchange
exchange = models.Exchange(
    name="Beefy", url="https://app.beefy.com/", created_by_user_id=admin_user.id
)
session.add(exchange)
session.commit()
session.refresh(exchange)

network = session.query(models.Network).filter_by(name="Ethereum").one()

# Create Wallet
wallet = models.Wallet(address="11", name="temp", created_by_user_id=admin_user.id)
session.add(wallet)
session.commit()
session.refresh(wallet)
wallet.networks = [network]
session.commit()

# Create asset Accounts
btc_account = models.Account(address="btc", network_id=1, coin_id=btc_coin.id)
eth_account = models.Account(address="eth", network_id=1, coin_id=eth_coin.id)
sol_account = models.Account(address="sol", network_id=1, coin_id=sol_coin.id)
lp_account = models.Account(address="lp-token1", network_id=1)

pool_account = models.Account(address="pool1", network_id=1, exchange_id=exchange.id)
wallet_account = models.Account(address="11", network_id=1, wallet_id=wallet.id)

session.add(btc_account)
session.add(eth_account)
session.add(lp_account)
session.add(pool_account)
session.add(sol_account)
session.add(wallet_account)
session.commit()
session.refresh(btc_account)
session.refresh(eth_account)
session.refresh(pool_account)
session.refresh(sol_account)
session.refresh(wallet_account)

# Create Transactions and Transfers
# Order of Transfers is randomized to simulate real data

# Deposit 1 BTC and 10 ETH into the pool
transaction = models.Transaction(
    action=str(models.TransactionAction.pool.value),
    confirmed_at=datetime.now(),
    created_by_user_id=admin_user.id,
    network_id=1,
    signer_account_id=wallet_account.id,
    tx_hash="1",
    wallet_id=wallet.id,
)
session.add(transaction)
session.commit()
session.refresh(transaction)

session.add(
    models.Transfer(
        amount=Decimal("0.0001"),
        asset_account_id=lp_account.id,
        from_account_id=pool_account.id,
        to_account_id=wallet_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("1"),
        asset_account_id=btc_account.id,
        from_account_id=wallet_account.id,
        to_account_id=pool_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("10"),
        asset_account_id=eth_account.id,
        from_account_id=wallet_account.id,
        to_account_id=pool_account.id,
        transaction_id=transaction.id,
    )
)


# Withdraw 0.5 BTC and 5 ETH into the pool
transaction = models.Transaction(
    action=str(models.TransactionAction.pool.value),
    confirmed_at=datetime.now(),
    created_by_user_id=admin_user.id,
    network_id=1,
    signer_account_id=wallet_account.id,
    tx_hash="2",
    wallet_id=wallet.id,
)
session.add(transaction)
session.commit()
session.refresh(transaction)

session.add(
    models.Transfer(
        amount=Decimal("0.0001"),
        asset_account_id=lp_account.id,
        from_account_id=wallet_account.id,
        to_account_id=pool_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("0.5"),
        asset_account_id=btc_account.id,
        from_account_id=pool_account.id,
        to_account_id=wallet_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("5"),
        asset_account_id=eth_account.id,
        from_account_id=pool_account.id,
        to_account_id=wallet_account.id,
        transaction_id=transaction.id,
    )
)


lp_account = models.Account(address="lp-token2", network_id=1)
pool_account = models.Account(address="pool2", network_id=1, exchange_id=exchange.id)

session.add(lp_account)
session.add(pool_account)
session.commit()
session.refresh(pool_account)

# Deposit 1 ETH and 20 SOL into the pool
transaction = models.Transaction(
    action=str(models.TransactionAction.pool.value),
    confirmed_at=datetime.now(),
    created_by_user_id=admin_user.id,
    network_id=1,
    signer_account_id=wallet_account.id,
    tx_hash="3",
    wallet_id=wallet.id,
)
session.add(transaction)
session.commit()
session.refresh(transaction)

session.add(
    models.Transfer(
        amount=Decimal("1"),
        asset_account_id=eth_account.id,
        from_account_id=wallet_account.id,
        to_account_id=pool_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("20"),
        asset_account_id=sol_account.id,
        from_account_id=wallet_account.id,
        to_account_id=pool_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("0.0001"),
        asset_account_id=lp_account.id,
        from_account_id=pool_account.id,
        to_account_id=wallet_account.id,
        transaction_id=transaction.id,
    )
)


# Withdraw 0.5 ETH and 10 SOL into the pool
transaction = models.Transaction(
    action=str(models.TransactionAction.pool.value),
    confirmed_at=datetime.now(),
    created_by_user_id=admin_user.id,
    network_id=1,
    signer_account_id=wallet_account.id,
    tx_hash="4",
    wallet_id=wallet.id,
)

session.add(transaction)
session.commit()
session.refresh(transaction)

session.add(
    models.Transfer(
        amount=Decimal("0.5"),
        asset_account_id=eth_account.id,
        from_account_id=pool_account.id,
        to_account_id=wallet_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("0.0001"),
        asset_account_id=lp_account.id,
        from_account_id=wallet_account.id,
        to_account_id=pool_account.id,
        transaction_id=transaction.id,
    )
)
session.add(
    models.Transfer(
        amount=Decimal("10"),
        asset_account_id=sol_account.id,
        from_account_id=pool_account.id,
        to_account_id=wallet_account.id,
        transaction_id=transaction.id,
    )
)

session.commit()
