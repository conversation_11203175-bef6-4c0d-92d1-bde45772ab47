/**
 * Base rules for being extended by main ESLint config.
 * This allows other configs to extend this one and override rules.
 * @type {import("eslint").Linter.Config}
 */
export default {
  name: "base-rules",
  rules: {
    "default-case-last": "warn",
    "default-case": "warn",
    "no-alert": "warn",
    "no-console": "warn",
    "no-duplicate-imports": "warn",
    "no-eval": "warn",
    "no-extend-native": "warn",
    "no-extra-bind": "warn",
    "no-implicit-coercion": "warn",
    "no-implied-eval": "warn",
    "no-lonely-if": "warn",
    "no-multi-assign": "warn",
    "no-new-func": "warn",
    "no-new-wrappers": "warn",
    "no-object-constructor": "warn",
    "no-promise-executor-return": "warn",
    "no-return-assign": "warn",
    "no-sequences": "warn",
    "no-throw-literal": "warn",
    "no-unmodified-loop-condition": "warn",
    "no-unneeded-ternary": "warn",
    "no-unreachable-loop": "warn",
    "no-unused-private-class-members": "warn",
    "no-useless-call": "warn",
    "no-useless-computed-key": "warn",
    "no-useless-concat": "warn",
    "no-useless-constructor": "warn",
    "no-useless-rename": "warn",
    "no-useless-return": "warn",
    "no-var": "warn",
    "prefer-const": "warn",
    "prefer-exponentiation-operator": "warn",
    "prefer-numeric-literals": "warn",
    "prefer-object-spread": "warn",
    "prefer-promise-reject-errors": "warn",
    "prefer-regex-literals": "warn",
    "prefer-rest-params": "warn",
    "prefer-spread": "warn",
    "require-atomic-updates": "warn",
    "require-unicode-regexp": "warn",
    eqeqeq: ["warn", "smart"],
  },
};
