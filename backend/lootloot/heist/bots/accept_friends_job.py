"""
<PERSON><PERSON><PERSON> to check the Heist API for incoming friend requests and accept them.
"""

import logging

from common.conf import settings
from lootloot.external.heist import HeistClient

logger = logging.getLogger(__name__)


def accept_friend_requests():
    heist_client = HeistClient()
    if not settings.heist_bank_bot_key:
        logger.info("No private key - exiting")
        return
    heist_client.authenticate_with_private_key(settings.heist_bank_bot_key)

    pending_addresses = heist_client.get_pending_friend_request_addresses()
    for address in pending_addresses:
        heist_client.accept_friend_request(address)
        logger.info(f"Accepted friend request from {address}")


if __name__ == "__main__":
    accept_friend_requests()
