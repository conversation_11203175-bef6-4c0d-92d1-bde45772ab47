"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cva, cx, type VariantProps } from "cva";
import { AlertCircle, Check, Info, XCircle } from "lucide-react";
import { forwardRef } from "react";

const alert = cva({
  base: "relative w-full rounded-lg border p-4 text-sm",
  variants: {
    variant: {
      neutral: "border-border bg-background text-foreground",
      error: "border-destructive bg-destructive/10 text-destructive",
      success: "border-green-600 bg-green-50/80 text-green-600 dark:bg-green-900/40",
      warning: "border-yellow-600 bg-yellow-50/80 text-yellow-600 dark:bg-yellow-900/40",
      info: "border-blue-600 bg-blue-50/80 text-blue-600 dark:bg-blue-900/40",
    },
  },
  defaultVariants: {
    variant: "info",
  },
});

export interface AlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alert> {
  onClear?: () => void;
}

const VARIANT_ICONS = {
  error: AlertCircle,
  success: Check,
  warning: AlertCircle,
  info: Info,
  neutral: Info,
} as const;

export const Alert = forwardRef<HTMLDivElement, AlertProps>(function Alert(props, ref) {
  const { className, variant = "info", children, onClear, ...rest } = props;
  const Icon = VARIANT_ICONS[variant];

  return (
    <div
      ref={ref}
      role="alert"
      className={cx(
        alert({ variant, className }),
        onClear
          ? "grid grid-cols-[auto_1fr_auto] items-center gap-3"
          : "grid grid-cols-[auto_1fr] items-center gap-3",
      )}
      {...rest}
    >
      <Icon className="h-4 w-4" />
      <div className="min-w-0 break-words">{children}</div>
      {onClear && (
        <Button
          onClick={onClear}
          variant="ghost"
          size="icon"
          className="h-6 w-6 rounded-full hover:bg-accent/70"
        >
          <XCircle className="h-4 w-4" strokeWidth={1.25} />
        </Button>
      )}
    </div>
  );
});
