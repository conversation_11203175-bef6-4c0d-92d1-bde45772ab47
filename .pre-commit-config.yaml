# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        additional_dependencies:
          - prettier-plugin-organize-imports@4.1.0
          - prettier-plugin-tailwindcss@0.6.11
          - prettier@3.5.3
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.9
    hooks:
      - id: ruff
        args: [--fix]
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        name: isort (python)
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        # Needed to add required-version=0 here to avoid missing release version
        # on the black program used by the hook:
        # > The required version `23` does not match the running version `0.1.dev1+gb0d1fba`
        entry: black --required-version=0
        language_version: python3.12
  - repo: local
    hooks:
      - id: terraform-fmt
        name: terraform-fmt
        types: [terraform]
        language: node
        # Simple wrapper package that provides the terraform binary with same version
        # of the package, and compatible with the current platform.
        additional_dependencies: ["@jahed/terraform@1.11.4"]
        # Need to set INIT_CWD to the Node.js virtual environment directory so that the
        # terraform wrapper package can find the directory where to store the terraform
        # executable. NODE_VIRTUAL_ENV is set by pre-commit when using language "node".
        entry: sh -c 'INIT_CWD="$NODE_VIRTUAL_ENV" exec terraform fmt "$@"' --
