module "gke_cluster" {
  source = "./cluster"

  project_name   = var.project_name
  project_uid    = var.project_uid
  region         = var.region
  zone           = var.zone
  zone_secondary = var.zone_secondary
}

resource "kubernetes_namespace" "namespace" {
  metadata {
    name = var.namespace
  }
}

resource "google_dns_managed_zone" "remnants_icu" {
  name     = "remnants-icu"
  dns_name = "remnants.icu."

  dnssec_config {
    state = "on"
  }
}

resource "google_dns_managed_zone" "production_stabletech_capital" {
  name     = "production-stabletech-capital"
  dns_name = "production.stabletech.capital."

  dnssec_config {
    state = "on"
  }
}

module "stabletech" {
  depends_on = [module.gke_cluster]
  source     = "../modules/stabletech"

  api_domain        = "api.production.stabletech.capital"
  env_name          = "production"
  managed_zone_name = "production-stabletech-capital"
  namespace         = var.namespace
  release           = local.release
  secrets           = local.secrets
  api_requests = {
    cpu    = "100m"
    memory = "256Mi"
  }
}

module "dashboard_ui" {
  depends_on = [module.gke_cluster]
  source     = "../modules/ui"

  dns_managed_zone = {
    name     = "stabletech-capital"
    dns_name = "stabletech.capital."
  }
  namespace           = var.namespace
  project_id          = local.project_id
  website_domain_name = "stabletech.capital"
}

# Internal

# module.pricer requires helm provider
provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.this.endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.this.master_auth[0].cluster_ca_certificate)
  }
}

resource "google_dns_managed_zone" "internal" {
  name     = "internal-stabletech-capital"
  dns_name = "internal.stabletech.capital."

  dnssec_config {
    state = "on"
  }
}

resource "kubernetes_namespace" "internal" {
  metadata {
    name = "internal"
  }
}

module "pricer" {
  depends_on = [module.gke_cluster]
  source     = "./internal/pricer"

  namespace           = kubernetes_namespace.internal.metadata[0].name
  python_runner_image = module.stabletech.python_runner_image
  secrets             = local.secrets
  replicas            = 2
}

module "internal_ingress" {
  depends_on = [module.gke_cluster]
  source     = "./internal/ingress"

  env_name         = "production"
  namespace        = kubernetes_namespace.internal.metadata[0].name
  dns_managed_zone = google_dns_managed_zone.internal
}
