import base58
from fastapi import APIRouter, HTTPException
from nacl.secret import SecretBox

from common import conf
from common.psql import DatabaseSession
from lootloot import schemas
from lootloot.auth.crud import AuthTokenManager, UserManager
from lootloot.auth.fastapi import CurrentUser
from lootloot.auth.matrica_client import MatricaClient
from lootloot.kinds import NetworkGroup, WalletOrigin
from lootloot.utils import verify_solana_signature
from lootloot.wallets.crud import UserWalletManager

router = APIRouter()

SECRET_BOX = SecretBox(bytes.fromhex(conf.settings.secret_box_hexed_key))
"""
Using SecretBox to generate encrypt/decrypt auth message from wallet address
https://pynacl.readthedocs.io/en/latest/secret/
"""

matrica = MatricaClient.from_env()


@router.post("/matrica-login", response_model=schemas.LoginResponse)
def matrica_login(matrica_login_request: schemas.MatricaLogin, db: DatabaseSession):
    """Login user with Matrica oauth2 code and code_verifier."""
    auth_resp = matrica.authenticate_with_code(
        code=matrica_login_request.code,
        redirect_uri=matrica_login_request.redirect_uri,
        code_verifier=matrica_login_request.code_verifier,
    )

    profile = matrica.get_oauth2_user_profile(auth_resp.access_token)
    matrica_wallets = matrica.get_oauth2_user_wallets(auth_resp.access_token)
    user_manager = UserManager(db)

    # Filter healthy and EVM/Solana as a list of tuple[NetworkGroup, address]
    wallet_keys: list[tuple[NetworkGroup, str]] = [
        (matrica_network_symbol_to_network_group(w.networkSymbol), w.id)
        for w in matrica_wallets
        if w.networkSymbol in ("ETH", "SOL")
    ]

    user_wallet_manager = UserWalletManager(db)

    with db.begin():
        user = user_manager.get_by_matrica_id(profile.id)
        existing = user_wallet_manager.list_by_network_group_and_address(wallet_keys)
        existing = {(wallet.network_group, wallet.address): wallet for wallet in existing}

        # Check that all the Wallets are owned by the same User
        existing_wallets = list(existing.values())
        if existing_wallets:
            exising_user_id = existing_wallets[0].user_id
            has_any_owned_by_different_user = any(
                wallet for wallet in existing_wallets if wallet.user_id != exising_user_id
            )
            if not user:
                # User was created during a Phantom login
                user = user_manager.get_by_id(exising_user_id)
                user.matrica_id = profile.id
                user.matrica_username = profile.username
            if has_any_owned_by_different_user or user.id != exising_user_id:
                # Move Wallets to this User's account as they might have been updated on Matrica
                # This should rarely happen
                for wallet in existing_wallets:
                    wallet.user_id = user.id

        if not user:
            # User doesn't exist yet
            user = user_manager.create_from_matrica(
                matrica_id=profile.id, username=profile.username
            )

        # At this point we are sure that existing wallets are owned by this user

    with db.begin():
        user_wallet_manager.delete_extra_wallets_for_user_and_origin(
            user_id=user.id, origin=WalletOrigin.matrica, keep=wallet_keys
        )

        to_create = [key for key in wallet_keys if key not in existing]
        for network_group, address in to_create:
            user_wallet_manager.create_with_auto_name(
                user_id=user.id,
                origin=WalletOrigin.matrica,
                network_group=network_group,
                address=address,
            )

    auth_token_manager = AuthTokenManager(db)

    with db.begin():
        token = auth_token_manager.get_or_create(user)

    return schemas.LoginResponse(user=schemas.User.model_validate(user), token=token)


def matrica_network_symbol_to_network_group(network_symbol: str) -> NetworkGroup:
    # Note: Polygon wallets come as ETH
    match network_symbol:
        case "ETH":
            return "evm"
        case "SOL":
            return "solana"
        case _:
            raise ValueError(f"Unknown network symbol: {network_symbol}")


@router.get("/me", response_model=schemas.User)
def me(user: CurrentUser):
    return schemas.User.model_validate(user)


@router.get("/wallet-auth-message", response_model=schemas.WalletAuthMessage)
def auth_message(address: str):
    base_message = "Lootloot sign-in message: "
    encrypted = SECRET_BOX.encrypt(bytes(address, "utf-8"))
    return schemas.WalletAuthMessage(
        message=f'{base_message}{base58.b58encode(encrypted).decode("utf-8")}'
    )


@router.post("/phantom-login", response_model=schemas.LoginResponse)
def phantom_login(phantom_login_request: schemas.PhantomLogin, db: DatabaseSession):
    address = phantom_login_request.address

    if not verify_solana_signature(
        phantom_login_request.signature, phantom_login_request.message, address
    ):
        raise HTTPException(status_code=403, detail="Sign-in verification failed")

    auth_token_manager = AuthTokenManager(db)
    network_group: NetworkGroup = "solana"
    user_manager = UserManager(db)
    user_wallet_manager = UserWalletManager(db)
    wallet = user_wallet_manager.get_by_address(network_group, address)

    if not wallet:
        user = user_manager.create_from_phantom()
        token = auth_token_manager.create(user)

        db.commit()
        # Create the Wallet for the User
        user_wallet_manager.create_with_auto_name(
            user_id=user.id,
            origin=WalletOrigin.phantom,
            network_group=network_group,
            address=address,
        )
    else:
        user = user_manager.get_by_id(wallet.user_id)
        token = auth_token_manager.get_or_create(user)
    db.commit()
    return schemas.LoginResponse(user=schemas.User.model_validate(user), token=token)
