import logging
from enum import IntEnum, StrEnum
from typing import Any
from urllib.parse import quote_plus

import httpx

from common.conf import settings
from wallet import schemas
from wallet.cubist.session_manager import CubeSignerSessionManager

logger = logging.getLogger(__name__)


class CubistKeyType(StrEnum):
    ETHEREUM = "SecpEthAddr"
    SOLANA = "Ed25519SolanaAddr"


class ChainID(IntEnum):
    ETHEREUM = 1
    SOLANA = 588


class CubistRole(StrEnum):
    ALIEN = "Alien"
    MEMBER = "Member"
    ADMIN = "Admin"


class CubistManagementClient:
    """Client for interacting with the CubeSigner API with automatic token refresh"""

    def __init__(
        self, session_manager: CubeSignerSessionManager, client: httpx.AsyncClient | None = None
    ) -> None:
        self._client = client or self._create_httpx_client()
        self._session_manager = session_manager

    @staticmethod
    async def create_default() -> "CubistManagementClient":
        """Create a new client with a default session manager"""
        client = CubistManagementClient._create_httpx_client()
        session_manager = await CubeSignerSessionManager.from_db(client)
        return CubistManagementClient(session_manager, client)

    @staticmethod
    def _create_httpx_client() -> httpx.AsyncClient:
        return httpx.AsyncClient(
            base_url=settings.cubist_domain,
            headers={"accept": "application/json"},
            timeout=60.0,
        )

    async def _get_client(self) -> httpx.AsyncClient:
        """Get the client with a valid token"""
        session = await self._session_manager.get_session()
        self._client.headers["authorization"] = session.token
        return self._client

    async def get_user(self) -> dict[str, Any]:
        client = await self._get_client()
        response = await client.get("/v0/about_me")
        response.raise_for_status()
        return response.json()

    async def add_user_to_org(
        self,
        org_id: str,
        email: str,
        identity: schemas.CubistOidcIdentity,
        name: str | None,
        mfa_policy: dict[str, str] | None = None,
    ) -> schemas.CubistRegisterOidcUserResp:

        body = {
            "email": email,
            "identity": identity.model_dump(),
            "mfa_policy": mfa_policy,
            "name": name,
            "role": CubistRole.ALIEN,
        }

        client = await self._get_client()
        response = await client.post(
            f"/v0/org/{quote_plus(org_id)}/users",  # quote_plus is used to escape special characters in the OrgID
            json=body,
        )

        response.raise_for_status()
        return schemas.CubistRegisterOidcUserResp.model_validate_json(response.content)

    async def verify_identity(
        self, org_id: str, identity_proof: schemas.CubistIdentityProof
    ) -> bool:
        client = await self._get_client()
        response = await client.post(
            f"/v0/org/{quote_plus(org_id)}/identity/verify",
            json=identity_proof.model_dump(mode="json"),
        )

        if response.status_code not in (200, 400):
            response.raise_for_status()

        # The cubist API returns 200 for success and 400 for failure.
        return True if response.status_code == 200 else False

    async def create_key(self, key_type: str, user_id: str) -> str:
        if key_type == CubistKeyType.ETHEREUM:
            chain_id = ChainID.ETHEREUM
        elif key_type == CubistKeyType.SOLANA:
            chain_id = ChainID.SOLANA
        else:
            raise ValueError(
                "Invalid key type, only 'SecpEthAddr' and 'Ed25519SolanaAddr' are supported"
            )

        body = {
            "key_type": key_type,
            "edit_policy": None,
            "metadata": None,
            "owner": user_id,
            "policy": None,
            "chain_id": chain_id,
            "count": 1,
        }

        client = await self._get_client()
        response = await client.post(
            f"/v0/org/{quote_plus(settings.cubist_org_id)}/keys",
            json=body,
        )

        response.raise_for_status()
        return response.json()


_cubist_management_client: CubistManagementClient | None = None


async def get_cubist_management_client() -> CubistManagementClient:
    global _cubist_management_client
    if not _cubist_management_client:
        _cubist_management_client = await CubistManagementClient.create_default()
    return _cubist_management_client
