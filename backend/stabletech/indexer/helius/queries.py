import itertools
import logging
from datetime import datetime
from decimal import Decimal

from sqlalchemy.orm import Session

from common.conf import settings
from common.helius.client import helius_client
from common.helius.models import TokenMetadataResult, TokenTransfer
from common.helius.models import Transaction as TransactionData
from stabletech.indexer.crud import (
    AccountManager,
    NetworkManager,
    NFTManager,
    TransactionManager,
    WalletManager,
)
from stabletech.indexer.models import Account, Transaction, Transfer, Wallet
from stabletech.schemas import WalletSyncResult

logger = logging.getLogger(__name__)

NATIVE_CONVERSION = **********


def save_solana_transactions(
    db: Session,
    address: str = "",
    transactions_data: dict[str, TransactionData] | None = None,
    fetch_till: datetime | None = None,
):
    """Save solana transactions to the database.

    If `address` is provided, fetches transactions from the Helius API for that address.
    If `transactions_data` is provided, saves the transactions from the provided data.
    Either `address` or `transactions_data` must be provided, but never both.

    Example response:

    {'description': 'AnJQJsuQZNttAuJqf2csjanfx1G2MuzX6CbNqRf6L6Hq transferred 1000000 Boo to 2cS7tZxRBhqcx3ncxmSGqCWuPnAqEbQpAqNEaVU1yRBo.',
     'type': 'TRANSFER',
     'source': 'SOLANA_PROGRAM_LIBRARY',
     'fee': 5000,
     'feePayer': 'AnJQJsuQZNttAuJqf2csjanfx1G2MuzX6CbNqRf6L6Hq',
     'signature': 'HPbW59FMRFhAKFWVp1tLHohw7b81buP7YzLMtXqKS65kv1iHT1wpMsUFqf2geynpwU86s4q2B93UFyZjNXZxaWs',
     'slot': *********,
     'timestamp': **********,
     'tokenTransfers': [{'fromTokenAccount': '2R45vDZWeM4TzLSJNABgYNTfC2Va23YWs395DdF99k2o',
       'toTokenAccount': '5TUccZxYHd3exD84xMCbuBkzKNFXNFw4PaPkgAKCSm1i',
       'fromUserAccount': 'AnJQJsuQZNttAuJqf2csjanfx1G2MuzX6CbNqRf6L6Hq',
       'toUserAccount': '2cS7tZxRBhqcx3ncxmSGqCWuPnAqEbQpAqNEaVU1yRBo',
       'tokenAmount': 1000000,
       'mint': 'FfpyoV365c7iR8QQg5NHGCXQfahbqzY67B3wpzXkiLXr',
       'tokenStandard': 'Fungible'
      }
    ]}
    """
    if bool(address) == (transactions_data is not None):
        raise ValueError("Either address or transactions_data must be provided exclusively")

    account_manager = AccountManager(db)
    accounts: list[Account] = []
    network = NetworkManager(db).get_solana()
    native_account = account_manager.get_or_create(
        Account.NATIVE_TOKEN_ADDRESS,
        network,
        network.native_coin.name,
        network.native_coin.ticker.upper(),
    )
    transaction_manager = TransactionManager(db)
    wallet_manager = WalletManager(db)

    wallet_transaction_data_pairs: list[tuple[Wallet, TransactionData]]

    if transactions_data is None:
        wallet = wallet_manager.get_by_network_id_and_address(network.id, address)
        if not wallet:
            raise ValueError(f"Couldn't find a wallet with address '{address}'")

        last_fetched_tx_hash = None
        latest_transaction = transaction_manager.get_latest(network, wallet)
        latest_confirmed_at = (
            (
                latest_transaction.confirmed_at
                if latest_transaction
                else settings.transactions_start_at
            )
            if not fetch_till
            else fetch_till
        )

        # Fetch transactions from API
        transactions_data = {}
        while True:
            fetch_transactions = helius_client.get_address_transactions(
                wallet.address, last_fetched_tx_hash
            )

            for transaction_data in fetch_transactions:
                if datetime.fromtimestamp(transaction_data.timestamp) > latest_confirmed_at:
                    last_fetched_tx_hash = transaction_data.signature
                    transactions_data[last_fetched_tx_hash] = transaction_data
                else:
                    last_fetched_tx_hash = None
            if not fetch_transactions or not last_fetched_tx_hash:
                break

        wallet_list = [wallet]
        wallet_transaction_data_pairs = [
            (wallet, tx_data) for tx_data in transactions_data.values()
        ]
    else:
        wallet_dict = {
            wallet.address: wallet for wallet in wallet_manager.get_all_active(network, None)
        }
        wallet_list = list(wallet_dict.values())
        wallet_transaction_data_pairs = []
        for tx_data in transactions_data.values():
            # Priority: feePayer, fromUserAccount, toUserAccount (native transfers first)
            candidate_addresses = itertools.chain(
                (tx_data.feePayer,),
                (transfer.fromUserAccount for transfer in tx_data.nativeTransfers),
                (transfer.fromUserAccount for transfer in tx_data.tokenTransfers),
                (transfer.toUserAccount for transfer in tx_data.nativeTransfers),
                (transfer.toUserAccount for transfer in tx_data.tokenTransfers),
            )
            wallet = next(
                (
                    wallet_dict[address]
                    for address in candidate_addresses
                    if address in wallet_dict
                ),
                None,
            )
            if wallet:
                wallet_transaction_data_pairs.append((wallet, tx_data))
            else:
                if not (tx_data.nativeTransfers or tx_data.tokenTransfers):
                    logger.info(
                        "Ignoring transaction that doesn't involve any transfers and any of our own wallets: %s",
                        tx_data,
                    )
                else:
                    logger.error(
                        "Couldn't find wallet for transaction: %s",
                        tx_data,
                        stack_info=True,
                        extra={"tx": tx_data},
                    )

    for wallet in wallet_list:
        account_manager.get_or_create(wallet.address, network, api_name=wallet.name)

    transactions_to_save: list[tuple[Wallet, Transaction, list[Transfer]]] = []
    for wallet, transaction_data in wallet_transaction_data_pairs:
        transaction_from_account = account_manager.get_or_create(
            transaction_data.feePayer, network
        )
        accounts.append(transaction_from_account)
        transaction = Transaction(
            api_type=(
                transaction_data.type if transaction_data.type.lower() != "unknown" else None
            ),
            confirmed_at=datetime.fromtimestamp(transaction_data.timestamp),
            fees_paid=Decimal(transaction_data.fee) / NATIVE_CONVERSION,
            json_data=transaction_data.model_dump_json(),
            network=network,
            signer_account=transaction_from_account,
            tx_hash=transaction_data.signature,
            wallet=wallet,
        )
        transfers: list[Transfer] = []
        transactions_to_save.append((wallet, transaction, transfers))

        num_native_transfers = 0
        for transfer_data in transaction_data.nativeTransfers + transaction_data.tokenTransfers:
            asset_account = (
                account_manager.get_or_create(transfer_data.mint, network)
                if isinstance(transfer_data, TokenTransfer)
                else native_account
            )
            from_address = transfer_data.fromUserAccount
            to_address = transfer_data.toUserAccount
            transaction_source = transaction_data.source.lower()
            transaction_source = (
                transaction_source.replace("_", " ")
                if transaction_source != "unknown"
                else None
            )
            from_account = account_manager.get_or_create(
                from_address, network, api_name=transaction_source
            )
            to_account = account_manager.get_or_create(
                to_address, network, api_name=transaction_source
            )
            if from_account.wallet_id or to_account.wallet_id:
                # Skip WSOL transfers if SOL transfers are already added as they are doubly accounted for on the API
                # (more than one transfer since one for transaction fee and one for the unwrapped SOL)
                if not (
                    asset_account.address == "So11111111111111111111111111111111111111112"
                    and num_native_transfers > 1
                ):
                    accounts.append(asset_account)
                    accounts.append(from_account)
                    accounts.append(to_account)
                    transfers.append(
                        Transfer(
                            amount=Decimal(
                                transfer_data.tokenAmount
                                if isinstance(transfer_data, TokenTransfer)
                                else transfer_data.amount / NATIVE_CONVERSION
                            ),
                            asset_account=asset_account,
                            from_account=from_account,
                            to_account=to_account,
                        )
                    )
                    if asset_account is native_account:
                        num_native_transfers += 1

    transactions_to_save.sort(key=lambda x: x[0].id)
    for wallet, group in itertools.groupby(transactions_to_save, lambda x: x[0]):
        transactions_to_transfers = {
            transaction: transfers for _, transaction, transfers in group
        }
        transaction_manager.add_with_transfers(wallet, transactions_to_transfers)

    update_solana_accounts(db, set(accounts))

    return WalletSyncResult(
        network_name=network.name,
        provider="helius",
        provider_network="solana_mainnet",
        processed_transactions=len(transactions_to_save),
        errors=[],
    )


def update_solana_accounts(db: Session, accounts: set[Account]):
    """
    Saves the metadata for any Solana Account that doesn't have one set.

    Example response:
    {
        "mint": "7eVFaGC3WNsYZTr2kZvaFt7nVqTj8YJhBhi2gKTbASTA",
        "onChainData": {
          "collection": {
            "key": "9VCDbHxPfmMCCX9knepRybNkwebeyWNwxozssoypZ8iR",
            "verified": true
          },
          "collectionDetails": null,
          "data": {
            "creators": [
              {
                "address": "FUHcjJn9mPNUrczVNQJio7v8btSiaZYSyQu9HfhPWHY5",
                "share": 0,
                "verified": true
              }
            ],
            "name": "Sorcie #469",
            "sellerFeeBasisPoints": 199,
            "symbol": "SOR",
            "uri": "https://arweave.net/0mLpArC8DVFiJn-5LaOl4-j9QDimC__j-yymNygSgHc"
          },
          "editionNonce": 255,
          "isMutable": true,
          "key": "MetadataV1",
          "mint": "7eVFaGC3WNsYZTr2kZvaFt7nVqTj8YJhBhi2gKTbASTA",
          "primarySaleHappened": true,
          "tokenStandard": "NonFungible",
          "updateAuthority": "Aq76HttqnmEu7LupjXZVg1TWJXnSNgAntxTdJz7btbCG",
          "uses": null
        },
        "offChainData": {
          "attributes": [
            {
              "traitType": "Background",
              "value": "Soft Red"
            }
          ],
          "description": "Sorcies is a rugged collection of 7,777 unique NFTs on a journey to a Better Tomorrow. ",
          "image": "https://arweave.net/4ugnvv92dsHJcS3wrda6ZaPnkKbhz5CHhe1rPIB-wAg?ext=png",
          "name": "Sorcie #468",
          "properties": {
            "category": "image",
            "creators": [
              {
                "address": "H7a7ruq62NHTs4YagWnuxJzfySCb1zBxx9LzNQS8oesP",
                "share": 84
              }
            ],
            "files": [
              {
                "type": "image/png",
                "uri": "https://arweave.net/4ugnvv92dsHJcS3wrda6ZaPnkKbhz5CHhe1rPIB-wAg?ext=png"
              }
            ]
          },
          "sellerFeeBasisPoints": 199,
          "symbol": "SOR"
        }
      }
    """
    accounts_map: dict[str, Account] = {}
    addresses: list[str] = []
    metadata: list[TokenMetadataResult] = []
    position = 0
    nft_manager = NFTManager(db)
    for account in accounts:
        if not (account.name or account.ticker_symbol):
            accounts_map[account.address] = account
            addresses.append(account.address)

    while position < len(addresses):
        # Limited to 1000 addresses per call
        metadata += helius_client.get_tokens_metadata(addresses[position : position + 1000])
        position += 1000

    for account_data in metadata:
        # Helius DAS API can return null results for addresses
        if not account_data:
            continue

        account = accounts_map[account_data.id]
        if not account_data.content.metadata:
            continue

        content_metadata = account_data.content.metadata
        account.api_name = content_metadata.name
        account.ticker_symbol = content_metadata.symbol

        if content_metadata.token_standard == "NonFungible":
            attributes = [
                attribute.model_dump_json() for attribute in content_metadata.attributes
            ]
            nft = nft_manager.add_nft(
                attributes,
                account_data.content.links.image,
                int(account.name.split("#")[-1]) if "#" in account.name else None,
            )
            account.nft_id = nft.id

    db.commit()
