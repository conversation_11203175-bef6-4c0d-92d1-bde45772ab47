import { AddAuthenticatorLock } from "@/auth/authenticator-lock";
import { AuthenticatorLockSelect } from "@/auth/authenticator-lock-select";
import {
  useAddCredentialFlow,
  useDeleteCredential,
  useListCredentials,
  usePatchCredential,
} from "@/lib/auth";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { ConfirmationButton } from "@/lib/components/confirmation-dialog";
import * as Dialog from "@/lib/components/dialog";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { getCredentialRegistered, unregisterCredential } from "@/lib/secret-store";
import { formatDate } from "@/lib/utils/date";
import { Credential } from "@/types/schemas";
import { useMutation, useQuery } from "@tanstack/react-query";
import { cx } from "class-variance-authority";
import { Check, KeyRound, Pencil, Shield, Trash2, Wallet, X } from "lucide-react";
import { useState } from "react";

export function ManageSecurityButton() {
  const [show, setShow] = useState<"listing" | "add-lock" | "edit-lock">("listing");

  const handleDone = () => {
    setShow("listing");
  };
  const handleEditLock = () => {
    setShow("edit-lock");
  };

  const content =
    show === "listing" ? (
      <AuthenticatorsSection
        onAddAuthenticatorLock={() => {
          setShow("add-lock");
        }}
        onEditLock={handleEditLock}
      />
    ) : show === "add-lock" ? (
      <AddAuthenticatorLock onDone={handleDone} onEditLock={handleEditLock} />
    ) : (
      <AuthenticatorLockSelect onDone={handleDone} />
    );

  return (
    <Dialog.Root
      onOpenChange={(open) => {
        if (open) setShow("listing");
      }}
    >
      <Dialog.Trigger asChild>
        <Button variant="secondary">Manage Security</Button>
      </Dialog.Trigger>
      <Dialog.Content title="Security" aria-describedby="">
        {content}
      </Dialog.Content>
    </Dialog.Root>
  );
}

function AuthenticatorsSection(props: {
  onAddAuthenticatorLock: () => void;
  onEditLock: () => void;
}) {
  const { onAddAuthenticatorLock, onEditLock } = props;
  const listCredentials = useListCredentials();
  const credentialDeletion = useDeleteCredential();
  const credentialPatch = usePatchCredential();
  const addCredentialFlow = useAddCredentialFlow();

  const add2faCredential = useMutation({
    async mutationFn() {
      await addCredentialFlow("2fa");
    },
  });

  const walletAuthenticatorCredential = useQuery({
    queryKey: ["walletAuthenticatorCredential"],
    queryFn: getCredentialRegistered,
    refetchInterval: 5000,
  });
  const walletAuthenticatorCredentialId = walletAuthenticatorCredential.data?.id;

  return (
    <section className="grid gap-4 *:min-w-0">
      <h2 className="text-lg">Authenticators</h2>
      {listCredentials.isSuccess && (
        <>
          {credentialDeletion.error && (
            <AlertError onClear={credentialDeletion.reset}>
              Error deleting: {getErrorMessage(credentialDeletion.error)}
            </AlertError>
          )}
          {credentialPatch.error && (
            <AlertError onClear={credentialPatch.reset}>
              Error editing: {getErrorMessage(credentialPatch.error)}
            </AlertError>
          )}
          {listCredentials.data.length === 0 ? (
            <p>No authenticator registered</p>
          ) : (
            <ul className="grid gap-3 *:min-w-0">
              {listCredentials.data.map((credential) => (
                <AuthenticatorListItem
                  key={credential.id}
                  credential={credential}
                  active={walletAuthenticatorCredentialId === credential.id}
                  disabled={
                    listCredentials.isFetching ||
                    credentialPatch.isPending ||
                    credentialDeletion.isPending
                  }
                  onDelete={async () => {
                    if (credential.use_for === "wrap-key") {
                      const registeredCredential = await getCredentialRegistered();
                      if (registeredCredential?.id === credential.id) {
                        throw new Error(
                          "Cannot delete this wallet authenticator because it is active on this device, deactivate it first to be able to remove it.",
                        );
                      }
                    }
                    await credentialDeletion.mutateAsync(credential.id);
                    await listCredentials.refetch({ cancelRefetch: false });
                  }}
                  onPatch={async (name) => {
                    await credentialPatch.mutateAsync({ id: credential.id, name });
                    await listCredentials.refetch({ cancelRefetch: false });
                  }}
                />
              ))}
            </ul>
          )}
        </>
      )}
      {(listCredentials.isPending || add2faCredential.isPending) && (
        <Spinner className="justify-self-center" size={32} />
      )}
      {listCredentials.error && (
        <AlertError>{getErrorMessage(listCredentials.error)}</AlertError>
      )}
      <Button
        onClick={() => {
          add2faCredential.mutate();
        }}
      >
        Add login authenticator
      </Button>
      {!add2faCredential.isPending && add2faCredential.isError && (
        <AlertError onClear={add2faCredential.reset}>
          {getErrorMessage(add2faCredential.error)}
        </AlertError>
      )}
      {walletAuthenticatorCredentialId ? (
        <>
          <Button variant="secondary" onClick={onEditLock}>
            Manage wallet authenticator
          </Button>
          <ConfirmationButton
            action={async () => {
              await unregisterCredential(walletAuthenticatorCredentialId);
              await walletAuthenticatorCredential.refetch();
            }}
            message={
              <>
                <p className="mb-2">
                  This will make authenticator protected wallets <strong>inaccessible</strong>{" "}
                  through this wallet authenticator on this device, even if re-activated later.
                  This does <strong>not</strong> affect other devices.
                </p>
                <p>
                  Are you sure you want to deactivate the current wallet authenticator on this
                  device?
                </p>
              </>
            }
            title="Deactivate wallet authenticator"
            asChild
          >
            <Button variant="destructive">Deactivate wallet authenticator</Button>
          </ConfirmationButton>
        </>
      ) : (
        <Button
          disabled={walletAuthenticatorCredential.isPending}
          onClick={onAddAuthenticatorLock}
        >
          Activate wallet authenticator
        </Button>
      )}
    </section>
  );
}

function AuthenticatorListItem(props: {
  credential: Credential;
  active: boolean;
  disabled: boolean;
  onDelete: () => Promise<unknown>;
  onPatch: (name: string) => Promise<unknown>;
}) {
  const { credential, active, disabled, onDelete, onPatch } = props;
  const [isEditing, setEditing] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    let name = formData.get("name");
    if (typeof name !== "string") throw new Error("Invalid name");
    name = name.trim();
    if (name !== credential.name) {
      void onPatch(name).finally(() => {
        setEditing(false);
      });
    } else {
      setEditing(false);
    }
  };

  return (
    <li>
      <form
        className="grid grid-cols-[1fr_auto] items-center gap-4 rounded-sm border px-3 py-2 *:min-w-0"
        onSubmit={handleSubmit}
      >
        <div className="grid gap-0.5 *:min-w-0">
          {isEditing ? (
            <input
              aria-label="Credential name"
              name="name"
              className="-ml-1 rounded-sm bg-accent pl-1 text-accent-foreground"
              defaultValue={credential.name}
            />
          ) : (
            <span className="flex items-center gap-2">
              {credential.use_for === "2fa" ? (
                <span title="Login authenticator">
                  <KeyRound size={16} />
                </span>
              ) : (
                <span title="Wallet authenticator">
                  <Wallet size={16} />
                </span>
              )}
              {credential.name || "Unnamed"}
              {active && (
                <span
                  className="rounded-full bg-green-800 p-0.5 text-white"
                  title="Currently active wallet authenticator on this device"
                >
                  <Shield size={14} />
                </span>
              )}
            </span>
          )}
          <span className="text-sm opacity-80">
            Created at {formatDate(credential.created_at)}
          </span>
          <span className="text-sm opacity-80">
            Latest use at {formatDate(credential.latest_use_at)}
          </span>
        </div>
        <div className={cx("flex gap-1.5", !isEditing && "hidden")}>
          <Button
            type="submit"
            aria-label="Save"
            title="Save"
            disabled={disabled}
            variant="primary"
            size="icon"
          >
            <Check />
          </Button>
          <Button
            aria-label="Cancel"
            title="Cancel"
            disabled={disabled}
            variant="secondary"
            size="icon"
            onClick={() => {
              setEditing(false);
            }}
          >
            <X />
          </Button>
        </div>
        <div className={cx("flex gap-1.5", isEditing && "hidden")}>
          <Button
            aria-label="Edit"
            title="Edit"
            disabled={disabled}
            variant="secondary"
            size="icon"
            onClick={() => {
              setEditing(true);
            }}
          >
            <Pencil />
          </Button>
          <ConfirmationButton
            action={onDelete}
            message={
              <>
                {credential.use_for === "wrap-key" && (
                  <p>
                    Attention, you will <strong>lose access</strong> to the wallets protected by
                    this authenticator on other devices, even if you re-add it later.
                    You&apos;ll still be able to use your password as usual.
                  </p>
                )}
                <p>
                  Are you sure you want to delete <em className="italic">{credential.name}</em>{" "}
                  {credential.use_for === "2fa" ? "login" : "wallet"} authenticator?
                </p>
              </>
            }
            title="Delete authenticator"
            disabled={disabled}
            asChild
          >
            <Button
              aria-label="Delete"
              title="Delete"
              disabled={disabled}
              variant="destructive"
              size="icon"
            >
              <Trash2 />
            </Button>
          </ConfirmationButton>
        </div>
      </form>
    </li>
  );
}
