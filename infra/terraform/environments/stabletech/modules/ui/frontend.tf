locals {
  # We have to use dashes instead of dots in the access log bucket, because that bucket is not a website
  website_domain_name_dashed = replace(var.website_domain_name, ".", "-")
  access_log_kms_keys        = var.access_logs_kms_key_name == "" ? [] : [var.access_logs_kms_key_name]
  website_kms_keys           = var.website_kms_key_name == "" ? [] : [var.website_kms_key_name]
}

# ------------------------------------------------------------------------------
# CREATE THE WEBSITE BUCKET
# ------------------------------------------------------------------------------

resource "google_storage_bucket" "ui" {
  project = var.project_id

  name          = var.website_domain_name
  location      = var.website_location
  storage_class = var.website_storage_class

  uniform_bucket_level_access = true

  versioning {
    enabled = var.enable_versioning
  }

  website {
    main_page_suffix = var.index_page
    not_found_page   = var.not_found_page
  }

  dynamic "cors" {
    for_each = var.enable_cors ? ["cors"] : []
    content {
      origin          = var.cors_origins
      method          = var.cors_methods
      response_header = var.cors_extra_headers
      max_age_seconds = var.cors_max_age_seconds
    }
  }

  force_destroy = var.force_destroy_website

  dynamic "encryption" {
    for_each = local.website_kms_keys
    content {
      default_kms_key_name = encryption.value
    }
  }

  labels = var.custom_labels
  logging {
    log_bucket        = google_storage_bucket.access_logs.name
    log_object_prefix = var.access_log_prefix != "" ? var.access_log_prefix : local.website_domain_name_dashed
  }
}

# make bucket public
resource "google_storage_bucket_iam_member" "default" {
  bucket = google_storage_bucket.ui.name
  role   = "roles/storage.objectViewer"
  member = "allUsers"
}

# ---------------------------------------------------------------------------------------------------------------------
# CREATE A SEPARATE BUCKET TO STORE ACCESS LOGS
# ---------------------------------------------------------------------------------------------------------------------

resource "google_storage_bucket" "access_logs" {
  project = var.project_id

  # Use the dashed domain name
  name          = "${local.website_domain_name_dashed}-logs"
  location      = var.website_location
  storage_class = var.website_storage_class

  force_destroy = var.force_destroy_access_logs_bucket

  dynamic "encryption" {
    for_each = local.access_log_kms_keys
    content {
      default_kms_key_name = encryption.value
    }
  }

  lifecycle_rule {
    action {
      type = "Delete"
    }

    condition {
      age = var.access_logs_expiration_time_in_days
    }
  }
  labels = var.custom_labels
}

# ---------------------------------------------------------------------------------------------------------------------
# GRANT WRITER ACCESS TO GOOGLE ANALYTICS
# ---------------------------------------------------------------------------------------------------------------------

resource "google_storage_bucket_acl" "analytics_write" {
  bucket = google_storage_bucket.access_logs.name

  # The actual identity is '<EMAIL>', but
  # we're required to prefix that with the type of identity
  role_entity = ["WRITER:<EMAIL>"]
}

# ---------------------------------------------------------------------------------------------------------------------
# CREATE CNAME ENTRY IN CLOUD DNS
# ---------------------------------------------------------------------------------------------------------------------
# Note that the A record to the naked domain is created in the load balancer module
resource "google_dns_record_set" "www_dns" {
  project = var.project_id

  name         = "www.${var.dns_managed_zone.dns_name}"
  managed_zone = var.dns_managed_zone.name
  type         = "A"
  ttl          = var.dns_record_ttl
  rrdatas      = [module.load_balancer.load_balancer_ip_address]
}

resource "google_compute_managed_ssl_certificate" "default" {
  name = local.website_domain_name_dashed

  managed {
    domains = [var.website_domain_name, "www.${var.website_domain_name}"]
  }
}

module "load_balancer" {
  source = "github.com/gruntwork-io/terraform-google-load-balancer.git//modules/http-load-balancer?ref=v0.3.0"

  name                  = local.website_domain_name_dashed
  project               = var.project_id
  url_map               = google_compute_url_map.urlmap.self_link
  create_dns_entries    = true
  custom_domain_names   = [var.website_domain_name]
  dns_managed_zone_name = var.dns_managed_zone.name
  dns_record_ttl        = var.dns_record_ttl
  enable_http           = true
  enable_ssl            = true
  ssl_certificates      = [google_compute_managed_ssl_certificate.default.id]
  custom_labels         = var.custom_labels
}

# ------------------------------------------------------------------------------
# CREATE THE BACKEND BUCKET
# ------------------------------------------------------------------------------

resource "google_compute_backend_bucket" "static" {
  depends_on = [google_storage_bucket.ui]
  project    = var.project_id

  name             = "${local.website_domain_name_dashed}-bucket"
  bucket_name      = var.website_domain_name
  enable_cdn       = true
  compression_mode = "AUTOMATIC"
  cdn_policy {
    cache_mode         = "CACHE_ALL_STATIC"
    client_ttl         = 31622400
    default_ttl        = 3600
    max_ttl            = 31622400
    request_coalescing = true
  }
}

# ------------------------------------------------------------------------------
# CREATE THE URL MAP WITH THE BACKEND BUCKET AS DEFAULT SERVICE
# ------------------------------------------------------------------------------

resource "google_compute_url_map" "urlmap" {
  depends_on = [google_compute_backend_bucket.static]
  project    = var.project_id

  name        = "${local.website_domain_name_dashed}-url-map"
  description = "URL map for ${local.website_domain_name_dashed}"

  default_service = google_compute_backend_bucket.static.self_link
}
