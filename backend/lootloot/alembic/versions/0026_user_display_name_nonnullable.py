"""User display_name nonnullable

Revision ID: 0026
Revises: 0025
Create Date: 2024-05-10 13:54:51.938368

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0026"
down_revision = "0025"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Set NULL display_names to an empty string
    # Double quotes are needed around the table name as 'user' is a PSQL keyword
    op.execute(
        """
        UPDATE "user" SET display_name='' WHERE display_name IS NULL;
        """
    )
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("user", "display_name", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("user", "display_name", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###
