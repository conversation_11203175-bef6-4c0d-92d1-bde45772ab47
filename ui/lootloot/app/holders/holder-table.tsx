"use client";
import { AlertError } from "@/lib/components/alert";
import { headerButton } from "@/lib/components/data-table";
import * as Select from "@/lib/components/select";
import { Spinner } from "@/lib/components/spinner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/lib/components/table";
import { UserDisplay } from "@/lib/components/user-display";
import { getErrorMessage } from "@/lib/error";
import { currencyFormatter, numberFormatter } from "@/lib/formatters";
import { useHolderQuery, useListNFTCollectionsQuery } from "@/lib/holder";
import { useRouter, useSearchParams } from "@/lib/navigation";
import { HolderItem, HolderListResp, PublicNFTCollectionListItem } from "@/types/schemas";
import {
  ColumnDef,
  ExpandedState,
  Row,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { MinusCircle, PlusCircle } from "lucide-react";
import { useMemo, useState } from "react";

export function HolderTable() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sorting = searchParams?.get("sorting") ?? "";
  const nftCollectionUid = searchParams?.get("nft_collection_uid") ?? "";

  const setSearch = (key: string, value: string) => {
    if (!router) return;
    const newParams = new URLSearchParams(location.search);
    if (value && value !== "~") {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    router.replace(`?${newParams.toString()}`);
  };

  const setSortByCount = () => {
    setSearch("sorting", "by_count");
  };
  const setSortByValue = () => {
    setSearch("sorting", "by_value");
  };

  const handleSelect = (value: string) => {
    setSearch("nft_collection_uid", value);
  };

  const holderQuery = useHolderQuery(sorting, nftCollectionUid);
  const nftCollectionsQuery = useListNFTCollectionsQuery();

  const isFetching = holderQuery.isFetching || nftCollectionsQuery.isFetching;
  const isError = holderQuery.isError || nftCollectionsQuery.isError;
  const error = holderQuery.error ?? nftCollectionsQuery.error;

  if (isError) return <AlertError>{getErrorMessage(error)}</AlertError>;

  const selectedNftCollection = nftCollectionsQuery.data?.items.find(
    (collection) => collection.uid === nftCollectionUid,
  );
  const nftsCount =
    selectedNftCollection != null
      ? selectedNftCollection.nft_count
      : nftCollectionsQuery.data?.items.reduce(
          (acc, collection) => acc + collection.nft_count,
          0,
        );

  return (
    <div className="relative flex flex-col gap-4 sm:min-w-[520px]">
      <div className="flex gap-2">
        <SelectNFTCollection
          nftCollections={nftCollectionsQuery.data?.items ?? []}
          value={nftCollectionUid}
          onSelect={handleSelect}
        />
        <div className="relative flex h-9 min-w-24 flex-none items-center justify-center rounded-sm bg-secondary px-1.5 text-center text-xs font-medium">
          {nftsCount != null && <span>{numberFormatter.format(nftsCount)} NFTs</span>}
          {isFetching && (
            <div className="absolute inset-0 grid place-items-center">
              <Spinner size={36} />
            </div>
          )}
        </div>
      </div>
      <HolderDataTable
        data={holderQuery.data ?? { items: [], sol_usd_price: "" }}
        setSortByCount={setSortByCount}
        setSortByValue={setSortByValue}
      />
    </div>
  );
}

interface HolderDataTableProps {
  data: HolderListResp;
  setSortByCount: () => void;
  setSortByValue: () => void;
}

interface ProcessedHolderItem extends HolderItem {
  subItems?: HolderItem[] | undefined;
}

function HolderDataTable({ data, setSortByCount, setSortByValue }: HolderDataTableProps) {
  const [expanded, setExpanded] = useState<ExpandedState>({});

  const columns: ColumnDef<ProcessedHolderItem, React.ReactNode>[] = [
    {
      accessorKey: "rank",
      header: "Rank",
      cell({ getValue }) {
        return <div className="text-center">{getValue()}</div>;
      },
    },
    {
      accessorKey: "value",
      header: "Name",
      cell({ row }) {
        const item = row.original;
        return (
          <div className="flex items-center gap-2">
            {row.getCanExpand() ? (
              <button
                onClick={row.getToggleExpandedHandler()}
                className="flex-none rounded-full focus-visible:ring-2 focus-visible:outline-hidden"
              >
                {row.getIsExpanded() ? (
                  <>
                    <span className="sr-only">Collapse</span>
                    <MinusCircle size={16} />
                  </>
                ) : (
                  <>
                    <span className="sr-only">Expand</span>
                    <PlusCircle size={16} />
                  </>
                )}
              </button>
            ) : null}
            <UserDisplay
              kind={item.kind}
              value={item.value}
              isOwn={item.own}
              showOwnLabel={!isUserOwnTopRows(row)}
              className="flex-1"
            />
          </div>
        );
      },
    },
    {
      accessorKey: "nft_count",
      header: () => (
        <div className="text-right">
          <button onClick={setSortByCount} className={headerButton()}>
            Count
          </button>
        </div>
      ),
      cell({ getValue }) {
        return <div className="text-right tabular-nums">{getValue()}</div>;
      },
    },
    {
      id: "usd_amount",
      accessorFn: (row: ProcessedHolderItem) =>
        currencyFormatter.format(
          Number.parseFloat(row.sol_amount) * Number.parseFloat(data.sol_usd_price),
        ),
      header: () => (
        <div className="text-right">
          <button onClick={setSortByValue} className={headerButton()}>
            Value $
          </button>
        </div>
      ),
      cell({ getValue }) {
        return <div className="text-right tabular-nums">{getValue()}</div>;
      },
    },
  ];

  // Limit the holder list to 100 items, and always include the user's own entries at the top
  const processedItems = useMemo((): ProcessedHolderItem[] => {
    const [firstOwnItem, ...restOwnItems] = data.items.filter((item) => item.own);
    const topOwnItem: ProcessedHolderItem | undefined =
      firstOwnItem != null && restOwnItems.length > 0
        ? { ...firstOwnItem, subItems: restOwnItems }
        : firstOwnItem;

    const holderItems = data.items.length > 100 ? data.items.slice(0, 100) : data.items;
    if (topOwnItem != null) {
      return [topOwnItem, ...holderItems];
    }
    return holderItems;
  }, [data.items]);

  const table = useReactTable({
    data: processedItems,
    columns,
    state: { expanded },
    onExpandedChange: setExpanded,
    getSubRows: (row) => row.subItems,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  const rowModel = table.getRowModel();

  return (
    <Table>
      <TableHeader>
        {table.getHeaderGroups().map((headerGroup) => (
          <TableRow key={headerGroup.id}>
            {headerGroup.headers.map((header) => {
              return (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              );
            })}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {rowModel.rows.map((row) => (
          <TableRow
            key={row.id}
            data-state={row.getIsSelected() && "selected"}
            className={
              isUserOwnTopRows(row)
                ? "bg-primary/5 hover:bg-primary/10! dark:bg-primary/20 dark:hover:bg-primary/30!"
                : undefined
            }
          >
            {row.getVisibleCells().map((cell) => (
              <TableCell key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </TableCell>
            ))}
          </TableRow>
        ))}
        {rowModel.rows.length === 0 && (
          <TableRow>
            <TableCell colSpan={columns.length} className="h-24 text-center">
              No results.
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}

function isUserOwnTopRows(row: Row<ProcessedHolderItem>): boolean {
  return row.original.own && (row.index === 0 || row.depth > 0);
}

interface SelectNFTCollectionProps {
  nftCollections: PublicNFTCollectionListItem[];
  value: string;
  onSelect: (value: string) => void;
}

function SelectNFTCollection({ nftCollections, value, onSelect }: SelectNFTCollectionProps) {
  return (
    <Select.Root onValueChange={onSelect} value={value}>
      <Select.Trigger placeholder="All collections" />
      <Select.Content>
        <Select.Item value="~">
          <span className="text-muted-foreground">All collections</span>
        </Select.Item>
        {nftCollections.map((nftCollection) => (
          <Select.Item key={nftCollection.uid} value={nftCollection.uid}>
            {nftCollection.name}
          </Select.Item>
        ))}
      </Select.Content>
    </Select.Root>
  );
}
