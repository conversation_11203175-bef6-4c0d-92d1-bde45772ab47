import crypto from "crypto";
import { Transfer } from "../types";
import { TransferLog } from "../types/abi-interfaces/Erc20Abi";

function getTransferId(
  blockNumber: number,
  index: number | string,
  txHash: string,
  fromAddress: string,
  toAddress: string,
  network: string,
): string {
  const idMaterial = `${blockNumber}-${index}-${txHash}-${fromAddress}-${toAddress}`;
  const hash = crypto.createHash("sha256").update(idMaterial, "utf8").digest();
  return `${network}-${Buffer.from(hash).toString("base64url")}`;
}

export async function handleEthereumLog(log: TransferLog): Promise<void> {
  await handleLog(log, "ethereum");
}

async function handleLog(log: TransferLog, network: "ethereum"): Promise<void> {
  logger.info(`New ${network} transfer transaction log at block ${log.args}`);
  if (!log.args) {
    logger.error(`No log.args found ${log}`);
    return;
  }

  const transaction = Transfer.create({
    id: getTransferId(
      log.blockNumber,
      log.logIndex,
      log.transactionHash,
      log.args.from,
      log.args.to,
      network,
    ),
    tx_hash: log.transactionHash, // Store transaction hash separately
    blockNumber: BigInt(log.blockNumber),
    to_address: log.args.to,
    from_address: log.args.from,
    value: log.args.value.toBigInt(),
    contractAddress: log.address,
    network: network,
    timestamp: log.transaction.blockTimestamp, // Current timestamp as fallback
  });

  await transaction.save();
}
