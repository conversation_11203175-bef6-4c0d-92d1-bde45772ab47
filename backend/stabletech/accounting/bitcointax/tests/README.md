# Capital Position Tests

## Run

Ssh into the api pod, go to the `backend/stabletech` folder and run pytest

```bash
cd /stabletech
pytest -vv -o log_cli=true
```

## Test Infra

We are creating a new database for each test case, so we avoid having to clean up the database
after each test. Leveraging `pytest_postgresql.janitor` DataJanitor it will create a new
database for each test case and clean it up after the test case is done.

This greatly simplifies data management and later on we can run the tests in parallel if needed.

It is all currently residing in the `conftest.py` file in the
`backend/stabletech/accounting/bitcointax/tests` folder. We can abstract it out once we find the
need for tests in the rest of the codebase.
