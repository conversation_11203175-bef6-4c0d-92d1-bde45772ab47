// ** Types
import { HorizontalNavItemsType, NavGroup, NavLink } from "@/@core/layouts/types";

// ** Custom Navigation Components
import { Settings } from "@/@core/context/settingsContext";
import HorizontalNavGroup from "@/@core/layouts/components/horizontal/navigation/HorizontalNavGroup";
import HorizontalNavLink from "@/@core/layouts/components/horizontal/navigation/HorizontalNavLink";

interface Props {
  hasParent?: boolean;
  horizontalNavItems?: HorizontalNavItemsType;
  settings: Settings;
}
const resolveComponent = (item: NavGroup | NavLink) => {
  if ((item as NavGroup).children) return HorizontalNavGroup;

  return HorizontalNavLink;
};

const HorizontalNavItems = (props: Props) => {
  const RenderMenuItems = props.horizontalNavItems?.map(
    (item: NavGroup | NavLink, index: number) => {
      const TagName = resolveComponent(item);

      return <TagName {...props} key={index} item={item} />;
    },
  );

  return <>{RenderMenuItems}</>;
};

export default HorizontalNavItems;
