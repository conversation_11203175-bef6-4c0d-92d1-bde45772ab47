import { apiQuery } from "@/api/api";
import { User } from "@/types/schemas";
import { useQuery, UseQueryResult } from "@tanstack/react-query";

export function useUsersQuery({
  includeAdmins = false,
  enabled = true,
}: {
  includeAdmins?: boolean | undefined;
  enabled?: boolean | undefined;
} = {}): UseQueryResult<User[]> {
  return useQuery({
    queryKey: ["auth", "users", { include_admins: includeAdmins }],
    queryFn: apiQuery<User[]>,
    enabled,
  });
}
