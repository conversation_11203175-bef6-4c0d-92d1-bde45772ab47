"""Initial

Revision ID: 0001
Revises:
Create Date: 2025-02-06 19:09:04.426861

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "0001"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "cubesignersession",
        sa.Column("session_name", sa.String(), nullable=False),
        sa.Column("data", postgresql.BYTEA(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("session_name"),
    )
    op.create_table(
        "user",
        sa.Column("email", sa.String(), nullable=False),
        sa.Column(
            "credentials_id",
            sa.Uuid(),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
        sa.Column("first_name", sa.String(), nullable=True),
        sa.Column("last_name", sa.String(), nullable=True),
        sa.Column("picture_url", sa.String(), nullable=True),
        sa.Column("cubist_user_id", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_user_credentials_id"), "user", ["credentials_id"], unique=True)
    op.create_index(op.f("ix_user_cubist_user_id"), "user", ["cubist_user_id"], unique=True)
    op.create_index(op.f("ix_user_email"), "user", ["email"], unique=True)
    op.create_table(
        "credential",
        sa.Column("id", sa.LargeBinary(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("public_key", sa.LargeBinary(), nullable=False),
        sa.Column("backed_up", sa.Boolean(), nullable=False),
        sa.Column("device_type", sa.String(), nullable=False),
        sa.Column("transports", postgresql.ARRAY(sa.String()), nullable=False),
        sa.Column("sign_count", sa.BigInteger(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "latest_use_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("user_credentials_id", sa.Uuid(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_credentials_id"],
            ["user.credentials_id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_credential_user_credentials_id"),
        "credential",
        ["user_credentials_id"],
        unique=False,
    )
    op.create_table(
        "ongoingcredentialauthentication",
        sa.Column("challenge", sa.LargeBinary(), nullable=False),
        sa.Column("require_user_verification", sa.Boolean(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_ongoingcredentialauthentication_user_id"),
        "ongoingcredentialauthentication",
        ["user_id"],
        unique=True,
    )
    op.create_table(
        "ongoingcredentialregistration",
        sa.Column("challenge", sa.LargeBinary(), nullable=False),
        sa.Column("require_user_verification", sa.Boolean(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_ongoingcredentialregistration_user_id"),
        "ongoingcredentialregistration",
        ["user_id"],
        unique=True,
    )
    op.create_table(
        "usersession",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("user_agent", sa.String(), nullable=False),
        sa.Column("ip_address", sa.String(), nullable=False),
        sa.Column("token", sa.String(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_usersession_token"), "usersession", ["token"], unique=True)
    op.create_index(op.f("ix_usersession_user_id"), "usersession", ["user_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_usersession_user_id"), table_name="usersession")
    op.drop_index(op.f("ix_usersession_token"), table_name="usersession")
    op.drop_table("usersession")
    op.drop_index(
        op.f("ix_ongoingcredentialregistration_user_id"),
        table_name="ongoingcredentialregistration",
    )
    op.drop_table("ongoingcredentialregistration")
    op.drop_index(
        op.f("ix_ongoingcredentialauthentication_user_id"),
        table_name="ongoingcredentialauthentication",
    )
    op.drop_table("ongoingcredentialauthentication")
    op.drop_index(op.f("ix_credential_user_credentials_id"), table_name="credential")
    op.drop_table("credential")
    op.drop_index(op.f("ix_user_email"), table_name="user")
    op.drop_index(op.f("ix_user_cubist_user_id"), table_name="user")
    op.drop_index(op.f("ix_user_credentials_id"), table_name="user")
    op.drop_table("user")
    op.drop_table("cubesignersession")
    # ### end Alembic commands ###
