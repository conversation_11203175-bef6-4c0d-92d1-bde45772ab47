"use client";

import {
  ReadonlyURLSearchParams,
  use<PERSON><PERSON><PERSON> as useNextRouter,
  useSearchPara<PERSON> as useNextSearchParams,
} from "next/navigation";
import { createContext, useContext } from "react";

const RouterCtx = createContext<ReturnType<typeof useNextRouter> | undefined>(undefined);
const SearchParamsCtx = createContext<ReadonlyURLSearchParams | undefined>(undefined);

export function useRouter() {
  return useContext(RouterCtx);
}

export function useSearchParams() {
  return useContext(SearchParamsCtx);
}

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const router = useNextRouter();
  const searchParams = useNextSearchParams();
  return (
    <RouterCtx.Provider value={router}>
      <SearchParamsCtx.Provider value={searchParams}>{children}</SearchParamsCtx.Provider>
    </RouterCtx.Provider>
  );
}
