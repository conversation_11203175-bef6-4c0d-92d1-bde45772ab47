import { AddProps, BaseAdd, nameUrlFields } from "@/components/add/common";
import { useIndexerBridgeAddMutation } from "@/store/services/api";
import { useState } from "react";

export const BridgeAdd = (props: AddProps) => {
  const [addBridge] = useIndexerBridgeAddMutation();
  const [nameValue, setNameValue] = useState("");
  const [urlValue, setUrlValue] = useState("");

  return (
    <BaseAdd
      close={props.close}
      addMutation={addBridge}
      disableAdd={!nameValue || !urlValue}
      label={"Add a new Bridge"}
      onSuccess={props.onSuccess}
      submitData={{ name: nameValue, url: urlValue }}
      fields={nameUrlFields(nameValue, setNameValue, urlValue, setUrlValue)}
    />
  );
};
