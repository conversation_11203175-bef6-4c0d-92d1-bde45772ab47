"""Create US Dollar Coin

Revision ID: 0002
Revises: 0001
Create Date: 2022-11-07 08:00:43.301441

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import table

# revision identifiers, used by Alembic.
revision = "0002"
down_revision = "0001"
branch_labels = None
depends_on = None


def upgrade() -> None:
    coin = table(
        "coin",
        sa.<PERSON>n("has_prices", sa.<PERSON>()),
        sa.<PERSON>("name", sa.String()),
        sa.<PERSON>("ticker", sa.String()),
        sa.<PERSON>umn("uid", sa.String()),
    )
    op.bulk_insert(
        coin,
        [{"has_prices": True, "name": "US Dollar", "ticker": "dollar", "uid": "dollar"}],
        multiinsert=False,
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
