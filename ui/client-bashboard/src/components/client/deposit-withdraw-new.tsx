import { useNetworksQuery, usePendingTransactionsQuery, useWalletsQuery } from "@/api/wallets";
import { DepositWithdrawDialog } from "@/components/client/deposit-withdraw-dialog";
import { AddWalletDialog, EditWalletDialog } from "@/components/client/wallet-dialogs";
import { Alert } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Spinner } from "@/components/ui/spinner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { addressFormatter, formatDate } from "@/helpers/formatters";
import { AutoLayout } from "@/layouts/auto";
import { Container } from "@/layouts/NewLayout";
import { useMarketCoinsUsableByClientsQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Pencil } from "lucide-react";
import { ReactElement } from "react";

export function DepositWithdrawNew() {
  // Data fetching
  const walletsQuery = useWalletsQuery();
  const networksQuery = useNetworksQuery();
  const pendingTransactionsQuery = usePendingTransactionsQuery();
  const { data: supportedCoins = [] } = useMarketCoinsUsableByClientsQuery();

  // Loading states
  const isLoading =
    walletsQuery.isLoading || networksQuery.isLoading || pendingTransactionsQuery.isLoading;

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Spinner className="size-10 text-primary" />
      </div>
    );
  }

  // Error states
  if (walletsQuery.isError || networksQuery.isError || pendingTransactionsQuery.isError) {
    return (
      <Container className="py-8">
        <Alert variant="error">
          {getErrorDetail(
            walletsQuery.error || networksQuery.error || pendingTransactionsQuery.error,
            { fallback: "Failed to load data" },
          )}
        </Alert>
      </Container>
    );
  }

  const wallets = walletsQuery.data || [];
  const networks = networksQuery.data || [];
  const pendingTransactions = pendingTransactionsQuery.data || [];

  return (
    <Container className="mb-8 flex flex-col gap-4 py-6">
      {/* Wallet Overview Section */}
      <Card>
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-xl font-semibold">Your Wallets</h2>
          <AddWalletDialog networks={networks}>
            <Button>Add Wallet</Button>
          </AddWalletDialog>
        </div>
        <Table className="mt-4">
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Address</TableHead>
              <TableHead>Networks</TableHead>
              <TableHead className="w-9" />
            </TableRow>
          </TableHeader>
          <TableBody>
            {wallets.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="py-4 text-center text-muted-foreground">
                  No wallets found. Add a wallet to get started.
                </TableCell>
              </TableRow>
            ) : (
              wallets.map((wallet) => (
                <TableRow key={wallet.id}>
                  <TableCell>{wallet.name}</TableCell>
                  <TableCell>
                    <span title={wallet.address}>{addressFormatter(wallet.address)}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {wallet.networks.map((network) => (
                        <span
                          key={network.id}
                          className="inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                        >
                          {network.name}
                        </span>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="w-9 pr-1! pl-0! text-right">
                    <EditWalletDialog networks={networks} selectedWallet={wallet}>
                      <Button variant="ghost" size="icon">
                        <Pencil className="size-5 text-foreground/60" />
                        <span className="sr-only">Edit</span>
                      </Button>
                    </EditWalletDialog>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Pending Transactions Section */}
      <Card>
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-xl font-semibold">Pending Transactions</h2>
          <div className="flex gap-2">
            <DepositWithdrawDialog isDeposit={true} coins={supportedCoins} networks={networks}>
              <Button>Deposit</Button>
            </DepositWithdrawDialog>
            <DepositWithdrawDialog isDeposit={false} coins={supportedCoins} networks={networks}>
              <Button>Withdraw</Button>
            </DepositWithdrawDialog>
          </div>
        </div>
        <Table className="mt-4">
          <TableHeader>
            {/* Desktop view headers (hidden on small screens, visible on sm and up) */}
            <TableRow className="hidden sm:table-row">
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Coin</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
            {/* Mobile view headers (visible on small screens, hidden on sm and up) */}
            <TableRow className="sm:hidden">
              <TableHead>Type</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pendingTransactions.length === 0 ? (
              <TableRow>
                {/* Desktop empty state */}
                <TableCell
                  colSpan={5}
                  className="hidden py-4 text-center text-muted-foreground sm:table-cell"
                >
                  No pending transactions.
                </TableCell>
                {/* Mobile empty state */}
                <TableCell
                  colSpan={3}
                  className="py-4 text-center text-muted-foreground sm:hidden"
                >
                  No pending transactions.
                </TableCell>
              </TableRow>
            ) : (
              pendingTransactions.map((transaction) => {
                const amountFormatted = formatAmount(transaction.amount);
                const tickerFormatted = transaction.coin.ticker.toUpperCase();
                const dateFormatted = formatDate(transaction.createdAt);
                return (
                  <TableRow key={transaction.id}>
                    {/* Desktop data cells */}
                    <TableCell className="hidden sm:table-cell">{dateFormatted}</TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {transaction.isDeposit ? "Deposit" : "Withdrawal"}
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">{amountFormatted}</TableCell>
                    <TableCell className="hidden sm:table-cell">{tickerFormatted}</TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <span className="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500">
                        Pending
                      </span>
                    </TableCell>
                    {/* Mobile data cells */}
                    <TableCell className="sm:hidden">
                      <div>{transaction.isDeposit ? "Deposit" : "Withdrawal"}</div>
                      <div className="text-xs">{dateFormatted}</div>
                    </TableCell>
                    <TableCell className="sm:hidden">
                      <div>{amountFormatted}</div>
                      <div className="text-xs">{tickerFormatted}</div>
                    </TableCell>
                    <TableCell className="sm:hidden">
                      <span className="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500">
                        Pending
                      </span>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </Card>
    </Container>
  );
}

DepositWithdrawNew.getLayout = function getLayout(page: ReactElement) {
  return <AutoLayout>{page}</AutoLayout>;
};

DepositWithdrawNew.getSpinner = function getSpinner() {
  return (
    <AutoLayout>
      <div className="flex h-64 items-center justify-center">
        <Spinner className="size-10 text-primary" />
      </div>
    </AutoLayout>
  );
};

DepositWithdrawNew.acl = {
  action: "read",
  subject: "Position",
};

/**
 * Formats deposit/withdraw amount with comma separators for thousands
 * and removes trailing zeros after decimal point.
 *
 * @param value - The raw amount value to format
 * @returns The formatted amount with commas as thousand separators
 */
function formatAmount(value: string) {
  if (!value) return "";
  // Split integer and decimal parts
  let [int, dec] = value.split(".");

  // Format integer part with commas - use regex for comma positioning
  // We need to use replace with regex for thousands separators positioning
  int = int.replaceAll(/\B(?=(\d{3})+(?!\d))/gu, ",");

  // Remove trailing zeros from decimal part
  // If decimal part is undefined or empty, return only the integer part
  if (dec) {
    dec = dec.replace(/0+$/u, "").replace(/\.$/u, "");
  }

  return dec ? `${int}.${dec}` : int;
}
