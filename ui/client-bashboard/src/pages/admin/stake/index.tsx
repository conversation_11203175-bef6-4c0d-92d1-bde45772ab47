import Link from "@/components/Link";
import { StakeProcessed, getAssetDisplayName, processStake } from "@/helpers/account";
import { currencyFormatter } from "@/helpers/formatters";
import { useIndexerStakeQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { <PERSON>ert, Card, CardContent, CardHeader, Tooltip, Typography } from "@mui/material";
import {
  DataGridPro,
  GridCellParams,
  GridColDef,
  GridInitialState,
} from "@mui/x-data-grid-pro";

type Row = StakeProcessed;

const INITIAL_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "farm", sort: "asc" }] },
};

export default function IncomePage() {
  const { data, error, isFetching } = useIndexerStakeQuery();

  const columns = computeColumns();
  const rows = error != null || data == null ? [] : data.map(processStake);

  const errorAlert = error != null && (
    <Alert severity="error">
      {getErrorDetail(error, { fallback: "Failed loading stake page" })}
    </Alert>
  );

  return (
    <Card>
      <CardHeader title="Stake" />
      <CardContent>
        {errorAlert}
        <DataGridPro
          columns={columns}
          rows={rows}
          initialState={INITIAL_STATE}
          loading={isFetching}
          getRowId={getRowId}
          autoHeight
          disableSelectionOnClick
        />
      </CardContent>
    </Card>
  );
}

function computeColumns(): GridColDef<Row, string | number | null>[] {
  return [
    {
      field: "farm",
      headerName: "Farm",
      flex: 1,
      renderCell: ({ row }) => (
        <Link href={`/admin/stake/${row.farm.id}/${row.network.id}/${row.asset.coin?.id}`}>
          {row.farm.name}
        </Link>
      ),
    },
    {
      field: "network",
      headerName: "Network",
      flex: 1,
      valueGetter: ({ row }: GridCellParams) => row.network.name,
    },
    {
      field: "assetName",
      headerName: "Asset",
      flex: 1,
      valueGetter: ({ row }: GridCellParams) => getAssetDisplayName(row.asset),
    },
    {
      field: "amount",
      headerName: "Amount Staked",
      headerAlign: "right",
      align: "right",
      flex: 1,
      valueGetter: ({ row }) => row.amount,
      renderCell: ({ row }) =>
        row.amount >= 0 ? (
          currencyFormatter.format(row.amount)
        ) : (
          <Tooltip title="Profit">
            <Typography
              fontFamily="inherit"
              fontSize="inherit"
              fontStyle="inherit"
              fontWeight="inherit"
              color="success.main"
            >
              {currencyFormatter.format(row.amount)}
            </Typography>
          </Tooltip>
        ),
    },
    {
      field: "currentValue",
      headerName: "Current $ Value",
      headerAlign: "right",
      align: "right",
      flex: 1,
      valueGetter: ({ row }) =>
        row.asset.coin != null && row.asset.coin.latestUsdPriceUpdatedAt != null
          ? row.asset.coin.latestUsdPrice * row.amount
          : null,
      renderCell: ({ value }) =>
        typeof value === "number" ? currencyFormatter.format(value) : "—",
    },
  ];
}

function getRowId(row: Row) {
  return `${row.farm.id}-${row.asset.id}`;
}
