import background from "@/assets/desert-dunes.jpg";
import { useLinks } from "@/layouts/components/navigation";
import { cx } from "cva";
import * as React from "react";
import { Toaster } from "sonner";

const TOAST_OPTIONS = {
  classNames: {
    toast: "font-sans",
    success: "*:data-[icon]:text-green-600",
    error: "*:data-[icon]:text-destructive",
    info: "*:data-[icon]:text-blue-600",
    warning: "*:data-[icon]:text-amber-600",
  },
} as const;

interface Props {
  readonly children: React.ReactNode;
  readonly hiddenNav?: boolean | undefined;
}
export default function LayoutSahaba({ children, hiddenNav = false }: Props) {
  const links = useLinks();

  return (
    <div
      className={cx(
        "min-h-dvh overflow-hidden bg-gray-50 font-sans text-base font-normal text-foreground",
        !hiddenNav && "pb-16 md:pb-0",
      )}
      style={{
        backgroundImage: `url(${background.src})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundAttachment: "fixed",
      }}
    >
      <header className="mb-4 bg-white shadow-xs">
        <Container className="flex h-16 items-center gap-4">
          <h1 className="text-2xl font-semibold text-primary">SAHABA</h1>
          <nav
            className={cx("ml-auto hidden h-12 items-center gap-8", !hiddenNav && "md:flex")}
          >
            {links}
          </nav>
        </Container>
      </header>
      {children}
      <nav
        className={cx(
          "fixed bottom-0 flex h-16 w-full items-center border-t border-gray-100 bg-white shadow-xs",
          hiddenNav ? "hidden" : "md:hidden",
        )}
      >
        <Container className="flex items-center justify-around gap-4 *:w-18">{links}</Container>
      </nav>
      <Toaster toastOptions={TOAST_OPTIONS} />
    </div>
  );
}

export function Container({
  children,
  className,
  component: Component = "div",
}: {
  children: React.ReactNode;
  className?: string | undefined;
  component?: React.ElementType | undefined;
}) {
  return (
    <Component className={cx("container mx-auto px-4 lg:max-w-5xl!", className)}>
      {children}
    </Component>
  );
}
