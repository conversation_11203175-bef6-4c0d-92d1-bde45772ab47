"""Remove indexer models

Revision ID: 0011
Revises: 0010
Create Date: 2024-01-30 19:32:52.133509

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0011"
down_revision = "0010"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_lendingprotocol_created_by_user_id", table_name="lendingprotocol")
    op.drop_index("ix_lendingprotocol_id", table_name="lendingprotocol")
    op.drop_index("ix_lendingprotocol_name", table_name="lendingprotocol")
    op.execute('DROP TABLE "lendingprotocol" CASCADE')
    op.drop_index("ix_transaction_action", table_name="transaction")
    op.drop_index("ix_transaction_created_by_user_id", table_name="transaction")
    op.drop_index("ix_transaction_id", table_name="transaction")
    op.drop_index("ix_transaction_network_id", table_name="transaction")
    op.drop_index("ix_transaction_signer_account_id", table_name="transaction")
    op.drop_index("ix_transaction_wallet_id", table_name="transaction")
    op.execute('DROP TABLE "transaction" CASCADE')
    op.execute('DROP TABLE "network_wallet_association" CASCADE')
    op.drop_index("ix_account_address", table_name="account")
    op.drop_index("ix_account_api_name", table_name="account")
    op.drop_index("ix_account_coin_id", table_name="account")
    op.drop_index("ix_account_id", table_name="account")
    op.drop_index("ix_account_network_id", table_name="account")
    op.drop_index("ix_account_nft_id", table_name="account")
    op.drop_index("ix_account_pool_id", table_name="account")
    op.drop_index("ix_account_ticker_symbol", table_name="account")
    op.drop_index("ix_account_wallet_id", table_name="account")
    op.execute('DROP TABLE "account" CASCADE')
    op.drop_index("ix_farm_created_by_user_id", table_name="farm")
    op.drop_index("ix_farm_id", table_name="farm")
    op.drop_index("ix_farm_name", table_name="farm")
    op.execute('DROP TABLE "farm" CASCADE')
    op.drop_index("ix_transfer_asset_account_id", table_name="transfer")
    op.drop_index("ix_transfer_from_account_id", table_name="transfer")
    op.drop_index("ix_transfer_id", table_name="transfer")
    op.drop_index("ix_transfer_to_account_id", table_name="transfer")
    op.drop_index("ix_transfer_transaction_id", table_name="transfer")
    op.execute('DROP TABLE "transfer" CASCADE')
    op.drop_index("ix_wallet_address", table_name="wallet")
    op.drop_index("ix_wallet_created_by_user_id", table_name="wallet")
    op.drop_index("ix_wallet_id", table_name="wallet")
    op.drop_index("ix_wallet_is_active", table_name="wallet")
    op.drop_index("ix_wallet_is_usable_by_clients", table_name="wallet")
    op.drop_index("ix_wallet_name", table_name="wallet")
    op.drop_index("ix_wallet_user_id", table_name="wallet")
    op.drop_index("ix_wallet_user_id_address", table_name="wallet")
    op.drop_index("ix_wallet_user_id_name", table_name="wallet")
    op.execute('DROP TABLE "wallet" CASCADE')
    op.drop_index("ix_exchange_created_by_user_id", table_name="exchange")
    op.drop_index("ix_exchange_id", table_name="exchange")
    op.drop_index("ix_exchange_name", table_name="exchange")
    op.execute('DROP TABLE "exchange" CASCADE')
    op.drop_index("ix_network_created_by_user_id", table_name="network")
    op.drop_index("ix_network_id", table_name="network")
    op.drop_index("ix_network_is_usable_by_clients", table_name="network")
    op.drop_index("ix_network_name", table_name="network")
    op.drop_index("ix_network_native_coin_id", table_name="network")
    op.execute('DROP TABLE "network" CASCADE')
    op.drop_index("ix_nft_id", table_name="nft")
    op.execute('DROP TABLE "nft" CASCADE')
    op.execute('DROP TABLE "coin_network_association" CASCADE')
    op.drop_index("ix_job_status_id", table_name="job")
    op.execute('DROP TABLE "job" CASCADE')
    op.drop_index("ix_pool_farm_id", table_name="pool")
    op.drop_index("ix_pool_id", table_name="pool")
    op.drop_index("ix_pool_network_id", table_name="pool")
    op.execute('DROP TABLE "pool" CASCADE')
    op.drop_index("ix_coin_id", table_name="coin")
    op.drop_index("ix_coin_is_usable_by_clients", table_name="coin")
    op.drop_index("ix_coin_uid", table_name="coin")
    op.execute('DROP TABLE "coin" CASCADE')
    op.drop_index("ix_bridge_created_by_user_id", table_name="bridge")
    op.drop_index("ix_bridge_id", table_name="bridge")
    op.drop_index("ix_bridge_name", table_name="bridge")
    op.execute('DROP TABLE "bridge" CASCADE')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "bridge",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('bridge_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("url", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"], ["user.id"], name="bridge_created_by_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="bridge_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_bridge_name", "bridge", ["name"], unique=True)
    op.create_index("ix_bridge_id", "bridge", ["id"], unique=False)
    op.create_index(
        "ix_bridge_created_by_user_id", "bridge", ["created_by_user_id"], unique=False
    )
    op.create_table(
        "coin",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('coin_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("has_prices", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column(
            "is_usable_by_clients",
            sa.BOOLEAN(),
            server_default=sa.text("false"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("ticker", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("uid", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "latest_usd_price",
            sa.NUMERIC(precision=32, scale=18),
            server_default=sa.text("'0'::numeric"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "latest_usd_price_updated_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id", name="coin_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_coin_uid", "coin", ["uid"], unique=True)
    op.create_index(
        "ix_coin_is_usable_by_clients", "coin", ["is_usable_by_clients"], unique=False
    )
    op.create_index("ix_coin_id", "coin", ["id"], unique=False)
    op.create_table(
        "job",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("kind", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("status", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("completed_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column(
            "data", postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False
        ),
        sa.Column("data_text", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("error", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "result",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id", name="job_pkey"),
    )
    op.create_index("ix_job_status_id", "job", ["status", "id"], unique=True)
    op.create_table(
        "network",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('network_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("covalent_chain_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("explorer_url", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "is_usable_by_clients",
            sa.BOOLEAN(),
            server_default=sa.text("false"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("native_coin_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"], ["user.id"], name="network_created_by_user_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["native_coin_id"], ["coin.id"], name="network_native_coin_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="network_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_network_native_coin_id", "network", ["native_coin_id"], unique=False)
    op.create_index("ix_network_name", "network", ["name"], unique=True)
    op.create_index(
        "ix_network_is_usable_by_clients", "network", ["is_usable_by_clients"], unique=False
    )
    op.create_index("ix_network_id", "network", ["id"], unique=False)
    op.create_index(
        "ix_network_created_by_user_id", "network", ["created_by_user_id"], unique=False
    )
    op.create_table(
        "coin_network_association",
        sa.Column("coin_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("network_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["coin_id"], ["coin.id"], name="coin_network_association_coin_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["network_id"], ["network.id"], name="coin_network_association_network_id_fkey"
        ),
        sa.PrimaryKeyConstraint("coin_id", "network_id", name="coin_network_association_pkey"),
    )
    op.create_table(
        "nft",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('nft_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column(
            "attributes",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("image_url", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("uid", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.PrimaryKeyConstraint("id", name="nft_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_nft_id", "nft", ["id"], unique=False)
    op.create_table(
        "exchange",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('exchange_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("url", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"], ["user.id"], name="exchange_created_by_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="exchange_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_exchange_name", "exchange", ["name"], unique=True)
    op.create_index("ix_exchange_id", "exchange", ["id"], unique=False)
    op.create_index(
        "ix_exchange_created_by_user_id", "exchange", ["created_by_user_id"], unique=False
    )
    op.create_table(
        "wallet",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('wallet_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("address", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("is_active", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column("is_usable_by_clients", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"], ["user.id"], name="wallet_created_by_user_id_fkey"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], name="wallet_user_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="wallet_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index(
        "ix_wallet_user_id_name",
        "wallet",
        [sa.text("COALESCE(user_id, 0)"), "name"],
        unique=True,
    )
    op.create_index(
        "ix_wallet_user_id_address",
        "wallet",
        [sa.text("COALESCE(user_id, 0)"), "address"],
        unique=True,
    )
    op.create_index("ix_wallet_user_id", "wallet", ["user_id"], unique=False)
    op.create_index("ix_wallet_name", "wallet", ["name"], unique=False)
    op.create_index(
        "ix_wallet_is_usable_by_clients", "wallet", ["is_usable_by_clients"], unique=False
    )
    op.create_index("ix_wallet_is_active", "wallet", ["is_active"], unique=False)
    op.create_index("ix_wallet_id", "wallet", ["id"], unique=False)
    op.create_index(
        "ix_wallet_created_by_user_id", "wallet", ["created_by_user_id"], unique=False
    )
    op.create_index("ix_wallet_address", "wallet", ["address"], unique=False)
    op.create_table(
        "farm",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('farm_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("url", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"], ["user.id"], name="farm_created_by_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="farm_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_farm_name", "farm", ["name"], unique=True)
    op.create_index("ix_farm_id", "farm", ["id"], unique=False)
    op.create_index("ix_farm_created_by_user_id", "farm", ["created_by_user_id"], unique=False)
    op.create_table(
        "lendingprotocol",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("url", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "liquidation_thresholds",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"], ["user.id"], name="lendingprotocol_created_by_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="lendingprotocol_pkey"),
    )
    op.create_index("ix_lendingprotocol_name", "lendingprotocol", ["name"], unique=True)
    op.create_index("ix_lendingprotocol_id", "lendingprotocol", ["id"], unique=False)
    op.create_index(
        "ix_lendingprotocol_created_by_user_id",
        "lendingprotocol",
        ["created_by_user_id"],
        unique=False,
    )
    op.create_table(
        "pool",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('pool_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("exchange_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("farm_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "is_closed",
            sa.BOOLEAN(),
            server_default=sa.text("false"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("network_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("url", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["exchange_id"], ["exchange.id"], name="pool_exchange_id_fkey"),
        sa.ForeignKeyConstraint(["farm_id"], ["farm.id"], name="pool_farm_id_fkey"),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"], name="pool_network_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="pool_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_pool_network_id", "pool", ["network_id"], unique=False)
    op.create_index("ix_pool_id", "pool", ["id"], unique=False)
    op.create_index("ix_pool_farm_id", "pool", ["farm_id"], unique=False)
    op.create_table(
        "account",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('account_id_seq'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("address", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("network_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("wallet_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("api_name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("ticker_symbol", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("bridge_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("coin_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("exchange_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("farm_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("lending_protocol_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("nft_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("pool_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(["bridge_id"], ["bridge.id"], name="account_bridge_id_fkey"),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"], name="account_coin_id_fkey"),
        sa.ForeignKeyConstraint(
            ["exchange_id"], ["exchange.id"], name="account_exchange_id_fkey"
        ),
        sa.ForeignKeyConstraint(["farm_id"], ["farm.id"], name="account_farm_id_fkey"),
        sa.ForeignKeyConstraint(
            ["lending_protocol_id"],
            ["lendingprotocol.id"],
            name="account_lending_protocol_id_fkey",
        ),
        sa.ForeignKeyConstraint(["network_id"], ["network.id"], name="account_network_id_fkey"),
        sa.ForeignKeyConstraint(["nft_id"], ["nft.id"], name="account_nft_id_fkey"),
        sa.ForeignKeyConstraint(["pool_id"], ["pool.id"], name="account_pool_id_fkey"),
        sa.ForeignKeyConstraint(["wallet_id"], ["wallet.id"], name="account_wallet_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="account_pkey"),
        sa.UniqueConstraint("address", "network_id", name="account_address_network_id_key"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_account_wallet_id", "account", ["wallet_id"], unique=False)
    op.create_index("ix_account_ticker_symbol", "account", ["ticker_symbol"], unique=False)
    op.create_index("ix_account_pool_id", "account", ["pool_id"], unique=False)
    op.create_index("ix_account_nft_id", "account", ["nft_id"], unique=False)
    op.create_index("ix_account_network_id", "account", ["network_id"], unique=False)
    op.create_index("ix_account_id", "account", ["id"], unique=False)
    op.create_index("ix_account_coin_id", "account", ["coin_id"], unique=False)
    op.create_index("ix_account_api_name", "account", ["api_name"], unique=False)
    op.create_index("ix_account_address", "account", ["address"], unique=False)
    op.create_table(
        "transaction",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("action", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("api_type", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("confirmed_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("created_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("json_data", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "fees_paid", sa.NUMERIC(precision=32, scale=18), autoincrement=False, nullable=True
        ),
        sa.Column(
            "has_approval",
            sa.BOOLEAN(),
            server_default=sa.text("false"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "is_collateral",
            sa.BOOLEAN(),
            server_default=sa.text("false"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("is_deposit", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column(
            "native_coin_dollar_price",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("network_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("signer_account_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("tx_hash", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("wallet_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "comment",
            sa.VARCHAR(),
            server_default=sa.text("''::character varying"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"], ["user.id"], name="transaction_created_by_user_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["network_id"], ["network.id"], name="transaction_network_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["signer_account_id"], ["account.id"], name="transaction_signer_account_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["wallet_id"], ["wallet.id"], name="transaction_wallet_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="transaction_pkey"),
        sa.UniqueConstraint("network_id", "tx_hash", name="transaction_network_id_tx_hash_key"),
    )
    op.create_index("ix_transaction_wallet_id", "transaction", ["wallet_id"], unique=False)
    op.create_index(
        "ix_transaction_signer_account_id", "transaction", ["signer_account_id"], unique=False
    )
    op.create_index("ix_transaction_network_id", "transaction", ["network_id"], unique=False)
    op.create_index("ix_transaction_id", "transaction", ["id"], unique=False)
    op.create_index(
        "ix_transaction_created_by_user_id", "transaction", ["created_by_user_id"], unique=False
    )
    op.create_index("ix_transaction_action", "transaction", ["action"], unique=False)
    op.create_table(
        "transfer",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("asset_account_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "asset_price_usd",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "asset_prices_json",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "amount", sa.NUMERIC(precision=48, scale=18), autoincrement=False, nullable=False
        ),
        sa.Column(
            "nft_id", sa.NUMERIC(precision=32, scale=0), autoincrement=False, nullable=True
        ),
        sa.Column("from_account_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("to_account_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("transaction_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["asset_account_id"], ["account.id"], name="transfer_asset_account_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["from_account_id"], ["account.id"], name="transfer_from_account_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["to_account_id"], ["account.id"], name="transfer_to_account_id_fkey"
        ),
        sa.ForeignKeyConstraint(
            ["transaction_id"], ["transaction.id"], name="transfer_transaction_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="transfer_pkey"),
    )
    op.create_index("ix_transfer_transaction_id", "transfer", ["transaction_id"], unique=False)
    op.create_index("ix_transfer_to_account_id", "transfer", ["to_account_id"], unique=False)
    op.create_index("ix_transfer_id", "transfer", ["id"], unique=False)
    op.create_index(
        "ix_transfer_from_account_id", "transfer", ["from_account_id"], unique=False
    )
    op.create_index(
        "ix_transfer_asset_account_id", "transfer", ["asset_account_id"], unique=False
    )
    op.create_table(
        "network_wallet_association",
        sa.Column("network_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("wallet_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["network_id"],
            ["network.id"],
            name="network_wallet_association_network_id_fkey",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["wallet_id"],
            ["wallet.id"],
            name="network_wallet_association_wallet_id_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint(
            "network_id", "wallet_id", name="network_wallet_association_pkey"
        ),
        sa.UniqueConstraint(
            "network_id",
            "wallet_id",
            name="network_wallet_association_network_id_wallet_id_key",
        ),
    )
    # ### end Alembic commands ###
