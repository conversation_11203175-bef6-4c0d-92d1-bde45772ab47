"""Load objects from a dump file."""

import argparse
import importlib
import json
import sys
from typing import Any, TextIO

import psycopg.errors
import sentry_sdk
from sqlalchemy.exc import IntegrityError, MultipleResultsFound

from common.conf import settings
from common.models import BaseModel
from common.psql import SessionLocal
from scripts.dump_objects import Ref, Value
from stabletech.indexer.models import Transfer

# Disable sentry
sentry_sdk.init(None)

db = SessionLocal()


def resolve(dump_item: Any) -> Any:
    match dump_item:
        case int() | str() | bool() | float() | None:
            return dump_item
        case {"kind": "Value"}:
            return resolve_value(dump_item)
        case {"kind": "Ref"}:
            return resolve_ref(dump_item)
        case _:
            raise ValueError(f"Invalid item: {dump_item!r}")


def resolve_value(value_dump: dict[str, Any]) -> Any:
    if value_dump["kind"] != "Value":
        raise ValueError("Invalid value")

    value = Value(**value_dump)
    if value.args:
        value.args = [resolve(arg) for arg in value.args]
    if value.kwargs:
        value.kwargs = {k: resolve(v) for k, v in value.kwargs.items()}

    module = importlib.import_module(value.module)
    constructor = getattr(module, value.constructor)
    if value.method:
        constructor = getattr(constructor, value.method)

    args = value.args or ()
    kwargs = value.kwargs or {}
    try:
        resolved = constructor(*args, **kwargs)
    except ValueError as e:
        raise ValueError(f"Error resolving {value!r}") from e

    if isinstance(resolved, BaseModel):
        persist(resolved)

    return resolved


def resolve_ref(ref_dump: dict[str, Any]) -> int:
    if ref_dump["kind"] != "Ref":
        raise ValueError("Invalid ref")

    ref = Ref(**ref_dump)
    ref.filter_by = {k: resolve(v) for k, v in ref.filter_by.items()}

    module = importlib.import_module(ref.module)
    model = getattr(module, ref.model)
    try:
        ref_id = db.query(model.id).filter_by(**ref.filter_by).scalar()
        if ref_id is None:
            raise ValueError(f"No result found for {ref!r}")
        return ref_id
    except MultipleResultsFound as e:
        raise ValueError(f"Multiple results found for {ref!r}") from e


def persist(obj: BaseModel) -> None:
    if isinstance(obj, Transfer):
        # Transfer only unique constraint is the id, so we will only add
        # if a similar transfer doesn't already exists
        transfer_id = (
            db.query(Transfer.id)
            .filter_by(
                asset_account_id=obj.asset_account_id,
                amount=obj.amount,
                from_account_id=obj.from_account_id,
                to_account_id=obj.to_account_id,
                transaction_id=obj.transaction_id,
            )
            .limit(1)
            .scalar()
        )
        if transfer_id is not None:
            print(f"⚠️ Skipped: {obj!r} -- Similar transfer already exists")
            return

    db.add(obj)
    try:
        db.commit()
        print(f"✅ {obj!r}")
    except IntegrityError as err:
        db.rollback()
        if isinstance(err.orig, psycopg.errors.UniqueViolation):
            print(f"⚠️ Skipped: {obj!r} -- {err.orig.diag.message_detail}")
        else:
            raise


def load_objects(file: TextIO):
    dump: list[dict[str, Any]] = json.load(file)
    for value in dump:
        resolve_value(value)


if __name__ == "__main__":
    if settings.is_production:
        print("Error: should not be run in production")
        sys.exit(1)
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "file",
        type=argparse.FileType("r", encoding="utf_8"),
        nargs="?",
        default=sys.stdin,
        help="Path to input file, defaults to stdin",
    )
    args = parser.parse_args()
    load_objects(args.file)
