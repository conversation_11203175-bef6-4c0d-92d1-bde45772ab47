from typing import Generic, TypeVar

from sqlalchemy import select, text
from sqlalchemy.orm import Session

from common.models import BaseModel

T = TypeVar("T", bound=BaseModel)


class BaseManager(Generic[T]):
    db: Session
    model: type[T]
    order_by: str | None = None

    def __init__(self, db: Session):
        self.db = db

    def add(self, schema, **kwargs) -> T:
        return self.add_model(self.model(**schema.model_dump(), **kwargs))

    def add_model(self, model_instance: T, *, commit: bool = True) -> T:
        with self.db.begin_nested():
            self.db.add(model_instance)
        if commit:
            self.db.commit()
        return model_instance

    def get_all(self):
        # Get all rows in the table
        query = select(self.model)
        if self.order_by:
            query = query.order_by(text(self.order_by))
        return self.db.scalars(query).all()

    def get_by_id(self, model_id: int):
        # Get a row by ID
        return self.db.scalars(select(self.model).filter_by(id=model_id)).one()
