from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Literal

from pydantic import BaseModel

# --------------------------------------------
# In-Game Trade Session
# --------------------------------------------


class Rarity(Enum):
    # Ordered by ascending rarity
    COMMON = "Common"  # 0
    UNCOMMON = "Uncommon"  # 1
    RARE = "Rare"  # 2
    EPIC = "Epic"  # 3
    LEGENDARY = "Legendary"  # 4
    MYTHIC = "Mythic"  # 5

    @property
    def weight(self) -> int:
        # Rarity expressed as a number - greater weights are more valuable.
        return next(i for i, rarity in enumerate(Rarity) if rarity == self)


class FungibleAsset(BaseModel):
    # Asset Metadata
    cardBackgroundImage: str
    category: str | None
    id: str
    image: str
    name: str


class FungibleEntry(BaseModel):
    amount: Decimal
    fungibleAsset: FungibleAsset
    fungibleAssetType: str
    participant: Literal["Initiator", "Recipient"]
    tradingSessionId: int

    @property
    def is_deposit(self) -> bool:
        return self.participant == "Initiator"

    @property
    def is_nana(self) -> bool:
        return self.fungibleAssetType == "NANA"


class Effect(BaseModel):
    id: int
    description: str
    effectType: Literal["Combat", "Exploration", "Stealth", "Yield"]


class ItemEffect(BaseModel):
    effect: Effect = None  # Missing from some messages
    effectId: int
    itemId: str
    value: int


class ConsumableMetadata(BaseModel):
    id: int
    dropChance: Decimal
    image: str
    name: str


NftSpecies = Literal["Chimp", "Gorilla", "Orangutan"]


class ItemMetadata(ConsumableMetadata):
    nftSpecies: NftSpecies | None


class Consumable(BaseModel):
    id: str
    consumableMetadata: ConsumableMetadata
    expirationDate: datetime | None
    expirationType: str
    rarity: Rarity
    walletId: str

    @property
    def metadata(self) -> ConsumableMetadata:
        return self.consumableMetadata

    @property
    def asset_type(self):
        return "consumable"


class CosmeticMetadata(BaseModel):
    id: int
    attributeName: str  # Body part the Cosmetic is for
    attributeValue: str
    baseWeight: Decimal
    displayImage: str | None
    image: str
    isBase: bool
    name: str
    nftSpecies: NftSpecies | None
    orderIndex: int
    rarity: Rarity


class Cosmetic(BaseModel):
    id: int
    cosmeticMetadata: CosmeticMetadata
    cosmeticMetadataId: int
    nftId: str | None
    walletId: str

    @property
    def asset_type(self):
        return "cosmetic"

    @property
    def metadata(self) -> CosmeticMetadata:
        return self.cosmeticMetadata

    @property
    def rarity(self):
        return self.cosmeticMetadata.rarity


class Item(BaseModel):
    id: str
    isDeleted: bool
    itemEffects: list[ItemEffect]
    itemMetadata: ItemMetadata
    rarity: Rarity
    walletId: str

    @property
    def asset_type(self):
        return "item"

    @property
    def metadata(self) -> ItemMetadata:
        return self.itemMetadata


class NonFungibleEntry(BaseModel):
    id: int
    consumable: Consumable | None = None  # Missing from some messages
    consumableId: str | None
    cosmetic: Cosmetic | None
    cosmeticId: int | None
    item: Item | None
    itemId: str | None
    tradingSessionId: int

    @property
    def asset(self) -> Consumable | Cosmetic | Item:
        asset = None
        if self.consumable:
            asset = self.consumable
        elif self.cosmetic:
            asset = self.cosmetic
        elif self.item:
            asset = self.item
        assert asset is not None
        return asset

    @property
    def asset_id(self) -> str:
        return getattr(self, f"{self.asset.asset_type}Id")

    @property
    def asset_name(self) -> str:
        return self.asset.metadata.name

    def get_is_deposit(self, bot_wallet_address: str) -> bool:
        return self.asset.walletId != bot_wallet_address


class TradeStatus(Enum):
    CONFIRM = "Confirm"
    PENDING = "Pending"
    READY = "Ready"


class TradeSession(BaseModel):
    id: int
    initiatorId: str  # User's wallet address
    initiatorStatus: TradeStatus
    recipientStatus: TradeStatus
    tradingSessionFungibleEntries: list[FungibleEntry] = []
    tradingSessionNonFungibleEntries: list[NonFungibleEntry] = []

    @property
    def is_empty(self) -> bool:
        return not (
            self.tradingSessionNonFungibleEntries
            or sum(
                fungible_entry.amount for fungible_entry in self.tradingSessionFungibleEntries
            )
            > 0
        )

    def get_nana_amount(self) -> Decimal:
        try:
            nana_entry = next(
                fungible_entry
                for fungible_entry in self.tradingSessionFungibleEntries
                if fungible_entry.is_nana
            )
            return nana_entry.amount
        except StopIteration:
            return Decimal("0")
