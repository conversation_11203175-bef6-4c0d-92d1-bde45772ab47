import { addressFormatter } from "@/helpers/formatters";
import { useClientWalletsQuery, useIndexerWalletsQuery } from "@/store/services/api";
import { Network, Wallet } from "@/types/schemas";
import { Autocomplete, TextField } from "@mui/material";
import { skipToken } from "@reduxjs/toolkit/query";
import { Dispatch, useEffect, useRef } from "react";

interface WalletSearchProps {
  additionalWallet?: Wallet;
  disabled?: boolean;
  isAdmin: boolean;
  label?: string;
  network: Network | null;
  onInitialLoad?: (wallets: readonly Wallet[]) => void;
  selectedWallet: Wallet | null;
  setSelectedWallet: Dispatch<Wallet | null>;
}

export const WalletSearch = (props: WalletSearchProps) => {
  const { onInitialLoad, selectedWallet, setSelectedWallet } = props;

  const adminWalletsQuery = useIndexerWalletsQuery([props.network, undefined], {
    skip: !props.isAdmin,
  });
  const clientWalletsQuery = useClientWalletsQuery(props.network || skipToken, {
    skip: props.isAdmin,
  });
  const walletsQuery = props.isAdmin ? adminWalletsQuery : clientWalletsQuery;

  const label = props.label ?? "Wallet";

  const options = walletsQuery.isSuccess
    ? props.additionalWallet
      ? [props.additionalWallet, ...walletsQuery.data]
      : walletsQuery.data
    : [];

  const handledInitialLoadRef = useRef(false);
  useEffect(() => {
    if (handledInitialLoadRef.current || !walletsQuery.isSuccess) return;
    handledInitialLoadRef.current = true;
    onInitialLoad?.(options);
  });

  return (
    <Autocomplete
      disabled={props.disabled}
      fullWidth
      renderInput={(params) => (
        <TextField
          {...params}
          label={label + (props.network ? ` (${props.network.name})` : "")}
        />
      )}
      options={options}
      getOptionLabel={(option) => `${option.name} (${addressFormatter(option.address)})`}
      onChange={(event: unknown, newValue: Wallet | null) => {
        setSelectedWallet(newValue);
      }}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      value={selectedWallet}
    />
  );
};
