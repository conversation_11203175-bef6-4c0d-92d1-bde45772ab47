## Getting Started

This projects uses [pnpm](https://pnpm.io/installation) for managing packages, make sure to have
it installed and up-to-date.

Then proceed to install the dependencies and run the development server:

```bash
pnpm install
pnpm dev
```

Open [http://localhost:3001](http://localhost:3001) with your browser to see the result.

## Components

This project uses [Radix UI] primitives for building accessible components and [Tailwind CSS]
for styling them. Also, we use [shadcn/ui] as a reference for building our own components when
possible.

## Resources

- [Radix UI] - collection of accessible, unstyled React components
- [shadcn/ui] - collection of copyable components built using Radix UI and Tailwind CSS
- [Tailwind CSS] - a utility-first CSS framework
- [tanstack/react-query] - data fetching and caching library for React
- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.
- [pnpm] - fast, disk space efficient package manager

[Radix UI]: https://www.radix-ui.com/primitives
[shadcn/ui]: https://ui.shadcn.com/
[Tailwind CSS]: https://tailwindcss.com/
[tanstack/react-query]: https://tanstack.com/query/latest/docs/react/overview
[pnpm]: https://pnpm.io/installation

## Local Development

### Steps

1. Port forward the lootloot api to localhost at port 8080 or any port you want, can use Lens ->
   Network -> Services -> api -> Forward -> Local port to forward from: 8080
2. Run lootloot with `pnpm dev`. Or, if using a port other than 8080,
   `LOCAL_API_PORT=<port> pnpm dev`.
