from enum import StrEnum
from typing import Literal

NetworkGroup = Literal["evm", "solana"]


class UserOrigin(StrEnum):
    google = "google"
    phantom = "phantom"
    matrica = "matrica"


class WalletOrigin(StrEnum):
    phantom = "phantom"
    matrica = "matrica"


class AuctionHouse(StrEnum):
    magic_eden = "magic_eden"


class HolderSorting(StrEnum):
    by_count = "by_count"
    by_value = "by_value"
