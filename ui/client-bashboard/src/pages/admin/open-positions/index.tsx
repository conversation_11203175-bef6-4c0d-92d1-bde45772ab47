import Spinner from "@/@core/components/spinner";
import Link from "@/components/Link";
import { getAccountedTransactionCoinDisplayName, getNumber } from "@/helpers/accounting";
import { getCoinDisplayName } from "@/helpers/coin";
import { currencyFormatter, formatDateTime } from "@/helpers/formatters";
import { useAccountingOpenPositionListSummaryQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AccountedTransaction, OpenPositionSummary } from "@/types/schemas";
import { HelpOutline } from "@mui/icons-material";
import {
  Alert,
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { useMemo } from "react";

type SummaryRow = OpenPositionSummary;
type AccountedTransactionRow = AccountedTransaction;

const INITIAL_SUMMARY_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "coin", sort: "asc" }] },
};

const INITIAL_ACCOUNTED_TRANSACTIONS_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "latestConfirmedAt", sort: "desc" }] },
};
export default function OpenPositionSummaryList() {
  const { data, error } = useAccountingOpenPositionListSummaryQuery();
  const summaryColumns = useMemo(computeSummaryColumns, []);
  const accountedTransactionColumns = useMemo(computeAccountedTransactionColumns, []);

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed loading open positions" })}
      </Alert>
    );
  }

  if (data == null) {
    return <Spinner />;
  }

  const { summaries, latestAccountedTransactions, totalCost, totalUnrealizedPnl, totalValue } =
    data;

  return (
    <Grid container gap={6}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Grid container spacing={6}>
              <Grid item xs={6} md={4}>
                <Tooltip title="Calculated from Classified Transactions">
                  <Typography variant={"h4"}>
                    Open Positions Overview
                    <HelpOutline sx={{ fontSize: "1.5rem" }} />
                  </Typography>
                </Tooltip>
              </Grid>
              <Grid item xs={12}>
                <Stack
                  direction="row"
                  columnGap={12}
                  rowGap={4}
                  justifyContent="center"
                  flexWrap="wrap"
                >
                  <Box textAlign="center">
                    <Typography noWrap>
                      {currencyFormatter.format(Number.parseFloat(totalCost))}
                    </Typography>
                    <Typography noWrap variant="body2" color={"primary"}>
                      Total Cost
                    </Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography noWrap>
                      {currencyFormatter.format(Number.parseFloat(totalValue))}
                    </Typography>
                    <Typography noWrap variant="body2" color={"primary"}>
                      Total Value
                    </Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography noWrap>
                      $ {currencyFormatter.format(Number.parseFloat(totalUnrealizedPnl))}
                    </Typography>
                    <Typography noWrap variant="body2" color={"primary"}>
                      Total Unrealized PnL
                    </Typography>
                  </Box>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <DataGridPro
                  columns={summaryColumns}
                  getRowId={getSummaryRowId}
                  initialState={INITIAL_SUMMARY_STATE}
                  rows={summaries}
                  autoHeight
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12}>
        <Card>
          <CardHeader
            title={
              <Stack direction="row" gap={4} alignItems="flex-end">
                <span>Accounted Transactions</span>
              </Stack>
            }
          />
          <CardContent>
            <DataGridPro
              columns={accountedTransactionColumns}
              getRowId={getAccountedTransactionRowId}
              initialState={INITIAL_ACCOUNTED_TRANSACTIONS_STATE}
              rows={latestAccountedTransactions || []}
              autoHeight
              disableSelectionOnClick
            />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

function getSummaryRowId(row: SummaryRow) {
  return row.coin.id;
}

function computeSummaryColumns(): GridColDef<SummaryRow, string | number>[] {
  return [
    {
      field: "coin",
      headerName: "Coin",
      flex: 1,
      renderCell: ({ row }) => (
        <Link href={`/admin/open-positions/${row.coin.id}`}>
          {getCoinDisplayName(row.coin)}
        </Link>
      ),
      valueGetter: ({ row }) => getCoinDisplayName(row.coin),
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.amount),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.amount)),
    },
    {
      field: "avgCost",
      headerName: "$ Avg Cost",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.avgCost),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.avgCost)),
    },
    {
      field: "cost",
      headerName: "$ Cost",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.cost),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.cost)),
    },
    {
      field: "value",
      headerName: "$ Value",
      flex: 1,
      width: 50,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.value),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.value)),
    },
    {
      field: "unrealizedPnl",
      headerName: "$ Unrealized PnL",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.unrealizedPnl),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.unrealizedPnl)),
    },
    {
      field: "openConfirmedAt",
      headerName: "Opened At",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => row.openConfirmedAt,
      renderCell: ({ row }) => formatDateTime(row.openConfirmedAt),
    },
  ];
}

function computeAccountedTransactionColumns(): GridColDef<
  AccountedTransactionRow,
  string | number
>[] {
  return [
    {
      field: "asset",
      headerName: "Asset",
      flex: 1,
      valueGetter: ({ row }) => getAccountedTransactionCoinDisplayName(row),
    },
    {
      field: "action",
      headerName: "Action",
      flex: 1,
      valueGetter: ({ row }) => row.action,
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.amount),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.amount)),
    },
    {
      field: "remainingAmount",
      headerName: "Remaining Amount",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.remainingAmount),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.remainingAmount)),
    },
    {
      field: "assetPriceUsd",
      headerName: "$ Price",
      flex: 1,
      valueGetter: ({ row }) => getNumber(row.assetPriceUsd),
      renderCell: ({ row }) => currencyFormatter.format(getNumber(row.assetPriceUsd)),
    },
    {
      field: "confirmedAt",
      headerName: "Confirmed At",
      flex: 1,
      renderCell: ({ row }) => formatDateTime(row.confirmedAt),
    },
    {
      field: "matchedCapitalPositionsCount",
      headerName: "Matches",
      flex: 1,
      valueGetter: ({ row }) => row.matchedCapitalPositionsCount || 0,
    },
    {
      field: "flattenedTransactionsCount",
      headerName: "Txns",
      flex: 1,
      valueGetter: ({ row }) => row.flattenedTransactionsCount || 0,
    },
    {
      field: "loanWithdrawalsConsumedCount",
      headerName: "Loan Withdrawals",
      flex: 1,
      valueGetter: ({ row }) => row.loanWithdrawalsConsumedCount || 0,
    },
  ];
}

function getAccountedTransactionRowId(row: AccountedTransactionRow) {
  return row.id;
}
