import Spinner from "@/@core/components/spinner";
import { SimpleTransactionTable } from "@/components/SimpleTransactionTable";
import { SnackSuccess } from "@/components/SnackSuccess";
import FilteredByCoinDisplay from "@/components/admin/FilteredByCoinDisplay";
import { UserSearch } from "@/components/autocomplete/UserSearch";
import { PortfolioOverview } from "@/components/dashboard/PortfolioOverview";
import { enableDashboardAdminUser, hasEarnProduct } from "@/context/env";
import { currencyFormatter, formatPercentage } from "@/helpers/formatters";
import { processDashboard, useAverageCost } from "@/helpers/portfolio-calculations";
import { useTransactionsFiltering } from "@/helpers/transactions-filtering";
import { useAppSelector } from "@/hooks/store";
import { selectUser } from "@/store/apps/auth";
import { useClientDashboardQuery } from "@/store/services/api";
import { User } from "@/types/schemas";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, Grid, Stack, Typography } from "@mui/material";
import { skipToken } from "@reduxjs/toolkit/query";
import { useEffect, useMemo, useRef, useState } from "react";

const DashboardOld = () => {
  const user = useAppSelector(selectUser);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  if (!user && !selectedUser) {
    return <Spinner />;
  }

  return (
    <Grid container spacing={6}>
      {user && user.role == "admin" && (
        <>
          <Grid item xs={12}>
            <Grid container spacing={6}>
              <Grid item sx={{ display: "flex", alignItems: "center" }}>
                <Typography variant={"h5"}>View another User&apos;s dashboard</Typography>
              </Grid>
              <Grid item xs={4}>
                <UserSearch
                  includeUser={
                    enableDashboardAdminUser
                      ? { ...user, firstName: "ADMIN", lastName: "" }
                      : undefined
                  }
                  setDefaultUser
                  selectedUser={selectedUser}
                  setSelectedUser={setSelectedUser}
                />
              </Grid>
            </Grid>
          </Grid>
          <DashboardContent selectedUser={selectedUser} />
        </>
      )}
      {user && user.role != "admin" && <DashboardContent selectedUser={user} />}
    </Grid>
  );
};

DashboardOld.acl = {
  action: "read",
  subject: "Position",
};

export { DashboardOld };

interface DashboardContentProps {
  selectedUser: User | null;
}

const DashboardContent = ({ selectedUser }: DashboardContentProps) => {
  const [showSuccessSnack, setShowSuccessSnack] = useState(false);
  const { data: dashboard } = useClientDashboardQuery(selectedUser || skipToken, {
    pollingInterval: 10_000,
  });

  const transactionsRef = useRef<HTMLDivElement>(null);
  const transactionsFiltering = useTransactionsFiltering(dashboard);
  const averageCost = useAverageCost(
    transactionsFiltering.transactions,
    transactionsFiltering.selectedCoinId,
  );

  const showInterest = hasEarnProduct && !!selectedUser?.earnsInterest;

  // Get the interest rate only if interest is enabled
  const interestRate =
    showInterest && selectedUser ? Number.parseFloat(selectedUser.interestRate) : 0;

  const processedData = useMemo(() => {
    if (!dashboard) return null;
    return processDashboard(dashboard, interestRate);
  }, [dashboard, interestRate]);

  if (!selectedUser) return null;

  if (!processedData) {
    return (
      <Grid item xs={12}>
        <Spinner />
      </Grid>
    );
  }

  return (
    <>
      <PortfolioSummary
        portfolioDollars={processedData.portfolioDollars}
        interestEarnedDollars={processedData.interestEarnedDollars}
        totalUnrealizedGainsPercent={processedData.totalUnrealizedGainsPercent}
        showInterest={showInterest}
      />
      <Grid item xs={12}>
        <SnackSuccess
          showSuccessSnack={showSuccessSnack}
          setShowSuccessSnack={setShowSuccessSnack}
        />
        <PortfolioOverview
          rows={processedData.rows}
          showInterest={showInterest}
          selectedRowId={transactionsFiltering.selectedCoinId}
          onRowClick={(row) => {
            transactionsFiltering.handleSelectCoin(row);
            if (row.id !== transactionsFiltering.selectedCoinId && transactionsRef.current) {
              transactionsRef.current.scrollIntoView({ behavior: "smooth" });
            }
          }}
        />
      </Grid>
      <Grid item xs={12} ref={transactionsRef}>
        <Card>
          <CardHeader
            title={
              <Stack direction="row" gap={4} alignItems="flex-end">
                <span>Transactions</span>
                <FilteredByCoinDisplay
                  selectedCoin={transactionsFiltering.selectedCoin}
                  averageCost={averageCost}
                  onClearSelectedCoin={transactionsFiltering.clearSelectedCoin}
                />
              </Stack>
            }
          />
          <CardContent>
            <SimpleTransactionTable rows={transactionsFiltering.transactions} />
          </CardContent>
        </Card>
      </Grid>
    </>
  );
};

interface PortfolioSummaryProps {
  readonly portfolioDollars: number;
  readonly interestEarnedDollars: number;
  readonly totalUnrealizedGainsPercent: number;
  readonly showInterest: boolean;
}

function PortfolioSummary({
  portfolioDollars,
  interestEarnedDollars,
  totalUnrealizedGainsPercent,
  showInterest,
}: PortfolioSummaryProps) {
  let summaryCardsSize: number;
  if (showInterest) {
    summaryCardsSize = totalUnrealizedGainsPercent != 0 ? 4 : 6;
  } else {
    summaryCardsSize = totalUnrealizedGainsPercent != 0 ? 6 : 12;
  }

  const portfolioValue = currencyFormatter.format(portfolioDollars);
  const interestValue = currencyFormatter.format(interestEarnedDollars);
  const unrealizedGainsValue = formatPercentage(totalUnrealizedGainsPercent);

  const portfolioTextCss = useAnimatedTextCss(portfolioValue);
  const interestTextCss = useAnimatedTextCss(interestValue);
  const unrealizedGainsTextCss = useAnimatedTextCss(unrealizedGainsValue);

  return (
    <>
      <Grid item md={summaryCardsSize} xs={12}>
        <Card sx={{ textAlign: "center" }}>
          <CardHeader title={"Σ Portfolio Value"} />
          <CardContent>
            <Typography variant="h4" sx={portfolioTextCss}>
              $ {portfolioValue}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      {showInterest && (
        <Grid item md={summaryCardsSize} xs={12}>
          <Card sx={{ textAlign: "center" }}>
            <CardHeader title={"Σ Interest Earned"} />
            <CardContent>
              <Typography variant="h4" sx={interestTextCss}>
                $ {interestValue}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      )}
      {totalUnrealizedGainsPercent !== 0 && (
        <Grid item md={summaryCardsSize} xs={12}>
          <Card sx={{ textAlign: "center" }}>
            <CardHeader title={"Unrealized Gain"} />
            <CardContent>
              <Typography variant="h4" sx={unrealizedGainsTextCss}>
                {unrealizedGainsValue}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      )}
    </>
  );
}

const TEXT_UPDATE_CSS = { color: "success.dark", transition: "color 100ms ease-out" };
const TEXT_NORMAL_CSS = { color: "primary.main", transition: "color 1s ease-in" };

function useAnimatedTextCss(text: string) {
  const [previousText, setPreviousText] = useState(text);

  useEffect(() => {
    const handle = setTimeout(() => {
      setPreviousText(text);
    }, 1000);
    return () => clearTimeout(handle);
  }, [text]);

  if (text !== previousText) return TEXT_UPDATE_CSS;
  return TEXT_NORMAL_CSS;
}
