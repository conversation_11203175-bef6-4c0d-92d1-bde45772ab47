# Development Workflow

## Local Development Environment

### Service Auto-Reload

The development environment is configured with auto-reload capabilities for improved developer
experience:

1. FastAPI/Uvicorn

   - Auto-reloads when API code changes
   - Controlled by uvicorn_auto_reload in Terraform configuration
   - Enabled by default in local environment

2. Jobs Consumer
   - Uses watchdog for auto-reload functionality
   - Monitors Python files for changes
   - Automatically restarts the process when code is modified
   - Enabled through enable_dev_mode in Terraform configuration

### Docker Development Setup

The project uses multi-stage Docker builds with specific targets for development:

1. Python Runner

   - Development target includes additional tools and dependencies
   - Installs dev-requirements.txt for development-only packages
   - Uses volume mounts for live code editing
   - Configured through python_runner module in Terraform

2. Local Environment Configuration
   - Uses docker_image module with target="dev"
   - Mounts host volumes for source code
   - Enables development features by default

### Development Features Control

Development features are controlled through Terraform variables:

1. enable_dev_mode

   - Controls development-specific features
   - Defaults to false for production safety
   - Enabled in local environment

2. uvicorn_auto_reload

   - Controls API auto-reload
   - Defaults to false
   - Enabled in local environment

3. host_volumes
   - Configures source code mounting
   - Used for live development
   - Defined in local environment

## Common Development Tasks

### Rebuilding Development Environment

When making changes to development configuration:

1. Rebuild Python runner image:

```bash
terraform taint time_static.last_build_time
terraform apply -target=module.python_runner
```

2. Redeploy affected services:

```bash
terraform apply -target=module.api
```

### Monitoring Service Logs

To view service logs during development:

1. Jobs Consumer:

```bash
kubectl logs -f -l app=api-jobs-consumer -n stabletech
```

2. API Service:

```bash
kubectl logs -f -l app=api -n stabletech
```

### Development Database

The local environment uses a PostgreSQL instance with:

- Automatic setup through Terraform
- Development credentials
- Persistent storage for development data

## Best Practices

1. Development Mode

   - Use development features only in local environment
   - Keep production configuration separate
   - Test without development features before deployment

2. Code Changes

   - Take advantage of auto-reload for rapid iteration
   - Monitor logs for reload-related issues
   - Verify changes in both development and production modes

3. Configuration Management
   - Use appropriate Terraform variables for environment control
   - Keep development-specific settings in local environment
   - Document new development features in cline_docs
