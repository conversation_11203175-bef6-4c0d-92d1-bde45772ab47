import { ButtonColor } from "@/types/style";
import { But<PERSON> } from "@mui/material";
import { Dispatch } from "react";

interface PaneShowButtonProps {
  color?: ButtonColor;
  label: string;
  nothingPane: number;
  paneToShow: number;
  setPaneToShow: Dispatch<number>;
  thisPane: number;
}

export const PaneShowButton = (props: PaneShowButtonProps) => {
  return (
    <Button
      onClick={() => {
        if (props.paneToShow == props.thisPane) {
          props.setPaneToShow(props.nothingPane);
        } else {
          props.setPaneToShow(props.thisPane);
        }
      }}
      color={props.color || "primary"}
      size={"small"}
      variant={"outlined"}
    >
      {props.paneToShow == props.thisPane ? "-" : "+"} {props.label}
    </Button>
  );
};
