"""Reprocess heist claims from raw data.

<PERSON>ript to update heist claims that were already processed, to fix issues or
to add new data.

Currently this script will:
- Update `arresting_officer_wallet_address` only when present on raw data
- Update `opposing_wallet_address` only when present on raw data
"""

import logging
from typing import Any

from sqlalchemy import CursorResult, or_, select, update

from common.psql import SessionLocal
from lootloot.external import heist
from lootloot.models import HeistClaim, RawHeistClaim

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

raw_id = 0
total_count = 0

# processed_id -> updates dict
to_update: dict[int, dict[str, Any]] = {}

while True:
    logger.info("Getting raw data batch from id %d", raw_id)
    with SessionLocal.begin() as db:
        result = db.execute(
            select(RawHeistClaim.id, RawHeistClaim.processed_id, RawHeistClaim.data)
            .where(RawHeistClaim.id > raw_id, RawHeistClaim.processed_id != None)
            .order_by(RawHeistClaim.id.asc())
            .limit(1000)
        )

    result = result.tuples().all()
    if not result:
        break  # no more data to process

    for raw_id, processed_id, data in result:
        if processed_id in to_update:
            continue  # already processed

        updates_dict = {}
        to_update[processed_id] = updates_dict

        api_data = heist.HeistClaim.model_validate(data["heist"])

        if api_data.arrestingOfficer:
            updates_dict["arresting_officer_wallet_address"] = (
                api_data.arrestingOfficer.walletId
            )

        if api_data.opposingFactionNft:
            updates_dict["opposing_wallet_address"] = api_data.opposingFactionNft.walletId

    results: list[CursorResult] = []
    with SessionLocal.begin() as db:
        for processed_id, updates_dict in to_update.items():
            if not updates_dict:
                continue  # skip, nothing to update

            stmt = update(HeistClaim).where(HeistClaim.id == processed_id)

            or_conditions = []
            for column_name, value in updates_dict.items():
                column = HeistClaim.__table__.c[column_name]
                if value is None:
                    # set to null only if it isn't already null
                    or_conditions.append(column != None)
                else:
                    # set to value only if it isn't already the value
                    or_conditions.append(column == None)
                    or_conditions.append(column != value)

            if or_conditions:
                stmt = stmt.where(or_(*or_conditions))

            results.append(db.execute(stmt, updates_dict))

    count = sum(result.rowcount for result in results)
    logger.info("Updated %d records in this batch", count)

    total_count += count
    to_update.clear()

logger.info("Finished after updating a total of %d records", total_count)
