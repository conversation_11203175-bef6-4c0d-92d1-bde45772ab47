from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

security = HTTPBearer()

Credentials = Annotated[HTTPAuthorizationCredentials, Depends(security)]


class SecretTokenAuth:
    def __init__(self, secret: str) -> None:
        self._secret = secret
        self.depends_authentication = Depends(self.authenticate)

    def authenticate(self, credentials: Credentials) -> None:
        if credentials.credentials != self._secret:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                headers={"WWW-Authenticate": "Bearer"},
                detail="Invalid Token",
            )
