"""
Bank Bot to deposit and withdraw fungibles and non-fungibles.
Listens for WS messages to accept deposits and process pending withdrawals.

Expects HEIST_BANK_BOT_KEY to be set in .env with the Bank Bot's private key.

Usage:
    python -m lootloot.heist.bots.bank_ws
"""

import json
import logging
import sys
from enum import Enum

from httpx import HTTPStatusError
from solders.keypair import Keypair
from websockets import Origin
from websockets.exceptions import ConnectionClosed
from websockets.sync.client import connect

from common.conf import settings
from common.psql import SessionLocal
from lootloot.external.heist import HeistClient
from lootloot.heist.bots.crud import (
    FungibleInventoryManager,
    InventoryException,
    NonFungibleInventoryManager,
    NonFungibleTransferManager,
)
from lootloot.heist.bots.schemas import TradeSession, TradeStatus
from lootloot.wallets.crud import UserWalletManager

logger = logging.getLogger(__name__)


class MessageType(Enum):
    # All possible Websocket messages sent from the Heist API
    BASELINE_NEW = "baseline:new"
    CHAT_MESSAGE_NEW = "message:append"
    CLAIM_FEED_HEIST = "claim-feed:append"
    # CLAIM_FEED_ROBBERY = "robbery-claim-feed:append"
    COUNT_CHANGE_COP = "cop-count:change"
    COUNT_CHANGE_CHIMP = "chimp-count:change"
    COUNT_CHANGE_FARMING = "farming-count:change"
    COUNT_CHANGE_FARMING_USER = "user-farming-count:change"
    COUNT_CHANGE_ORANGUTAN = "orangutan-count:change"
    COUNT_CHANGE_ROBBER = "robber-count:change"
    COUNT_CHANGE_USER_CHIMP = "user-active-chimp-count:change"
    COUNT_CHANGE_USER_ORANGUTAN = "user-active-orangutan-count:change"
    HIGH_RISK_CHANGE = "location-high-risk:change"
    NFT_FREEZE_PROGRESS = "nft-freeze:progress"
    NFT_ADD_REMOVE = "nft:changed"
    STAT_CHANGE = "stat:change"

    TRADE_APPROVE = "trading-session:approve"
    TRADE_CLOSE = "trading-session:close"
    TRADE_OPEN = "trading-session:append"
    TRADE_UPDATE = "trading-session:update"


def process_trade_session(
    trade_session: TradeSession,
    heist_client: HeistClient,
    bot_wallet_address: str,
):
    if (
        trade_session.initiatorStatus == TradeStatus.CONFIRM
        and trade_session.recipientStatus == TradeStatus.CONFIRM
    ):
        # A confirmation message gets sent out after the trade has been completed
        return

    wallet_address = trade_session.initiatorId
    with SessionLocal() as db, db.begin():
        try:
            fungible_inventory_manager = FungibleInventoryManager(db)
            wallet = UserWalletManager(db).get_by_address("solana", wallet_address)

            if wallet:
                # Check pending withdrawals and update trade session if any are missing.
                pending_fungibles = fungible_inventory_manager.get_pending_transfers_for_user(
                    wallet.user_id
                )
                pending_non_fungibles = NonFungibleTransferManager(
                    db
                ).get_pending_transfers_for_user(wallet.user_id)
                is_edited = False
                for pending_fungible in pending_fungibles:
                    amount = pending_fungible.amount
                    asset_name = pending_fungible.asset_type
                    is_found = False
                    for fungible_entry in trade_session.tradingSessionFungibleEntries:
                        if fungible_entry.fungibleAssetType == asset_name:
                            is_found = True
                            if fungible_entry.amount != amount:
                                is_found = False
                            break
                    if not is_found:
                        is_edited = True
                        heist_client.trade_set_fungible(asset_name, amount, trade_session.id)
                for pending_non_fungible in pending_non_fungibles:
                    asset_id = pending_non_fungible.game_id
                    asset_type = pending_non_fungible.asset_type
                    is_found = False
                    for non_fungible_entry in trade_session.tradingSessionNonFungibleEntries:
                        if (
                            non_fungible_entry.asset.asset_type == asset_type
                            and non_fungible_entry.asset_id == asset_id
                        ):
                            is_found = True
                            break
                    if not is_found:
                        is_edited = True
                        heist_client.trade_set_non_fungible(
                            asset_type, asset_id, trade_session.id
                        )

                if is_edited:
                    # Trade has to be re-confirmed by user first
                    return

            if not trade_session.is_empty:
                if (
                    trade_session.initiatorStatus == TradeStatus.READY
                    and trade_session.recipientStatus != TradeStatus.READY
                ):
                    # Ready up and wait for user to confirm
                    heist_client.trade_update_status(trade_session, TradeStatus.READY)
                elif (
                    trade_session.initiatorStatus == TradeStatus.CONFIRM
                    and trade_session.recipientStatus == TradeStatus.READY
                ):
                    # User has confirmed trade - Update inventory and confirm
                    try:
                        for fungible_entry in trade_session.tradingSessionFungibleEntries:
                            if fungible_entry.amount:
                                fungible_inventory_manager.add_entry(
                                    fungible_entry, wallet_address
                                )
                        for (
                            non_fungible_entry
                        ) in trade_session.tradingSessionNonFungibleEntries:
                            NonFungibleInventoryManager(db).add_entry(
                                non_fungible_entry,
                                wallet_address,
                                non_fungible_entry.get_is_deposit(bot_wallet_address),
                            )
                        heist_client.trade_update_status(trade_session, TradeStatus.CONFIRM)
                    except InventoryException as error:
                        # Error processing - return and do not confirm
                        logger.info(error)
        except HTTPStatusError as error:
            db.rollback()
            if error.response.status_code == 401:
                logger.info("Cookie expired - authenticating and retrying")
                heist_client.authenticate_with_private_key(settings.heist_bank_bot_key)
                return process_trade_session(trade_session, heist_client, bot_wallet_address)


def listen():
    """
    Trade Bot that can transfer fungible and non-fungible assets from a Wallet or to a User.
    The bot is always the "recipient" of the trade and the user is the "initiator" that opened the trade.
    """
    heist_client = HeistClient()
    if not settings.heist_bank_bot_key:
        logger.info("No private key - exiting")
        sys.exit(1)
    heist_client.authenticate_with_private_key(settings.heist_bank_bot_key)
    keypair = Keypair.from_base58_string(settings.heist_bank_bot_key)
    bot_wallet_address = str(keypair.pubkey())

    try:
        with connect(
            "wss://api.theheist.game/socket.io/?EIO=4&transport=websocket",
            origin=Origin("https://theheist.game"),
            additional_headers=[
                (
                    "Cookie",
                    f"accessToken={heist_client.cookies.get('accessToken')}",
                )
            ],
        ) as websocket:
            """
            To establish a new connection, the server expects the following before it sends new events:
              - an initial connect message is consumed
              - "40" is sent as a response
              - a second connection message is consumed
            """

            websocket.recv()
            websocket.send("40")
            websocket.recv()

            # Listen for new messages
            for message in websocket:
                if message == "2":
                    # Server keepalive ping - expects "3" as a response.
                    websocket.send("3")
                    continue

                message = json.loads("[" + message.split("[", 1)[1])
                message_type_data = message[0]
                message_data = message[1]

                try:
                    message_type = MessageType(message_type_data)
                except ValueError:
                    # Log unknown messages
                    logger.info(f"Unknown message type: {json.dumps(message, indent=2)}")
                    continue

                match message_type:
                    case MessageType.TRADE_OPEN:
                        # New Trade - check if there are any pending withdrawals to be added
                        process_trade_session(
                            TradeSession(**message_data),
                            heist_client,
                            bot_wallet_address,
                        )
                    case MessageType.TRADE_CLOSE:
                        # Trade is closed by User
                        pass
                    case MessageType.TRADE_UPDATE:
                        # Trade is updated
                        logger.debug(json.dumps(message_data, indent=2))
                        process_trade_session(
                            TradeSession(**message_data),
                            heist_client,
                            bot_wallet_address,
                        )

    except ConnectionClosed:
        logger.info("WS connection closed unexpectedly.")
        return


if __name__ == "__main__":
    while True:
        listen()
