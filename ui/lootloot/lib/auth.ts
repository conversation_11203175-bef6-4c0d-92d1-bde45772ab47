import { apiMutation, apiQuery } from "@/lib/api";
import { LoginResponse, PhantomLogin, WalletAuthMessage } from "@/types/schemas";
import { useMutation, useQuery } from "@tanstack/react-query";

/**
 * <PERSON>gin into the API server with a signed transaction from phantom.
 *
 * @returns mutation
 */
export function usePhantomLogin() {
  return useMutation({ mutationFn: phatomLogin });
}

async function phatomLogin(payload: PhantomLogin) {
  return apiMutation<LoginResponse>({
    method: "POST",
    resource: "auth/phantom-login",
    body: payload,
    auth: false,
  });
}

export function useWalletAuthMessage(address: string | undefined) {
  return useQuery({
    queryKey: ["auth", "wallet-auth-message", { address }],
    enabled: address !== undefined,
    queryFn: apiQuery<WalletAuthMessage>,
  });
}
