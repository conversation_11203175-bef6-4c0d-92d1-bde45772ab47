{"name": "lootloot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "npx --package=local-web-server -- ws -p 3001 -d out --rewrite '/api/(.*) -> http://localhost:8080/$1'", "lint": "eslint --fix --max-warnings 0 ."}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@next/third-parties": "^15.3.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@sentry/nextjs": "^9.19.0", "@solana/wallet-adapter-react": "^0.15.38", "@solana/wallet-adapter-react-ui": "^0.9.38", "@solana/web3.js": "^1.98.2", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@tanstack/react-table": "^8.21.3", "bs58": "^5.0.0", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "lucide-react": "^0.510.0", "next": "^15.3.2", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "recharts": "^2.15.3", "superstruct": "^2.0.2", "tw-animate-css": "^1.2.9"}, "devDependencies": {"@eslint/js": "^9.26.0", "@next/eslint-plugin-next": "^15.3.2", "@tailwindcss/postcss": "^4.1.6", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/node": "^22.15.18", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "eslint": "^9.26.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unicorn": "^59.0.1", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}, "pnpm": {"onlyBuiltDependencies": [], "ignoredBuiltDependencies": ["@tailwindcss/oxide"]}}