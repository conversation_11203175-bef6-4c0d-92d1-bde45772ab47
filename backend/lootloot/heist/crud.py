import logging
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Literal, Sequence

from sqlalchemy import (
    ColumnElement,
    bindparam,
    extract,
    func,
    insert,
    literal,
    or_,
    select,
    update,
)
from sqlalchemy.dialects.postgresql import insert as pg_insert

import lootloot.external.heist
from lootloot import schemas
from lootloot.base import BaseManager
from lootloot.heist import sql
from lootloot.models import (
    NFT,
    HeistClaim,
    HeistDeedClaim,
    HeistLocation,
    RawHeistClaim,
    RawHeistDeedClaim,
    User,
    UserWallet,
)

logger = logging.getLogger(__name__)


class HeistLocationManager(BaseManager):
    def create_from_api(self, location: lootloot.external.heist.Location) -> None:
        self.db.execute(
            insert(HeistLocation),
            {
                "game_id": location.id,
                "name": location.name,
                "location_type": location.location_type,
                "risk": location.risk,
                "district_id": location.districtId,
                "district_name": location.district.name if location.district else None,
                "active_chimp_count": location.activeChimpCount,
                "active_orangutan_count": location.activeOrangutanCount,
                "active_gorilla_count": location.activeGorillaCount,
            },
        )

    def update_from_api(self, location: lootloot.external.heist.Location):
        return self.db.execute(
            update(HeistLocation).where(HeistLocation.name == location.name),
            {
                "game_id": location.id,
                "location_type": location.location_type,
                "risk": location.risk,
                "district_id": location.districtId,
                "district_name": location.district.name if location.district else None,
                "active_chimp_count": location.activeChimpCount,
                "active_orangutan_count": location.activeOrangutanCount,
                "active_gorilla_count": location.activeGorillaCount,
            },
        )


class HeistClaimManager(BaseManager):
    def get_id_by_game_id_and_end(self, game_id: int, ended_at: datetime) -> int:
        return self.db.scalars(
            select(HeistClaim.id).where(
                HeistClaim.game_id == game_id,
                HeistClaim.ended_at == ended_at,
            )
        ).one()

    def process_raw(self, raw_claim: RawHeistClaim) -> None:
        """Process a raw claim, creating a new claim if it doesn't exist.

        This method will also update the raw claim with the processed claim's
        id, or mark it as unprocessable if the raw claim data fails the required
        validation.
        """
        try:
            api_data = lootloot.external.heist.HeistClaim.model_validate(
                raw_claim.data.get("heist")
            )
        except ValueError as e:
            logger.error(
                "Failed to validate raw heist claim data",
                exc_info=e,
                extra={"raw_claim_id": raw_claim.id},
            )
            raw_claim.unprocessable = True
            return

        insertion = (
            pg_insert(HeistClaim)
            .values(
                location_id=(
                    select(HeistLocation.id)
                    .where(HeistLocation.game_id == api_data.locationId)
                    .scalar_subquery()
                )
            )
            .on_conflict_do_nothing()
            .returning(HeistClaim.id)
        )

        claim_id = self.db.scalar(
            insertion,
            {
                "game_id": api_data.id,
                "started_at": api_data.startedAt,
                "ended_at": api_data.endedAt,
                "total_amount_emitted": api_data.totalAmountEmitted,
                "total_amount_claimed": api_data.totalAmountClaimed,
                "nft_address": api_data.nftId,
                "wallet_address": api_data.walletId,
                "event_id": api_data.eventId,
                "arresting_officer_address": api_data.arrestingOfficerId,
                "arresting_officer_wallet_address": (
                    api_data.arrestingOfficer.walletId if api_data.arrestingOfficer else None
                ),
                "is_bonus_roll": api_data.isBonusRoll,
                "secondary_event": api_data.secondaryEvent,
                "opposing_nft_address": api_data.opposingFactionNftId,
                "opposing_wallet_address": (
                    api_data.opposingFactionNft.walletId
                    if api_data.opposingFactionNft
                    else None
                ),
            },
        )

        if claim_id is None:
            claim_id = self.get_id_by_game_id_and_end(api_data.id, api_data.endedAt)

        raw_claim.processed_id = claim_id

    def stats_global(self):
        stmt = select(
            sql.claimed_sum.label("claimed"),
            sql.fumbled_sum.label("fumbled"),
            func.count().label("completed"),
            func.count().filter(sql.is_rekt).label("rekt"),
        )

        result = self.db.execute(stmt).one()
        return schemas.HeistStatsPublic(
            claimed=result.claimed,
            fumbled=result.fumbled,
            completed=result.completed,
            rekt=result.rekt,
        )

    def stats_for_wallets(self, *, wallet_addresses: Sequence[str] = []):
        wallet_addresses_param = bindparam("wallet_addresses", wallet_addresses, expanding=True)

        gained_from_ambush = (
            select(sql.gained_from_ambush_unfiltered)
            .where(sql.gained_from_ambush_condition(wallet_addresses_param))
            .scalar_subquery()
        )

        stmt = select(
            sql.claimed_sum.label("claimed"),
            sql.fumbled_sum.label("fumbled"),
            func.count().label("completed"),
            func.count().filter(sql.is_rekt).label("rekt"),
            sql.lost_to_ambush.label("lost_to_ambush"),
            gained_from_ambush.label("gained_from_ambush"),
        ).where(HeistClaim.wallet_address.in_(wallet_addresses_param))

        result = self.db.execute(stmt).one()
        return schemas.HeistStats(
            claimed=result.claimed,
            fumbled=result.fumbled,
            completed=result.completed,
            rekt=result.rekt,
            lost_to_ambush=result.lost_to_ambush,
            gained_from_ambush=result.gained_from_ambush,
        )

    def nana_by_day_global(self, *, timezone: str):
        timezone_param = bindparam("timezone", timezone)
        day_column: ColumnElement[datetime] = func.date_trunc(
            sql.DAY, HeistClaim.ended_at, timezone_param
        )

        stmt = (
            select(
                day_column.label("day"),
                sql.claimed_sum.label("gained"),
                sql.fumbled_sum.label("lost"),
            )
            .group_by("day")
            .order_by("day")
        )

        return [
            schemas.NanaInDay(at=row.day, lost=row.lost, gained=row.gained)
            for row in self.db.execute(stmt)
        ]

    def nana_by_day_for_wallets(self, *, timezone_name: str, wallet_addresses: Sequence[str]):
        """Get NANA gained and lost by day for specific wallets."""
        timezone_param = bindparam("timezone", timezone_name)
        day_column: ColumnElement[datetime] = func.date_trunc(
            sql.DAY, HeistClaim.ended_at, timezone_param
        )

        wallet_addresses_param = bindparam("wallet_addresses", wallet_addresses, expanding=True)

        gained_from_ambush = (
            select(
                day_column.label("day"),
                sql.gained_from_ambush_unfiltered.label("amount"),
            )
            .where(sql.gained_from_ambush_condition(wallet_addresses_param))
            .group_by("day")
            .cte("gained_from_ambush")
        )

        claimed_and_lost = (
            select(
                day_column.label("day"),
                sql.claimed_sum.label("claimed"),
                (sql.fumbled_sum + sql.lost_to_ambush).label("lost"),
            )
            .where(HeistClaim.wallet_address.in_(wallet_addresses_param))
            .group_by("day")
            .cte("claimed_and_lost")
        )

        stmt = (
            select(
                func.coalesce(claimed_and_lost.c.day, gained_from_ambush.c.day).label("day"),
                func.coalesce(claimed_and_lost.c.lost, sql.ZERO).label("lost"),
                (
                    func.coalesce(claimed_and_lost.c.claimed, sql.ZERO)
                    + func.coalesce(gained_from_ambush.c.amount, sql.ZERO)
                ).label("gained"),
            )
            .outerjoin(
                gained_from_ambush,
                claimed_and_lost.c.day == gained_from_ambush.c.day,
                full=True,
            )
            .order_by("day")
        )

        return [
            schemas.NanaInDay(at=row.day, lost=row.lost, gained=row.gained)
            for row in self.db.execute(stmt)
        ]

    def get_top_claims(
        self,
        *,
        timeframe: Literal["daily", "weekly", "monthly", "all-time"],
        wallet_addresses: Sequence[str] | None = None,
        sort: schemas.TopClaimsSort | None = None,
        filters: schemas.TopClaimsFilters | None = None,
    ) -> list[schemas.TopClaimItem]:
        """Get top 100 claims with most NANA claimed for the given timeframe.

        Args:
            timeframe: Time period to filter claims
            wallet_addresses: Optional list of wallet addresses to filter by
            sort: Optional sort configuration
            filters: Optional filters for species, event, and location

        Returns:
            List of top claims with rank, NFT details, and claim stats
        """
        now = datetime.now(timezone.utc)

        # First create a CTE with the filtered and ranked claims
        ranked_claims = select(
            HeistClaim.nft_address,
            HeistClaim.wallet_address,
            HeistClaim.total_amount_claimed,
            HeistClaim.total_amount_emitted,
            HeistClaim.started_at,
            HeistClaim.ended_at,
            HeistClaim.event_id,
            HeistClaim.location_id,
            (
                extract(
                    "epoch",
                    HeistClaim.ended_at - HeistClaim.started_at,
                )
                / 3600.0
            ).label("duration_hours"),
            (HeistClaim.total_amount_claimed / HeistClaim.total_amount_emitted).label(
                "base_multiplier"
            ),
            func.row_number().over(order_by=self._get_sort_order(sort)).label("rank"),
        ).select_from(HeistClaim)

        # Add timeframe filter if not all-time
        if timeframe != "all-time":
            if timeframe == "daily":
                start_time = now - timedelta(days=1)
            elif timeframe == "weekly":
                start_time = now - timedelta(weeks=1)
            elif timeframe == "monthly":
                start_time = now - timedelta(days=30)
            ranked_claims = ranked_claims.where(HeistClaim.ended_at >= start_time)

        # Add wallet filter if provided
        if wallet_addresses:
            wallet_addresses_param = bindparam(
                "wallet_addresses", wallet_addresses, expanding=True
            )
            ranked_claims = ranked_claims.where(
                or_(
                    HeistClaim.wallet_address.in_(wallet_addresses_param),
                    sql.gained_from_ambush_condition(wallet_addresses_param),
                )
            )
        else:
            ranked_claims = ranked_claims.where(
                HeistClaim.total_amount_claimed >= 1
            )  # Only include claims with amount >= 1

        # Join with NFT table if species filter is provided
        if filters and filters.species:
            ranked_claims = ranked_claims.join(
                NFT, NFT.address_uri == func.concat(literal("solana:"), HeistClaim.nft_address)
            )
            ranked_claims = ranked_claims.where(
                NFT.nft_collection_uid.in_(("theheist", "the_heist_generations")),
                or_(
                    *[
                        NFT.attributes_data.contains(
                            [{"traitType": "Species", "value": species}]
                        )
                        for species in filters.species
                    ]
                ),
            )

        # Add event filter if provided
        if filters and filters.event_id:
            # Special handling for Ambush event (id: 99)
            if 99 in filters.event_id and wallet_addresses:
                wallet_addresses_param = bindparam(
                    "wallet_addresses", wallet_addresses, expanding=True
                )
                other_events = [eid for eid in filters.event_id if eid != 99]
                if other_events:
                    # Include both ambush events and other selected events
                    ranked_claims = ranked_claims.where(
                        or_(
                            sql.gained_from_ambush_condition(wallet_addresses_param),
                            HeistClaim.event_id.in_(other_events),
                        )
                    )
                else:
                    # Only show ambush events
                    ranked_claims = ranked_claims.where(
                        sql.gained_from_ambush_condition(wallet_addresses_param)
                    )
            else:
                # Normal event filtering without ambush
                ranked_claims = ranked_claims.where(HeistClaim.event_id.in_(filters.event_id))

        # Add location filter if provided
        if filters and filters.location_id:
            ranked_claims = ranked_claims.join(
                HeistLocation, HeistLocation.id == HeistClaim.location_id
            ).where(HeistLocation.game_id.in_(filters.location_id))

        ranked_claims = ranked_claims.limit(100).cte("ranked_claims")

        # Join with NFT table and user data
        base_query = (
            select(
                ranked_claims,
                NFT.image_url.label("nft_image_url"),
                (
                    extract(
                        "epoch",
                        ranked_claims.c.ended_at - ranked_claims.c.started_at,
                    )
                    / 3600.0
                ).label("duration_hours"),
                (
                    ranked_claims.c.total_amount_claimed / ranked_claims.c.total_amount_emitted
                ).label("base_multiplier"),
                User.id.label("user_id"),
                User.display_name.label("user_display_name"),
                HeistLocation.name.label("location_name"),
                HeistLocation.district_name.label("district_name"),
            )
            .join(
                NFT,
                NFT.address_uri == func.concat(literal("solana:"), ranked_claims.c.nft_address),
            )
            .join(
                HeistLocation,
                HeistLocation.id == ranked_claims.c.location_id,
            )
            .outerjoin(
                UserWallet,
                UserWallet.address == ranked_claims.c.wallet_address,
            )
            .outerjoin(
                User,
                User.id == UserWallet.user_id,
            )
        )

        # Order by rank to maintain the order from the CTE
        stmt = base_query.order_by(ranked_claims.c.rank)

        # Execute query with parameters
        results: list[schemas.TopClaimItem] = []
        for row in self.db.execute(
            stmt,
            {
                "timeframe": timeframe,
                **({"wallet_addresses": wallet_addresses} if wallet_addresses else {}),
            },
        ):
            # Determine wallet kind and value
            if row.user_display_name:
                wallet_kind = "user"
                wallet_value = row.user_display_name
            elif row.user_id:
                wallet_kind = "user"
                wallet_value = None
            else:
                wallet_kind = "address"
                wallet_value = row.wallet_address

            # Check if this is an ambush event (opposing_wallet_address matches one of user's wallets)
            is_ambush = bool(wallet_addresses and row.wallet_address not in wallet_addresses)

            # For ambush events, negate the amount. For negative amounts (except ambush), set to 0
            amount = (
                -row.total_amount_claimed
                if is_ambush
                else Decimal("0") if row.total_amount_claimed < 0 else row.total_amount_claimed
            )

            # Calculate base multiplier:
            # - For ambush events, use absolute value
            # - For captured events (amount set to 0), set to 0
            # - Otherwise use normal calculation
            base_multiplier = (
                abs(row.total_amount_claimed) / row.total_amount_emitted
                if is_ambush
                else (
                    Decimal("0")
                    if amount == Decimal("0")
                    else row.total_amount_claimed / row.total_amount_emitted
                )
            )

            results.append(
                schemas.TopClaimItem(
                    nft_image_url=row.nft_image_url,
                    total_amount_claimed=amount,
                    duration_hours=round(row.duration_hours, 2),
                    total_amount_emitted=row.total_amount_emitted,
                    base_multiplier=round(float(base_multiplier), 2),
                    ended_at=row.ended_at,
                    address=row.nft_address,
                    wallet_kind=wallet_kind,
                    wallet_value=wallet_value,
                    location_name=row.location_name,
                    event_id=row.event_id,
                    is_ambush=is_ambush,
                )
            )

        return results

    def get_nft_claims_history(self, nft_address: str) -> schemas.NFTClaimsHistoryResp | None:
        """Get claims history for a specific NFT.

        Args:
            nft_address: The NFT address to get history for

        Returns:
            NFT claims history with NFT details and last 100 claims
        """
        # Get NFT details
        nft = self.db.scalar(
            select(NFT).where(NFT.address_uri == func.concat(literal("solana:"), nft_address))
        )

        if not nft:
            return None

        # Get the last 100 claims for this NFT
        claims = self.db.execute(
            select(
                HeistClaim.total_amount_claimed,
                HeistClaim.total_amount_emitted,
                HeistClaim.started_at,
                HeistClaim.ended_at,
                HeistClaim.event_id,
                (
                    extract(
                        "epoch",
                        HeistClaim.ended_at - HeistClaim.started_at,
                    )
                    / 3600.0
                ).label("duration_hours"),
                (HeistClaim.total_amount_claimed / HeistClaim.total_amount_emitted).label(
                    "base_multiplier"
                ),
                HeistLocation.name.label("location_name"),
            )
            .join(
                HeistLocation,
                HeistLocation.id == HeistClaim.location_id,
            )
            .where(HeistClaim.nft_address == nft_address)
            .order_by(HeistClaim.ended_at.desc())
            .limit(100)
        )

        return schemas.NFTClaimsHistoryResp(
            nft_name=nft.uid,
            nft_image_url=nft.image_url,
            claims=[
                schemas.NFTClaimHistoryItem(
                    total_amount_claimed=row.total_amount_claimed,
                    duration_hours=round(row.duration_hours, 2),
                    total_amount_emitted=row.total_amount_emitted,
                    base_multiplier=round(float(row.base_multiplier), 2),
                    ended_at=row.ended_at,
                    event_id=row.event_id,
                    location_name=row.location_name,
                )
                for row in claims
            ],
        )

    def _get_sort_order(self, sort: schemas.TopClaimsSort | None) -> list:
        """Get the sort order for the query based on the sort parameters."""
        if not sort:
            return [
                HeistClaim.total_amount_claimed.desc(),
                HeistClaim.ended_at.desc(),  # Secondary sort for consistent ordering
            ]

        match sort.by:
            case "total_amount_claimed":
                primary_sort = HeistClaim.total_amount_claimed
            case "duration_hours":
                primary_sort = HeistClaim.ended_at - HeistClaim.started_at
            case "base_multiplier":
                primary_sort = HeistClaim.total_amount_claimed / HeistClaim.total_amount_emitted
            case "ended_at":
                primary_sort = HeistClaim.ended_at

        if sort.order == "desc":
            primary_sort = primary_sort.desc()

        return [
            primary_sort,
            HeistClaim.ended_at.desc(),  # Always include secondary sort for consistent ordering
        ]


class RawHeistDeedClaimManager(BaseManager):
    def create_if_not_exists(self, items: Sequence[dict[str, Any]]) -> None:
        self.db.execute(pg_insert(RawHeistDeedClaim).on_conflict_do_nothing(), items)

    def get_unprocessed(self, *, limit: int):
        return self.db.scalars(
            select(RawHeistDeedClaim)
            .where(RawHeistDeedClaim.processed_id == None, ~RawHeistDeedClaim.unprocessable)
            .limit(limit)
        ).all()


class HeistDeedClaimManager(BaseManager):
    def process_raw(self, raw_claim: RawHeistDeedClaim) -> None:
        """Process a raw deed claim, creating a new deed claim if it doesn't exist.

        This method will also update the raw claim with the processed claim's id, or
        mark it as unprocessable if the raw data fails the required validation.
        """
        try:
            api_data = lootloot.external.heist.DeedClaim.model_validate(raw_claim.data)
        except ValueError as e:
            logger.error(
                "Failed to validate raw heist deed claim data",
                exc_info=e,
                extra={"raw_claim_id": raw_claim.id},
            )
            raw_claim.unprocessable = True
            return

        # We don't want to have claims with 0 amount for our data
        if api_data.totalAmount == 0:
            raw_claim.unprocessable = True
            return

        claim_id = self.db.scalar(
            # Use on_conflict_do_nothing to avoid exceptions, thus avoiding having
            # to roll back the whole transaction.
            pg_insert(HeistDeedClaim).on_conflict_do_nothing().returning(HeistDeedClaim.id),
            {
                "kind": api_data.claim_type,
                "amount": api_data.totalAmount,
                "at": api_data.createdAt,
                "wallet_address": api_data.wallet.id,
                "nft_address": api_data.deed.id,
            },
        )

        if claim_id is None:
            logger.error(
                "Unexpected failure to insert heist deed claim",
                extra={"raw_claim_id": raw_claim.id},
            )
        else:
            raw_claim.processed_id = claim_id
