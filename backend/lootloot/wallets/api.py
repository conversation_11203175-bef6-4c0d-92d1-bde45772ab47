from datetime import datetime, timezone

import psycopg.errors
import sqlalchemy.exc
from fastapi import APIRouter, HTTPException

from common.exceptions import HTTP400Exception
from common.psql import DatabaseSession
from lootloot import schemas
from lootloot.auth.fastapi import CurrentUser
from lootloot.kinds import NetworkGroup
from lootloot.models import UserWallet
from lootloot.utils import verify_solana_signature
from lootloot.wallets.crud import UserWalletManager

router = APIRouter()


@router.get("", response_model=schemas.ListUserWalletsResp)
def get_wallets(db: DatabaseSession, user: CurrentUser):
    wallets = UserWalletManager(db).all_from_user(user.id)
    return schemas.ListUserWalletsResp(items=wallets)


@router.delete("/{network_group}/{address}")
def delete_wallet(
    db: DatabaseSession, user: CurrentUser, network_group: NetworkGroup, address: str
):
    wallet = UserWalletManager(db).get_by_address_from_user(user.id, network_group, address)
    if not wallet:
        raise HTTPException(status_code=404, detail="Wallet not found")
    db.delete(wallet)
    db.commit()


def create_wallet(
    db: DatabaseSession,
    user: CurrentUser,
    data: schemas.AddUserWallet,
    wallet: UserWallet | None = None,
):
    user_wallet_manager = UserWalletManager(db)
    if not wallet:
        try:
            wallet = user_wallet_manager.create(user.id, data)
        except ValueError as e:
            raise HTTP400Exception(str(e))

    try:
        db.commit()
    except sqlalchemy.exc.IntegrityError as e:
        if not isinstance(e.orig, psycopg.errors.UniqueViolation):
            raise
        match e.orig.diag.constraint_name:
            case "userwallet_user_id_name_key":
                message = f'You already have a wallet with the name "{data.name}"'
            case "userwallet_network_group_address_key":
                db.rollback()
                wallet = user_wallet_manager.get_by_address(data.network_group, data.address)
                if wallet:
                    if wallet.user_id == user.id:
                        message = "Wallet is already added to your account"
                    else:
                        # Move the Wallet to this User as they've proved ownership
                        wallet.user_id = user.id
                        wallet.created_at = datetime.now(tz=timezone.utc)
                        wallet.name = data.name
                        # Run the above validation in case the name is already in use
                        create_wallet(db, user, data, wallet=wallet)
                        return wallet
                else:
                    message = "Temporary error, please try again"
            case _:
                message = "Unable to add that Wallet to this account"

        raise HTTP400Exception(message)

    return wallet


@router.post("/phantom", response_model=schemas.UserWallet)
def create_phantom_wallet(
    db: DatabaseSession, user: CurrentUser, add_phantom_wallet: schemas.AddPhantomWallet
):
    if not verify_solana_signature(
        add_phantom_wallet.signature, add_phantom_wallet.message, add_phantom_wallet.address
    ):
        raise HTTPException(status_code=403, detail="Wallet signature verification failed")

    return create_wallet(
        db,
        user,
        schemas.AddUserWallet(
            name=add_phantom_wallet.name,
            origin="phantom",
            address=add_phantom_wallet.address,
            network_group="solana",
        ),
    )
