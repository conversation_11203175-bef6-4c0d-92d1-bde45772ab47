resource "kubernetes_namespace" "stage" {
  metadata {
    name = var.namespace
  }
}

resource "google_dns_managed_zone" "stage_stabletech_capital" {
  name     = "stage-stabletech-capital"
  dns_name = "stage.stabletech.capital."

  dnssec_config {
    state = "on"
  }
}

module "stabletech" {
  source = "../modules/stabletech"

  api_domain        = "api.stage.stabletech.capital"
  env_name          = "stage"
  managed_zone_name = google_dns_managed_zone.stage_stabletech_capital.name
  namespace         = var.namespace
  release           = local.release
  secrets           = local.secrets
}

module "dashboard_ui" {
  source = "../modules/ui"

  dns_managed_zone    = google_dns_managed_zone.stage_stabletech_capital
  namespace           = var.namespace
  project_id          = local.project_id
  website_domain_name = "stage.stabletech.capital"
}
