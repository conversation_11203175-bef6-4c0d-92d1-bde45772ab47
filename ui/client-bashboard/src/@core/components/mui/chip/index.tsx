// ** MUI Imports
import MuiChip from "@mui/material/Chip";

// ** Types
import { CustomChipProps } from "@/@core/components/mui/chip/types";

// ** Hooks Imports
import useBgColor, { UseBgColorType } from "@/@core/hooks/useBgColor";

const Chip = (props: CustomChipProps) => {
  // ** Props
  const { sx, skin, color } = props;

  // ** Hook
  const bgColors = useBgColor();

  const colors: UseBgColorType = {
    primary: { ...bgColors.primaryLight },
    secondary: { ...bgColors.secondaryLight },
    success: { ...bgColors.successLight },
    error: { ...bgColors.errorLight },
    warning: { ...bgColors.warningLight },
    info: { ...bgColors.infoLight },
  };

  return (
    <MuiChip
      {...props}
      variant="filled"
      {...(skin === "light" && { className: "MuiChip-light" })}
      sx={skin === "light" && color ? Object.assign(colors[color], sx) : sx}
    />
  );
};

export default Chip;
