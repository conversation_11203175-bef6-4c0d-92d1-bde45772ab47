import { apiMutation, apiQuery, getUrlSearch } from "@/lib/api";
import {
  Credential,
  Credential2FaResponse,
  CredentialWrapKeyResponse,
  LoginResponse,
  WrapKeyAuthOptionsRequest,
  WrapKeyAuthOptionsResponse,
} from "@/types/schemas";
import {
  startRegistration,
  type AuthenticationResponseJSON,
  type PublicKeyCredentialCreationOptionsJSON,
  type PublicKeyCredentialRequestOptionsJSON,
  type RegistrationResponseJSON,
} from "@simplewebauthn/browser";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

interface OpenIdRequestParams {
  authorizationEndpoint: string;
  responseType: string;
  clientId: string;
  scope: string;
  redirectUri: string;
  state: string;
  nonce: string;
}

export function openIdRedirect(params: OpenIdRequestParams) {
  const url = new URL(params.authorizationEndpoint);
  const { searchParams } = url;
  searchParams.append("response_type", params.responseType);
  searchParams.append("client_id", params.clientId);
  searchParams.append("scope", params.scope);
  searchParams.append("redirect_uri", params.redirectUri);
  searchParams.append("state", params.state);
  searchParams.append("nonce", params.nonce);
  window.location.href = url.toString();
}

type AuthProviders = Record<string, { clientId: string }>;
let authProviders: AuthProviders | undefined;

export function getAuthProvider(name: string): AuthProviders[string] {
  authProviders ??= JSON.parse(import.meta.env.VITE_AUTH_PROVIDERS) as AuthProviders;
  const provider = authProviders[name];
  if (provider == null) throw new Error(`Provider '${name}' is not supported`);
  return provider;
}

/**
 * Login into the API server with an OIDC id and access tokens.
 *
 * @returns mutation
 */
export async function oidcLogin(params: { idToken: string; accessToken: string }) {
  const { idToken, accessToken } = params;
  return apiMutation<LoginResponse>({
    method: "POST",
    resource: "auth/oidc-login",
    body: { id_token: idToken, access_token: accessToken },
    auth: false,
  });
}

/**
 * Delete the current user session
 *
 * @returns mutation
 */
export function useDeleteUserSession() {
  return useMutation({ mutationFn: deleteUserSession });
}

async function deleteUserSession() {
  return apiMutation<null>({
    method: "DELETE",
    resource: "auth/user-session",
  });
}

export type RequestOptions = PublicKeyCredentialRequestOptionsJSON;

type RegistrationOptions = PublicKeyCredentialCreationOptionsJSON;

export async function createRegistrationOptions(useFor: Credential["use_for"]) {
  return apiMutation<RegistrationOptions>({
    method: "POST",
    resource: `auth/registration-options${getUrlSearch([["use_for", useFor]])}`,
  });
}

export function useCreateCredential() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createCredential,
    onSettled() {
      void queryClient.invalidateQueries({ queryKey: LIST_CREDENTIALS_KEY });
    },
  });
}

interface CreateCredentialParams {
  registrationResponse: RegistrationResponseJSON;
  useFor: Credential["use_for"];
}

async function createCredential(params: CreateCredentialParams) {
  return apiMutation<null>({
    method: "POST",
    resource: `auth/credentials${getUrlSearch([["use_for", params.useFor]])}`,
    body: params.registrationResponse,
  });
}

export function useAddCredentialFlow() {
  const credentialCreation = useCreateCredential();

  return async function addCredentialFlow(useFor: Credential["use_for"]) {
    const options = await createRegistrationOptions(useFor);
    const registrationResponse = await startRegistration({ optionsJSON: options });
    await credentialCreation.mutateAsync({ registrationResponse, useFor });
    return registrationResponse.id;
  };
}

const LIST_CREDENTIALS_KEY = ["auth", "credentials"] as const;

export function useListCredentials() {
  return useQuery({
    queryKey: LIST_CREDENTIALS_KEY,
    queryFn: apiQuery<Credential[]>,
  });
}

export function useDeleteCredential() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteCredential,
    onSettled() {
      void queryClient.invalidateQueries({ queryKey: LIST_CREDENTIALS_KEY });
    },
  });
}

async function deleteCredential(id: string) {
  return apiMutation<null>({
    method: "DELETE",
    resource: `auth/credentials/${id}`,
  });
}

export function usePatchCredential() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: patchCredential,
    onSettled() {
      void queryClient.invalidateQueries({ queryKey: LIST_CREDENTIALS_KEY });
    },
  });
}

async function patchCredential(params: { id: string; name: string }) {
  const { id, name } = params;
  return apiMutation<null>({
    method: "PATCH",
    resource: `auth/credentials/${id}`,
    body: { name },
  });
}

export async function verifyCredential(response: AuthenticationResponseJSON) {
  return apiMutation<Credential2FaResponse | CredentialWrapKeyResponse>({
    method: "POST",
    resource: "auth/credential-verification",
    body: response,
    auth: false,
  });
}

export async function createWrapKeyAuthOptions(credentialId: string) {
  return apiMutation<WrapKeyAuthOptionsResponse>({
    method: "POST",
    resource: "auth/wrap-key-options",
    body: { credential_id: credentialId } satisfies WrapKeyAuthOptionsRequest,
  });
}
