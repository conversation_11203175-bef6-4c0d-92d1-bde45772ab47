"use client";

import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";

import { But<PERSON> } from "@/lib/components/button";
import * as DropdownMenu from "@/lib/components/dropdown-menu";

export function ModeToggle() {
  const { setTheme } = useTheme();

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <Button variant="outline" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end">
        <DropdownMenu.Item
          onClick={() => {
            setTheme("light");
          }}
        >
          Light
        </DropdownMenu.Item>
        <DropdownMenu.Item
          onClick={() => {
            setTheme("dark");
          }}
        >
          Dark
        </DropdownMenu.Item>
        <DropdownMenu.Item
          onClick={() => {
            setTheme("system");
          }}
        >
          System
        </DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}
