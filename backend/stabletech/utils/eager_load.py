from sqlalchemy.orm import joinedload, selectinload

from stabletech.client.models import ClientTransaction
from stabletech.indexer.models import Account, Coin, Transaction, Transfer, Wallet

ACCOUNT = (
    joinedload(Account.bridge),
    joinedload(Account.coin).options(joinedload(Coin.alias_coin)),
    joinedload(Account.exchange),
    joinedload(Account.farm),
    joinedload(Account.lending_protocol),
)

TRANSACTION = (
    joinedload(Transaction.network),
    joinedload(Transaction.signer_account).options(*ACCOUNT),
    selectinload(Transaction.transfers).options(
        joinedload(Transfer.asset_account).options(*ACCOUNT),
        joinedload(Transfer.from_account).options(*ACCOUNT),
        joinedload(Transfer.to_account).options(*ACCOUNT),
    ),
    joinedload(Transaction.wallet).options(selectinload(Wallet.networks)),
)

CLIENT_TRANSACTION = (joinedload(ClientTransaction.transaction).options(*TRANSACTION),)
