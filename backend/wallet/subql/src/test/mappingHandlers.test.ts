import { subqlTest } from "@subql/testing";
import { Entity } from "@subql/types-core";
import { BigNumber } from "ethers";
import { MockTransferLog } from "./mockTypes";

interface TransferEntity extends Entity {
  blockNumber: bigint;
  tx_hash: string;
  to_address: string;
  from_address: string;
  value: bigint;
  contractAddress: string;
  network: string;
  timestamp: number;
}

describe("Mapping Handlers", () => {
  // Test successful transfer log handling
  test("handleEthereumLog processes transfer correctly", async () => {
    const value = BigNumber.from("1000000");
    const from = "0xsender";
    const to = "0xrecipient";

    // Create the args array with the correct type
    const args = [from, to, value] as [string, string, BigNumber];
    // Add named properties
    const argsWithProps = Object.assign(args, {
      from,
      to,
      value,
    });

    const mockTransferLog: MockTransferLog = {
      blockNumber: 1000003,
      blockHash: "0xblock123",
      transactionHash: "0x123",
      transactionIndex: 0,
      logIndex: 0,
      address: "0xcontract",
      topics: [],
      data: "0x",
      removed: false,
      args: argsWithProps,
      block: {
        hash: "0xblock123",
        number: 1000003,
        timestamp: 1000000,
      },
      transaction: {
        hash: "0x123",
        from: "0xsender",
        to: "0xcontract",
        value: 0,
        blockTimestamp: 1000000,
      },
    };

    const expectedTransfer: TransferEntity = {
      id: `ethereum-${mockTransferLog.blockNumber}-${mockTransferLog.logIndex}-${mockTransferLog.transactionHash}-${mockTransferLog.args[0]}-${mockTransferLog.args[1]}`,
      tx_hash: mockTransferLog.transactionHash,
      blockNumber: BigInt(mockTransferLog.blockNumber),
      to_address: mockTransferLog.args[1],
      from_address: mockTransferLog.args[0],
      value: BigInt(mockTransferLog.args[2].toString()),
      contractAddress: mockTransferLog.address,
      network: "ethereum",
      timestamp: mockTransferLog.transaction.blockTimestamp,
    };

    await subqlTest(
      "Handle Transfer Log",
      mockTransferLog.blockNumber,
      [],
      [expectedTransfer],
      "handleEthereumLog",
    );
  });

  // Test transfer log with missing args
  test("handleEthereumLog handles missing args", async () => {
    const invalidLog: MockTransferLog = {
      blockNumber: 1000003,
      blockHash: "0xblock123",
      transactionHash: "0x123",
      transactionIndex: 0,
      logIndex: 0,
      address: "0xcontract",
      topics: [],
      data: "0x",
      removed: false,
      args: undefined as any,
      block: {
        hash: "0xblock123",
        number: 1000003,
        timestamp: 1000000,
      },
      transaction: {
        hash: "0x123",
        from: "0xsender",
        to: "0xcontract",
        value: 0,
        blockTimestamp: 1000000,
      },
    };

    await subqlTest(
      "Handle Invalid Transfer Log",
      invalidLog.blockNumber,
      [],
      [],
      "handleEthereumLog",
    );
  });
});
