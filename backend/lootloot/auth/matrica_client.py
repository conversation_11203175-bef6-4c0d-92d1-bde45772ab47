from datetime import datetime
from typing import Any

import httpx
from pydantic import BaseModel, RootModel
from pydantic_settings import BaseSettings, SettingsConfigDict


class AuthWithCodeResponse(BaseModel):
    access_token: str
    expires_in: int
    refresh_token: str
    scope: str
    token_type: str


class UserWallet(BaseModel):
    id: str
    networkSymbol: str


UserWalletsResponse = RootModel[list[UserWallet]]


class UserProfile(BaseModel):
    id: str
    username: str
    isAdmin: bool
    registered: bool
    isSearchSynced: bool
    createdDate: datetime
    updatedDate: datetime
    profile: dict[str, Any]


class MatricaClient:
    def __init__(self, client_id: str, client_secret: str) -> None:
        self._client = httpx.Client(
            base_url="https://api.matrica.io/",
            headers=[("Accept", "application/json")],
        )
        self._id = client_id
        self._secret = client_secret

    @staticmethod
    def from_env() -> "MatricaClient":
        settings = MatricaSettings()
        return MatricaClient(settings.matrica_client_id, settings.matrica_client_secret)

    def authenticate_with_code(
        self, *, code: str, redirect_uri: str, code_verifier: str
    ) -> AuthWithCodeResponse:
        resp = self._client.post(
            "oauth2/token",
            data={
                "grant_type": "authorization_code",
                "code": code,
                "redirect_uri": redirect_uri,
                "client_id": self._id,
                "client_secret": self._secret,
                "code_verifier": code_verifier,
            },
        )
        resp.raise_for_status()
        return AuthWithCodeResponse.model_validate_json(resp.content)

    def get_oauth2_user_wallets(self, access_token: str) -> list[UserWallet]:
        resp = self._client.get(
            "oauth2/user/wallets",
            headers=[("Authorization", f"Bearer {access_token}")],
        )
        resp.raise_for_status()
        return UserWalletsResponse.model_validate_json(resp.content).root

    def get_oauth2_user_profile(self, access_token: str) -> UserProfile:
        resp = self._client.get(
            "oauth2/user/profile",
            headers=[("Authorization", f"Bearer {access_token}")],
        )
        resp.raise_for_status()
        return UserProfile.model_validate_json(resp.content)


class MatricaSettings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    # Defaults to dev credentials if not set
    matrica_client_id: str = "de25416793038c5"
    matrica_client_secret: str = "lKjwyIph4ANs2E3CIVmbSkds61CprN"
