"""Index heist claim for stats

Revision ID: 0024
Revises: 0023
Create Date: 2024-03-21 15:14:05.616475

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0024"
down_revision = "0023"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        op.f("ix_heistclaim_opposing_wallet_address"),
        "heistclaim",
        ["opposing_wallet_address"],
        unique=False,
    )
    op.create_index(
        op.f("ix_heistclaim_wallet_address"), "heistclaim", ["wallet_address"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_heistclaim_wallet_address"), table_name="heistclaim")
    op.drop_index(op.f("ix_heistclaim_opposing_wallet_address"), table_name="heistclaim")
    # ### end Alembic commands ###
