interface SourceObject {
  src: string;
  opacity?: number | undefined;
  x?: number | undefined;
  y?: number | undefined;
}

type Source = string | SourceObject;

interface Options {
  format?: string | undefined;
  quality?: number | undefined;
  width?: number | undefined;
  height?: number | undefined;
}

export async function mergeImages(sources: Source[], options: Options = {}) {
  const images = await Promise.all(sources.map(loadImage));

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx) {
    throw new Error("Could not get canvas context");
  }

  // Set canvas dimensions
  const getSize = (dim: "height" | "width") =>
    options[dim] ?? Math.max(...images.map((image) => image.img[dim]));
  canvas.width = getSize("width");
  canvas.height = getSize("height");

  // Draw images to canvas
  for (const image of images) {
    ctx.globalAlpha = image.opacity ?? 1;
    ctx.drawImage(image.img, image.x ?? 0, image.y ?? 0);
  }

  const { format = "image/png", quality = 0.92 } = options;
  return canvas.toDataURL(format, quality);
}

interface LoadedImage extends SourceObject {
  img: HTMLImageElement;
}

async function loadImage(source: Source): Promise<LoadedImage> {
  if (typeof source === "string") {
    source = { src: source };
  }
  return new Promise<LoadedImage>((resolve, reject) => {
    const img = new Image();
    img.addEventListener("error", (e) => {
      reject(new Error("Couldn't load image", { cause: e }));
    });
    img.addEventListener("load", () => {
      resolve({ ...source, img });
    });
    img.crossOrigin = "anonymous";
    img.src = source.src;
  });
}
