{"name": "stable-tech", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3000", "build": "next build", "sitemap": "next-sitemap", "start": "npx --package=serve -- serve -p 3000 out", "lint": "eslint --fix .", "landing-page:dev": "vite --config landing-page/vite.config.mjs", "landing-page:build": "vite build --config landing-page/vite.config.mjs", "landing-page:preview": "vite preview --config landing-page/vite.config.mjs"}, "dependencies": {"@casl/ability": "^6.7.3", "@casl/react": "^5.0.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^5.17.1", "@mui/lab": "5.0.0-alpha.173", "@mui/material": "^5.17.1", "@mui/utils": "^5.17.1", "@mui/x-data-grid-pro": "^5.17.26", "@mui/x-date-pickers": "^5.0.20", "@mui/x-license": "^7.29.1", "@popperjs/core": "^2.11.8", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-switch": "^1.2.4", "@reduxjs/toolkit": "^2.8.1", "@sentry/nextjs": "^9.19.0", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "chart.js": "^4.4.9", "chrono-node": "^2.8.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cva": "1.0.0-beta.3", "dayjs": "^1.11.13", "firebase": "^11.7.1", "lucide": "^0.510.0", "lucide-react": "^0.510.0", "mdi-material-ui": "^7.9.4", "next": "^15.3.2", "next-sitemap": "^4.2.3", "nprogress": "^0.2.0", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.3", "react-perfect-scrollbar": "^1.5.8", "react-popper": "^2.3.0", "react-redux": "^9.2.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "stylis": "4.2.0", "stylis-plugin-rtl": "^2.1.1", "tw-animate-css": "^1.2.9", "valibot": "^1.1.0"}, "devDependencies": {"@eslint/js": "^9.26.0", "@next/eslint-plugin-next": "^15.3.2", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/vite": "^4.1.6", "@types/nprogress": "^0.2.3", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "eslint": "^9.26.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unicorn": "^59.0.1", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}, "pnpm": {"overrides": {"@types/react": "18.3.12", "@types/react-dom": "18.3.1"}, "onlyBuiltDependencies": ["esbuild"], "ignoredBuiltDependencies": ["@firebase/util", "@tailwindcss/oxide", "protobufjs", "sharp"]}}