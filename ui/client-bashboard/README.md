## Getting Started

This project uses [pnpm](https://pnpm.io/installation) for managing packages, make sure to have
it installed and up-to-date.

## Install Dependencies

```
pnpm install
```

## Local Config

Get the local API service's URL using

```
minikube service api --url=true
```

Add a file `.env.local` with

```
NEXT_PUBLIC_API_URL=<URL from above>
```

## Start up local dev

```
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to access the app.

## Simulate another environment

By default, the app is run as the variant of `stabletech.capital`. To simulate another
environment, for example `lp.stabletech.capital`, add `SITE_URL` to your `.env.local` file:

```
SITE_URL=https://lp.stabletech.capital
```

> ⚠️ The API server configuration is independent of this setting. This will only change the UI.
> For consistent testing, make sure to also update the API server configuration to run it as the
> desired variant (see
> [backend/README.md](../../backend/README.md#simulate-another-environment)).

## Original Template

This project was bootstrapped using the [Materio Template]. For reference, you may [download the
exact version used][materio-download] from our drive.

[Materio Template]:
  https://demos.themeselection.com/materio-mui-react-nextjs-admin-template/documentation/guide/
[materio-download]:
  https://drive.google.com/file/d/1wPxfMjNZYH64YDu8W3cNmgi-Rrr45zv6/view?usp=sharing
