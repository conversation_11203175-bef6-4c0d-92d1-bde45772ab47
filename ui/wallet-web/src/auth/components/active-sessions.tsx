import { MetadataIcon } from "@/auth/walletkit-metadata-icon";
import { Button, button } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import WalletKitClient from "@reown/walletkit";
import { useMutation } from "@tanstack/react-query";
import { SessionTypes } from "@walletconnect/types";
import { getSdkError } from "@walletconnect/utils";

interface ActiveSessionsProps {
  sessions: SessionTypes.Struct[];
  isLoading: boolean;
  getWalletKit: () => Promise<WalletKitClient>;
  refetchActiveSessions: () => void;
}

export function ActiveSessions({
  sessions,
  isLoading,
  getWalletKit,
  refetchActiveSessions,
}: ActiveSessionsProps) {
  const disconnection = useMutation({
    async mutationFn(topic: string) {
      const walletKit = await getWalletKit();
      await walletKit.disconnectSession({ topic, reason: getSdkError("USER_DISCONNECTED") });
    },
    onSettled() {
      refetchActiveSessions();
    },
  });
  if (isLoading) {
    return (
      <div className="flex flex-col items-center gap-2">
        <h3 className="text-center text-lg font-medium">Active Sessions</h3>
        <div className="flex items-center justify-center p-4">
          <Spinner size={24} />
        </div>
      </div>
    );
  }

  if (sessions.length === 0) {
    return (
      <div className="flex flex-col items-center gap-2">
        <h3 className="text-center text-lg font-medium">Active Sessions</h3>
        <div className="text-center text-sm text-gray-500">
          No active sessions. Connect to a dApp to see sessions here.
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-2">
      <h3 className="text-center text-lg font-medium">Active Sessions</h3>
      <div className="grid w-full gap-2">
        {sessions.map((session) => (
          <div
            key={session.topic}
            className="flex items-center justify-between rounded-md border border-gray-200 bg-white p-3"
          >
            <div className="flex items-center gap-2">
              <a
                className={button({
                  variant: "ghost",
                  className: "grid grid-cols-[auto_1fr] items-center gap-2",
                })}
                href={session.peer.metadata.url}
                rel="noreferrer nofollow"
                referrerPolicy="no-referrer"
                target="_blank"
              >
                <MetadataIcon metadata={session.peer.metadata} />
                {session.peer.metadata.name}
              </a>
            </div>
            <Button
              variant="destructive"
              size="sm"
              disabled={disconnection.isPending}
              onClick={() => {
                disconnection.mutate(session.topic);
              }}
            >
              Disconnect
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}
