resource "google_project_iam_custom_role" "view_service_accounts_excluding_keys" {
  description = "Created on: 2025-01-09 Based on: View Service Accounts"
  permissions = [
    "iam.serviceAccounts.get",
    "iam.serviceAccounts.getIamPolicy",
    "iam.serviceAccounts.list",
    "iam.serviceAccounts.listEffectiveTags",
    "iam.serviceAccounts.listTagBindings",
    "resourcemanager.projects.get",
  ]
  role_id = "ViewServiceAccountsExcludingKeys"
  stage   = "GA"
  title   = "View Service Accounts Excluding Keys"
}

resource "google_project_iam_custom_role" "storage_github_web_deployer" {
  description = "Created on: 2024-05-16 Based on: Storage Object Admin"
  permissions = [
    "orgpolicy.policy.get",
    "resourcemanager.projects.get",
    "storage.buckets.get",
    "storage.managedFolders.create",
    "storage.managedFolders.delete",
    "storage.managedFolders.get",
    "storage.managedFolders.list",
    "storage.multipartUploads.abort",
    "storage.multipartUploads.create",
    "storage.multipartUploads.list",
    "storage.multipartUploads.listParts",
    "storage.objects.create",
    "storage.objects.delete",
    "storage.objects.get",
    "storage.objects.getIamPolicy",
    "storage.objects.list",
    "storage.objects.overrideUnlockedRetention",
    "storage.objects.restore",
    "storage.objects.setIamPolicy",
    "storage.objects.setRetention",
    "storage.objects.update",
  ]
  role_id = "StorageGitHubWebDeployer"
  stage   = "GA"
  title   = "Storage GitHub Web Deployer"
}

resource "google_project_iam_custom_role" "storage_bucket_iam_manager" {
  role_id     = "StorageBucketIamManager"
  title       = "Storage Bucket IAM Manager"
  description = "Allows managing IAM policies on storage buckets"
  permissions = [
    "storage.buckets.get",
    "storage.buckets.list",
    "storage.buckets.getIamPolicy",
    "storage.buckets.setIamPolicy"
  ]
}

resource "google_project_iam_custom_role" "storage_admin_minus_read_write_objects" {
  description = "Created on: 2024-05-16 Based on: Storage Admin"
  permissions = [
    "firebase.projects.get",
    "orgpolicy.policy.get",
    "recommender.iamPolicyInsights.get",
    "recommender.iamPolicyInsights.list",
    "recommender.iamPolicyInsights.update",
    "recommender.iamPolicyRecommendations.get",
    "recommender.iamPolicyRecommendations.list",
    "recommender.iamPolicyRecommendations.update",
    "resourcemanager.projects.get",
    "storage.anywhereCaches.create",
    "storage.anywhereCaches.disable",
    "storage.anywhereCaches.get",
    "storage.anywhereCaches.list",
    "storage.anywhereCaches.pause",
    "storage.anywhereCaches.resume",
    "storage.anywhereCaches.update",
    "storage.bucketOperations.cancel",
    "storage.bucketOperations.get",
    "storage.bucketOperations.list",
    "storage.buckets.create",
    "storage.buckets.createTagBinding",
    "storage.buckets.delete",
    "storage.buckets.deleteTagBinding",
    "storage.buckets.enableObjectRetention",
    "storage.buckets.get",
    "storage.buckets.getIamPolicy",
    "storage.buckets.getObjectInsights",
    "storage.buckets.list",
    "storage.buckets.listEffectiveTags",
    "storage.buckets.listTagBindings",
    "storage.buckets.restore",
    "storage.buckets.setIamPolicy",
    "storage.buckets.update",
    "storage.managedFolders.create",
    "storage.managedFolders.delete",
    "storage.managedFolders.get",
    "storage.managedFolders.getIamPolicy",
    "storage.managedFolders.list",
    "storage.managedFolders.setIamPolicy",
  ]
  role_id = "StorageAdminMinusReadWriteObjects"
  stage   = "GA"
  title   = "Storage Admin minus read/write objects"
}
