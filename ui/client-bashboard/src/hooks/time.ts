import { useCallback, useEffect, useRef, useState } from "react";

/**
 * Use a boolean value that will invert after a given duration.
 * Use `duration` < 0 to set the value permanently.
 * @param initial initial value
 * @returns current value and a setter function
 */
export function useTimedBool(initial: boolean) {
  const [value, setValue] = useState(initial);
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();

  /**
   * @param value value to be set.
   * @param duration duration in milliseconds, use < 0 to not invert the value
   * after duration.
   */
  const set = useCallback((value: boolean, duration: number) => {
    if (timeoutRef.current != null) clearTimeout(timeoutRef.current);
    setValue(value);
    if (duration >= 0) {
      timeoutRef.current = setTimeout(() => {
        setValue(!value);
      }, duration);
    }
  }, []);

  useEffect(
    () => () => {
      // Clear timeout on unmount
      if (timeoutRef.current != null) clearTimeout(timeoutRef.current);
    },
    [],
  );

  return [value, set] as const;
}
