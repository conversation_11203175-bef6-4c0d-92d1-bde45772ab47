// ** React Imports
import React, { useEffect, useState } from "react";

// ** Next Imports
import { useRouter } from "next/router";

// ** Types
import type { ACLObj, AppAbility } from "@/configs/acl";

// ** Context Imports
import { AbilityContext } from "@/layouts/components/acl/Can";

// ** Config Import
import { buildAbilityFor } from "@/configs/acl";

// ** Component Import
import BlankLayout from "@/@core/layouts/BlankLayout";
import NotAuthorized from "@/pages/401";

// ** Hooks
import { useAuth } from "@/hooks/useAuth";

interface AclGuardProps {
  children: React.JSX.Element;
  guestGuard: boolean;
  aclAbilities: ACLObj;
  spinner?: React.JSX.Element | undefined;
}

const AclGuard = (props: AclGuardProps) => {
  // ** Props
  const { aclAbilities, children, guestGuard } = props;

  const [ability, setAbility] = useState<AppAbility>();

  // ** Hooks
  const auth = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!auth.user) {
      setAbility(undefined);
    }
  }, [auth.user]);

  // If guestGuard is true and user is not logged in, or it's an error page, render the page without checking access
  if (
    !auth.user ||
    guestGuard ||
    router.route === "/404" ||
    router.route === "/500" ||
    router.route === "/"
  ) {
    return children;
  }

  // User is logged in, build ability for the user based on his role
  if (auth.user && auth.user.role && !ability) {
    setAbility(buildAbilityFor(auth.user.role, aclAbilities.subject));
  }

  // Check the access of current user and render pages
  if (ability && ability.can(aclAbilities.action, aclAbilities.subject)) {
    return <AbilityContext.Provider value={ability}>{children}</AbilityContext.Provider>;
  }

  // Render Not Authorized component if the current user has limited access
  return (
    <BlankLayout>
      <NotAuthorized />
    </BlankLayout>
  );
};

export default AclGuard;
