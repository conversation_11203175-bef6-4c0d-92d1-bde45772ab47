import Spinner from "@/@core/components/spinner";
import Link from "@/components/Link";
import { Toast, useToast } from "@/components/Toast";
import { getCoinDisplayName } from "@/helpers/coin";
import { currencyFormatter, formatDateTime } from "@/helpers/formatters";
import {
  useAccountingCapitalPositionListSummaryQuery,
  useAccountingDownloadForm8949Mutation,
  useAccountingRegenerateCapitalPositionsMutation,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { CapitalPosition, CapitalPositionSummary } from "@/types/schemas";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  Alert,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { useMemo, useState } from "react";

interface SummaryRow extends CapitalPositionSummary {
  formattedVolume: string;
  formattedCost: string;
  formattedProceeds: string;
  formattedPnl: string;
}

interface CapitalPositionRow extends CapitalPosition {
  formattedAmount: string;
  formattedSoldUsdAmount: string;
  formattedBoughtUsdAmount: string;
  formattedPnl: string;
}

const INITIAL_SUMMARY_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "coin", sort: "asc" }] },
};

const INITIAL_CAPITAL_POSITIONS_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "dateSold", sort: "desc" }] },
  pagination: { pageSize: 50 },
};

// Pre-process number formatting
const formatNumber = (value: string) => currencyFormatter.format(Number.parseFloat(value));

export default function CapitalPositionSummaryList() {
  const [generatedOnly, setGeneratedOnly] = useState(false);
  const [selectedYear, setSelectedYear] = useState<number>();
  const { data, error, isLoading } = useAccountingCapitalPositionListSummaryQuery({
    generatedOnly,
    year: selectedYear,
  });

  // Pre-process data when received
  const processedSummary = useMemo(() => {
    if (!data?.summary) return [];
    return data.summary.map((row) => ({
      ...row,
      formattedVolume: formatNumber(row.volume),
      formattedCost: formatNumber(row.cost),
      formattedProceeds: formatNumber(row.proceeds),
      formattedPnl: formatNumber(row.pnl),
    }));
  }, [data?.summary]);

  const processedPositions = useMemo(() => {
    if (!data?.latestCapitalPositions) return [];
    return data.latestCapitalPositions.map((row) => ({
      ...row,
      formattedAmount: formatNumber(row.amount),
      formattedSoldUsdAmount: formatNumber(row.soldUsdAmount),
      formattedBoughtUsdAmount: formatNumber(row.boughtUsdAmount),
      formattedPnl: formatNumber(row.pnl),
    }));
  }, [data?.latestCapitalPositions]);

  const [regenerateCapitalPosition, regenerateResult] =
    useAccountingRegenerateCapitalPositionsMutation();
  const [downloadForm8949, downloadForm8949Result] = useAccountingDownloadForm8949Mutation();
  const summaryColumns = useMemo(computeSummaryColumns, []);
  const capitalPositionColumns = useMemo(computeCapitalPositionColumns, []);
  const [toastProps, toast] = useToast();

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed loading capital positions" })}
      </Alert>
    );
  }

  if (data == null) {
    return <Spinner />;
  }

  const renderContent = () => {
    const { totalCost, totalPnl, totalProceeds, totalVolume } = data;

    return (
      <>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Grid container spacing={6}>
                <Grid item xs={6} md={4}>
                  <Typography variant={"h4"}>Capital Positions Overview</Typography>
                </Grid>
                <Grid item xs={5} display={"Grid"} justifyContent={"end"}>
                  <Stack direction={"row"} justifyContent={"end"} spacing={4}>
                    <FormControl sx={{ minWidth: 120 }}>
                      <InputLabel id="year-select-label">Year</InputLabel>
                      <Select
                        labelId="year-select-label"
                        value={selectedYear ?? "all"}
                        label="Year"
                        onChange={(e) => {
                          const value = e.target.value;
                          setSelectedYear(value === "all" ? undefined : Number(value));
                        }}
                        displayEmpty
                      >
                        <MenuItem value="all">All</MenuItem>
                        <MenuItem value={2021}>2021</MenuItem>
                        <MenuItem value={2022}>2022</MenuItem>
                        <MenuItem value={2023}>2023</MenuItem>
                        <MenuItem value={2024}>2024</MenuItem>
                      </Select>
                    </FormControl>
                    <Stack direction="row" spacing={2}>
                      <Button
                        variant={generatedOnly ? "contained" : "outlined"}
                        onClick={() => setGeneratedOnly(!generatedOnly)}
                      >
                        Generated Only
                      </Button>
                      <Tooltip title={!selectedYear ? "Please select a year first" : ""}>
                        <span>
                          <LoadingButton
                            variant="outlined"
                            disabled={!selectedYear}
                            loading={downloadForm8949Result.isLoading}
                            onClick={async () => {
                              if (!selectedYear) return;
                              try {
                                const blob = await downloadForm8949({
                                  year: selectedYear,
                                }).unwrap();
                                const url = window.URL.createObjectURL(blob);
                                const a = document.createElement("a");
                                a.href = url;
                                a.download = `Form 8949 Capital Gains - Year ${selectedYear}.xlsx`;
                                document.body.append(a);
                                a.click();
                                window.URL.revokeObjectURL(url);
                                a.remove();
                              } catch (error) {
                                toast({
                                  message: getErrorDetail(error, {
                                    fallback: "Failed downloading Form 8949",
                                  }),
                                  severity: "error",
                                });
                              }
                            }}
                          >
                            Download Form 8949
                          </LoadingButton>
                        </span>
                      </Tooltip>
                    </Stack>
                  </Stack>
                </Grid>
                <Grid item xs={3} display={"Grid"} justifyContent={"end"}>
                  <Stack direction={"row"} justifyContent={"end"} spacing={4}>
                    <LoadingButton
                      loading={regenerateResult.isLoading}
                      onClick={() => {
                        regenerateCapitalPosition()
                          .unwrap()
                          .then(() => {
                            toast({
                              message: "Capital positions regenerated",
                              severity: "success",
                            });
                          })
                          .catch((error: unknown) => {
                            toast({
                              message: getErrorDetail(error, {
                                fallback: "Failed regenerating capital positions",
                              }),
                              severity: "error",
                            });
                          });
                      }}
                    >
                      Regenerate Capital Positions
                    </LoadingButton>
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Stack
                    direction="row"
                    columnGap={12}
                    rowGap={4}
                    justifyContent="center"
                    flexWrap="wrap"
                  >
                    <Box textAlign="center">
                      <Typography noWrap>
                        {currencyFormatter.format(Number.parseFloat(totalVolume))}
                      </Typography>
                      <Typography noWrap variant="body2" color={"primary"}>
                        Total Volume
                      </Typography>
                    </Box>
                    <Box textAlign="center">
                      <Typography noWrap>
                        $ {currencyFormatter.format(Number.parseFloat(totalCost))}
                      </Typography>
                      <Typography noWrap variant="body2" color={"primary"}>
                        Total Cost
                      </Typography>
                    </Box>
                    <Box textAlign="center">
                      <Typography noWrap>
                        $ {currencyFormatter.format(Number.parseFloat(totalProceeds))}
                      </Typography>
                      <Typography noWrap variant="body2" color={"primary"}>
                        Total Proceeds
                      </Typography>
                    </Box>
                    <Box textAlign="center">
                      <Typography noWrap>
                        $ {currencyFormatter.format(Number.parseFloat(totalPnl))}
                      </Typography>
                      <Typography noWrap variant="body2" color={"primary"}>
                        Total PnL
                      </Typography>
                    </Box>
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <DataGridPro
                    columns={summaryColumns}
                    getRowId={getSummaryRowId}
                    initialState={INITIAL_SUMMARY_STATE}
                    rows={processedSummary}
                    autoHeight
                    disableSelectionOnClick
                    loading={isLoading}
                    rowBuffer={20}
                    rowThreshold={100}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title={
                <Stack direction="row" gap={4} alignItems="flex-end">
                  <span>Capital Positions</span>
                </Stack>
              }
            />
            <CardContent>
              <DataGridPro
                columns={capitalPositionColumns}
                getRowId={getCapitalPositionRowId}
                initialState={INITIAL_CAPITAL_POSITIONS_STATE}
                rows={processedPositions}
                pagination
                autoHeight
                disableSelectionOnClick
                loading={isLoading}
                rowBuffer={20}
                rowThreshold={100}
              />
            </CardContent>
          </Card>
        </Grid>
      </>
    );
  };

  return (
    <Grid container gap={6}>
      {renderContent()}
      <Toast {...toastProps} />
    </Grid>
  );
}

function getSummaryRowId(row: SummaryRow) {
  return row.coin.id;
}

function computeSummaryColumns(): GridColDef<SummaryRow, string | number>[] {
  return [
    {
      field: "coin",
      headerName: "Coin",
      width: 300,
      renderCell: ({ row }) => (
        <Link href={`/admin/capital-positions/${row.coin.id}`}>
          {getCoinDisplayName(row.coin)}
        </Link>
      ),
      valueGetter: ({ row }) => getCoinDisplayName(row.coin),
    },
    {
      field: "volume",
      headerName: "Volume",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.volume),
      renderCell: ({ row }) => row.formattedVolume,
    },
    {
      field: "cost",
      headerName: "$ Cost",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.cost),
      renderCell: ({ row }) => row.formattedCost,
    },
    {
      field: "proceeds",
      headerName: "$ Proceeds",
      flex: 1,
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.proceeds),
      renderCell: ({ row }) => row.formattedProceeds,
    },
    {
      field: "pnl",
      headerName: "$ PnL",
      align: "right",
      headerAlign: "right",
      valueGetter: ({ row }) => Number.parseFloat(row.pnl),
      renderCell: ({ row }) => row.formattedPnl,
    },
  ];
}

function computeCapitalPositionColumns(): GridColDef<CapitalPositionRow, string | number>[] {
  return [
    {
      field: "asset",
      headerName: "Asset",
      flex: 1,
      valueGetter: ({ row }) => getCoinDisplayName(row.coin),
    },
    {
      field: "dateSold",
      headerName: "Date Sold",
      flex: 1,
      valueGetter: ({ row }) => row.soldAt,
      renderCell: ({ row }) => formatDateTime(row.soldAt),
    },
    {
      field: "dateAcquired",
      headerName: "Date Acquired",
      flex: 1,
      valueGetter: ({ row }) => row.boughtAt,
      renderCell: ({ row }) => formatDateTime(row.boughtAt),
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.amount),
      renderCell: ({ row }) => row.formattedAmount,
    },
    {
      field: "proceeds",
      headerName: "Proceeds",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.soldUsdAmount),
      renderCell: ({ row }) => row.formattedSoldUsdAmount,
    },
    {
      field: "cost",
      headerName: "Cost",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.boughtUsdAmount),
      renderCell: ({ row }) => row.formattedBoughtUsdAmount,
    },
    {
      field: "pnl",
      headerName: "PnL",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.pnl),
      renderCell: ({ row }) => row.formattedPnl,
    },
  ];
}

function getCapitalPositionRowId(row: CapitalPositionRow) {
  return row.id;
}
