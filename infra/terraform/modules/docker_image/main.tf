variable "app_name" {
  type        = string
  description = "Base image name to use"
}
variable "docker_build" {
  description = "Docker build configuration"
  type = object({
    container_registry = string
    image_version      = string
    build_image        = optional(bool, true)
    platform           = optional(string)
  })
  nullable = false
}
variable "target" {
  type    = string
  default = null
}
variable "dockerfile" { type = string }
variable "path" { type = string }

locals {
  image_name = "${var.docker_build.container_registry}${var.app_name}:${var.docker_build.image_version}"
}

resource "terraform_data" "image" {
  triggers_replace = [local.image_name, var.dockerfile, var.target, var.path]
  provisioner "local-exec" {
    command     = "docker build --progress=plain -f ${var.dockerfile} --target ${var.target} -t ${local.image_name} ./"
    working_dir = var.path
  }
}

output "image_name" {
  description = "fully qualified image name"
  value       = local.image_name
  depends_on = [
    # The image is only available after it has been built, the lack of
    # this dependency may cause "failed to pull image" errors.
    terraform_data.image,
  ]
}
