"use client";

import { FilterButton, useNftFiltering } from "@/app/nfts/[collection-uid]/nft-filtering";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { getErrorMessage } from "@/lib/error";
import { NavigationProvider } from "@/lib/navigation";
import { useListNftsQuery } from "@/lib/nfts";
import { NFTListItem } from "@/types/schemas";
import { X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Suspense } from "react";

const EMPTY_NFTS: readonly NFTListItem[] = [];

const GRID = "grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5";

export function NftList({ collectionUid }: { collectionUid: string }) {
  return (
    <Suspense fallback={<NftListContent collectionUid={collectionUid} />}>
      <NavigationProvider>
        <NftListContent collectionUid={collectionUid} />
      </NavigationProvider>
    </Suspense>
  );
}

function NftListContent({ collectionUid }: { collectionUid: string }) {
  const nftsQuery = useListNftsQuery(collectionUid);
  const nftFiltering = useNftFiltering(nftsQuery.data?.items ?? EMPTY_NFTS);
  const nftsCount = nftFiltering.nfts.length;

  const filters = (
    <div className="mb-4 flex flex-wrap items-center gap-3">
      <FilterButton
        options={nftFiltering.options}
        state={nftFiltering.state}
        setFilter={nftFiltering.setFilter}
      />
      {nftsQuery.isSuccess ? (
        <span className="flex h-9 items-center rounded-sm bg-secondary px-4 text-sm font-medium">
          {nftsCount} NFTs
        </span>
      ) : nftsQuery.isLoading ? (
        <div className="h-9 w-28 animate-pulse rounded-sm bg-muted" />
      ) : null}
      {(collectionUid === "theheist" || collectionUid === "the_heist_generations") && (
        <div className="text-sm text-muted-foreground">
          Click on any NFT&apos;s image to view its most recent claims
        </div>
      )}
      {[...nftFiltering.state].map(([name, value]) => (
        <Button
          key={name}
          title="Clear filter"
          variant="secondary"
          onClick={() => {
            nftFiltering.clearFilters([name]);
          }}
        >
          {name}: {value} <X size={16} className="ml-2" />
        </Button>
      ))}
      {nftFiltering.state.size > 1 && (
        <Button
          title="Clear all filters"
          variant="secondary"
          onClick={() => {
            const activeFilters = [...nftFiltering.state].map(([name]) => name);
            nftFiltering.clearFilters(activeFilters);
          }}
        >
          Clear all
        </Button>
      )}
    </div>
  );

  const content = nftsQuery.isLoading ? (
    <div className={GRID}>
      {new Array(6).fill(null).map((_, i) => (
        <div key={i} className="aspect-square animate-pulse rounded-sm bg-muted" />
      ))}
    </div>
  ) : nftsQuery.isError ? (
    <AlertError>Error: {getErrorMessage(nftsQuery.error)}</AlertError>
  ) : nftFiltering.nfts.length === 0 ? (
    <div className="grid h-32 w-full place-items-center">
      <div className="flex flex-col items-center gap-4">
        <span>No NFTs found</span>
        {nftFiltering.state.size > 0 && (
          <Button
            variant="link"
            size="lg"
            onClick={() => {
              const activeFilters = [...nftFiltering.state].map(([name]) => name);
              nftFiltering.clearFilters(activeFilters);
            }}
          >
            Clear filters
          </Button>
        )}
      </div>
    </div>
  ) : (
    <div className={GRID}>
      {nftFiltering.nfts.map((nft) => (
        <Link
          key={nft.address_uri}
          href={`/heists/nft?id=${nft.address_uri.replace("solana:", "")}`}
          className="relative aspect-square"
        >
          <Image
            className="h-full w-full rounded-sm object-cover shadow-sm"
            src={nft.image_url}
            alt=""
            width={256}
            height={256}
            referrerPolicy="no-referrer"
            unoptimized
          />
          <div className="absolute inset-x-1 bottom-1 flex">
            <div className="flex max-w-full gap-2 rounded-sm bg-black/80 px-2 py-[1px] text-white">
              <span className="flex-1 truncate font-medium">{nft.uid}</span>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );

  return (
    <>
      {filters}
      {content}
    </>
  );
}
