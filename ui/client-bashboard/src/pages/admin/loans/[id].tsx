import Spinner from "@/@core/components/spinner";
import { ButtonCopy } from "@/components/ButtonCopy";
import Link from "@/components/Link";
import TransactionsTable, {
  useTransfersMapperTickers,
} from "@/components/admin/TransactionsTable";
import { LendingProtocolLiqTdUpdate } from "@/components/edit/LendingProtocolLiqTdUpdate";
import { currencyFormatter, formatPercentage } from "@/helpers/formatters";
import { usePathId } from "@/helpers/routing";
import { useIndexerLoanQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { AccountLoanFull, LoanCoinTotal } from "@/types/schemas";
import { ArrowBack, HelpOutline } from "@mui/icons-material";
import {
  Alert,
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  Tooltip,
  Typography,
} from "@mui/material";
import Stack from "@mui/material/Stack";
import { skipToken } from "@reduxjs/toolkit/query";
import { useMemo, useState } from "react";

export default function LoanDetailPage() {
  const loanAccountId = usePathId();

  const { data, isLoading, error } = useIndexerLoanQuery(loanAccountId ?? skipToken);
  const loanData = useMemo(() => (data != null ? processLoanFull(data) : null), [data]);
  const [collaterals, borrows] = useMemo(
    () => (loanData != null ? splitCollateralsBorrows(loanData.coinTotals) : [[], []]),
    [loanData],
  );

  const tickersMapper = useTransfersMapperTickers(
    loanData?.coinTotals?.map((ct) => ct.coin.ticker) ?? [],
  );

  if (loanAccountId == null) {
    return <Alert severity="error">Invalid loan account id</Alert>;
  }

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed to load loan account" })}
      </Alert>
    );
  }

  if (loanData == null || isLoading) {
    return <Spinner />;
  }

  const header = (
    <Stack direction="row" gap={4} alignItems="baseline">
      <Link href="/admin/loans" lineHeight={1} alignSelf="center">
        <ArrowBack fontSize="large" />
      </Link>
      <Typography variant="h4">{loanData.name}</Typography>
    </Stack>
  );

  const loanStats = (
    <Stack direction="row" columnGap={12} rowGap={4} justifyContent="center" flexWrap="wrap">
      <Box textAlign="center">
        <Typography noWrap>$ {currencyFormatter.format(loanData.totalCollateral)}</Typography>
        <Typography noWrap variant="body2">
          Collateral
        </Typography>
      </Box>
      <Box textAlign="center">
        <Typography noWrap>$ {currencyFormatter.format(loanData.totalBorrowed)}</Typography>
        <Typography noWrap variant="body2">
          Borrowed
        </Typography>
      </Box>
      <Box textAlign="center">
        <Typography noWrap>$ {currencyFormatter.format(loanData.balance)}</Typography>
        <Typography noWrap variant="body2">
          Balance
        </Typography>
      </Box>
      <Box textAlign="center">
        <Typography noWrap>{formatPercentage(loanData.ltv)}</Typography>
        <Tooltip title="Loan to Value (Borrowed ÷ Collateral)">
          <Typography noWrap variant="body2">
            LTV
            <HelpOutline sx={{ fontSize: "0.75rem" }} />
          </Typography>
        </Tooltip>
      </Box>
      <Box textAlign="center">
        <Typography noWrap>{formatPercentage(loanData.liqT)}</Typography>
        <Tooltip title="Liquidation Threshold">
          <Typography noWrap variant="body2">
            LiqT
            <HelpOutline sx={{ fontSize: "0.75rem" }} />
          </Typography>
        </Tooltip>
      </Box>
      <Box textAlign="center">
        <Typography noWrap>{formatPercentage(loanData.ptl)}</Typography>
        <Tooltip title="Percent to Liquidation">
          <Typography noWrap variant="body2">
            PTL
            <HelpOutline sx={{ fontSize: "0.75rem" }} />
          </Typography>
        </Tooltip>
      </Box>
    </Stack>
  );

  const overview = (
    <Grid container gap={6}>
      <Grid item xs={12}>
        {loanStats}
      </Grid>
      <Grid item xs={12} md display="flex" flexDirection="column">
        <CoinTotalList
          coinTotals={collaterals}
          isCollateral
          title="Collateral Balance"
          loanId={loanData.id}
        />
      </Grid>
      <Grid item xs={12} md display="flex" flexDirection="column">
        <CoinTotalList coinTotals={borrows} title="Borrowed Balance" loanId={loanData.id} />
      </Grid>
    </Grid>
  );

  return (
    <Card>
      <CardContent>
        <Stack gap={6}>
          {header}
          {overview}
          <Typography variant="h6">Collateral Transactions</Typography>
          <TransactionsTable
            rows={loanData.transactions.filter((t) => t.isCollateral)}
            transfersMapper={tickersMapper}
          />
          <Typography variant="h6">Borrow Transactions</Typography>
          <TransactionsTable
            rows={loanData.transactions.filter((t) => !t.isCollateral)}
            transfersMapper={tickersMapper}
          />
        </Stack>
      </CardContent>
    </Card>
  );
}

interface CoinTotalListProps {
  readonly coinTotals: readonly LoanCoinTotalProcessed[];
  readonly isCollateral?: boolean;
  readonly title: string;
  readonly loanId: number;
}

function CoinTotalList(props: CoinTotalListProps) {
  const { coinTotals, isCollateral, title } = props;
  const [selectedCoinTotal, setSelectedCoinTotal] = useState<LoanCoinTotalProcessed | null>(
    null,
  );
  return (
    <>
      <Typography align="center" mb={3}>
        {title}
      </Typography>
      <Stack direction="row" gap={4} flexWrap="wrap" justifyContent="center" flex={1}>
        {coinTotals.length > 0
          ? coinTotals.map((coinTotal) => (
              <CoinTotalCard
                key={coinTotal.coin.id}
                coinTotal={coinTotal}
                isCollateral={props.isCollateral}
                onSelected={() => setSelectedCoinTotal(coinTotal)}
              />
            ))
          : renderEmptyCoinTotalCard()}
      </Stack>
      {isCollateral && (
        <LendingProtocolLiqTdUpdate
          coinTotal={selectedCoinTotal}
          onClose={() => setSelectedCoinTotal(null)}
          loanId={props.loanId}
        />
      )}
    </>
  );
}

interface CoinTotalCardProps {
  readonly coinTotal: LoanCoinTotalProcessed;
  readonly isCollateral?: boolean;
  onSelected: () => void;
}

function CoinTotalCard(props: CoinTotalCardProps) {
  const {
    borrowAmount,
    collateralAmount,
    coin,
    lossAmount,
    profitAmount,
    liquidationThreshold,
  } = props.coinTotal;
  const amount = props.isCollateral ? collateralAmount : borrowAmount;
  const extraAmount = props.isCollateral ? profitAmount : lossAmount;
  const valueInUsd = amount * coin.latestUsdPrice;
  const textColor = props.isCollateral ? "success.main" : "error.main";
  const ticker = coin.ticker.toUpperCase();
  return (
    <Card variant="outlined">
      <CardContent>
        <Stack my={-3} gap={0.5}>
          <ButtonCopy textToCopy={coin.latestUsdPrice}>
            <Typography color="inherit" fontSize="inherit" noWrap>
              $ {currencyFormatter.format(coin.latestUsdPrice)}
            </Typography>
          </ButtonCopy>
          <ButtonCopy textToCopy={amount}>
            <Typography fontSize={18}>
              {currencyFormatter.format(amount)} {ticker}
            </Typography>
          </ButtonCopy>
          <ButtonCopy textToCopy={valueInUsd}>
            = $ {currencyFormatter.format(valueInUsd)}
          </ButtonCopy>
          {props.isCollateral && (
            <Button color={"primary"} onClick={props.onSelected} size={"small"}>
              <Typography color="inherit" fontSize="inherit" noWrap textAlign={"center"}>
                Liq {liquidationThreshold}%
              </Typography>
            </Button>
          )}
          {extraAmount > 0 && (
            <Box textAlign="center">
              <Typography color={textColor} fontSize="small" noWrap>
                {props.isCollateral ? "Profit" : "Loss"}
              </Typography>
              <Typography fontSize="small" color={textColor} noWrap>
                {currencyFormatter.format(extraAmount)} {ticker}
              </Typography>
            </Box>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
}

function renderEmptyCoinTotalCard() {
  return (
    <Card variant="outlined" sx={{ display: "flex", width: 150 }}>
      <CardContent
        sx={{ flex: 1, display: "flex", alignItems: "center", justifyContent: "center" }}
      >
        <Typography pb={1}>—</Typography>
      </CardContent>
    </Card>
  );
}

export type LoanCoinTotalProcessed = ReturnType<typeof processLoanCoinTotal>;

function processLoanCoinTotal(raw: LoanCoinTotal) {
  return {
    ...raw,
    coin: {
      ...raw.coin,
      latestUsdPrice: Number.parseFloat(raw.coin.latestUsdPrice),
    },
    borrowAmount: Number.parseFloat(raw.borrowAmount),
    collateralAmount: Number.parseFloat(raw.collateralAmount),
    lossAmount: Number.parseFloat(raw.lossAmount),
    profitAmount: Number.parseFloat(raw.profitAmount),
    liquidationThreshold: raw.liquidationThreshold,
  };
}

function processLoanFull(raw: AccountLoanFull) {
  const totalCollateral = Number.parseFloat(raw.totalCollateral);
  const totalBorrowed = Number.parseFloat(raw.totalBorrowed);
  const totalLiquidationThreshold = Number.parseFloat(raw.totalLiquidationThreshold);
  const coinTotals = raw.coinTotals.map(processLoanCoinTotal);

  const ltv = totalBorrowed != 0 && totalCollateral != 0 ? totalBorrowed / totalCollateral : 0;
  const liqT =
    totalLiquidationThreshold != 0 && totalCollateral != 0
      ? totalLiquidationThreshold / totalCollateral
      : 0;
  const balance = totalCollateral - totalBorrowed;
  const ptl = ltv != 0 && liqT != 0 ? liqT - ltv : 0;

  return {
    ...raw,
    totalCollateral,
    totalBorrowed,
    balance,
    ltv,
    liqT,
    ptl,
    coinTotals,
  };
}

function splitCollateralsBorrows(
  coinTotals: readonly LoanCoinTotalProcessed[],
): [LoanCoinTotalProcessed[], LoanCoinTotalProcessed[]] {
  const collaterals: LoanCoinTotalProcessed[] = [];
  const borrows: LoanCoinTotalProcessed[] = [];
  for (const coinTotal of coinTotals) {
    if (coinTotal.borrowAmount + coinTotal.lossAmount !== 0) borrows.push(coinTotal);
    if (coinTotal.collateralAmount + coinTotal.profitAmount !== 0) collaterals.push(coinTotal);
  }
  return [collaterals, borrows];
}
