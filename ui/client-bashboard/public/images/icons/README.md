## ImageMagick Convert

Use the following commands to generate icons from `input.png` with the correct sizes using
ImageMagick on Linux.

```
convert -resize x16 -gravity center -crop 16x16+0+0 -flatten -colors 256 input.png output-16x16.ico
convert -resize x32 -gravity center -crop 32x32+0+0 -flatten -colors 256 input.png output-32x32.ico
convert output-16x16.ico output-32x32.ico favicon.ico
convert -resize x152 input.png apple-touch-icon-152x152.png
convert -resize x120 input.png apple-touch-icon-120x120.png
convert -resize x76  input.png apple-touch-icon-76x76.png
convert -resize x60  input.png apple-touch-icon-60x60.png
```
