"use client";

import { toggle } from "@/lib/components/toggle";
import * as RadixToggleGroup from "@radix-ui/react-toggle-group";
import { VariantProps, cx } from "class-variance-authority";
import { createContext, use } from "react";

const ToggleGroupContext = createContext<VariantProps<typeof toggle>>({
  size: "default",
  variant: "default",
});

export function ToggleGroup({
  className,
  variant,
  size,
  children,
  ...props
}: React.ComponentProps<typeof RadixToggleGroup.Root> & VariantProps<typeof toggle>) {
  return (
    <RadixToggleGroup.Root
      className={cx("flex items-center justify-center gap-1", className)}
      {...props}
    >
      <ToggleGroupContext value={{ variant, size }}>{children}</ToggleGroupContext>
    </RadixToggleGroup.Root>
  );
}

export function ToggleGroupItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof RadixToggleGroup.Item>) {
  const context = use(ToggleGroupContext);

  return (
    <RadixToggleGroup.Item
      className={toggle({
        variant: context.variant,
        size: context.size,
        className,
      })}
      {...props}
    >
      {children}
    </RadixToggleGroup.Item>
  );
}

export { ToggleGroupItem as Item, ToggleGroup as Root };
