"""Initialization script for the pricer service.

This script is run during the initialization of the pricer service to:
1. Create the pricer database if it doesn't exist
2. Run Alembic migrations to create and update database tables
3. Perform any other necessary initialization tasks
"""

import logging

import psycopg.errors
from alembic import config as alembic_config
from sqlalchemy import create_engine, text
from sqlalchemy.exc import ProgrammingError

from common.conf import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

logger.info("Starting pricer service initialization")

# Create a connection to the default PostgreSQL database
pg_host = settings.postgresql_host
pg_user = settings.postgresql_user
pg_password = settings.postgresql_password
pg_db = settings.postgresql_db

# Create a connection to the default PostgreSQL database
engine = create_engine(f"postgresql+psycopg://{pg_user}:{pg_password}@{pg_host}/postgres")


def create_database_if_not_exists(db_name: str) -> None:
    logger.info(f"Creating {db_name} database if it doesn't exist")
    with engine.connect().execution_options(isolation_level="AUTOCOMMIT") as connection:
        try:
            connection.execute(text(f"CREATE DATABASE {db_name}"))
            logger.info(f"{db_name} database created successfully")
        except ProgrammingError as e:
            if not isinstance(e.orig, psycopg.errors.DuplicateDatabase):
                logger.error(f"Failed to create {db_name} database: %s", e)
                raise
            logger.info(f"{db_name} database already exists")


# Create the pricer database if it doesn't exist
create_database_if_not_exists(pg_db)

# Run Alembic migrations for PostgreSQL
logger.info("Running Alembic migrations")
alembic_config.main(argv=["--raiseerr", "-c", "pricer/alembic/alembic.ini", "upgrade", "head"])

# Create database for Metabase
create_database_if_not_exists("metabase")

logger.info("Pricer service initialization completed successfully")
