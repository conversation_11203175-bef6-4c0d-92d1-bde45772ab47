import { VerticalNavItemsType } from "@/@core/layouts/types";
import { hasEarnProduct } from "@/context/env";
import {
  AddLink,
  History,
  MonetizationOn,
  Payments,
  PointOfSale,
  Savings,
  Toll,
} from "@mui/icons-material";
import CandlestickChartIcon from "@mui/icons-material/CandlestickChart";
import CellTowerIcon from "@mui/icons-material/CellTower";
import SwapHorizIcon from "@mui/icons-material/SwapHoriz";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import { Bank, HandCoin, Pool, Wallet } from "mdi-material-ui";
import AccountOutline from "mdi-material-ui/AccountOutline";
import HomeOutline from "mdi-material-ui/HomeOutline";
import ShieldOutline from "mdi-material-ui/ShieldOutline";

type NavItem = VerticalNavItemsType[number];

const navigation = (): VerticalNavItemsType => {
  const items: (NavItem | false)[] = [
    {
      title: "Dashboard",
      path: "/dashboard",
      action: "read",
      subject: "Position",
      icon: HomeOutline,
    },
    hasEarnProduct && {
      title: "Deposit & Withdraw",
      path: "/client/deposit-withdraw",
      action: "read",
      subject: "Position",
      icon: SwapHorizIcon,
    },
    hasEarnProduct && {
      title: "Your Wallets",
      path: "/client/wallets",
      action: "read",
      subject: "Position",
      icon: Wallet,
    },
    {
      sectionTitle: "Transactions",
      action: "manage",
      subject: "all",
    },
    {
      title: "New",
      path: "/portfolio/transactions/new",
      action: "manage",
      subject: "Transaction",
      icon: AddLink,
    },
    {
      title: "History",
      path: "/portfolio/transactions/history",
      action: "manage",
      subject: "Transaction",
      icon: History,
    },
    {
      sectionTitle: "DeFi",
      action: "manage",
      subject: "all",
    },
    {
      title: "Liquidity Pools",
      path: "/admin/pools",
      action: "manage",
      subject: "Pool",
      icon: Pool,
    },
    {
      title: "Loans",
      path: "/admin/loans",
      action: "manage",
      subject: "Loan",
      icon: MonetizationOn,
    },
    {
      title: "Stake",
      path: "/admin/stake",
      action: "manage",
      subject: "all",
      icon: Savings,
    },
    {
      sectionTitle: "Accounting",
      action: "manage",
      subject: "all",
    },
    {
      title: "Asset Holdings",
      path: "/admin/asset-holdings",
      action: "manage",
      subject: "admin-asset-holdings",
      icon: HandCoin,
    },
    {
      title: "Income",
      path: "/admin/income",
      action: "manage",
      subject: "all",
      icon: Toll,
    },
    {
      title: "Expenses",
      path: "/admin/expenses",
      action: "manage",
      subject: "all",
      icon: Payments,
    },
    {
      title: "Capital Positions",
      path: "/admin/capital-positions",
      action: "manage",
      subject: "all",
      icon: PointOfSale,
    },
    {
      title: "Open Positions",
      path: "/admin/open-positions",
      action: "manage",
      subject: "all",
      icon: CandlestickChartIcon,
    },
    {
      sectionTitle: "Clients",
      action: "manage",
      subject: "all",
    },
    {
      title: "Transactions",
      icon: ShieldOutline,
      path: "/admin/client-transactions",
      action: "manage",
      subject: "ClientTransaction",
    },
    hasEarnProduct && {
      title: "Liabilities",
      path: "/admin/liabilities",
      action: "manage",
      subject: "Position",
      icon: Bank,
    },
    {
      sectionTitle: "Admin",
      action: "manage",
      subject: "all",
    },
    {
      title: "Admin Wallets",
      path: "/admin/wallets",
      action: "manage",
      subject: "Wallet",
      icon: Wallet,
    },
    {
      title: "Coins",
      path: "/admin/coins",
      action: "manage",
      subject: "Coin",
      icon: HandCoin,
    },
    {
      title: "Coin Alias",
      path: "/admin/coin-alias",
      action: "manage",
      subject: "Coin Alias",
      icon: HandCoin,
    },
    {
      title: "Networks",
      path: "/admin/networks",
      action: "manage",
      subject: "Network",
      icon: CellTowerIcon,
    },
    {
      title: "Users",
      icon: AccountOutline,
      path: "/admin/users",
      action: "manage",
      subject: "User",
    },
    {
      title: "Import",
      path: "/admin/import",
      action: "manage",
      subject: "all",
      icon: UploadFileIcon,
    },
  ];
  return items.filter(isNotFalse);
};

export default navigation;

function isNotFalse(value: NavItem | false): value is NavItem {
  return value !== false;
}
