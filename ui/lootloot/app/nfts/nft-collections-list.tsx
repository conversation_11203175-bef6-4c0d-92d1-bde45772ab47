"use client";

import { AlertError } from "@/lib/components/alert";
import { useListNftCollectionsQuery } from "@/lib/nfts";
import Image from "next/image";
import Link from "next/link";

const GRID = "grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5";

export function NftCollectionsList() {
  const nftCollectionsQuery = useListNftCollectionsQuery();

  const nftsCount = nftCollectionsQuery.data?.items.reduce(
    (acc, item) => acc + item.nft_count,
    0,
  );

  const topBar = (
    <div className="mb-4 flex h-9 items-center">
      {nftsCount != null ? (
        <span className="flex h-full items-center rounded-sm bg-secondary px-4 text-sm font-medium">
          {nftsCount} NFTs
        </span>
      ) : nftCollectionsQuery.isLoading ? (
        <div className="h-full w-28 animate-pulse rounded-sm bg-muted" />
      ) : null}
    </div>
  );

  const content = nftCollectionsQuery.isLoading ? (
    <div className={GRID}>
      {new Array(6).fill(null).map((_, i) => (
        <div key={i} className="aspect-square animate-pulse rounded-sm bg-muted" />
      ))}
    </div>
  ) : nftCollectionsQuery.isError ? (
    <AlertError>Error: {nftCollectionsQuery.error.message}</AlertError>
  ) : nftCollectionsQuery.data == null || nftCollectionsQuery.data.items.length === 0 ? (
    <div className="grid h-32 w-full place-items-center">No NFTs found</div>
  ) : (
    <div className={GRID}>
      {nftCollectionsQuery.data.items.map((nftCollection) => (
        <Link
          key={nftCollection.uid}
          href={`/nfts/${nftCollection.uid}`}
          className="relative aspect-square"
        >
          <Image
            className="h-full w-full rounded-sm object-cover shadow-sm"
            src={nftCollection.image_url}
            alt=""
            width={256}
            height={256}
            referrerPolicy="no-referrer"
            unoptimized
          />
          <div className="absolute inset-x-1 bottom-1 flex">
            <div className="flex max-w-full gap-2 rounded-sm bg-black/80 px-2 py-[1px] text-white">
              <span className="flex-1 truncate font-medium">{nftCollection.name}</span>
              <span className="flex-none font-light text-gray-300">
                {nftCollection.nft_count}
              </span>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );

  return (
    <>
      {topBar}
      {content}
    </>
  );
}
