import { useRouter } from "next/router";

/**
 * @returns the id from the path, or null if it is missing or not a valid id
 */
export function usePathId(): number | null {
  const { id } = useRouter().query;
  return parseId(id);
}

function parseId(value: unknown): number | null {
  if (typeof value !== "string") {
    return null;
  }
  const id = Number.parseInt(value, 10);
  if (!Number.isFinite(id)) {
    // NaN or Infinity
    return null;
  }

  return id;
}

export function useStakeIdNetworkIdCoinId(): [number | null, number | null, number | null] {
  const { farmId, networkId, coinId } = useRouter().query;
  return [parseId(farmId), parseId(networkId), parseId(coinId)];
}

export function useCoinId(): number | null {
  const { coinId } = useRouter().query;
  return parseId(coinId);
}
