import { useEffect, useReducer, useRef } from "react";

export function useDebounce<T>(delay: number, value: T): T {
  const [, forceUpdate] = useReducer(reducer, 0);
  const debounced = useRef(value);

  useEffect(() => {
    if (delay <= 0) {
      // We need to keep updating the debounced value even if delay <= 0
      // because the delay can change to a positive value later, and we
      // want to use the latest previous value when that happens.
      debounced.current = value;
      return;
    }
    const timeout = setTimeout(() => {
      debounced.current = value;
      forceUpdate();
    }, delay);
    return () => {
      clearTimeout(timeout);
    };
  }, [value, delay]);

  return delay <= 0 ? value : debounced.current;
}

function reducer(x: number) {
  return x + 1;
}
