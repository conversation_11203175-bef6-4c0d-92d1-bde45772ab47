import { ACLObj } from "@/configs/acl";
import type { NextComponentType, NextPageContext } from "next/dist/shared/lib/utils";
import type { ReactElement, ReactNode } from "react";

declare module "next" {
  export declare type NextPage<P = unknown, IP = P> = NextComponentType<
    NextPageContext,
    IP,
    P
  > & {
    acl?: ACLObj;
    authGuard?: boolean;
    guestGuard?: boolean;
    setConfig?: () => void;
    getLayout?: (page: ReactElement) => ReactNode;
    getSpinner?: () => React.JSX.Element;
  };
}
