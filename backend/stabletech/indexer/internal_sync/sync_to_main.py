import logging

from httpx import codes
from sqlalchemy.orm import Session

from common.conf import Environment, settings
from stabletech.client.crud import ClientTransactionManager
from stabletech.indexer.internal_sync.client import (
    INTERNAL_SYNC_ACTIONS,
    SUBSIDIARY_ENVS,
    InternalSyncClient,
)
from stabletech.indexer.models import Transaction

logger = logging.getLogger(__name__)


def sync_transaction_to_main(
    transaction: Transaction, db: Session, replace_transaction_id: int | None = None
):
    """Sync a transaction from a subsidiary to the main company."""
    if (
        settings.env_name not in SUBSIDIARY_ENVS
        or transaction.action not in INTERNAL_SYNC_ACTIONS
    ):
        return

    # Don't sync if this is a ClientTransaction
    if ClientTransactionManager(db).get_by_transaction_id(transaction.id):
        logger.info("Trying to sync a client transaction")
        return

    client = InternalSyncClient(
        Environment.local if settings.is_local else Environment.production
    )
    if replace_transaction_id:
        resp = client.delete_transaction(replace_transaction_id)
        if not resp.status_code == codes.OK:
            # Continue syncing in case Transaction was manually removed
            logger.info(
                f"Transaction {replace_transaction_id} being replaced not found at the main company"
            )

    resp = client.sync_transaction(transaction)
    if not resp.status_code == codes.OK:
        logger.info(f"Error syncing: {resp.json()}")
