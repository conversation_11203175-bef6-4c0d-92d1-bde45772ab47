"""Remove price table

Revision ID: 0036
Revises: 0035
Create Date: 2023-09-15 14:37:14.702389

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0036"
down_revision = "0035"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_price_at", table_name="price")
    op.drop_index("ix_price_coin_id", table_name="price")
    op.drop_index("ix_price_id", table_name="price")
    op.drop_constraint("coin_latest_price_id_fkey", "coin", type_="foreignkey")
    op.drop_constraint("leg_price_id_fkey", "poolleg", type_="foreignkey")
    op.drop_column("coin", "latest_price_id")
    op.drop_column("poolleg", "price_id")
    op.drop_table("price")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "price",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("coin_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "dollar_price",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "last_updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.Column(
            "market_cap",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "price_change_last_day",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "volume_last_day",
            sa.NUMERIC(precision=32, scale=18),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"], name="price_coin_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="price_pkey"),
    )
    op.create_index("ix_price_id", "price", ["id"], unique=False)
    op.create_index("ix_price_coin_id", "price", ["coin_id"], unique=False)
    op.create_index("ix_price_at", "price", ["at"], unique=False)
    op.add_column(
        "poolleg", sa.Column("price_id", sa.INTEGER(), autoincrement=False, nullable=True)
    )
    op.create_foreign_key("leg_price_id_fkey", "poolleg", "price", ["price_id"], ["id"])
    op.add_column(
        "coin", sa.Column("latest_price_id", sa.INTEGER(), autoincrement=False, nullable=True)
    )
    op.create_foreign_key(
        "coin_latest_price_id_fkey", "coin", "price", ["latest_price_id"], ["id"]
    )
    # ### end Alembic commands ###
