import {
  DEFAULT_SORT,
  SortableTableHeader,
  SortBy,
  SortContext,
  SortOrder,
  sortRows,
  TableHeading,
} from "@/components/dashboard/portfolio-table";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { currencyFormatter, diffCurrencyFormatter } from "@/helpers/formatters";
import { DashboardRow } from "@/helpers/portfolio-calculations";
import { cx } from "cva";
import { useEffect, useMemo, useState } from "react";

interface PortfolioOverviewNewProps {
  rows: readonly DashboardRow[];
  showInterest: boolean;
  selectedRowId?: number;
  onRowClick: (row: DashboardRow) => void;
}

export function PortfolioOverviewNew({
  rows,
  showInterest,
  selectedRowId,
  onRowClick,
}: PortfolioOverviewNewProps) {
  const [showInterestColumn, setShowInterestColumn] = useState(true);
  const [sortState, setSortState] = useState<readonly [SortBy, SortOrder]>(DEFAULT_SORT);
  const [sortBy] = sortState;

  const interestVisible = showInterestColumn && showInterest;
  const sortingByInterest = sortBy === "interest" || sortBy === "interest-usd";
  const conflictingSort = !interestVisible && sortingByInterest;

  // Reset sort state to DEFAULT_SORT when there is a conflict
  useEffect(() => {
    if (conflictingSort) {
      setSortState(DEFAULT_SORT);
    }
  }, [conflictingSort]);

  // Fallback to DEFAULT_SORT if there's a conflict
  // This avoids flickering created by the effect to reset the sort state
  const effectiveSortState = conflictingSort ? DEFAULT_SORT : sortState;

  // Apply sorting to rows
  const sortedRows = useMemo(() => {
    const [sortBy, sortOrder] = effectiveSortState;
    return sortRows(rows, sortBy, sortOrder);
  }, [rows, effectiveSortState]);

  // Handler to update sorting
  const handleSorting = (newSortBy: SortBy) => {
    setSortState((prev) => {
      const [prevSortBy, prevSortOrder] = prev;
      if (prevSortBy === newSortBy) {
        return [newSortBy, prevSortOrder === "asc" ? "desc" : "asc"];
      }
      // Default sort order: ascending for asset names, descending for numeric values
      const newSortOrder = newSortBy === "asset" ? "asc" : "desc";
      return [newSortBy, newSortOrder];
    });
  };

  // Reusable button style for sortable headers
  const sortableButtonStyle = cx(
    "block w-full rounded",
    "ring-ring/30 ring-offset-4 focus-visible:ring-2 focus-visible:outline-0",
    "cursor-pointer border-0 bg-transparent p-0",
  );

  // Reusable 24h Change button element
  const change24hButton = (
    <button
      type="button"
      className={cx(sortableButtonStyle, "text-right")}
      onClick={() => handleSorting("24h-change")}
      aria-label="Sort by 24h Change"
    >
      <TableHeading className="font-semibold text-gray-700" itemKey="24h-change" align="end">
        24h Change
      </TableHeading>
    </button>
  );

  const renderedRows = useMemo(() => {
    const renderRow = (row: DashboardRow) => {
      const change24hColorClass = cx(
        row.usd24hChange && row.usd24hChange < 0 ? "text-red-700" : "text-green-700",
      );

      const formattedChange24h = row.usd24hChange
        ? `${diffCurrencyFormatter.format(row.usd24hChange)}%`
        : "";

      const formattedAmount = currencyFormatter.format(row.amount);
      const formattedPrice = `$${currencyFormatter.format(row.price)}`;
      const formattedDollarValue = `$${currencyFormatter.format(row.dollarValue)}`;
      const formattedInterestAmount = currencyFormatter.format(row.interestAmount);
      const formattedInterestDollars = `$${currencyFormatter.format(row.interestEarnedDollars)}`;

      return (
        <tr
          key={row.id}
          className={cx(
            "border-border align-top not-last:border-b hover:bg-gray-50",
            selectedRowId === row.id && "bg-blue-50",
          )}
          onClick={() => onRowClick(row)}
        >
          {/* Mobile view cells (hidden on md and up) */}
          <td className="py-4 pr-2 pl-4 md:hidden">
            <div className="block font-medium sm:hidden">{row.ticker}</div>
            <div className="hidden font-medium sm:block md:hidden">{row.displayName}</div>
            <div className="mt-px font-light">{formattedAmount}</div>
            {interestVisible && (
              <div className="mt-px font-light text-primary">+{formattedInterestAmount}</div>
            )}
          </td>
          <td className="px-2 py-4 text-right md:hidden">
            <div className="font-medium">{formattedDollarValue}</div>
            <div className="mt-px font-light">{formattedPrice}</div>
            {interestVisible && (
              <div className="mt-px font-light text-primary">{formattedInterestDollars}</div>
            )}
          </td>
          <td className={cx("py-4 pr-4 pl-2 text-right md:hidden", change24hColorClass)}>
            {formattedChange24h}
          </td>

          {/* Desktop view cells (hidden on small screens, visible on md and up) */}
          <td className="hidden py-4 pr-2 pl-4 text-left font-medium md:table-cell">
            {row.displayName}
          </td>
          <td className="hidden px-2 py-4 text-right md:table-cell">
            <div className="font-medium">{formattedDollarValue}</div>
            <div className="mt-px font-light">{formattedAmount}</div>
          </td>
          {interestVisible && (
            <td className="hidden px-2 py-4 text-right text-primary md:table-cell">
              <div className="font-medium">{formattedInterestDollars}</div>
              <div className="mt-px font-light">{formattedInterestAmount}</div>
            </td>
          )}
          <td className="hidden px-2 py-4 text-right md:table-cell">{formattedPrice}</td>
          <td
            className={cx(
              "hidden py-4 pr-4 pl-2 text-right md:table-cell",
              change24hColorClass,
            )}
          >
            {formattedChange24h}
          </td>
        </tr>
      );
    };
    return sortedRows.map(renderRow);
  }, [interestVisible, onRowClick, selectedRowId, sortedRows]);

  const tableElement = (
    <table className="w-full text-sm">
      <thead>
        <tr className="border-b border-border bg-gray-50 align-top shadow-xs">
          {/* Mobile view headers (hidden on md and up) */}
          <th className="py-4 pr-2 pl-4 md:hidden">
            <SortableTableHeader
              headings={[
                { text: "Asset", itemKey: "asset" },
                { text: "Amount", itemKey: "amount" },
                {
                  text: "Interest",
                  itemKey: "interest",
                  className: "text-primary",
                  showCondition: interestVisible,
                },
              ]}
              onSelect={handleSorting}
            />
          </th>
          <th className="px-2 py-4 text-right md:hidden">
            <SortableTableHeader
              headings={[
                { text: "$ Total", itemKey: "total-usd" },
                { text: "Market Price", itemKey: "price" },
                {
                  text: "$ Interest",
                  itemKey: "interest-usd",
                  className: "text-primary",
                  showCondition: interestVisible,
                },
              ]}
              align="end"
              onSelect={handleSorting}
            />
          </th>
          <th className="py-4 pr-4 pl-2 text-right md:hidden">{change24hButton}</th>

          {/* Desktop view headers (hidden on small screens, visible on md and up) */}
          <th className="hidden py-4 pr-2 pl-4 text-left md:table-cell">
            <button
              type="button"
              className={cx(sortableButtonStyle, "text-left")}
              onClick={() => handleSorting("asset")}
              aria-label="Sort by Asset"
            >
              <TableHeading
                className="font-semibold text-gray-700"
                itemKey="asset"
                align="start"
              >
                Asset
              </TableHeading>
            </button>
          </th>
          <th className="hidden px-2 py-4 text-right md:table-cell">
            <button
              type="button"
              className={cx(sortableButtonStyle, "text-right")}
              onClick={() => handleSorting("total-usd")}
              aria-label="Sort by $ Total"
            >
              <TableHeading
                className="font-semibold text-gray-700"
                itemKey="total-usd"
                align="end"
              >
                $ Total
              </TableHeading>
            </button>
            <button
              type="button"
              className={cx(sortableButtonStyle, "mt-1 text-right")}
              onClick={() => handleSorting("amount")}
              aria-label="Sort by Amount"
            >
              <TableHeading className="font-medium text-gray-600" itemKey="amount" align="end">
                Amount
              </TableHeading>
            </button>
          </th>
          {interestVisible && (
            <th className="hidden px-2 py-4 text-right md:table-cell">
              <button
                type="button"
                className={cx(sortableButtonStyle, "text-right")}
                onClick={() => handleSorting("interest-usd")}
                aria-label="Sort by Interest $ Value"
              >
                <TableHeading
                  className="font-semibold text-primary"
                  itemKey="interest-usd"
                  align="end"
                >
                  $ Interest
                </TableHeading>
              </button>
              <button
                type="button"
                className={cx(sortableButtonStyle, "mt-1 text-right")}
                onClick={() => handleSorting("interest")}
                aria-label="Sort by Interest Amount"
              >
                <TableHeading
                  className="font-medium text-primary/90"
                  itemKey="interest"
                  align="end"
                >
                  Amount
                </TableHeading>
              </button>
            </th>
          )}
          <th className="hidden px-2 py-4 text-right md:table-cell">
            <button
              type="button"
              className={cx(sortableButtonStyle, "text-right")}
              onClick={() => handleSorting("price")}
              aria-label="Sort by Market Price"
            >
              <TableHeading className="font-semibold text-gray-700" itemKey="price" align="end">
                Market Price
              </TableHeading>
            </button>
          </th>
          <th className="hidden py-4 pr-4 pl-2 text-right md:table-cell">{change24hButton}</th>
        </tr>
      </thead>
      <tbody>{renderedRows}</tbody>
    </table>
  );

  return (
    <SortContext.Provider value={effectiveSortState}>
      <Card padding="none">
        <div className="flex items-center justify-between border-b border-border p-4">
          <h2 className="text-base font-semibold text-primary">Portfolio Overview</h2>

          {showInterest && (
            <Label variant="raw" className="inline-flex items-center gap-2">
              <Switch checked={showInterestColumn} onCheckedChange={setShowInterestColumn} />
              <span className="text-sm font-medium font-stretch-semi-condensed">
                Show Interest
              </span>
            </Label>
          )}
        </div>

        <div className="overflow-x-auto">{tableElement}</div>
      </Card>
    </SortContext.Provider>
  );
}
