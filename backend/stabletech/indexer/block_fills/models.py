from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

from common.models import BaseModel


class Base(DeclarativeBase, BaseModel):
    pass


class BlockFillsInstrument(Base):
    instrument_id: Mapped[int] = mapped_column(index=True, unique=True)

    # Asset being bought/sold
    base_asset_ticker: Mapped[str]
    # Asset being traded for the above one
    contra_asset_ticker: Mapped[str | None]
    description: Mapped[str]
