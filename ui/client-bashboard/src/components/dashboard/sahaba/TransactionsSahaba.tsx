import { Card } from "@/components/ui/card";
import { currencyFormatter } from "@/helpers/formatters";
import { SimpleTransaction, simplifyTransactions } from "@/helpers/transaction-simplifier";
import { Coin, TransactionForClient } from "@/types/schemas";
import { cx } from "cva";
import dayjs from "dayjs";
import { Filter, X } from "lucide-react";
import { useMemo } from "react";

interface TransactionsSahabaProps {
  transactions: readonly TransactionForClient[];
  selectedCoin: Coin | null;
  averageCost: number | null;
  onClearSelectedCoin: () => void;
}

export function TransactionsSahaba({
  transactions,
  selectedCoin,
  averageCost,
  onClearSelectedCoin,
}: TransactionsSahabaProps) {
  // Combine transfers of the same coin and sort
  const tableRows = useMemo(() => {
    const processedTransactions = transactions
      .map(simplifyTransactions)
      .map(processTransaction)
      .sort(sortTransactions);

    return processedTransactions.map((tx) => {
      const dateText = (
        <span className="text-sm text-gray-500">{formatTransactionDate(tx.confirmedAt)}</span>
      );

      const transfers = tx.transfers.map((transfer, index) => {
        const isNegative = transfer.isWithdrawal && tx.action !== "spend";
        return (
          <div
            key={transfer.id}
            className={cx(
              "text-sm font-medium",
              index > 0 && "mt-px",
              tx.clientAction === "Interest Payment"
                ? "text-primary"
                : isNegative
                  ? "text-red-600"
                  : "text-green-600",
            )}
          >
            {isNegative ? "-" : "+"}
            {currencyFormatter.format(Number(transfer.amount))}{" "}
            {transfer.coin?.ticker.toUpperCase()}
          </div>
        );
      });

      const txHash = (
        <a
          className="inline-block border-b border-transparent text-sm text-gray-400 hover:border-gray-400 hover:text-gray-600"
          href={`${tx.network.explorerUrl}/tx/${tx.txHash}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {tx.txHash.length > 10
            ? tx.txHash.slice(0, 6) + "…" + tx.txHash.slice(-4)
            : tx.txHash}
        </a>
      );

      const actionText = (
        <span
          className={cx(
            "text-sm font-medium",
            tx.clientAction === "Interest Payment"
              ? "text-primary"
              : tx.clientAction === "Deposit"
                ? "text-green-600"
                : tx.clientAction === "Withdrawal"
                  ? "text-red-600"
                  : "text-slate-600",
          )}
        >
          {tx.clientAction}
        </span>
      );

      return (
        <tr key={tx.id} className="align-top">
          {/* Mobile layout (2 columns) - visible on small screens, hidden on md+ */}
          <td className="p-3 text-center align-middle md:hidden">
            <div>{actionText}</div>
            <div className="mt-px">{dateText}</div>
          </td>
          <td className="p-3 text-center align-middle md:hidden">
            {transfers}
            <div className="mt-px">{txHash}</div>
          </td>

          {/* Desktop layout (4 columns) - hidden on small screens, visible on md+ */}
          <td className="hidden p-3 text-center group-last:rounded-br-lg md:table-cell">
            {dateText}
          </td>
          <td className="hidden p-3 text-center md:table-cell">{actionText}</td>
          <td className="hidden p-3 text-center group-last:rounded-br-lg md:table-cell">
            {transfers}
          </td>
        </tr>
      );
    });
  }, [transactions]);

  // More minimalistic selected coin badge
  const selectedCoinBadge = selectedCoin && (
    <span className="flex max-w-64 min-w-0 items-center gap-1 border-b border-blue-200 py-1 pr-1 pl-0 text-sm font-medium text-blue-700">
      <Filter className="size-3 flex-none stroke-blue-700" />
      <span className="overflow-hidden text-ellipsis">
        {selectedCoin.name} ({selectedCoin.ticker.toUpperCase()})
      </span>
      <button
        onClick={onClearSelectedCoin}
        className="ml-1 flex-none text-blue-700 hover:text-blue-900 focus-visible:outline-0"
      >
        <X className="size-3" />
        <span className="sr-only">Clear filter</span>
      </button>
    </span>
  );

  // More minimalistic average cost badge
  const averageCostBadge = averageCost != null && (
    <div className="border-b border-gray-200 py-1 text-sm font-medium text-gray-600">
      <span>Avg: ${currencyFormatter.format(averageCost)}</span>
    </div>
  );

  return (
    <Card padding="none">
      <div className="flex flex-col items-start gap-2 p-4 sm:flex-row sm:items-center sm:justify-between sm:gap-4 lg:p-5">
        <div className="flex flex-none flex-col">
          <h2 className="relative pb-1.5 text-xl font-medium text-gray-800">
            Transactions
            <span className="absolute bottom-0 left-0 h-[2px] w-6 bg-primary"></span>
          </h2>
        </div>
        {(selectedCoinBadge || averageCostBadge) && (
          <div className="flex min-w-0 flex-auto items-center gap-4">
            {selectedCoinBadge}
            {averageCostBadge}
          </div>
        )}
      </div>

      <div className="overflow-x-auto">
        <table className="w-full table-auto">
          <thead>
            <tr className="border-b border-primary/20">
              {/* Mobile view headers (hidden on md+) */}
              <th className="p-3 text-center text-sm font-medium text-primary md:hidden">
                Transaction
              </th>
              <th className="p-3 text-center text-sm font-medium text-primary md:hidden">
                Details
              </th>

              {/* Desktop view headers (hidden on small screens) */}
              <th className="hidden p-3 text-center text-sm font-medium text-primary md:table-cell">
                Date
              </th>
              <th className="hidden p-3 text-center text-sm font-medium text-primary md:table-cell">
                Action
              </th>
              <th className="hidden p-3 text-center text-sm font-medium text-primary md:table-cell">
                Transfers
              </th>
            </tr>
          </thead>
          <tbody className="text-sm">{tableRows}</tbody>
        </table>
      </div>
    </Card>
  );
}

// Custom date formatter for more minimalistic format
const formatTransactionDate = (date: dayjs.ConfigType) => {
  const today = dayjs();
  const txDate = dayjs(date);

  // If it's today, just show the time
  if (txDate.isSame(today, "day")) {
    return txDate.format("h:mm A");
  }

  // If it's this year, don't show the year
  if (txDate.isSame(today, "year")) {
    return txDate.format("MMM D");
  }

  // Otherwise show month, day and year
  return txDate.format("MMM D, YYYY");
};

function processTransaction(transaction: SimpleTransaction<TransactionForClient>) {
  return {
    ...transaction,
    confirmedAt: new Date(transaction.confirmedAt),
  };
}

type ProcessedTransaction = ReturnType<typeof processTransaction>;

function sortTransactions(a: ProcessedTransaction, b: ProcessedTransaction) {
  return b.confirmedAt.getTime() - a.confirmedAt.getTime();
}
