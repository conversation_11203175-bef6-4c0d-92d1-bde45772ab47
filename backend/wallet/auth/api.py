import base64
import time
from datetime import datetime, timedelta, timezone
from typing import Annotated, Any, TypedDict

import hishel
import httpx
import jwt
from fastapi import APIRouter, <PERSON><PERSON>, HTTPException, Request, Response
from webauthn.helpers import base64url_to_bytes
from webauthn.helpers.exceptions import (
    InvalidAuthenticationResponse,
    InvalidRegistrationResponse,
)

from common import conf
from common.exceptions import HTTPNotFoundException
from common.psql import DatabaseSession
from wallet import schemas
from wallet.auth.aaguid_names import aaguid_to_name
from wallet.auth.crud import (
    CredentialManager,
    CredentialWrapKeyManager,
    OngoingCredentialAuthenticationManager,
    OngoingCredentialRegistrationManager,
    UserManager,
    UserSessionManager,
)
from wallet.auth.fastapi import CurrentUser, DependsUserSession
from wallet.auth.webauthn import (
    create_credential_authentication_options_for,
    credential_model_to_schema,
    registration_options_for,
    verify_authentication,
    verify_registration,
)
from wallet.kinds import CredentialUse

router = APIRouter()

http_client = hishel.CacheClient()

ALLOWED_ISSUERS = conf.settings.allowed_issuers
ISSUED_AT_DIFF_MIN_S = -5 * 60  # -5 minutes
ISSUED_AT_DIFF_MAX_S = 10  # 10 seconds


class DecodedJwt(TypedDict):
    header: dict[str, Any]
    payload: dict[str, Any]
    signature: bytes


class Jwks(TypedDict):
    keys: list[dict[str, str]]


@router.post("/oidc-login", response_model=schemas.LoginResponse)
def oidc_login(
    body: schemas.OidcLogin,
    db: DatabaseSession,
    request: Request,
    user_agent: Annotated[str | None, Header()],
):
    """Login user with OpenID Connect (OIDC) id_token and access_token.

    Roughly based on https://pyjwt.readthedocs.io/en/stable/usage.html#oidc-login-flow

    See also:
    - https://developers.google.com/identity/openid-connect/openid-connect#validatinganidtoken
    - https://openid.net/specs/openid-connect-core-1_0.html
    """
    if not request.client or not user_agent:
        # Invalid request
        return Response(status_code=401)

    decoded_token: DecodedJwt = jwt.api_jwt.decode_complete(
        body.id_token, options={"verify_signature": False}
    )  # type: ignore
    header, payload = decoded_token["header"], decoded_token["payload"]

    # Don't allow any issuer
    issuer: str | None = payload.get("iss")
    if not issuer:
        return Response(status_code=401)
    client_id = ALLOWED_ISSUERS.get(issuer)
    if not client_id:
        return Response(status_code=401)

    # Must have a kid to identify the public key
    token_kid: str | None = header.get("kid")
    if not token_kid:
        return Response(status_code=401)

    # Get the issuer's configuration
    try:
        config_resp = http_client.get(
            f"{issuer}/.well-known/openid-configuration", follow_redirects=True, timeout=10
        )
    except (httpx.ConnectError, httpx.TimeoutException):
        return Response(status_code=503)
    if config_resp.status_code != 200:
        return Response(status_code=401)
    config: dict[str, Any] = config_resp.json()

    # Get the issuer's public keys
    jwks_uri: str = config["jwks_uri"]
    try:
        jwks_resp = http_client.get(jwks_uri, timeout=10)
    except (httpx.ConnectError, httpx.TimeoutException):
        return Response(status_code=503)
    if jwks_resp.status_code != 200:
        return Response(status_code=401)
    jwks: Jwks = jwks_resp.json()

    # Find the correct public key to verify the signature
    for public_key in jwks["keys"]:
        if public_key["use"] == "sig" and public_key["kid"] == token_kid:
            break
    else:
        return Response(status_code=401)
    public_key = jwt.PyJWK.from_dict(public_key)

    # Verify id_token signature
    signing_algos: list[str] = config["id_token_signing_alg_values_supported"]
    try:
        decoded_token_verified = jwt.api_jwt.decode_complete(
            body.id_token,
            key=public_key.key,
            algorithms=signing_algos,
            audience=client_id,
            options={"require": ["exp", "iat"], "strict_aud": True},
            leeway=10,
        )
    except jwt.InvalidTokenError:
        return Response(status_code=401)

    header: dict[str, Any] = decoded_token_verified["header"]
    payload: dict[str, Any] = decoded_token_verified["payload"]

    # Verify access_token
    alg: str | None = header.get("alg")
    if not alg:
        return Response(status_code=401)
    alg_obj = jwt.get_algorithm_by_name(alg)
    digest = alg_obj.compute_hash_digest(bytes(body.access_token, "utf_8"))
    at_hash = base64.urlsafe_b64encode(digest[: (len(digest) // 2)]).rstrip(b"=")
    at_hash_from_payload: str | None = payload.get("at_hash")
    if not at_hash_from_payload:
        return Response(status_code=401)
    if at_hash != bytes(at_hash_from_payload, "utf_8"):
        return Response(status_code=401)

    # Must have been issued recently, we don't allow login with old tokens (nor future)
    iat = payload.get("iat")
    if not isinstance(iat, (int, float)):
        return Response(status_code=401)
    issued_at_diff = iat - time.time()
    if issued_at_diff < ISSUED_AT_DIFF_MIN_S or issued_at_diff > ISSUED_AT_DIFF_MAX_S:
        return Response(status_code=401)

    # Must have a non-empty email
    user_email: str | None = payload.get("email")
    if not user_email:
        return Response(status_code=401)

    # Respond with token (if no 2fa) and user data
    user_manager = UserManager(db)
    credential_manager = CredentialManager(db)
    ongoing_auth_manager = OngoingCredentialAuthenticationManager(db)
    session_manager = UserSessionManager(db)
    with db.begin():
        user = user_manager.get_by_email(user_email)
        if user:
            credentials = credential_manager.list_by_credentials_id(
                user.credentials_id, use_for="2fa"
            )
            if credentials:
                # User must authenticate with a second factor
                options_json = create_credential_authentication_options_for(
                    "2fa", ongoing_auth_manager, user, credentials
                )
                return schemas.LoginResponse(
                    user=schemas.User.model_validate(user),
                    token=None,
                    expected_2fa=options_json,
                )
        else:
            user = user_manager.create_from_jwt_payload(user_email, payload)

        session = session_manager.create(
            user=user, user_agent=user_agent, ip_address=request.client.host
        )

    return schemas.LoginResponse(
        user=schemas.User.model_validate(user), token=session.token, expected_2fa=None
    )


@router.post("/credential-verification", response_model=schemas.CredentialVerificationResponse)
def verify_credential_authentication_response(
    db: DatabaseSession,
    response: dict[str, Any],
    request: Request,
    user_agent: Annotated[str | None, Header()],
) -> schemas.CredentialVerificationResponse:
    """Verifies a WebAuthn credential during 2FA login and returns a session token upon successful verification."""
    if not request.client or not user_agent:
        raise HTTPException(status_code=400, detail="Invalid request")

    credential_manager = CredentialManager(db)
    ongoing_auth_manager = OngoingCredentialAuthenticationManager(db)
    credential_id = base64url_to_bytes(response["rawId"])
    credential = credential_manager.get_by_id(credential_id)
    if not credential:
        raise HTTPException(status_code=400, detail="Credential doesn't exist")

    user = credential.user
    ongoing_authentication = ongoing_auth_manager.get_by_user_id(user.id)
    if not ongoing_authentication:
        raise HTTPException(status_code=400, detail="User has no ongoing authentication")
    if datetime.now(timezone.utc) - ongoing_authentication.created_at > timedelta(minutes=10):
        raise HTTPException(status_code=400, detail="Authentication expired after 10 minutes")

    try:
        verification = verify_authentication(
            expected_challenge=ongoing_authentication.challenge,
            credential_current_sign_count=credential.sign_count,
            credential_public_key=credential.public_key,
            require_user_verification=ongoing_authentication.require_user_verification,
            authentication_response=response,
        )
    except InvalidAuthenticationResponse as e:
        raise HTTPException(status_code=400, detail=str(e))

    credential_manager.update_on_auth(
        verification.credential_id,
        backed_up=verification.credential_backed_up,
        device_type=verification.credential_device_type,
        sign_count=verification.new_sign_count,
    )
    ongoing_auth_manager.delete(user.id)

    match credential.use_for:
        case "2fa":
            token = (
                UserSessionManager(db)
                .create(user=user, user_agent=user_agent, ip_address=request.client.host)
                .token
            )
            resp = schemas.Credential2faResponse(kind="2fa", token=token)
        case "wrap-key":
            secret_key = CredentialWrapKeyManager(db).get_or_create_secret_key(credential.id)
            resp = schemas.CredentialWrapKeyResponse(
                kind="wrap-key",
                secret_key=base64.urlsafe_b64encode(secret_key).rstrip(b"=").decode("utf-8"),
            )

    db.commit()
    return resp


@router.post("/registration-options")
def create_credential_registration_options(
    db: DatabaseSession, user: CurrentUser, use_for: CredentialUse = "2fa"
) -> Response:
    """Initiates WebAuthn credential registration by returning JSON encoded registration options."""
    credential_manager = CredentialManager(db)
    ongoing_registration_manager = OngoingCredentialRegistrationManager(db)
    existing_credentials = credential_manager.list_by_credentials_id(
        user.credentials_id, use_for=use_for
    )

    require_user_verification = use_for != "2fa"

    challenge, json_encoded_options = registration_options_for(
        use_for, user, existing_credentials
    )

    ongoing_registration_manager.create(
        challenge=challenge,
        require_user_verification=require_user_verification,
        user_id=user.id,
    )
    db.commit()

    return Response(content=json_encoded_options, media_type="application/json")


@router.post("/credentials")
def create_credential(
    registration_response: dict[str, Any],
    user: CurrentUser,
    db: DatabaseSession,
    use_for: CredentialUse = "2fa",
) -> None:
    """Completes setup by registering a new WebAuthn credential for the user."""
    credential_manager = CredentialManager(db)
    ongoing_registration_manager = OngoingCredentialRegistrationManager(db)
    ongoing_registration = ongoing_registration_manager.get_by_user_id(user.id)
    if not ongoing_registration:
        raise HTTPException(status_code=400, detail="User has no ongoing registration")
    if datetime.now(timezone.utc) - ongoing_registration.created_at > timedelta(minutes=10):
        raise HTTPException(status_code=400, detail="Registration expired after 10 minutes")

    try:
        verification = verify_registration(
            expected_challenge=ongoing_registration.challenge,
            require_user_verification=ongoing_registration.require_user_verification,
            registration_response=registration_response,
        )
    except InvalidRegistrationResponse as e:
        raise HTTPException(status_code=400, detail=str(e))

    transports: list[str] = registration_response["response"]["transports"]
    name = aaguid_to_name.get(verification.aaguid, "Security Key")

    credential_manager.create(
        credential_id=verification.credential_id,
        name=name,
        public_key=verification.credential_public_key,
        backed_up=verification.credential_backed_up,
        device_type=verification.credential_device_type,
        transports=transports,
        sign_count=verification.sign_count,
        user_credentials_id=user.credentials_id,
        use_for=use_for,
    )
    ongoing_registration_manager.delete(user.id)
    db.commit()
    return None


@router.get("/credentials", response_model=list[schemas.Credential])
def list_credentials(user: CurrentUser, db: DatabaseSession) -> list[schemas.Credential]:
    """Lists all WebAuthn credentials registered by the user."""
    credential_manager = CredentialManager(db)
    credentials_list = credential_manager.list_by_credentials_id(
        user.credentials_id, use_for=None
    )
    return [credential_model_to_schema(credential) for credential in credentials_list]


@router.delete("/credentials/{credential_id}")
def delete_credential(credential_id: str, user: CurrentUser, db: DatabaseSession) -> None:
    """Removes a specific WebAuthn credential from the user."""
    credential_manager = CredentialManager(db)
    credential_id_bytes = base64url_to_bytes(credential_id)
    credential_manager.delete_for_user(
        credential_id=credential_id_bytes,
        user_credentials_id=user.credentials_id,
    )
    db.commit()
    return None


@router.patch("/credentials/{credential_id}")
def update_credential(
    credential_id: str,
    credential_patch: schemas.CredentialPatch,
    user: CurrentUser,
    db: DatabaseSession,
) -> None:
    """Updates the display name of a specific WebAuthn credential."""
    credential_manager = CredentialManager(db)
    credential_id_bytes = base64url_to_bytes(credential_id)
    credential_manager.update_for_user(
        credential_id=credential_id_bytes,
        user_credentials_id=user.credentials_id,
        name=credential_patch.name,
    )
    db.commit()
    return None


@router.delete("/user-session")
def delete_user_session(db: DatabaseSession, user_session: DependsUserSession) -> None:
    """Delete current user session."""
    db.delete(user_session)
    db.commit()
    return None


@router.post("/wrap-key-options", response_model=schemas.WrapKeyAuthOptionsResponse)
def create_wrap_key_options(
    db: DatabaseSession, user: CurrentUser, request: schemas.WrapKeyAuthOptionsRequest
) -> schemas.WrapKeyAuthOptionsResponse:
    credential_manager = CredentialManager(db)
    ongoing_auth_manager = OngoingCredentialAuthenticationManager(db)

    credential = credential_manager.get_for_user(
        credential_id=base64url_to_bytes(request.credential_id),
        user_credentials_id=user.credentials_id,
    )
    if not credential:
        raise HTTPNotFoundException("Credential not found")
    if credential.use_for != "wrap-key":
        raise HTTPException(status_code=400, detail="Invalid credential for this usage")

    options_json = create_credential_authentication_options_for(
        "wrap-key", ongoing_auth_manager, user, [credential]
    )

    db.commit()
    return schemas.WrapKeyAuthOptionsResponse(auth_options=options_json)
