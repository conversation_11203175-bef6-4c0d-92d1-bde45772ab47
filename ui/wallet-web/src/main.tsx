import "@/sentry-init"; // Importing side effect, MUST be 1st import

import Auth from "@/auth/auth";
import { AuthProvider, loadStoredAuthData } from "@/auth/auth-ctx";
import { CubeSignerSignOutWatcher, getCubeSignerAuthStore } from "@/auth/cubesigner-auth-store";
import { PasswordProvider } from "@/auth/password";
import { setDefaultToken } from "@/lib/api";
import ProtectedLayout from "@/lib/components/protected/protected-layout";
import { ThemeProvider } from "@/lib/contexts/theme-provider";
import "@/main.css";
import Wallet from "@/wallet/page";
import "@fontsource-variable/inter/opsz-italic.css";
import "@fontsource-variable/inter/opsz.css";
import * as Sentry from "@sentry/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { Buffer } from "buffer";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter, Route, Routes } from "react-router";

// Polyfill Buffer for bigint-buffer (@solana/web3.js sub-dependency)
window.Buffer = Buffer;

const rootElement = document.querySelector("#root");
if (!rootElement) {
  throw new Error("Root element not found");
}

const root = createRoot(rootElement, {
  // @ts-expect-error - React own types are mismatching
  onUncaughtError: Sentry.reactErrorHandler((error, errorInfo) => {
    console.warn("Uncaught error", error, errorInfo.componentStack); // eslint-disable-line no-console
  }),
  // @ts-expect-error - React own types are mismatching
  onCaughtError: Sentry.reactErrorHandler(),
  onRecoverableError: Sentry.reactErrorHandler(),
});

const queryClient = new QueryClient();
const storedUserData = loadStoredAuthData();
if (storedUserData?.token) {
  setDefaultToken(storedUserData.token);
  void getCubeSignerAuthStore().restore();
}

root.render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider initialUser={storedUserData?.user ?? null}>
          <ThemeProvider>
            <PasswordProvider>
              <Routes>
                <Route element={<ProtectedLayout />}>
                  <Route path="/" element={<Wallet />} />
                </Route>
                <Route path="auth" element={<Auth />} />
                <Route
                  path="*"
                  element={
                    <div className="p-6">
                      <h1 className="text-xl">Not Found</h1>
                      <p className="mt-2">{"Sorry, we couldn't find this page 🙇"}</p>
                    </div>
                  }
                />
              </Routes>
            </PasswordProvider>
          </ThemeProvider>
          <CubeSignerSignOutWatcher />
        </AuthProvider>
      </BrowserRouter>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  </StrictMode>,
);
