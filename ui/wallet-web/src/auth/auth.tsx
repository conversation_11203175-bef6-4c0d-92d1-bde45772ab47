"use client";
import { AuthContext } from "@/auth/auth-ctx";
import { getCubeSignerAuthStore, useCubeSignerAuth } from "@/auth/cubesigner-auth-store";
import { CubistTOTP } from "@/auth/cubist-totp";
import { setDefaultToken } from "@/lib/api";
import {
  RequestOptions,
  getAuthProvider,
  oidcLogin,
  openIdRedirect,
  useDeleteUserSession,
  verifyCredential,
} from "@/lib/auth";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { SignInWithGoogle } from "@/lib/components/buttons/sign-in-with-google";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { randomId } from "@/lib/utils/random";
import { startAuthentication } from "@simplewebauthn/browser";
import { useQuery } from "@tanstack/react-query";
import { use } from "react";
import { useNavigate } from "react-router";

const LOGIN_ID_KEY = "auth.loginId";

export default function Auth() {
  const authCtx = use(AuthContext);
  const navigate = useNavigate();

  const clearState = () => {
    signOut.reset();
    authCtx.set(null);
  };

  const signOut = useDeleteUserSession();

  const handleSignOut = () => {
    signOut.mutate(undefined, { onSettled: clearState });
  };

  const authInit = useAuthInit();
  const cubeSignerAuth = useCubeSignerAuth();

  // Redirect to wallet if CubeSigner is authenticated
  if (cubeSignerAuth.status === "authenticated") {
    void navigate("/");
  }

  return (
    <div className="flex min-h-dvh">
      <div className="flex w-full flex-col">
        <div className="w-full flex-1 overflow-x-auto">
          <main className="grid justify-items-center gap-6 p-8 pb-16">
            <h1 className="text-xl">Authentication</h1>
            {authInit.isFetching ? (
              <div className="p-5">
                <Spinner size={50} />
              </div>
            ) : cubeSignerAuth.status === "mfa-required" ? (
              <CubistTOTP />
            ) : authCtx.user ? (
              <>
                <Button
                  variant="secondary"
                  disabled={signOut.isPending}
                  onClick={handleSignOut}
                >
                  Sign out
                </Button>
                {signOut.isError && <AlertError>{getErrorMessage(signOut.error)}</AlertError>}
              </>
            ) : (
              <div className="mt-5 flex flex-col items-center gap-4">
                {authInit.isError && (
                  <AlertError>Sign in error: {getErrorMessage(authInit.error)}</AlertError>
                )}
                <SignInWithGoogle onClick={handleSignInWithGoogle} />
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
}

function useAuthInit() {
  const navigate = useNavigate();
  const authCtx = use(AuthContext);

  return useQuery({
    retry: false,
    staleTime: Infinity,
    queryKey: ["authInit"],
    queryFn: async () => {
      const authResp = window.location.hash.slice(1);

      if (!authResp) return null;

      void navigate("", { preventScrollReset: true, replace: true }); // Remove the oauth redirect from the URL

      const loginId = localStorage.getItem(LOGIN_ID_KEY);
      const params = new URLSearchParams(authResp);
      if (!loginId || params.get("state") !== loginId) {
        throw new Error("Invalid authentication state, please try to sign in again");
      }

      const idToken = params.get("id_token");
      if (!idToken) {
        throw new Error("Missing id_token in the authentication response");
      }

      const accessToken = params.get("access_token");
      if (!accessToken) {
        throw new Error("Missing access_token in the authentication response");
      }

      const resp = await oidcLogin({ accessToken, idToken });

      let token: string;
      if (resp.token) {
        token = resp.token;
      } else if (resp.expected_2fa) {
        // 2fa required
        const authResp = await startAuthentication({
          optionsJSON: resp.expected_2fa as RequestOptions,
        });
        const verificationResp = await verifyCredential(authResp);
        if (verificationResp.kind !== "2fa") {
          throw new Error("Unexpected 2FA verification response");
        }
        token = verificationResp.token;
      } else {
        throw new Error("Cannot sign in due to unexpected response");
      }

      // We need to call private endpoints of our api to sign in to CubeSigner
      setDefaultToken(token);
      try {
        await getCubeSignerAuthStore().signIn(idToken);
      } catch (error) {
        // Ensure that we are logged out from api and app
        setDefaultToken("");
        authCtx.set(null);
        throw new Error("Unexpected authentication failure", { cause: error });
      }

      authCtx.set({ user: resp.user, token });

      return null;
    },
  });
}

function handleSignInWithGoogle() {
  const nonce = randomId(16);

  // This id will allow us to reliably detect if the user came back from the upcoming
  // oauth redirect, and thus we can give continuity to the sign in flow initiated here
  const id = randomId(4);
  localStorage.setItem(LOGIN_ID_KEY, id);

  openIdRedirect({
    authorizationEndpoint: "https://accounts.google.com/o/oauth2/v2/auth",
    responseType: "id_token token",
    clientId: getAuthProvider("google").clientId,
    scope: "openid profile email",
    redirectUri: window.location.origin + "/auth",
    state: id,
    nonce,
  });
}
