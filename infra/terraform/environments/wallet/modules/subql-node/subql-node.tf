resource "kubernetes_stateful_set" "subql-node" {
  metadata {
    name      = var.name
    namespace = var.namespace
  }

  spec {
    service_name = var.name
    replicas     = 1
    selector {
      match_labels = {
        app = var.name
      }
    }

    template {
      metadata {
        labels = {
          app = var.name
        }
      }
      spec {
        container {
          name              = var.name
          image             = var.subql_runner_image
          image_pull_policy = "IfNotPresent"
          args = [
            "sh", "-c", var.subql_command
          ]

          dynamic "env_from" {
            for_each = var.config_maps
            iterator = config_map
            content {
              config_map_ref {
                name = config_map.value
              }
            }
          }

          dynamic "env_from" {
            for_each = var.secrets
            iterator = secret
            content {
              secret_ref {
                name = secret.value
              }
            }
          }

          env {
            name  = "TZ"
            value = "UTC"
          }

          resources {
            requests = {
              cpu    = "60m"
              memory = "128Mi"
            }
          }

          dynamic "volume_mount" {
            for_each = var.host_volumes
            content {
              name       = volume_mount.value["name"]
              mount_path = volume_mount.value["mount_path"]
            }
          }
        }
        dynamic "volume" {
          for_each = var.host_volumes
          content {
            name = volume.value["name"]
            host_path {
              path = volume.value["host_path"]
            }
          }
        }
      }
    }
  }
}
