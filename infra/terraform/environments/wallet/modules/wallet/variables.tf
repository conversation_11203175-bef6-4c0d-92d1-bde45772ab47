variable "namespace" {
  description = "Name of the Kubernetes namespace to use"
  type        = string
  nullable    = false
}

variable "env_name" {
  description = "Name of the environment"
  type        = string
  nullable    = false
  validation {
    condition     = contains(["stage", "qa", "production"], var.env_name)
    error_message = "must be stage, qa, or production"
  }
}

variable "dns_managed_zone" {
  description = "Google DNS Managed Zone object"
  type = object({
    name     = string
    dns_name = string
  })
  nullable = false
}

variable "api_replicas" {
  description = "Number of api Pods to run"
  type        = number
  default     = 1
  nullable    = false
}

variable "pg_disk_size" {
  description = "The DB disk size in GB"
  type        = number
  default     = 10
  nullable    = false
}

variable "pg_tier" {
  description = "The DB tier"
  type        = string
  default     = "db-f1-micro"
  nullable    = false
}

variable "auth_allowed_issuers" {
  description = "Allowed issuers of tokens for authentication. Mapping of issuer to client_id."
  type        = map(string)
  nullable    = false
}

variable "secrets" {
  type = object({
    helius_api_key = string
  })
  nullable  = false
  sensitive = true
}

variable "docker_repository" {
  description = "Fully qualified docker repository name where images are stored"
  type        = string
  default     = "us-west1-docker.pkg.dev/temet-359508/temet"
  nullable    = false
}

variable "release" {
  description = "Current version of the code, usually the git short commit SHA"
  type        = string
  nullable    = false
}
