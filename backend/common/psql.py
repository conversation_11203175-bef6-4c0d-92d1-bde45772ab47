from common import conf  # isort: split
from typing import Annotated

from fastapi import Depends
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker

PSQL_URL = (
    f"://{conf.settings.postgresql_user}:{conf.settings.postgresql_password}"
    f"@{conf.settings.postgresql_host}/{conf.settings.postgresql_db}"
)

engine = create_engine("postgresql+psycopg" + PSQL_URL)
SessionLocal = sessionmaker(bind=engine, autoflush=False)

async_engine = create_async_engine("postgresql+psycopg" + PSQL_URL)
async_session = async_sessionmaker(async_engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


DatabaseSession = Annotated[Session, Depends(get_db)]
