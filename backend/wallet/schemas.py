from datetime import datetime
from decimal import Decimal
from typing import Generic, Literal, TypeVar

import pydantic
from pydantic import <PERSON>son

from wallet.kinds import CredentialUse

T = TypeVar("T")


class BaseSchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(from_attributes=True)


class NonPaginatedResp(BaseSchema, Generic[T]):
    items: list[T]


# --------------------------------------------
# Auth
# --------------------------------------------


class User(BaseSchema):
    email: str
    first_name: str | None = None
    last_name: str | None = None
    picture_url: str | None = None


class OidcLogin(BaseSchema):
    id_token: str
    access_token: str


class LoginResponse(BaseSchema):
    expected_2fa: Json | None
    """Defined when must perform 2nd factor authentication."""
    token: str | None
    """`None` when must perform 2nd factor authentication."""
    user: User


class Credential2faResponse(BaseSchema):
    kind: Literal["2fa"]
    token: str


class CredentialWrapKeyResponse(BaseSchema):
    kind: Literal["wrap-key"]
    secret_key: str  # base64url encoded


CredentialVerificationResponse = Credential2faResponse | CredentialWrapKeyResponse


class Credential(BaseSchema):
    id: str  # base64url encoded
    name: str
    created_at: datetime
    latest_use_at: datetime
    use_for: CredentialUse


class CredentialPatch(BaseSchema):
    name: str


class WrapKeyAuthOptionsRequest(BaseSchema):
    credential_id: str  # base64url encoded


class WrapKeyAuthOptionsResponse(BaseSchema):
    auth_options: Json


# --------------------------------------------
# Cubist
# --------------------------------------------


class CubistMFA(BaseSchema):
    type: str


class CubistOidcIdentity(BaseSchema):
    iss: str
    sub: str


class CubistOidcUserInfo(BaseSchema):
    configured_mfa: list[CubistMFA]
    initialized: bool
    name: str
    user_id: str


class CubistIdentityProof(BaseSchema):
    aud: str
    email: str
    exp_epoch: int
    id: str
    identity: CubistOidcIdentity
    user_info: CubistOidcUserInfo | None


class CubistCreateKeyRequest(BaseSchema):
    key_type: str


class CubistRegisterOidcUserResp(BaseSchema):
    user_id: str


# --------------------------------------------
# Indexer
# --------------------------------------------


class Transfer(BaseSchema):
    id: str
    block_number: Decimal
    to_address: str
    from_address: str
    value: Decimal
    contract_address: str
    network: str


class TransfersResp(NonPaginatedResp[Transfer]):
    pass


class IndexTransfersResp(BaseSchema):
    transfers_processed: int
