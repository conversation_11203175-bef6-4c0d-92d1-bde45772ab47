name: Backend CI
on:
  push:
    branches: [main, stage]
    paths: ["backend/**", ".github/workflows/backend-ci.yml"]
  pull_request:
    paths: ["backend/**", ".github/workflows/backend-ci.yml"]
permissions:
  contents: read
  pull-requests: read
jobs:
  # Test PostgreSQL migrations
  # - All migrations must run successfully
  # - Model changes not reflected in the migrations are not allowed
  pg-migration:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:17-alpine
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: temet
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 5s --health-timeout 1s --health-retries 5
    defaults:
      run:
        working-directory: backend
    env:
      ENV_NAME: local
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.12
          cache: pip
          cache-dependency-path: backend/common/requirements.txt
      - run: pip install -r common/requirements.txt
      - name: Tests
        run: |
          set -euxo pipefail

          # PostgreSQL container id
          CONTAINER_ID=$(docker ps --format '{{.ID}}' --filter name=postgres)

          for project in lootloot stabletech wallet ; do
            alembic_config="$project/alembic/alembic.ini"

            # Check that all migrations run successfully
            alembic -c "$alembic_config" upgrade head

            # Check that there are no model changes missing migration
            python3 scripts/diff_db_schema.py "$alembic_config"

            # Keep the database clean
            docker exec -e PGUSER=postgres -e PGPASSWORD=postgres $CONTAINER_ID \
              sh -c "dropdb temet && createdb temet"
          done
