import { apiMutation, apiQuery } from "@/lib/api";
import { useMutation, useQuery } from "@tanstack/react-query";

// TODO: import these from wallet-web/src/types/schemas.ts when this feature is migrated there
interface Transfer {
  id: string;
  block_number: string;
  to_address: string;
  from_address: string;
  value: string;
  contract_address: string;
  network: string;
}

interface TransfersResp {
  items: Transfer[];
}

export function useTransfersQuery(address: string) {
  return useQuery({
    queryKey: ["indexer", "transfers", address],
    queryFn: apiQuery<TransfersResp>,
  });
}

async function indexTransfers(address: string) {
  return apiMutation({
    method: "POST",
    resource: `indexer/transfers/solana/index/${address}`,
  });
}

export function useIndexTransfers() {
  return useMutation({
    mutationFn: indexTransfers,
  });
}
