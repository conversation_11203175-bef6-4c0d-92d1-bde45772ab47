"""Position updates

Revision ID: 0021
Revises: 0020
Create Date: 2023-04-18 20:45:42.716874

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0021"
down_revision = "0020"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_account_id"), "account", ["id"], unique=False)
    op.create_index(op.f("ix_authtoken_id"), "authtoken", ["id"], unique=False)
    op.create_index(op.f("ix_bridge_id"), "bridge", ["id"], unique=False)
    op.create_index(op.f("ix_coin_id"), "coin", ["id"], unique=False)
    op.create_index(op.f("ix_exchange_id"), "exchange", ["id"], unique=False)
    op.create_index(op.f("ix_farm_id"), "farm", ["id"], unique=False)
    op.create_index(op.f("ix_lendingprotocol_id"), "lendingprotocol", ["id"], unique=False)
    op.create_index(op.f("ix_network_id"), "network", ["id"], unique=False)
    op.create_index(op.f("ix_nft_id"), "nft", ["id"], unique=False)
    op.create_index(op.f("ix_pool_id"), "pool", ["id"], unique=False)
    op.create_index(op.f("ix_poolleg_id"), "poolleg", ["id"], unique=False)
    op.create_index(op.f("ix_pooltransaction_id"), "pooltransaction", ["id"], unique=False)
    op.add_column(
        "position",
        sa.Column("cost_basis_usd", sa.Numeric(precision=32, scale=18), nullable=True),
    )
    op.add_column("position", sa.Column("swap_position_id", sa.Integer(), nullable=True))
    op.add_column("position", sa.Column("swap_transaction_id", sa.Integer(), nullable=True))
    op.add_column("position", sa.Column("ended_at", sa.DateTime(), nullable=True))
    op.alter_column("position", "started_at", nullable=False, type_=sa.DateTime())
    op.create_index(
        op.f("ix_position_swap_position_id"), "position", ["swap_position_id"], unique=False
    )
    op.create_index(
        op.f("ix_position_swap_transaction_id"),
        "position",
        ["swap_transaction_id"],
        unique=False,
    )
    op.create_foreign_key(None, "position", "position", ["swap_position_id"], ["id"])
    op.create_foreign_key(None, "position", "transaction", ["swap_transaction_id"], ["id"])
    op.create_index(op.f("ix_price_id"), "price", ["id"], unique=False)
    op.drop_index("ix_transaction_swap_user_id", table_name="transaction")
    op.create_index(op.f("ix_transaction_id"), "transaction", ["id"], unique=False)
    op.drop_constraint("transaction_swap_user_id_fkey", "transaction", type_="foreignkey")
    op.drop_column("transaction", "swap_user_id")
    op.create_index(op.f("ix_transfer_id"), "transfer", ["id"], unique=False)
    op.create_index(op.f("ix_user_id"), "user", ["id"], unique=False)
    op.create_index(op.f("ix_wallet_id"), "wallet", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_wallet_id"), table_name="wallet")
    op.drop_index(op.f("ix_user_id"), table_name="user")
    op.drop_index(op.f("ix_transfer_id"), table_name="transfer")
    op.add_column(
        "transaction",
        sa.Column("swap_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "transaction_swap_user_id_fkey", "transaction", "user", ["swap_user_id"], ["id"]
    )
    op.drop_index(op.f("ix_transaction_id"), table_name="transaction")
    op.create_index(
        "ix_transaction_swap_user_id", "transaction", ["swap_user_id"], unique=False
    )
    op.drop_index(op.f("ix_price_id"), table_name="price")
    op.drop_index(op.f("ix_position_swap_transaction_id"), table_name="position")
    op.drop_index(op.f("ix_position_swap_position_id"), table_name="position")
    op.alter_column("position", "started_at", nullable=False, type_=sa.Date())
    op.drop_column("position", "ended_at")
    op.drop_column("position", "swap_transaction_id")
    op.drop_column("position", "swap_position_id")
    op.drop_column("position", "cost_basis_usd")
    op.drop_index(op.f("ix_pooltransaction_id"), table_name="pooltransaction")
    op.drop_index(op.f("ix_poolleg_id"), table_name="poolleg")
    op.drop_index(op.f("ix_pool_id"), table_name="pool")
    op.drop_index(op.f("ix_nft_id"), table_name="nft")
    op.drop_index(op.f("ix_network_id"), table_name="network")
    op.drop_index(op.f("ix_lendingprotocol_id"), table_name="lendingprotocol")
    op.drop_index(op.f("ix_farm_id"), table_name="farm")
    op.drop_index(op.f("ix_exchange_id"), table_name="exchange")
    op.drop_index(op.f("ix_coin_id"), table_name="coin")
    op.drop_index(op.f("ix_bridge_id"), table_name="bridge")
    op.drop_index(op.f("ix_authtoken_id"), table_name="authtoken")
    op.drop_index(op.f("ix_account_id"), table_name="account")
    # ### end Alembic commands ###
