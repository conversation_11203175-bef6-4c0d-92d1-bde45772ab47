import Spinner from "@/@core/components/spinner";
import Link from "@/components/Link";
import { getCoinDisplayName } from "@/helpers/coin";
import { currencyFormatter, formatDateTime } from "@/helpers/formatters";
import { useCoinId } from "@/helpers/routing";
import { useAccountingCapitalPositionSummaryQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { coinFmt } from "@/types/market";
import { CapitalPosition } from "@/types/schemas";
import { ArrowBack } from "@mui/icons-material";
import {
  Alert,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Stack,
  Typography,
} from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { skipToken } from "@reduxjs/toolkit/query";
import { useMemo, useState } from "react";

const INITIAL_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "dateSold", sort: "desc" }] },
};

export default function CapitalPositionCoinSummary() {
  const coinId = useCoinId();
  const [generatedOnly, setGeneratedOnly] = useState(false);
  const { data, error } = useAccountingCapitalPositionSummaryQuery(
    coinId ? { coinId, generatedOnly } : skipToken,
  );

  const columns = useMemo(computeColumns, []);

  if (coinId == null) {
    return <Alert severity="error">Invalid coin id</Alert>;
  }

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed loading capital positions" })}
      </Alert>
    );
  }

  if (data == null) {
    return <Spinner />;
  }

  const { coin, cost, latestCapitalPositions, pnl, proceeds, volume } = data;

  return (
    <Grid container gap={6}>
      <Grid item xs={12}>
        <Grid container gap={6}>
          <Grid item xs={1}>
            <Stack direction="row" gap={4} alignItems="baseline">
              <Link href="/admin/capital-positions" lineHeight={1} alignSelf="center">
                <ArrowBack fontSize="large" />
              </Link>
            </Stack>
            <Stack direction={"row"} justifyContent={"end"} spacing={4}>
              <Button onClick={() => setGeneratedOnly(!generatedOnly)}>Generated Only</Button>
            </Stack>
          </Grid>
          <Grid item xs={10}>
            <Stack
              direction="row"
              columnGap={12}
              rowGap={4}
              justifyContent="center"
              flexWrap="wrap"
            >
              <Box textAlign="center">
                <Typography noWrap>{coinFmt(coin)}</Typography>
                <Typography noWrap variant="body2">
                  Asset
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  {currencyFormatter.format(Number.parseFloat(volume))}
                </Typography>
                <Typography noWrap variant="body2">
                  Volume
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(cost))}
                </Typography>
                <Typography noWrap variant="body2">
                  Cost
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(proceeds))}
                </Typography>
                <Typography noWrap variant="body2">
                  Proceeds
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography noWrap>
                  $ {currencyFormatter.format(Number.parseFloat(pnl))}
                </Typography>
                <Typography noWrap variant="body2">
                  PnL
                </Typography>
              </Box>
            </Stack>
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12}>
        <Card>
          <CardHeader
            title={
              <Stack direction="row" gap={4} alignItems="flex-end">
                <span>Capital Positions</span>
              </Stack>
            }
          />
          <CardContent>
            <DataGridPro
              columns={columns}
              rows={latestCapitalPositions || []}
              initialState={INITIAL_STATE}
              autoHeight
              disableSelectionOnClick
              getRowId={getRowId}
            />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

function computeColumns(): GridColDef<CapitalPosition, string | number>[] {
  return [
    {
      field: "asset",
      headerName: "Asset",
      flex: 1,
      valueGetter: ({ row }) => getCoinDisplayName(row.coin),
    },
    {
      field: "dateSold",
      headerName: "Date Sold",
      flex: 1,
      valueGetter: ({ row }) => formatDateTime(row.soldAt),
      renderCell: ({ row }) => formatDateTime(row.soldAt),
    },
    {
      field: "dateAcquired",
      headerName: "Date Acquired",
      flex: 1,
      renderCell: ({ row }) => formatDateTime(row.boughtAt),
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.amount),
    },
    {
      field: "proceeds",
      headerName: "Proceeds",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.boughtUsdAmount),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.boughtUsdAmount)),
    },
    {
      field: "cost",
      headerName: "Cost",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.soldUsdAmount),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.soldUsdAmount)),
    },
    {
      field: "pnl",
      headerName: "PnL",
      flex: 1,
      valueGetter: ({ row }) => Number.parseFloat(row.pnl),
      renderCell: ({ row }) => currencyFormatter.format(Number.parseFloat(row.pnl)),
    },
  ];
}

function getRowId(row: CapitalPosition) {
  return row.id;
}
