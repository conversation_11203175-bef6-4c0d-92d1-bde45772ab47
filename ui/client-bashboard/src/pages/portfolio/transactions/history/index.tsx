import { TransactionCardHistory } from "@/components/TransactionCard";
import TransactionsSearch from "@/components/TransactionSearch";
import { useIndexerTransactionsQuery } from "@/store/services/api";
import { Transaction } from "@/types/schemas";
import Grid from "@mui/material/Grid";

const Transactions = () => {
  return (
    <TransactionsSearch
      transactionHook={useIndexerTransactionsQuery}
      displayTransactions={true}
      renderTransaction={(transaction, isFetching) => {
        return (
          <Grid item key={transaction.id} xs={12}>
            <TransactionCardHistory
              isLoading={isFetching}
              transaction={transaction as Transaction}
            />
          </Grid>
        );
      }}
    />
  );
};

Transactions.acl = {
  action: "manage",
  subject: "Transaction",
};

export default Transactions;
