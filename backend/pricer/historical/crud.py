"""CRUD operations for historical price data.

This module provides functionality for storing and querying historical price data
for cryptocurrencies with customizable time intervals. It uses PostgreSQL's specialized
functions for time-series data processing, particularly the `date_bin` function for
precise interval binning.

The main class, `CoinHistoricalPriceManager`, provides methods for:
- Recording price points
- Querying historical price data with OHLCV (Open, High, Low, Close, Volume) aggregation
- Getting the latest prices

Supported time intervals:
- 1h: Hourly data
- 4h: 4-hour data
- 1d: Daily data
- 1w: Weekly data

Performance considerations:
- The composite index on (coingecko_id, timestamp DESC) optimizes queries
- Window functions are used for efficient OHLCV calculations
- Query performance should be under 500ms for typical requests
"""

from collections.abc import Sequence
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Literal

from sqlalchemy import Result, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from pricer.models import CoinHistoricalPrice

IntervalPeriod = Literal["1h", "4h", "1d", "1w"]

# Mapping of interval periods to their timedelta duration
INTERVAL_TIMEDELTAS = {
    "1h": timedelta(hours=1),
    "4h": timedelta(hours=4),
    "1d": timedelta(days=1),
    "1w": timedelta(weeks=1),
}


class CoinHistoricalPriceManager:
    """Async manager for CoinHistoricalPrice model operations."""

    def __init__(self, db: AsyncSession):
        """Initialize the manager with an async database session.

        Args:
            db: SQLAlchemy async database session
        """
        self.db = db

    def _get_ohlcv_query(
        self, *, include_range_filter: bool = False, include_limit: bool = False
    ):
        """Build the base OHLCV query with optional date filter and limit.

        Args:
            include_range_filter: Whether to include date range filtering
            include_limit: Whether to include a LIMIT clause

        Returns:
            The complete SQL query string
        """
        range_filter = (
            "AND timestamp BETWEEN :start_time AND :end_time" if include_range_filter else ""
        )
        limit_clause = "LIMIT :limit" if include_limit else ""

        return text(
            f"""
            SELECT DISTINCT ON (interval_timestamp)
                date_bin(:interval, timestamp, timestamp '2000-01-01') as interval_timestamp,
                FIRST_VALUE(price_usd) OVER w as open_price,
                MAX(price_usd) OVER w as high_price,
                MIN(price_usd) OVER w as low_price,
                LAST_VALUE(price_usd) OVER w as close_price,
                MAX(volume_24h_usd) OVER w as volume
            FROM coin_historical_price
            WHERE coingecko_id = :coingecko_id
            {range_filter}
            WINDOW w AS (
                PARTITION BY date_bin(:interval, timestamp, timestamp '2000-01-01')
                ORDER BY timestamp
                RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
            )
            ORDER BY interval_timestamp DESC
            {limit_clause}
        """
        )

    async def get_price_series(
        self,
        coingecko_id: str,
        start_time: datetime,
        end_time: datetime,
        interval: IntervalPeriod,
    ) -> Sequence[tuple[datetime, Decimal, Decimal, Decimal, Decimal, Decimal]]:
        """Get OHLCV data for a specific time range and interval.

        Args:
            coingecko_id: The CoinGecko ID of the coin to get price data for
            start_time: The start of the time range
            end_time: The end of the time range
            interval: The time interval for aggregation (1h, 4h, 1d, 1w)

        Returns:
            Sequence of (timestamp, open, high, low, close, volume) tuples
        """
        interval_timedelta = INTERVAL_TIMEDELTAS[interval]

        result: Result[tuple[datetime, Decimal, Decimal, Decimal, Decimal, Decimal]] = (
            await self.db.execute(
                self._get_ohlcv_query(include_range_filter=True),
                {
                    "interval": interval_timedelta,
                    "coingecko_id": coingecko_id,
                    "start_time": start_time,
                    "end_time": end_time,
                },
            )
        )
        return result.tuples().all()

    async def get_latest_price(self, coingecko_id: str) -> CoinHistoricalPrice | None:
        """Get the most recent price record for a coin.

        Args:
            coingecko_id: The CoinGecko ID of the coin to get the latest price for

        Returns:
            The most recent CoinHistoricalPrice record or None if no records exist
        """
        stmt = (
            select(CoinHistoricalPrice)
            .where(CoinHistoricalPrice.coingecko_id == coingecko_id)
            .order_by(CoinHistoricalPrice.timestamp.desc())
            .limit(1)
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    def record_price(
        self,
        coingecko_id: str,
        price_usd: Decimal,
        volume_24h_usd: Decimal,
        market_cap_usd: Decimal,
        timestamp: datetime | None = None,
    ) -> CoinHistoricalPrice:
        """Record a price point for a coin at a specific timestamp.

        Args:
            coingecko_id: The CoinGecko ID of the coin to record price for
            price_usd: The price in USD
            volume_24h_usd: 24-hour trading volume in USD
            market_cap_usd: Market capitalization in USD
            timestamp: The timestamp of the price point (defaults to current time if None)

        Returns:
            The created CoinHistoricalPrice record
        """
        price = CoinHistoricalPrice(
            coingecko_id=coingecko_id,
            timestamp=timestamp or datetime.now(timezone.utc),
            price_usd=price_usd,
            volume_24h_usd=volume_24h_usd,
            market_cap_usd=market_cap_usd,
        )
        self.db.add(price)
        return price

    async def get_latest_prices_by_interval(
        self, coingecko_id: str, interval: IntervalPeriod, limit: int = 100
    ) -> Sequence[tuple[datetime, Decimal, Decimal, Decimal, Decimal, Decimal]]:
        """Get the latest OHLCV data for a specific interval.

        Args:
            coingecko_id: The CoinGecko ID of the coin to get price data for
            interval: The time interval for aggregation (1h, 4h, 1d, 1w)
            limit: Maximum number of intervals to return (default: 100)

        Returns:
            Sequence of (timestamp, open, high, low, close, volume) tuples
        """
        interval_timedelta = INTERVAL_TIMEDELTAS[interval]

        result: Result[tuple[datetime, Decimal, Decimal, Decimal, Decimal, Decimal]] = (
            await self.db.execute(
                self._get_ohlcv_query(include_limit=True),
                {
                    "interval": interval_timedelta,
                    "coingecko_id": coingecko_id,
                    "limit": limit,
                },
            )
        )
        return result.tuples().all()
