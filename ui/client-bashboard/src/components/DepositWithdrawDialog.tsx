import { CoinSearch } from "@/components/autocomplete/CoinSearch";
import { NetworkSearch } from "@/components/autocomplete/NetworkSearch";
import { formatDecimal } from "@/helpers/formatters";
import {
  useClientTransactionAddMutation,
  useLazyClientDepositAddressQuery,
  useMarketCoinsUsableByClientsQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Coin, Network } from "@/types/schemas";
import {
  Alert,
  Button,
  DialogActions,
  DialogTitle,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { useEffect, useState } from "react";

export interface DepositWithdrawDialogProps {
  isDeposit: boolean;
  handleClose: () => void;
}

export default function DepositWithdrawDialog(props: DepositWithdrawDialogProps) {
  const [amount, setAmount] = useState<number | null>(null);
  const [coin, setCoin] = useState<Coin | null>(null);
  const [depositAddress, setDepositAddress] = useState<string | null>(null);
  const [errorAlert, setErrorAlert] = useState<string | null>(null);
  const [selectedNetwork, setSelectedNetwork] = useState<Network | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const [addClientTransaction] = useClientTransactionAddMutation();
  const [getDepositAddress] = useLazyClientDepositAddressQuery();
  const { data: supportedCoins } = useMarketCoinsUsableByClientsQuery();

  useEffect(() => {
    setErrorAlert(null);
    if (coin && selectedNetwork) {
      getDepositAddress(selectedNetwork.id).then(({ data: address, error }) => {
        if (error != null || address == null) {
          setErrorAlert(getErrorDetail(error, { fallback: "Couldn't get deposit address" }));
          return;
        }
        setDepositAddress(address);
      });
    }
  }, [coin, getDepositAddress, selectedNetwork]);

  const coinInput = (
    <>
      <TextField
        label={"Amount"}
        type={"number"}
        onChange={(event) => setAmount(Math.abs(Number(event.target.value)))}
        value={amount || ""}
      />
      <CoinSearch
        coinsToSearch={supportedCoins || []}
        selectedCoin={coin}
        setSelectedCoin={setCoin}
      />
    </>
  );

  const confirmInput = (
    <>
      <NetworkSearch
        networksToSearch={
          coin && supportedCoins
            ? (supportedCoins.find((c) => c.id === coin.id)?.networks ?? [])
            : []
        }
        selectedNetwork={selectedNetwork}
        setSelectedNetwork={setSelectedNetwork}
      />
      {selectedNetwork &&
        (props.isDeposit ? (
          depositAddress &&
          selectedNetwork && (
            <>
              <Stack spacing={4} direction={"row"} justifyContent={"center"}>
                <Typography align={"center"}>Please send</Typography>
                <Typography color={"warning.dark"} align={"center"}>
                  {amount && formatDecimal(amount)} {coin?.ticker.toUpperCase()}
                </Typography>
                <Typography align={"center"}>to the following</Typography>
                <Typography color={"warning.dark"} align={"center"}>
                  {selectedNetwork.name}
                </Typography>
                <Typography align={"center"}>address</Typography>
              </Stack>
              <Typography p={2} align={"center"}>
                {depositAddress}
              </Typography>
            </>
          )
        ) : (
          <>
            <Typography align={"center"}>
              Begin processing a withdrawal on {selectedNetwork.name} for
            </Typography>
            <Typography color={"error.dark"} align={"center"}>
              {amount} {coin?.ticker.toUpperCase()}
            </Typography>
            <Typography color={"text.secondary"} align={"center"}>
              Please note it might take up to 10 business days to process.
            </Typography>
          </>
        ))}
    </>
  );

  const submitButton = amount !== null &&
    coin &&
    depositAddress &&
    !showSuccess &&
    selectedNetwork && (
      <Button
        onClick={() => {
          setErrorAlert(null);
          if (amount && coin)
            addClientTransaction({
              amount: amount.toString(),
              coinId: coin.id,
              isDeposit: props.isDeposit,
              networkId: selectedNetwork.id,
            })
              .unwrap()
              .then(() => setShowSuccess(true))
              .catch((error) => setErrorAlert(getErrorDetail(error)));
        }}
        variant={"outlined"}
      >
        {props.isDeposit ? "Confirm Deposit Sent" : "Confirm Withdrawal"}
      </Button>
    );

  return (
    <Dialog open={true}>
      <DialogTitle>{props.isDeposit ? "Deposit" : "Withdraw"}</DialogTitle>
      <DialogContent>
        {showSuccess ? (
          <Typography>Your request has been submitted successfully.</Typography>
        ) : (
          <Stack spacing={6} pt={4} minWidth={"xs"}>
            {errorAlert !== null && <Alert severity={"error"}>{errorAlert}</Alert>}

            {coinInput}

            {amount !== null && amount > 0 && coin && confirmInput}
          </Stack>
        )}
      </DialogContent>
      <DialogActions>
        {/* eslint-disable-next-line jsx-a11y/no-autofocus */}
        <Button autoFocus onClick={props.handleClose}>
          {showSuccess ? "Close" : "Cancel"}
        </Button>
        {submitButton}
      </DialogActions>
    </Dialog>
  );
}
