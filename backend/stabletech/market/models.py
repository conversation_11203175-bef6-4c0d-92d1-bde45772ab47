from datetime import datetime
from decimal import Decimal
from enum import StrEnum, auto

from sqlalchemy import ForeignKey, Numeric
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship

from common.models import BaseModel
from stabletech.indexer.models import Account


class Base(DeclarativeBase, BaseModel):
    pass


class AlertStatus(StrEnum):
    healthy = auto()  # Loan above 10% from liquidation threshold
    initial_alert = auto()  # Loan below or equal to 10% from liquidation threshold
    final_alert = auto()  # Loan below or equal to 5% from liquidation threshold
    liquidated = auto()  # Loan below or equal to 0% from liquidation threshold


class Alert(Base):
    """
    An Alert is generated when a Loan goes below 10% from liquidation threshold,
    we track the collateral_amount at the time of the alert to know if the loan is mutated later on.

    is_active: bool, defines whether we have an existing alert with the same collateral_amount
    at the time we validate the health of the loan, if the collateral_amount of the existing alert
    is different from the current collateral_amount, we create a new alert and set the existing alert
    to is_active = False.
    """

    account_id: Mapped[int] = mapped_column(ForeignKey(Account.id), index=True)
    account: Mapped[Account] = relationship(foreign_keys=[account_id])
    collateral_amount: Mapped[Decimal] = mapped_column(Numeric(precision=48, scale=18))
    created_at: Mapped[datetime]
    updated_at: Mapped[datetime | None]
    sent_at: Mapped[datetime | None]
    status: Mapped[str]
    is_active: Mapped[bool] = mapped_column(server_default="true", index=True)

    def __repr__(self) -> str:
        return (
            f"Alert(id={self.id!r}, account_id={self.account_id}, collateral_amount={self.collateral_amount!r} "
            f"created_at={self.created_at!r}, updated_at={self.updated_at!r}), sent_at={self.sent_at!r} "
            f"status={self.status!r}, is_active={self.is_active!r})"
        )
