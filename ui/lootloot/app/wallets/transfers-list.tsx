"use client";

import { AlertError } from "@/lib/components/alert";
import { But<PERSON> } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";
import { useTransfersQuery } from "@/lib/transfers";
import { RotateCw } from "lucide-react";

interface TransfersListProps {
  address: string;
}

export function TransfersList({ address }: TransfersListProps) {
  const transfersQuery = useTransfersQuery(address);

  if (transfersQuery.isLoading) {
    return <Spinner className="mt-12 flex justify-center" size={50} priority />;
  }

  if (transfersQuery.isError) {
    return <AlertError>{getErrorMessage(transfersQuery.error)}</AlertError>;
  }

  const transfers = transfersQuery.data?.items ?? [];

  return (
    <div className="mb-4">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl">Transfers {transfers.length > 0 && `(${transfers.length})`}</h2>
        <div className="flex gap-2">
          <Button
            onClick={() => {
              void transfersQuery.refetch();
            }}
            variant="ghost"
            size="icon"
            disabled={transfersQuery.isFetching}
          >
            <RotateCw className={transfersQuery.isFetching ? "animate-spin" : ""} />
          </Button>
        </div>
      </div>

      <div id="transfers-panel" className="rounded-lg border shadow-xs">
        {transfers.length === 0 ? (
          <p className="p-4">No transfers found</p>
        ) : (
          <div className="max-h-[800px] overflow-y-auto">
            <table className="min-w-full table-auto" role="grid">
              <thead className="sticky top-0 shadow-xs">
                <tr className="border-b">
                  <th scope="col" className="px-4 py-2 text-left">
                    From
                  </th>
                  <th scope="col" className="px-4 py-2 text-left">
                    To
                  </th>
                  <th scope="col" className="px-4 py-2 text-left">
                    Value
                  </th>
                  <th scope="col" className="px-4 py-2 text-left">
                    Network
                  </th>
                </tr>
              </thead>
              <tbody>
                {transfers.map((transfer) => (
                  <tr key={transfer.id} className="border-b">
                    <td className="px-4 py-2 font-mono text-sm">
                      {transfer.from_address === address ? (
                        <strong className="font-medium">You</strong>
                      ) : (
                        transfer.from_address
                      )}
                    </td>
                    <td className="px-4 py-2 font-mono text-sm">
                      {transfer.to_address === address ? (
                        <strong className="font-medium">You</strong>
                      ) : (
                        transfer.to_address
                      )}
                    </td>
                    <td className="px-4 py-2">{transfer.value}</td>
                    <td className="px-4 py-2">{transfer.network}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
