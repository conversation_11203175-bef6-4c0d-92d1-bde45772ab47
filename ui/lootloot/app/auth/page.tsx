"use client";

import { SignInWithMatrica } from "@/app/auth/matrica";
import { SignInWithPhantom } from "@/app/auth/phantom";
import { useAuth, useLogOut } from "@/lib/auth-ctx";
import { Button, button } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import { User } from "@/types/schemas";
import Image from "next/image";
import Link from "next/link";

export default function Auth() {
  const auth = useAuth();

  return (
    <main className="grid justify-items-center gap-6 p-8">
      <h1 className="text-xl">Authentication</h1>
      {!auth.initialized ? (
        <Spinner size={42} />
      ) : auth.user ? (
        <SignedIn user={auth.user} />
      ) : (
        <>
          <SignInWithPhantom />
          <SignInWithMatrica />
        </>
      )}
    </main>
  );
}

function SignedIn({ user }: { user: User }) {
  const logOut = useLogOut();
  return (
    <>
      <div className="flex max-w-full flex-col items-center overflow-hidden">
        {user.picture_url ? (
          <Image
            className="mb-4 rounded-full shadow-md"
            src={user.picture_url}
            alt=""
            width={70}
            height={70}
            referrerPolicy="no-referrer"
            unoptimized
            priority
          />
        ) : (
          <div className="mb-4 grid h-[70px] w-[70px] place-items-center overflow-hidden rounded-full bg-gray-200 text-4xl shadow-md">
            👤
          </div>
        )}
        <span className="mb-1 max-w-full truncate font-medium">
          {user.first_name} {user.last_name}
        </span>
        <span className="max-w-full truncate text-sm font-light">
          {/* eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing */}
          {user.email || user.matrica_username || user.display_name || "Anonymous"}
        </span>
      </div>
      <div className="flex flex-col gap-3">
        <p className="text-center text-sm font-medium">You are signed in</p>
        <Link href="/" className={button({ variant: "primary" })}>
          Go to app
        </Link>
        <Button variant="secondary" onClick={logOut}>
          Sign out
        </Button>
      </div>
    </>
  );
}
