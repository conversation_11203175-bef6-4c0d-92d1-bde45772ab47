import { Alert, Snackbar } from "@mui/material";
import { useMemo, useState } from "react";

interface Props {
  open: boolean;
  message: React.ReactNode;
  severity: "error" | "warning" | "info" | "success";
  timeout: number;
  onClose: () => void;
}

const INITIAL_PROPS: Props = {
  open: false,
  message: "",
  severity: "info",
  timeout: 0,
  onClose: () => {
    // empty
  },
};

interface ToastParams {
  message: React.ReactNode;
  severity: "error" | "warning" | "info" | "success";
  timeout?: number;
}

export function useToast() {
  const [props, setProps] = useState(INITIAL_PROPS);

  const toast = useMemo(() => {
    function close() {
      setProps((prev) => ({ ...prev, open: false }));
    }

    function toast({ message, severity, timeout = 0 }: ToastParams) {
      setProps({ open: true, message, severity, timeout, onClose: close });
    }

    toast.close = close;
    return toast;
  }, []);

  return [props, toast] as const;
}

export type Toaster = ReturnType<typeof useToast>[1];

export function Toast({ open, message, severity, timeout, onClose }: Props) {
  return (
    <Snackbar
      anchorOrigin={{ horizontal: "center", vertical: "top" }}
      open={open}
      onClose={onClose}
      autoHideDuration={timeout > 0 ? timeout : null}
    >
      <Alert severity={severity} sx={{ width: "100%" }} onClose={onClose}>
        {message}
      </Alert>
    </Snackbar>
  );
}
