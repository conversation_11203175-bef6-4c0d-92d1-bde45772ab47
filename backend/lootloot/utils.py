import base58
from nacl.secret import Secret<PERSON>ox
from sqlalchemy import text

from common import conf
from common.pricer.client import PricerClientSync

"""
Using SecretBox to generate encrypt/decrypt auth message from wallet address
https://pynacl.readthedocs.io/en/latest/secret/
"""
SECRET_BOX = SecretBox(bytes.fromhex(conf.settings.secret_box_hexed_key))

pricer = PricerClientSync.from_env()


def verify_solana_signature(signature, message, address):
    signed_tx = base58.b58decode(signature).decode("utf-8", "replace")
    if not address:
        return False
    if message in signed_tx:
        ## Verify Wallet Address
        decrypted_wallet = ""

        if ": " in message:
            box_encryption = message.split(": ")[1]
            decrypted_wallet = SECRET_BOX.decrypt(base58.b58decode(box_encryption)).decode(
                "utf-8"
            )

        if not decrypted_wallet or address != decrypted_wallet:
            return False
    else:
        return False
    return True


def lock_table_command(model):
    return text(f"LOCK TABLE {model.__tablename__} IN ACCESS EXCLUSIVE MODE;")
