import { isSahaba } from "@/context/env";
import { cva, cx, VariantProps } from "cva";

const card = cva({
  base: cx("rounded-lg", isSahaba ? "bg-card/85 backdrop-blur-sm" : "bg-card"),
  variants: {
    padding: {
      none: "",
      default: "p-4 lg:p-6",
    },
    variant: {
      raised: "shadow",
      outlined: "border border-border",
    },
  },
  defaultVariants: {
    variant: "raised",
    padding: "default",
  },
});

interface CardProps extends React.ComponentPropsWithoutRef<"div">, VariantProps<typeof card> {}

export function Card(props: CardProps) {
  const { className, padding, variant, ...rest } = props;
  return <div className={card({ className, padding, variant })} {...rest} />;
}
