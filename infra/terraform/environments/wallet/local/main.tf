resource "kubernetes_namespace" "wallet" {
  metadata {
    name = var.namespace
  }
}

resource "time_rotating" "image_version" {
  rotation_hours = 12 # cap auto image build once per 12 hours
}

locals {
  # Local to automatically create dependency on kubernetes_namespace.wallet
  namespace = kubernetes_namespace.wallet.metadata[0].name
  env_name  = "local"

  postgresql_db       = "temet"
  postgresql_host     = "postgresql"
  postgresql_user     = "postgres"
  postgresql_password = "password"

  host_volumes = [
    {
      name       = "ipython"
      host_path  = "/data/ipython/"
      mount_path = "/root/.ipython"
    },
    {
      name       = "src"
      host_path  = var.host_path
      mount_path = "/usr/src/app"
    },
  ]

  docker_build = {
    container_registry = "temet/"
    # Run `terraform taint time_rotating.image_version` to force rebuild
    image_version = time_rotating.image_version.unix
  }
}

module "helm_infra" {
  source = "../../../modules/helm_infra"

  enable_postgresql = true
  namespace         = local.namespace
}

module "python_runner" {
  source = "../../../modules/docker_image"

  app_name     = "python-runner"
  docker_build = local.docker_build
  target       = "dev"
  dockerfile   = "infra/docker/python-runner.dockerfile"
  path         = "../../../../../"
}

module "common_config_map" {
  source = "../../../modules/immutable_config_map"

  prefix    = "common"
  namespace = local.namespace
  data = {
    ENV_NAME        = local.env_name
    NAMESPACE       = local.namespace
    ALLOWED_ISSUERS = jsonencode(var.auth_allowed_issuers)
  }
}

module "postgresql_secret" {
  source = "../../../modules/immutable_secret"

  prefix    = "postgresql"
  namespace = local.namespace
  data = {
    POSTGRESQL_DB       = local.postgresql_db
    POSTGRESQL_HOST     = local.postgresql_host
    POSTGRESQL_USER     = local.postgresql_user
    POSTGRESQL_PASSWORD = local.postgresql_password
  }
}

module "init" {
  source     = "../modules/init"
  depends_on = [module.helm_infra]

  namespace           = local.namespace
  python_runner_image = module.python_runner.image_name
  host_volumes        = local.host_volumes

  config_maps = [module.common_config_map.name]
  secrets = [
    module.postgresql_secret.name,
  ]
}

module "helius_secret" {
  source = "../../../modules/immutable_secret"

  prefix    = "helius"
  namespace = local.namespace
  data = {
    HELIUS_API_KEY = var.helius_api_key
  }
}

module "wallet_api" {
  source     = "../modules/api"
  depends_on = [module.helm_infra, module.init]

  namespace              = local.namespace
  python_runner_image    = module.python_runner.image_name
  auto_reload            = true
  host_volumes           = local.host_volumes
  disable_liveness_probe = true

  config_maps = [module.common_config_map.name]
  secrets = [
    module.helius_secret.name,
    module.postgresql_secret.name,
  ]
}

resource "helm_release" "metabase" {
  // https://artifacthub.io/packages/helm/pmint93/metabase
  depends_on = [module.helm_infra, module.init]
  name       = "metabase"
  namespace  = local.namespace
  repository = "https://pmint93.github.io/helm-charts"
  chart      = "metabase"
  version    = "2.9.0"

  values = [yamlencode({
    database : {
      host : local.postgresql_host
      dbname : "metabase"
      username : local.postgresql_user
      password : local.postgresql_password
      type : "postgres"
    }
  })]
}
