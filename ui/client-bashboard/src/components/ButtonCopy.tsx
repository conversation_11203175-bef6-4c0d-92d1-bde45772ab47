import { <PERSON><PERSON>, <PERSON> } from "@mui/material";
import React, { useState } from "react";

import { SnackSuccess } from "@/components/SnackSuccess";
import { ButtonColor } from "@/types/style";

const MINIMAL_CSS = {
  display: "inline",
  py: 0,
  px: 1,
  mx: -1,
  minHeight: 0,
  minWidth: 0,
  textAlign: "left",
  width: "fit-content",
};

interface ButtonCopyProps {
  children: React.ReactNode;
  color?: ButtonColor;
  size?: "small" | "medium" | "large";
  textToCopy: number | string | undefined;
  minimal?: boolean | undefined;
}

export const ButtonCopy = (props: ButtonCopyProps) => {
  const [showCopiedSnack, setShowCopiedSnack] = useState(false);

  return (
    <React.Fragment>
      <Portal>
        <SnackSuccess
          label={"Copied to clipboard"}
          showSuccessSnack={showCopiedSnack}
          setShowSuccessSnack={setShowCopiedSnack}
        />
      </Portal>

      <Button
        color={props.color || "primary"}
        onClick={() => {
          navigator.clipboard.writeText(String(props.textToCopy ?? "")).then(() => {
            setShowCopiedSnack(true);
          });
        }}
        size={props.size || "small"}
        sx={props.minimal ? MINIMAL_CSS : undefined}
      >
        {props.children}
      </Button>
    </React.Fragment>
  );
};
