import csv
import dataclasses
import itertools
import json
import logging
import sys
import typing
from datetime import datetime, timedelta
from decimal import Decimal
from zoneinfo import ZoneInfo

import dateutil.parser
import psycopg.errors
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from common.pricer.client import PricerClientSync
from stabletech.auth.crud import UserManager
from stabletech.auth.models import User
from stabletech.indexer.crud import (
    AccountManager,
    CoinManager,
    ExchangeManager,
    LendingProtocolManager,
    NetworkManager,
    TransactionManager,
    WalletManager,
)
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.models import Account, Coin, Job, Network, Transaction, Transfer, Wallet
from stabletech.schemas import ImportFailedRow, ImportJobParams, ImportResult
from stabletech.utils.decimal_format import decimal_to_str

NAME = "CoinTracking"
URL = "https://cointracking.info/"


logger = logging.getLogger(__name__)


class CoinNotFoundException(Exception):
    pass


@dataclasses.dataclass(kw_only=True)
class ImportContext:
    coin_manager: CoinManager
    acc_manager: AccountManager
    transaction_manager: TransactionManager
    user: User
    network: Network
    wallet: Wallet
    income_acc: Account
    spend_acc: Account
    exchange_acc: Account
    loan_acc: Account
    wallet_acc: Account
    pricer_client: PricerClientSync
    pricer_coins: list[Coin]  # Cached Pricer Coins

    @classmethod
    def with_defaults(
        cls,
        *,
        coin_manager: CoinManager,
        acc_manager: AccountManager,
        network_manager: NetworkManager,
        exchange_manager: ExchangeManager,
        lending_protocol_manager: LendingProtocolManager,
        wallet_manager: WalletManager,
        transaction_manager: TransactionManager,
        user: User,
    ):
        network = network_manager.get_or_create_by_name(name=f"{NAME}Network")
        wallet = wallet_manager.get_or_create_by_address(
            address=f"{NAME}Wallet", name=f"{NAME}Wallet", networks=[network]
        )
        lending_protocol = lending_protocol_manager.get_or_create_by_name(
            name=f"{NAME}Loan", url=URL
        )
        exchange = exchange_manager.get_or_create_by_name(name=NAME, url=URL)

        income_acc = acc_manager.get_or_create_with_network_id(
            address=f"{NAME}Income", network=network
        )
        loan_acc = acc_manager.get_or_create_with_network_id(
            address=f"{NAME}Loan",
            network=network,
            lending_protocol=lending_protocol,
        )
        spend_acc = acc_manager.get_or_create_with_network_id(
            address=f"{NAME}Spend", network=network
        )
        exchange_acc = acc_manager.get_or_create_with_network_id(
            address=f"{NAME}Exchange", network=network, exchange=exchange
        )
        wallet_acc = acc_manager.get_or_create_with_network_id(
            address=f"{NAME}Wallet", network=network, wallet=wallet
        )

        pricer_client = PricerClientSync.from_env()

        return cls(
            coin_manager=coin_manager,
            acc_manager=acc_manager,
            transaction_manager=transaction_manager,
            user=user,
            network=network,
            wallet=wallet,
            income_acc=income_acc,
            spend_acc=spend_acc,
            exchange_acc=exchange_acc,
            loan_acc=loan_acc,
            wallet_acc=wallet_acc,
            pricer_client=pricer_client,
            pricer_coins=[],
        )

    def get_asset_account(self, currency: str) -> Account:
        if not currency:
            raise ValueError("Currency must be non-empty")
        coin = self.coin_manager.get_priced_coin_by_ticker(currency)
        if not coin:
            # Query the Pricer to try to find and add the Coin
            if not self.pricer_coins:
                self.pricer_coins = self.pricer_client.get_coins()
            matching_coins = [
                c for c in self.pricer_coins if c.symbol.upper() == currency.upper()
            ]
            if not matching_coins:
                raise CoinNotFoundException(currency)

            # Sort by market cap (descending) and take the first one
            matching_coins.sort(key=lambda c: c.market_cap_usd or 0, reverse=True)
            selected_coin = matching_coins[0]

            # Enable the Coin's prices
            logger.info(
                f"Auto-enabled prices for {selected_coin.symbol} with UID {selected_coin.id}."
            )
            coin = self.coin_manager.get_coin_by_uid(selected_coin.id)
            self.coin_manager.update_coin_has_prices(coin.id)

        acc = self.acc_manager.get_or_create_with_network_id(
            address=currency, network=self.network, ticker_symbol=currency, coin=coin
        )
        return acc


# CSV row: Type, Buy, Cur., Sell, Cur., Fee, Cur., Exchange, Group, Comment, Date, Tx-ID
CSV_ROW_LENGTH = 12


def ensure_csv_row_length(row: list[str]) -> list[str]:
    """Ensure the row has the correct length."""
    diff_len = CSV_ROW_LENGTH - len(row)
    if diff_len > 0:
        return row + [""] * diff_len
    elif diff_len < 0:
        return row[:CSV_ROW_LENGTH]
    return row


def parse_decimal(value: str) -> Decimal:
    if value == "":
        return Decimal(0)
    return Decimal(value)


entry_type_to_action_map = {
    "airdrop": TransactionAction.income.value,
    "income": TransactionAction.income.value,
    "mining": TransactionAction.income.value,
    "other expense": TransactionAction.loan.value,
    "other income": TransactionAction.loan.value,
    "receive loan": TransactionAction.loan.value,
    "repay loan": TransactionAction.loan.value,
    "spend": TransactionAction.spend.value,
    "staking": TransactionAction.income.value,
    "trade": TransactionAction.swap.value,
}


def get_action_from_type(action_type: str) -> str:
    return entry_type_to_action_map[action_type.lower()]


@dataclasses.dataclass
class Entry:
    """Represents a CoinTracking entry."""

    entry_type: str
    buy: Decimal
    buy_currency: str
    sell: Decimal
    sell_currency: str
    fee: Decimal
    fee_currency: str
    exchange: str
    group: str
    comment: str
    date: datetime
    tx_id: str

    @classmethod
    def from_row(cls, row: list[str]):
        """Create from a CSV row."""
        row = ensure_csv_row_length(row)
        row = [value.strip() for value in row]
        (
            entry_type,
            buy,
            buy_currency,
            sell,
            sell_currency,
            fee,
            fee_currency,
            exchange,
            group,
            comment,
            date,
            tx_id,
        ) = row
        buy = parse_decimal(buy)
        sell = parse_decimal(sell)
        fee = parse_decimal(fee)
        date = dateutil.parser.parse(date)
        return cls(
            entry_type,
            buy,
            buy_currency,
            sell,
            sell_currency,
            fee,
            fee_currency,
            exchange,
            group,
            comment,
            date,
            tx_id,
        )

    def action(self) -> str:
        return get_action_from_type(self.entry_type)

    def json(self) -> str:
        return json.dumps(dataclasses.asdict(self), default=str)

    def tx_hash(self) -> str:
        bought = self.buy_currency or "__"
        comment = self.comment or "__"
        sold = self.sell_currency or "__"
        timestamp = self.date.timestamp()
        return f"CT-{bought}-{decimal_to_str(self.buy)}-{sold}-{decimal_to_str(self.sell)}-{comment}-{timestamp}"

    def compute_transfers(self, ctx: ImportContext) -> list[Transfer]:
        match self.action():
            case TransactionAction.income.value:
                # Handles case where mining transaction is registered as 0 in cointracking
                if self.buy == Decimal(0):
                    return []

                transfer = Transfer(
                    asset_account=ctx.get_asset_account(self.buy_currency),
                    amount=self.buy,
                    from_account=ctx.income_acc,
                    to_account=ctx.wallet_acc,
                )
                return [transfer]

            case TransactionAction.loan.value:
                if self.buy:
                    asset_account = ctx.get_asset_account(self.buy_currency)
                    amount = self.buy
                    from_account = ctx.loan_acc
                    to_account = ctx.wallet_acc
                else:
                    asset_account = ctx.get_asset_account(self.sell_currency)
                    amount = self.sell
                    from_account = ctx.wallet_acc
                    to_account = ctx.loan_acc

                transfer = Transfer(
                    asset_account=asset_account,
                    amount=amount,
                    from_account=from_account,
                    to_account=to_account,
                )
                return [transfer]

            case TransactionAction.spend.value:
                transfer = Transfer(
                    asset_account=ctx.get_asset_account(self.sell_currency),
                    amount=self.sell,
                    from_account=ctx.wallet_acc,
                    to_account=ctx.spend_acc,
                )
                return [transfer]

            case TransactionAction.swap.value:
                # Need a way to identify the transaction as a NFT transaction
                buy_transfer = Transfer(
                    asset_account=ctx.get_asset_account(self.buy_currency),
                    amount=self.buy,
                    from_account=ctx.exchange_acc,
                    to_account=ctx.wallet_acc,
                )
                sell_transfer = Transfer(
                    asset_account=ctx.get_asset_account(self.sell_currency),
                    amount=self.sell,
                    from_account=ctx.wallet_acc,
                    to_account=ctx.exchange_acc,
                )
                return [buy_transfer, sell_transfer]

    def is_nft(self):
        return self.group == "NFT"

    def compute_transaction(self, ctx: ImportContext) -> Transaction | None:
        transfers = self.compute_transfers(ctx)
        if not transfers:
            return None

        return Transaction(
            action=self.action(),
            api_type=self.entry_type,
            comment=(
                f"{self.exchange if self.exchange else ''}"
                f"{' - ' if self.exchange and self.comment else ''}"
                f"{self.comment}"
            ),
            confirmed_at=self.date,
            json_data=self.json(),
            fees_paid=self.fee,
            network=ctx.network,
            signer_account=ctx.wallet_acc,
            tx_hash=self.tx_hash(),
            wallet=ctx.wallet,
            transfers=transfers,
            is_deposit=(
                True if self.action() == TransactionAction.loan.value and self.buy else False
            ),
        )


def is_csv_header(row: list[str]) -> bool:
    """Heuristic to determine if the row is a CSV header."""
    return row[0].lower() == "type" and row[1].lower() == "buy"


def read_csv(
    filelike: typing.Iterable[str], csv_header_check=is_csv_header
) -> tuple[list[str], list[list[str]]]:
    """Read CSV filelike and return a tuple of header and rows.

    If no header, header will be an empty list. If the file is empty or only
    has a header, rows will be an empty list.
    """
    reader = csv.reader(filelike)
    rows = list(reader)
    if len(rows) == 0:
        return [], []
    if csv_header_check(rows[0]):
        header, rows = rows[0], rows[1:]
    else:
        header = []
    return header, rows


def csv_rows_to_transactions(db: Session, ctx: ImportContext, rows: list[list[str]]):
    """Convert CSV rows to transactions.

    Returns a tuple of transactions and validation errors. Transactions will
    have a 1-to-1 mapping with the provided rows list. Validation errors are
    a list of tuples of transaction/row index and the error identifier.
    """
    transactions = []
    missing_coins = set()
    for row in rows:
        try:
            transaction = Entry.from_row(row).compute_transaction(ctx)
            if transaction is not None:
                transactions.append(transaction)
        except CoinNotFoundException as err:
            missing_coins.add(str(err))

    if missing_coins:
        raise CoinNotFoundException(
            f"Coin not found for ticker: {', '.join(str(err) for err in missing_coins)}"
        )

    tx_hashes = [tx.tx_hash for tx in transactions]

    in_file_duplicate_indexes = _validate_unique_in_file(tx_hashes)
    already_in_db_indexes = _validate_is_new(ctx, tx_hashes)
    validation_errors = [(i, "in-file-duplicate") for i in in_file_duplicate_indexes] + [
        (i, "already-in-db") for i in already_in_db_indexes
    ]

    return transactions, validation_errors


def merge_transactions_transfers(transaction_list: list[Transaction]) -> Transaction:
    """Extend the first transaction's transfers with transfers from other transactions.

    No other transactions fields besides transfers are merged. The provided list
    must not be empty. Returns the first transaction.
    """
    first_transaction = transaction_list[0]
    first_transaction.transfers.extend(
        transfer for transaction in transaction_list[1:] for transfer in transaction.transfers
    )
    return first_transaction


def _validate_is_new(ctx: ImportContext, tx_hashes: list[str]):
    """Return indexes of the transactions that already exist."""
    dupe_hashes = ctx.transaction_manager.filter_existing_tx_hashes(ctx.network.id, tx_hashes)
    dupe_hashes = set(dupe_hashes)
    return [i for i, tx_hash in enumerate(tx_hashes) if tx_hash in dupe_hashes]


def _validate_unique_in_file(tx_hashes: list[str]):
    """Return indexes of the transactions that are duplicated."""
    tx_hashes_count: dict[str:int] = {}
    for tx_hash in tx_hashes:
        tx_hashes_count[tx_hash] = tx_hashes_count.get(tx_hash, 0) + 1
    return [i for i, tx_hash in enumerate(tx_hashes) if tx_hashes_count[tx_hash] > 1]


def import_csv(db: Session, params: ImportJobParams, csv_text: str) -> ImportResult:
    header, rows = read_csv(csv_text.splitlines())

    resp = ImportResult(ok=False, imported_rows_count=0, header=header, failed_rows=[])

    user = UserManager(db).get_active_user_by_id(params.user_id)
    ctx = ImportContext.with_defaults(
        coin_manager=CoinManager(db),
        acc_manager=AccountManager(db),
        network_manager=NetworkManager(db),
        exchange_manager=ExchangeManager(db),
        lending_protocol_manager=LendingProtocolManager(db),
        wallet_manager=WalletManager(db),
        transaction_manager=TransactionManager(db),
        user=user,
    )

    transactions, validation_errors = csv_rows_to_transactions(db, ctx, rows)
    transactions_to_add = transactions

    # Don't add transactions that are already in the database, if desired.
    if params.skip_already_in_db:
        already_in_db = set(
            transactions[i] for i, err in validation_errors if err == "already-in-db"
        )
        transactions_to_add = [tx for tx in transactions_to_add if tx not in already_in_db]
        validation_errors = [(i, err) for i, err in validation_errors if err != "already-in-db"]

    # Merge transactions that are duplicated within the file, if desired.
    if params.merge_in_file_duplicates:
        transactions_to_add = sorted(transactions_to_add, key=lambda tx: tx.tx_hash)
        grouped_by_tx_hash = itertools.groupby(transactions_to_add, lambda tx: tx.tx_hash)
        grouped_by_tx_hash = [list(txs) for _, txs in grouped_by_tx_hash]
        transactions_to_add = [merge_transactions_transfers(txs) for txs in grouped_by_tx_hash]
        validation_errors = [
            (i, err) for i, err in validation_errors if err != "in-file-duplicate"
        ]

    resp.failed_rows = [
        ImportFailedRow(error=err, index=i, row=rows[i]) for i, err in validation_errors
    ]
    if len(resp.failed_rows) > 0:
        return resp

    merge_cointracking_transactions_with_stabletech_transactions(db, ctx, transactions_to_add)

    resp.ok = True
    resp.imported_rows_count = len(transactions_to_add)

    return resp


def process_import_job(db: Session, job: Job) -> str | ImportResult:
    if job.kind != "import_cointracking_csv":
        raise ValueError(f"Unexpected job kind: {job.kind}")

    params = ImportJobParams(**job.data)
    csv_text = job.data_text

    try:
        return import_csv(db, params, csv_text)
    except IntegrityError as err:
        db.rollback()
        if isinstance(err.orig, psycopg.errors.UniqueViolation):
            # A race condition might cause this to happen.
            return (
                "Transaction got duplicated after checks, this might happen when "
                f"importing multiple files in parallel: {err.orig.diag.message_detail}"
            )
        raise
    except CoinNotFoundException as err:
        return str(err)


def merge_ct_transactions(
    ct_transaction: Transaction, remaining_txs: list[Transaction]
) -> list[Transaction]:
    merged_tx_hashes = []
    for tx in remaining_txs:
        if (
            ct_transaction.confirmed_at == tx.confirmed_at
            and ct_transaction.action == tx.action
            and ct_transaction.action
            in [TransactionAction.income.value, TransactionAction.spend.value]
        ):
            merged_tx_hashes.append(tx.tx_hash)
            for transfer in tx.transfers:
                ct_transaction.transfers.append(transfer)
            ct_transaction.tx_hash = f"{ct_transaction.tx_hash}-{tx.tx_hash}"

    filtered_txs = [tx for tx in remaining_txs if tx.tx_hash not in merged_tx_hashes]
    if filtered_txs:
        return [ct_transaction] + merge_ct_transactions(filtered_txs[0], filtered_txs[1:])

    return [ct_transaction]


UTC = ZoneInfo("UTC")
PT = ZoneInfo("US/Pacific")


def is_match(tx: Transaction, ct_tx: Transaction) -> bool:
    if (
        (
            tx.confirmed_at.replace(microsecond=0, tzinfo=UTC)
            == ct_tx.confirmed_at.replace(microsecond=0, tzinfo=PT)
        )
        or (
            tx.confirmed_at.replace(microsecond=0, tzinfo=UTC)
            == ct_tx.confirmed_at.replace(microsecond=0, tzinfo=UTC)
        )
    ) and len(tx.transfers) >= len(ct_tx.transfers):
        for ct_transfer in ct_tx.transfers:
            for transfer in tx.transfers:
                tolerance = Decimal("0.000001") if transfer.amount < 1 else Decimal("0.01")
                if ct_transfer.amount.quantize(tolerance) == transfer.amount.quantize(
                    tolerance
                ):
                    return True
    return False


def get_ticker(transfer: Transfer) -> str:
    if transfer.asset_account.coin:
        if transfer.asset_account.coin.alias_coin:
            return transfer.asset_account.coin.alias_coin.coin.ticker.upper()
        return transfer.asset_account.coin.ticker.upper()
    return transfer.asset_account.ticker_symbol.upper()


def merge_cointracking_transactions_with_stabletech_transactions(
    db: Session,
    ctx: ImportContext,
    ct_transactions: list[Transaction],
):
    """Merge CoinTracking transactions with existing transactions."""
    earliest_transaction = min(
        ct_transactions, key=lambda tx: tx.confirmed_at
    ).confirmed_at.date() + timedelta(days=-1)
    oldest_transaction = max(
        ct_transactions, key=lambda tx: tx.confirmed_at
    ).confirmed_at.date() + timedelta(days=2)
    start_date = datetime(
        year=earliest_transaction.year,
        month=earliest_transaction.month,
        day=earliest_transaction.day,
    )
    end_date = datetime(
        year=oldest_transaction.year, month=oldest_transaction.month, day=oldest_transaction.day
    )

    transactions = ctx.transaction_manager.get_in_range(start_date, end_date)

    try:
        sys.setrecursionlimit(20000)
        merged_ct_transactions = merge_ct_transactions(ct_transactions[0], ct_transactions[1:])
    finally:
        sys.setrecursionlimit(1000)

    non_pre_existing_txs = []
    merged_txs = 0
    non_merged_txs = 0

    # Disable merging Transactions for now as it is too greedy. For example:
    # A ST tx was saved as 3 CT txs where 1 of them was a different amount than the ST tx. This caused an
    # the amount to get changed on the different amount tx and caused CT to not be the source of truth anymore.
    non_pre_existing_txs = merged_ct_transactions
    if False:
        for ct_transaction in merged_ct_transactions:
            matching_transaction = next(
                (tx for tx in transactions if is_match(tx, ct_transaction)),
                None,
            )

            if matching_transaction is not None:
                # Always set the timestmap to match Cointracking if match found, any BitcoinTax capital position
                # Generated from it will use Cointracking timestamp.
                matching_transaction.confirmed_at = ct_transaction.confirmed_at
                if matching_transaction.action is None:
                    merged_txs += 1
                    matching_transaction.action = ct_transaction.action
                    # Updated confirmed_at so it is in line with CT and a possible BT Capital position
                    # We have a few ST transactions that are off by a few hours in line with the CT transactions
                    if (
                        matching_transaction.comment is None
                        and ct_transaction.comment is not None
                    ):
                        matching_transaction.comment = ct_transaction.comment
                    if ct_transaction.action is TransactionAction.loan.value:
                        if matching_transaction.is_deposit is None:
                            matching_transaction.is_deposit = ct_transaction.is_deposit
                    for transfer in matching_transaction.transfers:
                        if transfer.asset_account.coin is None:
                            ct_transfer = next(
                                (
                                    ct_transfer
                                    for ct_transfer in ct_transaction.transfers
                                    if ct_transfer.amount == transfer.amount
                                    and ct_transfer.is_deposit == transfer.is_deposit
                                ),
                                None,
                            )
                            if ct_transfer is not None:
                                transfer.asset_account.coin = ct_transfer.asset_account.coin
                                continue
                            if transfer.asset_account.ticker_symbol is not None:
                                asset_account = (
                                    ctx.acc_manager.get_asset_account_by_network_id_coin_ticker(
                                        matching_transaction.network_id,
                                        transfer.asset_account.ticker_symbol,
                                    )
                                )
                                if asset_account is not None:
                                    transfer.asset_account = asset_account
                            else:
                                coin = ctx.coin_manager.get_or_create_by_ticker(
                                    transfer.asset_account.ticker_symbol
                                )
                                transfer.asset_account.coin = coin

                else:
                    non_merged_txs += 1
            else:
                non_pre_existing_txs.append(ct_transaction)

    transactions_to_transfers = {tx: tx.transfers for tx in non_pre_existing_txs}
    ctx.transaction_manager.add_with_transfers(
        None,
        transactions_to_transfers,
        skip_existence_check=True,
        commit=False,
    )
    db.commit()

    logger.info(
        f"Imported CT transactions: {len(ct_transactions)}, Merged: {len(merged_ct_transactions)} Stabletech Added: {len(non_pre_existing_txs)}, Merged: {merged_txs}, Not-Merged: {non_merged_txs}",
    )
