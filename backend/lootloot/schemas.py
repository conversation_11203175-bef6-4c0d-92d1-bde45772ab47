from datetime import datetime
from decimal import Decimal
from typing import Annotated, Generic, Literal, TypeVar

import pydantic
from pydantic import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, StringConstraints

from lootloot.kinds import AuctionHouse, NetworkGroup, WalletOrigin


class BaseSchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(from_attributes=True)


# --------------------------------------------
# Pagination generics
# --------------------------------------------

T = TypeVar("T")


class PaginatedResp(BaseSchema, Generic[T]):
    next: bool
    prev: bool
    items: list[T]


class NonPaginatedResp(BaseSchema, Generic[T]):
    items: list[T]


# --------------------------------------------
# User
# --------------------------------------------


class User(BaseSchema):
    email: str | None
    first_name: str | None
    last_name: str | None
    picture_url: str | None
    display_name: str | None
    matrica_username: str | None
    is_admin: bool


class ListUsersResp(NonPaginatedResp[User]):
    pass


# --------------------------------------------
# Auth
# --------------------------------------------


class LoginResponse(BaseSchema):
    token: str
    user: User


class MatricaLogin(BaseSchema):
    code: str
    redirect_uri: str
    code_verifier: str


class PhantomLogin(BaseSchema):
    message: str
    signature: str
    address: str


# --------------------------------------------
# Profile
# --------------------------------------------


class UpdateUserInfo(BaseSchema):
    first_name: str | None = Field(None, max_length=50)
    last_name: str | None = Field(None, max_length=50)
    display_name: str | None = Field(None, max_length=30)


# --------------------------------------------
# Wallet
# --------------------------------------------


class UserWallet(BaseSchema):
    name: str
    address: str
    network_group: NetworkGroup


class ListUserWalletsResp(NonPaginatedResp[UserWallet]):
    pass


class AddUserWallet(BaseSchema):
    name: str
    origin: WalletOrigin
    address: str
    network_group: NetworkGroup


class AddPhantomWallet(BaseSchema):
    name: str
    address: str
    message: str
    signature: str


class WalletAuthMessage(BaseSchema):
    message: str


# --------------------------------------------
# NFT
# --------------------------------------------


class AddNFT(BaseSchema):
    address_uri: str
    nft_collection_uid: str


class AddNFTListing(BaseSchema):
    auction_house: AuctionHouse
    nft_address_uri: str
    sol_price: Decimal
    at: datetime | None = None
    nft_collection_uid: str


class PublicNFTCollectionListItem(BaseSchema):
    name: str
    uid: str
    nft_count: int


class PublicListNFTCollectionsResp(NonPaginatedResp[PublicNFTCollectionListItem]):
    pass


class NFTCollectionListItem(BaseSchema):
    uid: str
    name: str
    nft_count: int
    image_url: str


class ListNFTCollectionsResp(NonPaginatedResp[NFTCollectionListItem]):
    pass


class NFTAttribute(BaseSchema):
    trait_type: str = Field(validation_alias=AliasChoices("trait_type", "traitType"))
    value: str


class NFTListItem(BaseSchema):
    address_uri: str
    uid: str
    image_url: str
    is_burnt: bool
    is_frozen: bool
    attributes_data: list[NFTAttribute]


class ListNFTResp(NonPaginatedResp[NFTListItem]):
    pass


# --------------------------------------------
# Holders
# --------------------------------------------


class HolderItem(BaseSchema):
    rank: int
    kind: Literal["user", "address"]
    value: str
    nft_count: int
    sol_amount: Decimal
    own: bool


class HolderListResp(NonPaginatedResp[HolderItem]):
    sol_usd_price: Decimal


# --------------------------------------------
# Heist
# --------------------------------------------


class HeistPrices(BaseSchema):
    sol: float | None
    nana: float | None


class HeistStats(BaseSchema):
    claimed: Decimal
    fumbled: Decimal
    completed: int
    rekt: int
    lost_to_ambush: Decimal
    gained_from_ambush: Decimal


class HeistStatsPublic(BaseSchema):
    claimed: Decimal
    fumbled: Decimal
    completed: int
    rekt: int


class HeistWardrobe(BaseSchema):
    species: str

    background: list[str]
    clothing: list[str]
    fur: list[str]
    head: list[str]
    mouth: list[str]


class NanaInDay(BaseSchema):
    at: datetime
    lost: Decimal
    gained: Decimal


class NanaByDayResp(NonPaginatedResp[NanaInDay]):
    pass


TopClaimsSortBy = Literal[
    "total_amount_claimed", "duration_hours", "base_multiplier", "ended_at"
]
TopClaimsSortOrder = Literal["asc", "desc"]
HeistSpecies = Literal["Chimp", "Orangutan", "Gorilla"]


class TopClaimsFilters(BaseSchema):
    species: list[HeistSpecies] | None = None
    event_id: list[int] | None = None
    location_id: list[int] | None = None


class TopClaimsSort(BaseSchema):
    by: TopClaimsSortBy = "total_amount_claimed"
    order: TopClaimsSortOrder = "desc"


class TopClaimItem(BaseSchema):
    nft_image_url: str | None
    total_amount_claimed: Decimal
    duration_hours: float
    total_amount_emitted: Decimal
    base_multiplier: float
    ended_at: datetime
    address: str
    wallet_kind: Literal["user", "address"]
    wallet_value: str | None = None
    location_name: str
    event_id: int
    is_ambush: bool = False


class TopClaimsResp(NonPaginatedResp[TopClaimItem]):
    pass


class NFTClaimHistoryItem(BaseSchema):
    total_amount_claimed: Decimal
    duration_hours: float
    total_amount_emitted: Decimal
    base_multiplier: float
    ended_at: datetime
    event_id: int
    location_name: str


class NFTClaimsHistoryResp(BaseSchema):
    nft_name: str
    nft_image_url: str
    claims: list[NFTClaimHistoryItem]


# --------------------------------------------
# Heist Bots
# --------------------------------------------


class AddRaffle(BaseSchema):
    entries_count: int
    secret_word: Annotated[str, StringConstraints(to_lower=True)]
    winners_count: int


class RaffleUserDepositCount(BaseSchema):
    count: int
    display_name: str
    rank: int


class RaffleResp(BaseSchema):
    pending_prizes_count: int
    user_deposits: list[RaffleUserDepositCount]
    withdrawals_count: int
