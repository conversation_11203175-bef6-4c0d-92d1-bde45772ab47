// ** React Imports
import { ReactElement, useEffect } from "react";

// ** Next Imports
import { useRouter } from "next/router";

// ** Hooks Import
import { useAuth } from "@/hooks/useAuth";

import { newPagesEnabled } from "@/context/env";

interface AuthGuardProps {
  children: React.JSX.Element;
  fallback: ReactElement | null;
}

const AuthGuard = (props: AuthGuardProps) => {
  const { children, fallback } = props;
  const auth = useAuth();
  const router = useRouter();

  const { replace: replaceRoute, pathname, isReady } = router;
  useEffect(() => {
    if (!isReady || auth.loading || auth.user != null) {
      return;
    }

    if (pathname !== "/signin" && pathname !== "/login") {
      replaceRoute({
        pathname: newPagesEnabled ? "/signin" : "/login",
        query: router.asPath !== "/" ? { returnUrl: router.asPath } : undefined,
      });
    }
  }, [auth.loading, auth.user, isReady, pathname, replaceRoute, router.asPath]);

  if (auth.loading || auth.user == null) {
    return fallback;
  }

  return children;
};

export default AuthGuard;
