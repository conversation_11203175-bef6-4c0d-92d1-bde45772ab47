import functools
import time
from typing import Callable, Literal, NamedTuple, NotRequired, ParamSpec, TypedDict, TypeVar

import httpx


class ListedCoin(TypedDict):
    id: str
    symbol: str
    name: str
    market_cap: float | None


class SimplePrice(TypedDict):
    usd: float
    last_updated_at: int
    usd_24h_change: float | None


class HistoricMarketData(TypedDict):
    current_price: dict[str, float]
    market_cap: dict[str, float]
    total_volume: dict[str, float]


class CoinHistory(TypedDict):
    """Response from CoinGecko's coin history API (non-exhaustive).

    See https://www.coingecko.com/api/documentations/v3#/coins/get_coins__id__history
    """

    id: str
    symbol: str
    name: str
    market_data: NotRequired[HistoricMarketData]


class MarketChartData(TypedDict):
    """Response from CoinGecko's market chart API.

    See https://docs.coingecko.com/reference/coins-id-market-chart
    """

    prices: list[list[float]]  # [[timestamp, price], ...]
    market_caps: list[list[float]]  # [[timestamp, market_cap], ...]
    total_volumes: list[list[float]]  # [[timestamp, volume_24hr], ...]


class _UnavailableState(NamedTuple):
    code: Literal[429, 503]
    until: float


T = TypeVar("T")
P = ParamSpec("P")


def _handle_common_http_errors(func: Callable[P, T]) -> Callable[P, T]:
    @functools.wraps(func)
    async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        self: "CoingeckoClient" = args[0]

        if self._unavailable:
            if time.time() < self._unavailable.until:
                raise CoingeckoUnavailableError(*self._unavailable)
            self._unavailable = None

        try:
            return await func(*args, **kwargs)
        except CoingeckoError as e:
            code = e.response.status_code
            if code not in (429, 503):
                raise
            retry_after = _get_retry_after(e.response.headers)
            until = time.time() + retry_after
            self._unavailable = _UnavailableState(code, until)
            raise CoingeckoUnavailableError(code, until) from e

    return wrapper


class CoingeckoClient:
    _client: httpx.AsyncClient
    _unavailable: _UnavailableState | None = None

    def __init__(self, coingecko_api_key: str = "", timeout: float = 10) -> None:
        if coingecko_api_key:
            self._client = httpx.AsyncClient(
                base_url="https://pro-api.coingecko.com/api/v3/",
                headers=[("x-cg-pro-api-key", coingecko_api_key)],
                timeout=timeout,
            )
        else:
            self._client = httpx.AsyncClient(
                base_url="https://api.coingecko.com/api/v3/",
                timeout=timeout,
            )

    @_handle_common_http_errors
    async def get_simple_price(self, ids: list[str]) -> dict[str, SimplePrice]:
        resp = await self._client.get(
            "simple/price",
            params={
                "ids": ",".join(ids),
                "vs_currencies": "usd",
                "include_last_updated_at": "true",
                "include_24hr_change": "true",
                "precision": 18,
            },
        )
        if resp.status_code != 200:
            raise CoingeckoError(
                f"Failed to fetch coins price: {resp.text}",
                request=resp.request,
                response=resp,
            )
        data: dict[str, dict[str]] = resp.json()
        # If missing usd price, remove it to consider it as not found
        data = {cid: price for cid, price in data.items() if "usd" in price}
        return data

    @_handle_common_http_errors
    async def get_coins_list(self) -> list[ListedCoin]:
        coins_by_id: dict[str, ListedCoin] = {}
        page = 1

        while True:
            resp = await self._client.get(
                "coins/markets",
                params={
                    "vs_currency": "usd",
                    "order": "market_cap_desc",
                    "per_page": 250,
                    "page": page,
                    "sparkline": False,
                },
            )
            if resp.status_code != 200:
                raise CoingeckoError(
                    f"Failed to fetch coins list: {resp.text}",
                    request=resp.request,
                    response=resp,
                )

            data = resp.json()
            if not data:  # No more coins to fetch
                break

            for coin in data:
                coin_id = coin["id"]
                # Only add if not seen before (keep first occurrence which has highest market cap)
                if coin_id not in coins_by_id:
                    coins_by_id[coin_id] = coin
            page += 1

        return list(coins_by_id.values())

    @_handle_common_http_errors
    async def get_coin_history(self, coin_id: str, date: str) -> CoinHistory | None:
        resp = await self._client.get(
            f"coins/{coin_id}/history", params={"date": date, "localization": "false"}
        )
        if resp.status_code == 404:
            return None
        if resp.status_code != 200:
            raise CoingeckoError(
                f"Failed to fetch coin history: {resp.text}",
                request=resp.request,
                response=resp,
            )
        return resp.json()

    @_handle_common_http_errors
    async def get_market_chart(self, coin_id: str, days: int) -> MarketChartData | None:
        """Fetch historical market chart data for a coin.

        Args:
            coin_id: The CoinGecko ID of the coin
            days: Number of days of data to fetch (1, 7, 14, 30, 90, 180, 365, max)

        Returns:
            Market chart data with prices, market caps, and volumes, or None if not found
        """
        params = {
            "vs_currency": "usd",
            "days": str(days),
        }

        resp = await self._client.get(f"coins/{coin_id}/market_chart", params=params)
        if resp.status_code == 404:
            return None
        if resp.status_code != 200:
            raise CoingeckoError(
                f"Failed to fetch market chart: {resp.text}",
                request=resp.request,
                response=resp,
            )
        return resp.json()

    @_handle_common_http_errors
    async def get_market_chart_range(
        self, coin_id: str, from_timestamp: int, to_timestamp: int
    ) -> MarketChartData | None:
        """Fetch historical market chart data for a coin within a specific time range.

        Args:
            coin_id: The CoinGecko ID of the coin
            from_timestamp: Start timestamp in Unix seconds
            to_timestamp: End timestamp in Unix seconds

        Returns:
            Market chart data with prices, market caps, and volumes, or None if not found
        """
        resp = await self._client.get(
            f"coins/{coin_id}/market_chart/range",
            params={
                "vs_currency": "usd",
                "from": str(from_timestamp),
                "to": str(to_timestamp),
            },
        )
        if resp.status_code == 404:
            return None
        if resp.status_code != 200:
            raise CoingeckoError(
                f"Failed to fetch market chart range: {resp.text}",
                request=resp.request,
                response=resp,
            )
        return resp.json()


class CoingeckoBaseError(Exception):
    pass


class CoingeckoError(CoingeckoBaseError, httpx.HTTPStatusError):
    pass


class CoingeckoUnavailableError(CoingeckoBaseError):
    def __init__(self, code: Literal[429, 503], until: float) -> None:
        super().__init__(f"Unavailable with code {code} until {until:.3f}")
        self.code = code
        self.until = until


def _get_retry_after(headers: httpx.Headers) -> float:
    retry_after = headers.get("Retry-After", "")
    try:
        return max(1, float(retry_after))
    except ValueError:
        return 60
