import { UserSearch } from "@/components/autocomplete/UserSearch";
import Wallets from "@/pages/client/wallets";
import { useIndexerWalletsQuery } from "@/store/services/api";
import { User } from "@/types/schemas";
import Grid from "@mui/material/Grid";
import { useState } from "react";

export const AdminWallets = () => {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const { data: walletsData } = useIndexerWalletsQuery([null, selectedUser || null]);

  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <UserSearch fullWidth selectedUser={selectedUser} setSelectedUser={setSelectedUser} />
      </Grid>
      <Grid item xs={12}>
        <Wallets isAdmin selectedUser={selectedUser} walletsData={walletsData} />
      </Grid>
    </Grid>
  );
};

AdminWallets.acl = {
  action: "manage",
  subject: "Wallet",
};

export default AdminWallets;
