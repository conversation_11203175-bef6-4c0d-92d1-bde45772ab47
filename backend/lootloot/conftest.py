import os

# Set before any imports to prevent SECRET_BOX initialization error
os.environ["SECRET_BOX_HEXED_KEY"] = (
    "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"
)

import json
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import JSON, LargeBinary, create_engine, event
from sqlalchemy.dialects.postgresql import ARRAY, BYTEA, JSONB
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.types import TypeDecorator

from common.psql import get_db
from lootloot.api import app
from lootloot.auth.models import AuthToken
from lootloot.auth.models import Base as AuthBase
from lootloot.auth.models import User
from lootloot.models import Base as LootlootBase

# Mock CubistManagementClient before importing app
mock_client = MagicMock()
mock_client_instance = AsyncMock()
mock_client.create_default = AsyncMock(return_value=mock_client_instance)


class SQLiteJSONB(TypeDecorator):
    """Represents a JSONB column for SQLite testing."""

    impl = JSON
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == "sqlite":
            return dialect.type_descriptor(JSON())
        return dialect.type_descriptor(self.impl)


class SQLiteBYTEA(TypeDecorator):
    """Represents a BYTEA column for SQLite testing."""

    impl = LargeBinary
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == "sqlite":
            return dialect.type_descriptor(LargeBinary())
        return dialect.type_descriptor(self.impl)


class SQLiteARRAY(TypeDecorator):
    """Represents an ARRAY column for SQLite testing."""

    impl = JSON
    cache_ok = True

    def __init__(self, item_type):
        super().__init__()
        self.item_type = item_type

    def load_dialect_impl(self, dialect):
        if dialect.name == "sqlite":
            return dialect.type_descriptor(JSON())
        return dialect.type_descriptor(self.impl)

    def process_bind_param(self, value, dialect):
        if value is None:
            return None
        return json.dumps(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return None
        return json.loads(value)


# Register type adapters for SQLite


@event.listens_for(LootlootBase.metadata, "before_create")
@event.listens_for(AuthBase.metadata, "before_create")
def _replace_auth_types(target, connection, **kw):
    """Replace PostgreSQL-specific types with SQLite-compatible ones"""
    for table in target.tables.values():
        for column in table.columns:
            if isinstance(column.type, JSONB):
                column.type = SQLiteJSONB()
            elif isinstance(column.type, BYTEA):
                column.type = SQLiteBYTEA()
            elif isinstance(column.type, ARRAY):
                column.type = SQLiteARRAY(column.type.item_type)


@pytest.fixture(scope="session")
def session():
    """Create a test session factory with all tables."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    for metadata in (LootlootBase.metadata, AuthBase.metadata):
        metadata.create_all(bind=engine)
    TestSessionLocal = sessionmaker(bind=engine, expire_on_commit=False)
    yield TestSessionLocal()
    engine.dispose()


@pytest.fixture(scope="session")
def client(session):
    """Create a test client with authentication and database overrides."""

    def get_test_db():
        """Get test database session with transaction management."""
        try:
            yield session
            session.commit()
        finally:
            session.close()

    app.dependency_overrides[get_db] = get_test_db
    test_email = "<EMAIL>"
    test_user = session.query(User).filter_by(email=test_email).first()
    if not test_user:
        user = User(
            email=test_email,  # This will make the user an admin
            origin="test",
            display_name="Test User",
        )
        session.add(user)
        session.commit()  # Commit to ensure token is available
        session.refresh(user)
        token = AuthToken.create(user=user)
        session.add(token)
        session.commit()  # Commit to ensure token is available
        session.refresh(token)
    with TestClient(app, headers={"Authorization": f"Bearer {token.id}"}) as test_client:
        yield test_client
