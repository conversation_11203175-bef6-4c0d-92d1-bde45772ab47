"""Interest rate per user

Revision ID: 0064
Revises: 0063
Create Date: 2025-04-03 16:26:12.802569

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0064"
down_revision = "0063"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user",
        sa.Column(
            "interest_rate",
            sa.Numeric(precision=4, scale=4),
            nullable=True,
        ),
    )
    op.execute(sa.text('UPDATE "user" SET interest_rate = 0.08 WHERE interest_rate IS NULL'))
    op.alter_column("user", "interest_rate", nullable=False, server_default="0.07")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "interest_rate")
    # ### end Alembic commands ###
