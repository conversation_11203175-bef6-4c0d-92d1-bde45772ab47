import itertools
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Generic, Sequence, TypeVar

from dateutil.relativedelta import relativedelta
from sqlalchemy import (
    BindParameter,
    ColumnElement,
    Date,
    Integer,
    Result,
    String,
    Time,
    bindparam,
    case,
    cast,
    column,
    delete,
    exists,
    func,
    insert,
    or_,
    select,
    text,
    tuple_,
    values,
)
from sqlalchemy.dialects.postgresql import insert as pg_insert
from sqlalchemy.exc import IntegrityError, NoResultFound
from sqlalchemy.orm import aliased
from sqlalchemy.sql.selectable import ScalarSelect, Select

from common.conf import settings
from common.exceptions import HTTP400Exception, HTTPNotFoundException, HTTPUnauthorizedException
from stabletech import schemas
from stabletech.accounting.models import AccountedTransaction, CapitalPosition
from stabletech.client.crud import ClientTransactionManager
from stabletech.client.models import ClientTransaction
from stabletech.indexer.enums import TransactionAction
from stabletech.indexer.internal_sync.client import INTERNAL_SYNC_ACTIONS, is_subsidiary
from stabletech.indexer.internal_sync.schemas import InternalTransaction
from stabletech.indexer.internal_sync.sync_to_main import sync_transaction_to_main
from stabletech.indexer.models import (
    NFT,
    Account,
    AliasCoin,
    Bridge,
    Coin,
    CovalentSync,
    Exchange,
    Farm,
    LendingProtocol,
    Network,
    PoolPriceAlert,
    Transaction,
    Transfer,
    Wallet,
)
from stabletech.mail.handlers import classified_client_transaction_emails
from stabletech.market.crud import AliasCoinManager, CoinManager
from stabletech.utils import eager_load
from stabletech.utils.models import BaseManager

logger = logging.getLogger(__name__)
T = TypeVar("T")


@dataclass
class ListResult(Generic[T]):
    items: list[T]
    next: bool = False
    prev: bool = False


class AccountManager(BaseManager[Account]):
    model = Account
    order_by = "Account.name"
    FromAccount = aliased(Account)
    ToAccount = aliased(Account)

    def get_by_address(self, address: str, network: Network) -> Account:
        return self.get_by_address_network_id(address, network.id)

    def get_by_address_network_id(self, address: str, network_id: int) -> Account:
        return (
            self.db.query(Account)
            .filter(
                func.lower(Account.address) == address.casefold(),
                Account.network_id == network_id,
            )
            .one()
        )

    def get_by_wallet(self, wallet: Wallet, network: Network) -> Account:
        return (
            self.db.query(Account)
            .filter(Account.network_id == network.id, Account.wallet_id == wallet.id)
            .one()
        )

    def get_asset_account_by_network_id_coin_ticker(
        self, network_id: int, coin_ticker: str
    ) -> Account | None:
        return (
            self.db.query(Account)
            .filter(
                Account.network_id == network_id,
                Account.coin.has(Coin.ticker == coin_ticker.lower()),
            )
            .limit(1)
            .one_or_none()
        )

    def get_or_create(
        self, address, network, api_name="", ticker_symbol="", *, commit=True
    ) -> Account:
        return self.get_or_create_with_network_id(
            address, network.id, api_name=api_name, ticker_symbol=ticker_symbol, commit=commit
        )

    def get_or_create_with_network_id(
        self,
        address: str,
        network_id=0,
        api_name="",
        coin_id: int | None = None,
        commit: bool = True,
        ticker_symbol="",
        *,
        network: Network | None = None,
        is_pool_token: bool = False,
        **kwargs,
    ) -> Account:
        if network is None and network_id == 0 or network is not None and network_id != 0:
            raise ValueError("Either network or network_id must be provided")

        if Account.address_is_burn(address):
            api_name = ticker_symbol = ""

        if network is None:
            network = NetworkManager(self.db).get_by_id(network_id)

        wallet = kwargs.pop("wallet", None)
        if wallet is None:
            # Check if existing Wallet has the same address
            wallet = WalletManager(self.db).get_active_by_network_address(network, address)
        elif not isinstance(wallet, Wallet):
            raise TypeError("wallet must be a Wallet instance")

        try:
            account = self.get_by_address_network_id(address, network.id)
        except NoResultFound:
            try:
                account = self.add_model(
                    Account(
                        address=address,
                        network=network,
                        api_name=api_name,
                        coin_id=coin_id,
                        is_pool_token=is_pool_token,
                        ticker_symbol=ticker_symbol,
                        wallet=wallet,
                        **kwargs,
                    ),
                    commit=False,
                )
            except IntegrityError:
                # Account was created by another process in the meantime
                account = self.get_by_address_network_id(address, network.id)
            else:
                if commit:
                    self.db.commit()
                return account

        should_commit = False
        if api_name and not account.wallet_id:
            account.api_name = api_name
            should_commit = True
        if is_pool_token and not account.is_pool_token:
            account.is_pool_token = is_pool_token
            should_commit = True
        if ticker_symbol and account.ticker_symbol != ticker_symbol:
            account.ticker_symbol = ticker_symbol
            should_commit = True
        if account.wallet is None and wallet:
            account.wallet = wallet
            should_commit = True
        if commit and should_commit:
            self.db.commit()
            self.db.refresh(account)
        return account

    def get_pools(self) -> list[schemas.AccountPoolFull]:
        accounts = transactions_to_account_pools(
            self.db.query(Transaction).filter_by(action="pool").all()
        )
        return list(accounts.values())

    def get_pool(self, account_id: int) -> schemas.AccountPoolFull | None:
        transactions = (
            self.db.query(Transaction)
            .filter(
                Transaction.action == "pool",
                Transaction.id == Transfer.transaction_id,
                or_(
                    Transfer.from_account_id == account_id, Transfer.to_account_id == account_id
                ),
            )
            .all()
        )
        accounts = transactions_to_account_pools(transactions)

        if len(accounts) == 0:
            return None
        assert len(accounts) == 1, "Multiple pools found for an account_id"

        pool = accounts[account_id]
        return pool

    exists_lending_protocol_transfer = (
        select(1)
        .select_from(Transfer)
        .join(FromAccount, FromAccount.id == Transfer.from_account_id)
        .join(ToAccount, ToAccount.id == Transfer.to_account_id)
        .where(
            Transfer.transaction_id == Transaction.id,
            or_(FromAccount.lending_protocol != None, ToAccount.lending_protocol != None),
        )
        .exists()
    )

    subquery_transaction_transfers_count = (
        select(func.count()).where(Transfer.transaction_id == Transaction.id).scalar_subquery()
    )

    # Will return all loans separated by receiver (to_account) address, if test loans and all have the
    # same `loan` address then they will all be aggregated under the same loan.
    def get_loans_transactions(
        self, *, account: Account | None = None, p2p=False
    ) -> list[Transaction]:
        assert account is None or not p2p, "cannot filter p2p loans with an account"

        statement = (
            select(Transaction).where(Transaction.action == TransactionAction.loan.value)
            # Order by confirmed_at to traverse the Transactions in chronological order when calculating totals
            .order_by(Transaction.confirmed_at)
        )

        if p2p:
            statement = statement.where(
                # currently only support single transfer p2p loans transactions
                self.subquery_transaction_transfers_count == 1,
                # exclude loans made with lending protocols
                ~self.exists_lending_protocol_transfer,
            )
        elif account:
            # all loans from the same Network and LendingProtocol as the Account
            network_id = account.network_id
            lending_protocol_id = account.lending_protocol_id
            assert lending_protocol_id, "Account is not a loan account"
            statement = statement.where(
                self.exists_lending_protocol_transfer.where(
                    Transaction.network_id == network_id,
                    or_(
                        self.FromAccount.lending_protocol_id == lending_protocol_id,
                        self.ToAccount.lending_protocol_id == lending_protocol_id,
                    ),
                )
            )
        else:
            # all loans made with lending protocols
            statement = statement.where(self.exists_lending_protocol_transfer)

        return self.db.scalars(statement).unique().all()

    def get_loans(self, *, account: Account = None, p2p=False) -> list[schemas.AccountLoanFull]:
        transactions = self.get_loans_transactions(account=account, p2p=p2p)
        return transactions_to_account_loans(transactions, p2p=p2p)

    def get_loan(self, account: Account, *, p2p=False) -> schemas.AccountLoanFull | None:
        accounts = self.get_loans(account=account, p2p=p2p)
        if len(accounts) == 0:
            return None
        assert len(accounts) == 1, "Found multiple lending protocols on the same network"

        loan = accounts.pop()
        return loan

    def is_exchange(self, account_id: int) -> bool:
        result = self.db.query(Account.exchange_id).filter_by(id=account_id).first()
        if result is None:
            # Account does not exist, so it is not an exchange
            return False
        (exchange_id,) = result
        return exchange_id is not None


@dataclass
class AccountPoolDraft:
    name: str
    tickers: list[str]
    exchange: Exchange
    network: Network
    transactions: list[Transaction]


def transactions_to_account_pools(
    transactions: list[Transaction],
) -> dict[int, schemas.AccountPoolFull]:
    """Returns a dict of pools keyed by pool account_id.

    The account pool data is derived only from the transactions provided.
    """
    drafts_dict: dict[int, AccountPoolDraft] = {}
    for transaction in transactions:
        transfer0, transfer1 = get_pool_transfers(transaction)
        if transfer0.from_account.is_wallet:
            assert transfer0.to_account == transfer1.to_account
            pool_acc = transfer0.to_account
        else:
            assert transfer0.from_account == transfer1.from_account
            pool_acc = transfer0.from_account

        account_id: int = pool_acc.id
        draft = drafts_dict.get(account_id)
        if draft is None:
            name, tickers = get_pool_name_and_tickers(transfer0, transfer1)
            drafts_dict[account_id] = AccountPoolDraft(
                name=name,
                tickers=tickers,
                exchange=pool_acc.exchange,
                network=pool_acc.network,
                transactions=[transaction],
            )
        else:
            draft.transactions.append(transaction)

    accounts: dict[int, schemas.AccountPoolFull] = {}
    for account_id, draft in drafts_dict.items():
        pool_stats = calculate_pool_stats(draft.tickers, draft.transactions)
        state = "no-balance"
        if all(coin_total.amount_total > 0 for coin_total in pool_stats.coin_totals):
            state = "has-funds"
        elif all(coin_total.amount_total < 0 for coin_total in pool_stats.coin_totals):
            state = "missing-transactions"
        accounts[account_id] = schemas.AccountPoolFull(
            id=account_id,
            name=draft.name,
            exchange=draft.exchange,
            network=draft.network,
            impermanent_loss_percent=pool_stats.impermanent_loss_percent,
            state=state,
            tvl=pool_stats.tvl,
            coin_totals=pool_stats.coin_totals,
            k_ratio=pool_stats.k_ratio,
            tickers=draft.tickers,
            transactions=draft.transactions,
        )

    return accounts


def get_pool_transfers(transaction: Transaction) -> list[Transfer]:
    """Return the transfers involving the pool assets.

    This assumes the `transaction` is a pool transaction. It should filter out,
    for example, the LP token transfer if any.
    """
    if transaction.is_deposit:
        # Transaction is depositing into the pool.
        # Take only the transfers that are withdrawing from wallets.
        return [t for t in transaction.transfers if t.is_withdrawal]
    # Transaction is withdrawing from the pool.
    # Take only the transfers that are depositing to wallets.
    return [t for t in transaction.transfers if t.is_deposit]


def get_pool_name_and_tickers(
    transfer0: Transfer, transfer1: Transfer
) -> tuple[str, list[str]]:
    tickers = sorted(
        [transfer0.asset_account.alias_coin.ticker, transfer1.asset_account.alias_coin.ticker]
    )
    name = "-".join(tickers).upper()
    return name, tickers


@dataclass
class PoolStats:
    impermanent_loss_percent: Decimal | None
    tvl: Decimal
    k_ratio: Decimal
    coin_totals: list[schemas.PoolCoinTotal]


def calculate_pool_stats(tickers: list[str], pool_txs: list[Transaction]) -> PoolStats:
    coin_totals, total_k_ratio = calculate_pool_totals(tickers, pool_txs)
    tvl = Decimal(0)

    impermanent_loss_percent = None
    if len(coin_totals) == 2:
        # Calculate IL exposure for dual asset pools
        first_coin_total, second_coin_total = coin_totals
        impermanent_loss_percent = calculate_dual_pool_il(
            total_k_ratio, first_coin_total, second_coin_total
        )
        tvl = (
            first_coin_total.amount_current * first_coin_total.dollar_price
            + second_coin_total.amount_current * second_coin_total.dollar_price
        )

    return PoolStats(
        impermanent_loss_percent=impermanent_loss_percent,
        tvl=tvl,
        coin_totals=coin_totals,
        k_ratio=total_k_ratio,
    )


def calculate_pool_totals(
    tickers: list[str], pool_txs: list[Transaction]
) -> tuple[list[schemas.PoolCoinTotal], Decimal]:
    """Calculate coin totals, total k ratio, and total value locked (TVL) for a pool."""
    coin_totals: dict[Coin, schemas.PoolCoinTotal] = {}
    total_k_ratio = Decimal(0)

    for transaction in pool_txs:
        k_ratio: Decimal | None = None
        transfer: Transfer
        for transfer in transaction.transfers:
            if (
                not transfer.asset_account.alias_coin
                or transfer.asset_account.alias_coin.ticker not in tickers
            ):
                # Ignore transfers that are not pool assets (e.g. LP tokens)
                continue
            # Should we be using the alias coin or the actual coin?
            coin: Coin = transfer.asset_account.alias_coin
            coin_total = coin_totals.get(coin)
            if not coin_total:
                dollar_price = coin.latest_usd_price
                coin_total = schemas.PoolCoinTotal.with_defaults(
                    coin=coin, dollar_price=dollar_price
                )
                coin_totals[coin] = coin_total
            amount = transfer.amount
            if not transaction.is_deposit:
                # i.e. removing from pool
                amount = amount.copy_negate()
            coin_total.amount_total += amount
            if k_ratio is None:
                k_ratio = amount
            else:
                k_ratio *= amount
            if amount < 0 < k_ratio:
                k_ratio = k_ratio.copy_negate()
            if amount > 0:
                coin_total.amount_added += amount

        if k_ratio is not None:
            if k_ratio >= 0:
                total_k_ratio += k_ratio.sqrt()
            else:
                total_k_ratio -= k_ratio.copy_negate().sqrt()

    if total_k_ratio < Decimal("0.000000001"):
        total_k_ratio = Decimal(0)

    return list(coin_totals.values()), total_k_ratio


def calculate_dual_pool_il(
    total_k_ratio: Decimal,
    first_coin_total: schemas.PoolCoinTotal,
    second_coin_total: schemas.PoolCoinTotal,
) -> Decimal | None:
    """Calculate impermanent loss for dual asset pool."""
    impermanent_loss_percent = None
    first_coin_latest_price = first_coin_total.dollar_price
    second_coin_latest_price = second_coin_total.dollar_price

    # If possible, calculate impermanent loss -- checks avoid division by zero
    if (first_coin_latest_price and second_coin_latest_price) and (
        first_coin_total.amount_added or second_coin_total.amount_added
    ):
        first_coin_current_amount = first_coin_total.amount_current = (
            second_coin_latest_price / first_coin_latest_price
        ).sqrt() * total_k_ratio
        second_coin_current_amount = second_coin_total.amount_current = (
            first_coin_latest_price / second_coin_latest_price
        ).sqrt() * total_k_ratio
        initial_market_value = (first_coin_total.amount_added * first_coin_latest_price) + (
            second_coin_total.amount_added * second_coin_latest_price
        )
        current_market_value = (
            first_coin_latest_price
            * (
                first_coin_current_amount
                - first_coin_total.amount_total
                + first_coin_total.amount_added
            )
        ) + (
            second_coin_latest_price
            * (
                second_coin_current_amount
                - second_coin_total.amount_total
                + second_coin_total.amount_added
            )
        )
        impermanent_loss_percent = (
            (initial_market_value - current_market_value) / initial_market_value * 100
        )

    return impermanent_loss_percent


def get_loan_transfer(transaction: Transaction) -> Transfer:
    """Return the transfer involving the loanable asset.

    This assumes the `transaction` is a loan transaction.
    """
    transfers = transaction.transfers
    if transaction.is_collateral == transaction.is_deposit:
        # Transaction is depositing into the loan protocol.
        # Take the transfer that is withdrawing from wallet.
        withdrawals = [t for t in transfers if t.is_withdrawal and t.asset_account.coin_id]
        assert (
            len(withdrawals) == 1
        ), "None or multiple wallet withdrawals found for a loan deposit"
        return withdrawals[0]
    # Transaction is withdrawing from the loan protocol.
    # Take the transfer that is depositing to wallet.
    deposits = [t for t in transfers if t.is_deposit and t.asset_account.coin_id]
    assert len(deposits) == 1, "None or multiple wallet deposits found for a loan withdrawal"
    return deposits[0]


@dataclass
class AccountLoanDraft:
    @dataclass
    class Entry:
        amount: Decimal
        coin: Coin
        is_collateral: bool
        is_deposit: bool
        liquidation_threshold: Decimal = Decimal(0)

    id: int
    name: str
    network: Network
    entries: list[Entry]
    transactions: list[Transaction]


def transactions_to_account_loans(
    transactions: list[Transaction], *, p2p=False
) -> list[schemas.AccountLoanFull]:
    """Returns account loans keyed by account_id.

    The account loan data is derived only from the transactions provided.
    """
    drafts_dict: dict[(int, int), AccountLoanDraft] = {}
    for transaction in transactions:
        if p2p:
            transfer = transaction.transfers[0]
            account = transfer.from_account if transaction.is_deposit else transfer.to_account
        else:
            transfer = get_loan_transfer(transaction)
            account = (
                transfer.to_account
                if transaction.is_collateral == transaction.is_deposit
                else transfer.from_account
            )
        account_name = get_account_loan_name(account)
        uid = (account.network_id, account.lending_protocol_id)
        draft = drafts_dict.get(uid)
        if draft is None:
            draft = AccountLoanDraft(
                id=account.id,
                name=account_name,
                network=account.network,
                entries=[],
                transactions=[],
            )
            drafts_dict[uid] = draft
        draft.transactions.append(transaction)
        account_loan_draft_entry = AccountLoanDraft.Entry(
            amount=transfer.amount,
            coin=transfer.asset_account.alias_coin,
            is_collateral=transaction.is_collateral,
            is_deposit=bool(transaction.is_deposit),
        )
        if not p2p and account.lending_protocol.liquidation_thresholds is not None:
            liquidation_threshold = account.lending_protocol.liquidation_thresholds.get(
                transfer.asset_account.alias_coin.uid,
                "0",
            )
            account_loan_draft_entry.liquidation_threshold = Decimal(liquidation_threshold)

        draft.entries.append(account_loan_draft_entry)

    accounts: list[schemas.AccountLoanFull] = []
    for draft in drafts_dict.values():
        (
            coin_totals,
            total_collateral,
            total_borrowed,
            total_liquidation_threshold,
        ) = calculate_loan_stats(draft, p2p=p2p)
        accounts.append(
            schemas.AccountLoanFull(
                id=draft.id,
                name=draft.name,
                network=draft.network,
                total_collateral=total_collateral,
                total_borrowed=total_borrowed,
                total_liquidation_threshold=total_liquidation_threshold,
                coin_totals=coin_totals,
                transactions=draft.transactions,
            )
        )

    return accounts


def calculate_loan_stats(
    draft: AccountLoanDraft, *, p2p=False
) -> tuple[list[schemas.LoanCoinTotal], Decimal, Decimal, Decimal]:
    loan_coin_totals: dict[Coin, schemas.LoanCoinTotal] = {}

    for entry in draft.entries:
        amount = entry.amount
        if not entry.is_deposit:
            amount = amount.copy_negate()
        if entry.coin not in loan_coin_totals:
            loan_coin_totals[entry.coin] = schemas.LoanCoinTotal(
                coin=entry.coin,
                borrow_amount=Decimal(0),
                collateral_amount=Decimal(0),
                loss_amount=Decimal(0),
                profit_amount=Decimal(0),
                liquidation_threshold=entry.liquidation_threshold,
            )

        loan_coin_total = loan_coin_totals[entry.coin]
        if p2p or not entry.is_collateral:
            # Borrow
            loan_coin_total.borrow_amount += amount
            if loan_coin_total.borrow_amount < 0:
                # Realized Loss
                loan_coin_total.loss_amount += abs(loan_coin_total.borrow_amount)
                loan_coin_total.borrow_amount = Decimal(0)
        else:
            # Collateral
            loan_coin_total.collateral_amount += amount
            if loan_coin_total.collateral_amount < 0:
                # Realized Profit
                loan_coin_total.profit_amount += abs(loan_coin_total.collateral_amount)
                loan_coin_total.collateral_amount = Decimal(0)

    coin_totals = sorted(loan_coin_totals.values(), key=lambda ct: ct.coin.uid)
    total_borrowed = total_collateral = total_liquidation_threshold = Decimal(0)
    for coin_total in coin_totals:
        # Calculate the total USD value of borrowed and collateral amounts
        total_borrowed += coin_total.borrow_amount * coin_total.coin.latest_usd_price
        total_collateral += coin_total.collateral_amount * coin_total.coin.latest_usd_price
        total_liquidation_threshold += (
            coin_total.collateral_amount * coin_total.coin.latest_usd_price
        ) * (coin_total.liquidation_threshold / 100)

    return coin_totals, total_collateral, total_borrowed, total_liquidation_threshold


def get_account_loan_name(account: Account) -> str:
    """Return the best name available for a loan account."""
    if account.lending_protocol:
        return account.lending_protocol.name
    if account.name:
        return account.name
    return account.address


class BridgeManager(BaseManager[Bridge]):
    model = Bridge
    order_by = "Bridge.name"


class ExchangeManager(BaseManager[Exchange]):
    model = Exchange
    order_by = "Exchange.name"

    def get_or_create_by_name(self, name: str, commit: bool = True, **kwargs) -> Exchange:
        exchange = self.db.query(Exchange).filter_by(name=name).one_or_none()
        if not exchange:
            exchange = self.add_model(Exchange(name=name, **kwargs), commit=commit)
        return exchange

    def get_all_with_account_address(
        self,
    ) -> Result[tuple[Exchange, str | None, Network | None]]:
        return self.db.execute(
            select(Exchange, Account.address, Network)
            .outerjoin(Account, Account.exchange_id == Exchange.id)
            .outerjoin(Network, Network.id == Account.network_id)
        )


class FarmManager(BaseManager[Farm]):
    model = Farm
    order_by = "Farm.name"
    schema_add = schemas.AddFarm

    def get_all_with_account_address(self) -> Result[tuple[Farm, str | None, Network | None]]:
        return self.db.execute(
            select(Farm, Account.address, Network)
            .outerjoin(Account, Account.farm_id == Farm.id)
            .outerjoin(Network, Network.id == Account.network_id)
        )


class LendingProtocolManager(BaseManager[LendingProtocol]):
    model = LendingProtocol
    order_by = "LendingProtocol.name"

    def get_or_create_by_name(self, name: str, **kwargs) -> LendingProtocol:
        lending_protocol = self.db.scalar(
            pg_insert(LendingProtocol).on_conflict_do_nothing().returning(LendingProtocol),
            {"name": name, **kwargs},
        )
        if not lending_protocol:
            lending_protocol = self.db.scalars(
                select(LendingProtocol).where(LendingProtocol.name == name)
            ).one()
        return lending_protocol

    def get_lending_protocol_by_name(self, name: str) -> LendingProtocol | None:
        return self.db.query(LendingProtocol).filter_by(name=name).one_or_none()

    def update_liquidation_threshold(
        self,
        lending_protocol: LendingProtocol,
        schema: schemas.LendingProtocolLiquidationThreshold,
    ):
        try:
            CoinManager(self.db).get_coin_by_uid(schema.coin_uid)
        except NoResultFound as e:
            raise HTTP400Exception(
                f"Could not find coin with ticker {schema.coin_ticker}"
            ) from e

        if schema.liquidation_threshold >= 100:
            raise HTTP400Exception("Liquidation threshold must be less than 100")
        if schema.liquidation_threshold.as_tuple().exponent < -2:
            raise HTTP400Exception("Liquidation threshold must have at most 2 decimal places")

        if lending_protocol.liquidation_thresholds is None:
            lending_protocol.liquidation_thresholds = {
                schema.coin_uid: str(schema.liquidation_threshold)
            }
        else:
            lending_protocol.liquidation_thresholds[schema.coin_uid] = str(
                schema.liquidation_threshold
            )

        self.db.commit()

    def get_all_with_account_address(
        self,
    ) -> Result[tuple[LendingProtocol, str | None, Network | None]]:
        return self.db.execute(
            select(LendingProtocol, Account.address, Network)
            .outerjoin(Account, Account.lending_protocol_id == LendingProtocol.id)
            .outerjoin(Network, Network.id == Account.network_id)
        )


class NetworkManager(BaseManager[Network]):
    model = Network
    order_by = "Network.name"

    def get_all_with_admin_wallets(self) -> Sequence[Network]:
        return self.db.scalars(
            select(Network).where(
                Network.wallets != None,
            )
        ).all()

    def get_by_name(self, name: str) -> Network:
        return self.db.query(Network).filter_by(name=name).one()

    def get_by_ids(self, ids: list[int]) -> list[Network]:
        return self.db.query(Network).filter(Network.id.in_(ids)).all()

    def get_ethereum(self) -> Network:
        return self.get_by_name("Ethereum")

    def get_solana(self) -> Network:
        return self.get_by_name("Solana")

    def get_usable_by_clients(self) -> list[Network]:
        return self.db.query(Network).filter_by(is_usable_by_clients=True).all()

    def get_or_create_by_name(self, name: str, **kwargs) -> Network:
        network = self.db.query(Network).filter_by(name=name).one_or_none()
        if not network:
            network = self.add_model(Network(name=name, **kwargs))
        return network

    def update(self, schema: schemas.Network) -> Network:
        network = self.get_by_id(schema.id)
        if schema.is_usable_by_clients:
            # Only allow enabling networks and not disabling since Clients may have already started using the Network.
            # Fund Wallets can be disabled and used to gate deposits and withdrawals if needed.
            network.is_usable_by_clients = True
        network.explorer_url = schema.explorer_url
        self.db.commit()
        return network


class NFTManager(BaseManager[NFT]):
    model = NFT

    def add_nft(self, attributes: list, image_url: str | None, uid: int | None) -> NFT:
        return self.add_model(NFT(attributes=attributes, image_url=image_url, uid=uid))

    def get_or_create(self, uid: str):
        nft = self.db.query(NFT).filter_by(uid=uid).one_or_none()
        if not nft:
            nft = self.add_model(NFT(uid=uid))
        return nft


class PoolPriceAlertManager(BaseManager[PoolPriceAlert]):
    model = PoolPriceAlert

    def create_or_update(
        self, *, account_id: int, coin_id: int, min_price_usd: Decimal, max_price_usd: Decimal
    ) -> None:
        stmt = pg_insert(PoolPriceAlert)
        stmt = stmt.on_conflict_do_update(
            index_elements=(PoolPriceAlert.account_id, PoolPriceAlert.coin_id),
            set_={
                PoolPriceAlert.last_triggered_at: None,  # Reset when updating
                PoolPriceAlert.min_price_usd: stmt.excluded.min_price_usd,
                PoolPriceAlert.max_price_usd: stmt.excluded.max_price_usd,
            },
            where=(
                (PoolPriceAlert.min_price_usd != stmt.excluded.min_price_usd)
                | (PoolPriceAlert.max_price_usd != stmt.excluded.max_price_usd)
            ),
        )
        self.db.execute(
            stmt,
            {
                "account_id": account_id,
                "coin_id": coin_id,
                "min_price_usd": min_price_usd,
                "max_price_usd": max_price_usd,
            },
        )

    def delete(self, account_id: int, coin_id: int) -> None:
        self.db.execute(
            delete(PoolPriceAlert).where(
                PoolPriceAlert.account_id == account_id, PoolPriceAlert.coin_id == coin_id
            )
        )

    def get_all_from_account(self, account_id: int) -> Sequence[PoolPriceAlert]:
        return self.db.scalars(
            select(PoolPriceAlert).where(PoolPriceAlert.account_id == account_id)
        ).all()

    def get_all_in_alert(self) -> Sequence[PoolPriceAlert]:
        disregarded_at = func.now() - text("interval '12 hours'")
        return self.db.scalars(
            select(PoolPriceAlert)
            .join(PoolPriceAlert.coin)
            .where(
                (PoolPriceAlert.last_triggered_at == None)
                | (PoolPriceAlert.last_triggered_at < disregarded_at),
                (Coin.latest_usd_price <= PoolPriceAlert.min_price_usd)
                | (Coin.latest_usd_price >= PoolPriceAlert.max_price_usd),
            )
        ).all()


class TransferManager(BaseManager[Transfer]):
    model = Transfer

    def add_transfer(
        self,
        amount,
        asset_account,
        from_account,
        to_account,
        transaction,
        nft_id: int | None = None,
    ):
        if nft_id and amount != 1:
            raise Exception("Cannot transfer more than 1 of the same NFT")

        return self.add_model(
            Transfer(
                asset_account_id=asset_account.id,
                amount=amount,
                nft_id=nft_id,
                from_account_id=from_account.id,
                to_account_id=to_account.id,
                transaction_id=transaction.id,
            )
        )


class TransactionManager(BaseManager[Transaction]):
    model = Transaction

    def get_after(self, after: datetime) -> Sequence[Transaction]:
        query = (
            select(Transaction)
            .where(Transaction.confirmed_at >= after)
            .where(
                Transaction.action.in_(
                    (
                        TransactionAction.income.value,
                        TransactionAction.spend.value,
                        TransactionAction.swap.value,
                        TransactionAction.loan.value,
                    )
                )
            )
            .order_by(Transaction.confirmed_at)
        )
        query = query.outerjoin(ClientTransaction).where(
            ClientTransaction.transaction_id == None
        )

        return self.db.scalars(query).all()

    def get_pooled_fund_transactions(self) -> Sequence[Transaction]:
        return self.db.scalars(
            select(Transaction)
            .join(ClientTransaction)
            .where(Transaction.action != None)
            .order_by(Transaction.confirmed_at.desc())
            .options(*eager_load.TRANSACTION)
        ).all()

    def get_by_id(self, model_id: int, classified: bool | None = None) -> Transaction:
        statement = self.db.query(Transaction).where(Transaction.id == model_id)
        if classified is not None:
            statement = statement.where(
                Transaction.action != None if classified else Transaction.action == None
            )
        return statement.one()

    def get_by_internal_id(self, internal_id: str) -> Transaction | None:
        """Get a transaction by its internal ID."""
        return self.db.scalar(select(Transaction).where(Transaction.internal_id == internal_id))

    def get_cointracking_transactions(self) -> Sequence[Transaction]:
        cointracking_prepend = bindparam("cointracking_preprend", "CT-%")
        return self.db.scalars(
            select(Transaction)
            .where(Transaction.tx_hash.ilike(cointracking_prepend))
            .order_by(Transaction.confirmed_at)
        ).all()

    def classify(
        self,
        classify_schema: schemas.TransactionClassify,
        *,
        client_transaction: ClientTransaction | None = None,
        transaction: Transaction | None = None,
        commit_and_sync=True,
    ):
        """
        Classifies a Transaction by setting its `action`.
        """
        action = classify_schema.action
        if not transaction:
            transaction = self.get_by_id(classify_schema.id)

        match action:
            case TransactionAction.bridge:
                error_msg = "Transaction action=bridge"
                if not classify_schema.bridge_id:
                    raise HTTP400Exception(f"{error_msg} is missing bridge_id")
                if classify_schema.is_deposit is None:
                    raise HTTP400Exception(f"{error_msg} is missing is_deposit")
            case TransactionAction.income:
                pass
            case TransactionAction.loan:
                error_msg = "Transaction action=loan"
                if not (classify_schema.lending_protocol_id or classify_schema.user_id):
                    raise HTTP400Exception(
                        f"{error_msg} needs either lending_protocol_id or user_id, both can't be empty"
                    )
                if not classify_schema.user_id:
                    if classify_schema.is_deposit is None:
                        raise HTTP400Exception(f"{error_msg} is missing is_deposit")
                    if classify_schema.is_collateral is None:
                        raise HTTP400Exception(
                            f"{error_msg} Non-P2P loans can either be a collateral or borrow"
                            " and have to set is_collateral"
                        )
            case TransactionAction.pool:
                error_msg = "Transaction action=pool"
                if not classify_schema.exchange_id:
                    raise HTTP400Exception(f"{error_msg} is missing exchange_id")
                if classify_schema.is_deposit is None:
                    raise HTTP400Exception(f"{error_msg} is missing is_deposit")
            case TransactionAction.spend:
                pass
            case TransactionAction.stake:
                error_msg = "Transaction action=stake"
                if not classify_schema.farm_id:
                    raise HTTP400Exception(f"{error_msg} is missing farm_id")
                if classify_schema.is_deposit is None:
                    raise HTTP400Exception(f"{error_msg} is missing is_deposit")
            case TransactionAction.swap:
                pass

        is_deposit = classify_schema.is_deposit
        if is_deposit is None:
            if action == TransactionAction.income:
                is_deposit = True
            elif action == TransactionAction.spend:
                is_deposit = False

        transaction.action = action.value
        transaction.is_collateral = classify_schema.is_collateral
        transaction.is_deposit = is_deposit

        # Ensure the Transaction's Transfers match its `is_collateral` and `is_deposit` fields
        has_deposit = False
        has_withdrawal = False
        for transfer in transaction.transfers:
            if transfer.is_deposit:
                has_deposit = True
            if transfer.is_withdrawal:
                has_withdrawal = True

        if classify_schema.action in (TransactionAction.pool, TransactionAction.stake) or (
            classify_schema.action == TransactionAction.loan
            and not classify_schema.user_id
            and classify_schema.is_collateral
        ):
            if transaction.is_deposit and not has_withdrawal:
                raise HTTP400Exception(
                    f"Transaction is a {action.value} "
                    "deposit but does not involve any withdrawals from a Wallet"
                )
            elif transaction.is_deposit is False and not has_deposit:
                raise HTTP400Exception(
                    f"Transaction is a {action.value} "
                    "withdrawal but does not involve any deposits to a Wallet"
                )
        else:
            if transaction.is_deposit and not has_deposit:
                raise HTTP400Exception(
                    "Transaction is a Deposit but does not involve any deposits to a Wallet"
                )
            elif transaction.is_deposit is False and not has_withdrawal:
                raise HTTP400Exception(
                    "Transaction is a Withdrawal but does not involve any withdrawals from a Wallet"
                )

        if action == TransactionAction.pool:
            transfers = get_pool_transfers(transaction)
            if len(transfers) != 2:
                raise HTTP400Exception(
                    "Pool transactions need to be 2 transfers from or to a Wallet address,"
                    f" got {len(transfers)} instead.",
                )
            first_transfer, *remain = transfers
            if any(transfer.to_account != first_transfer.to_account for transfer in remain):
                raise HTTP400Exception(
                    "Pool assets transfers have different destination addresses"
                )
            if any(transfer.from_account != first_transfer.from_account for transfer in remain):
                raise HTTP400Exception("Pool assets transfers have different origin addresses")
            if first_transfer.to_account.is_wallet == first_transfer.from_account.is_wallet:
                raise HTTP400Exception(
                    "Pool assets transfers are not exclusively from or to a wallet"
                )

            # Validate Coins that already interacted with the Account are the same as the new ones
            existing_transaction = self.get_earliest_classified_for_account(
                transaction.network,
                (
                    first_transfer.from_account_id
                    if not first_transfer.from_account.is_wallet
                    else first_transfer.to_account_id
                ),
                action,
            )
            if existing_transaction:
                new_pool_name = get_pool_name_and_tickers(*transfers)[0]
                existing_pool_name = get_pool_name_and_tickers(
                    *get_pool_transfers(existing_transaction)
                )[0]
                if existing_pool_name != new_pool_name:
                    raise HTTP400Exception(
                        f"Trying to use {new_pool_name} but this pool is already configured for {existing_pool_name}"
                    )

        if classify_schema.user_id or classify_schema.is_for_pooled_fund:
            # Create new ClientTransaction
            if action not in [
                TransactionAction.loan,
                TransactionAction.spend,
                TransactionAction.swap,
            ]:
                raise HTTP400Exception(
                    "The User can only be set for Loan, Spend and Swap Transactions"
                )

            # Validate Coin and amount match ClientTransaction
            if client_transaction:
                if classify_schema.user_id:
                    # Only validate client transaction amounts when dealing with a User
                    possible_transfers = [
                        transfer
                        for transfer in transaction.transfers
                        if client_transaction.coin_id == transfer.asset_account.coin_id
                    ]
                    if not possible_transfers:
                        raise HTTP400Exception(
                            "Transaction uses a different Coin than the ClientTransaction"
                        )

                    for transfer in possible_transfers:
                        if (
                            client_transaction.amount == transfer.amount
                            and transfer.asset_account.coin_id
                        ):
                            user_balance = ClientTransactionManager(
                                self.db
                            ).get_user_current_balance_for_coin(
                                classify_schema.user_id,
                                transfer.asset_account.coin_id,
                            )
                            if not transfer.is_deposit and (
                                not user_balance or user_balance < transfer.amount
                            ):
                                raise HTTP400Exception(
                                    "User does not have the balance to withdraw from (excluding interest)"
                                )
                            break
                    else:
                        raise HTTP400Exception(
                            "Transaction has a different amount than the ClientTransaction"
                        )

                client_transaction.reviewed_at = datetime.now()
                client_transaction.transaction = transaction
            else:
                for transfer in transaction.transfers:
                    coin = transfer.asset_account.coin
                    if not coin:
                        raise HTTP400Exception(
                            f"{transfer.asset_account.ticker_symbol} is not linked to a Coin"
                        )

                if action == TransactionAction.swap:
                    accounts_set: set[Account] = set()
                    from_asset_account = None
                    to_asset_account = None
                    to_asset_amount = Decimal(0)
                    transfers_len = len(transaction.transfers)

                    for transfer in transaction.transfers:
                        asset_account = transfer.asset_account
                        if asset_account.is_native_token and transfers_len > 2:
                            # Extra DEX swap fee is included as a separate Transfer -
                            # Fold it into the Transaction's fee.
                            if not transaction.fees_paid:
                                transaction.fees_paid = Decimal(0)
                            transaction.fees_paid += transfer.amount
                            self.db.delete(transfer)
                        else:
                            accounts_set.add(asset_account)
                            if transfer.is_deposit:
                                to_asset_account = asset_account
                                to_asset_amount += transfer.amount
                            elif transfer.is_withdrawal:
                                from_asset_account = asset_account

                    # Transaction should only use 2 assets for a Swap
                    if len(accounts_set) != 2:
                        raise HTTP400Exception(
                            "A swap should involve exactly 2 assets - please edit the Transaction and try again."
                        )

                    if not (from_asset_account and to_asset_account):
                        raise HTTP400Exception("Asset account missing")

                    from_coin = from_asset_account.alias_coin
                    if not from_coin:
                        raise HTTP400Exception("No Coin found for asset being sold.")
                    if not from_coin.latest_usd_price_updated_at:
                        raise HTTP400Exception(f"No price found for {from_coin.ticker}.")
                    from_asset_account.coin_id = from_coin.id
                    asset_account = to_asset_account
                    asset_amount = to_asset_amount
                else:
                    # Set asset data for Spend
                    assert len(transaction.transfers) == 1
                    first_transfer = transaction.transfers[0]
                    asset_account = first_transfer.asset_account
                    asset_amount = first_transfer.amount

                assert asset_account.alias_coin is not None

                client_transaction = ClientTransaction(
                    amount=asset_amount,
                    coin_id=asset_account.alias_coin.id,
                    created_at=transaction.confirmed_at,
                    network_id=transaction.network_id,
                    reviewed_at=datetime.now(),
                    transaction_id=transaction.id,
                    user_id=classify_schema.user_id,
                )
                ClientTransactionManager(self.db).add_model(client_transaction, commit=False)

        alias_coin_manager = AliasCoinManager(self.db)
        coin_manager = CoinManager(self.db)
        for transfer in transaction.transfers:
            # Tag Accounts with the same bridge, exchange, farm, or lending protocol they are classified under
            for account in transfer.from_account, transfer.to_account:
                if not account.wallet_id and not account.is_burn:
                    account.bridge_id = classify_schema.bridge_id or account.bridge_id
                    account.exchange_id = classify_schema.exchange_id or account.exchange_id
                    account.farm_id = classify_schema.farm_id or account.farm_id
                    account.lending_protocol_id = (
                        classify_schema.lending_protocol_id or account.lending_protocol_id
                    )
            # Populate Coins
            if not transfer.asset_account.coin and transfer.asset_account.ticker_symbol:
                coin = coin_manager.get_coin_by_ticker(
                    transfer.asset_account.ticker_symbol.casefold().split(" ")[0], priced=True
                )
                if coin:
                    alias_coin = alias_coin_manager.get_by_aliased_coin_id(coin.id)
                    if alias_coin:
                        coin = alias_coin.coin
                    # Ensure Coin prices are tracked as it is now being used
                    coin.enable_prices(commit=False)
                    transfer.asset_account.coin = coin

        if transaction.action == TransactionAction.swap.value:
            self.simplify_swap_if_same_assets(transaction)
            action = TransactionAction(transaction.action)  # update action after simplification

        # Validate Transaction has the minimum required Coins for its classification
        coins = set(
            transfer.asset_account.coin
            for transfer in transaction.transfers
            if transfer.asset_account.coin
        )
        coin_count = len(coins)
        min_coin_count = get_min_coin_count(action)
        if coin_count < min_coin_count:
            raise HTTP400Exception(
                f"Transaction is missing {min_coin_count - coin_count} Coin(s) required for its classification."
                "Please make sure all relevant Coins are enabled or aliased and try again."
            )

        if settings.enable_pooled_fund:
            # In this case we don't want to allow transfers that don't have a valid coin
            for transfer in transaction.transfers:
                if (
                    transfer.asset_account.alias_coin is None
                    and not transfer.asset_account.is_pool_token
                ):
                    ticker = transfer.asset_account.ticker_symbol or ""
                    transfer_info = f"amount {transfer.amount} {ticker}".rstrip()
                    raise HTTP400Exception(
                        f"Transaction has a transfer with an invalid coin ({transfer_info})"
                    )

        if settings.is_sahaba or settings.is_local:
            if classify_schema.is_revenue_share:
                if transaction.action not in [
                    TransactionAction.income.value, TransactionAction.swap.value
                ]:
                    raise HTTP400Exception(
                        "Revenue shares are only allowed for Income and Swap transactions. it is "
                    )

                # Create client transactions for all non-admin users
                from stabletech.auth.crud import UserManager

                user_manager = UserManager(self.db)
                non_admin_users = user_manager.get_active_users(include_admins=False)

                # Calculate total balance amount of each user using transactions and transfers, add deposits and
                # subtract withdrawals
                if transaction.transfers:
                    total_revenue_amount = sum(
                        transfer.amount
                        for transfer in transaction.transfers
                        if transfer.is_deposit
                    )
                    logger.info(f"Total revenue amount: {total_revenue_amount}")
                    revenue_share = total_revenue_amount * Decimal(0.8)
                    logger.info(f"Revenue share: {revenue_share}")


                    if non_admin_users and revenue_share > 0:
                        logger.info("Calculating user balances for revenue share")
                        # Calculate each user's balance for the same coin using SQLAlchemy
                        user_balances, total_all_balances = (
                            self._calculate_user_balances_for_coin(
                                user_ids=[user.id for user in non_admin_users],
                                transaction_confirmed_at=transaction.confirmed_at,
                            )
                        )
                        logger.info(f"User balances: {user_balances}")
                        logger.info(f"Total all balances: {total_all_balances}")

                        # Create all client transaction data for bulk insert
                        revenue_client_transaction_data = []
                        if total_all_balances > 0:
                            current_time = datetime.now()
                            fiat_network = NetworkManager(self.db).get_by_name("Fiat")

                            # Fetch Fiat coin
                            coin_manager = CoinManager(self.db)
                            fiat_coin = coin_manager.get_coin_by_ticker("Fiat")
                            if not fiat_coin:
                                raise HTTP400Exception("Fiat coin not found")

                            for user in non_admin_users:
                                user_balance = user_balances.get(user.id, Decimal(0))
                                if user_balance > 0:
                                    # Calculate proportional share: (user_balance / total_balances) * total_revenue
                                    proportional_amount = (
                                        user_balance / total_all_balances
                                    ) * revenue_share

                                    revenue_client_transaction_data.append(
                                        {
                                            "amount": proportional_amount,
                                            "coin_id": fiat_coin.id,
                                            "created_at": transaction.confirmed_at,
                                            "network_id": fiat_network.id,
                                            "reviewed_at": current_time,
                                            "revenue_transaction_id": transaction.id,
                                            "user_id": user.id,
                                            "is_deposit": True,
                                        }
                                    )

                        logger.info(
                            f"Creating {len(revenue_client_transaction_data)} client transactions for revenue share"
                        )
                        # Single bulk insert for all client transactions
                        if revenue_client_transaction_data:
                            self.db.execute(
                                insert(ClientTransaction).values(
                                    revenue_client_transaction_data
                                )
                            )
                            logger.info("Created client transactions for revenue share")
                            # All client transactions will be committed together with the main transaction

        if commit_and_sync:
            self.db.commit()
            logger.info("Committed transaction for revenue share")
            if client_transaction:
                classified_client_transaction_emails(client_transaction)
            elif is_subsidiary():
                # Sync non-client transaction if this is a subsidiary environment
                sync_transaction_to_main(transaction, self.db)

        return transaction

    def _calculate_user_balances_for_coin(
        self, user_ids: list[int], transaction_confirmed_at: datetime | None = None
    ) -> tuple[dict[int, Decimal], Decimal]:
        """
        Calculate user balances assuming all amounts are USD.
        Only considers deposits and withdrawals made within 7 days of the transaction's confirmed_at.

        Args:
            user_ids: List of user IDs to include in the calculation
            transaction_confirmed_at: Reference date for the 7-day window filter

        Returns:
            tuple: (user_balances_dict, total_all_balances)
                - user_balances_dict: {user_id: balance} for users with positive balances
                - total_all_balances: sum of all positive balances
        """
        # Calculate the 7-day window if transaction_confirmed_at is provided
        seven_days_ago = None
        if transaction_confirmed_at:
            seven_days_ago = transaction_confirmed_at - timedelta(days=7)

        # Subquery to calculate balance per user
        balance_subquery = select(
            ClientTransaction.user_id,
            func.sum(
                case(
                    # For deposits, add the amount
                    (ClientTransaction.is_deposit == True, ClientTransaction.amount),
                    # For withdrawals, subtract the amount
                    (ClientTransaction.is_deposit == False, -ClientTransaction.amount),
                    else_=0,
                )
            ).label("balance"),
        ).where(ClientTransaction.user_id.in_(user_ids))

        # Add 7-day filter if transaction_confirmed_at is provided
        if seven_days_ago:
            balance_subquery = balance_subquery.where(
                ClientTransaction.created_at <= seven_days_ago
            )

        balance_subquery = balance_subquery.group_by(ClientTransaction.user_id).subquery()

        # Get user balances (only positive balances)
        user_balance_results = self.db.execute(
            select(
                balance_subquery.c.user_id,
                func.greatest(balance_subquery.c.balance, 0).label("positive_balance"),
            ).where(balance_subquery.c.balance > 0)
        ).all()

        user_balances = {row.user_id: row.positive_balance for row in user_balance_results}
        total_all_balances = sum(user_balances.values(), Decimal(0))

        return user_balances, total_all_balances

    def add(self, schema, **kwargs) -> model:
        # Use `add_with_transfers` instead
        raise NotImplementedError

    def add_internal(self, internal_transaction: InternalTransaction) -> Transaction:
        add_transaction_data = internal_transaction.model_dump()
        try:
            network = NetworkManager(self.db).get_by_name(internal_transaction.network_name)
        except NoResultFound:
            raise HTTP400Exception(
                f"{internal_transaction.network_name} not found. Please create it and retry."
            )

        # Delete duplicate unclassified Transaction if exists
        existing_transaction = self.db.scalars(
            select(Transaction).where(
                Transaction.network_id == network.id,
                Transaction.tx_hash == internal_transaction.tx_hash,
            )
        ).one_or_none()
        if existing_transaction:
            if existing_transaction.action:
                raise HTTP400Exception(
                    f"Tx hash {internal_transaction.tx_hash} is already classified on {network.name}. Please reset it to continue."
                )
            self.delete(existing_transaction.id, commit=False)

        account_manager = AccountManager(self.db)
        coin_manager = CoinManager(self.db)
        wallet_manager = WalletManager(self.db)
        try:
            wallet = wallet_manager.get_or_create_by_address(
                internal_transaction.wallet_address,
                ilike=True,  # TODO: standardize wallet casing for applicable networks
                name=internal_transaction.wallet_name,
                networks=[network],
            )
        except Exception:
            raise HTTP400Exception(
                f"Could not create a wallet named {internal_transaction.wallet_name}"
            )
        if network not in wallet.networks:
            wallet.networks.append(network)
        for transfer in add_transaction_data["transfers"]:
            coin = None
            coin_uid = transfer["asset_account"]["coin_uid"]
            if coin_uid:
                try:
                    coin = coin_manager.get_coin_by_uid(coin_uid)
                except NoResultFound:
                    raise HTTP400Exception(f"Could not find coin with UID of {coin_uid}")
            try:
                account = account_manager.get_by_address_network_id(
                    transfer["asset_account"]["address"],
                    network.id,
                )
                if account.alias_coin:
                    # Prefer local Account's coin
                    coin = account.alias_coin
            except NoResultFound:
                pass

            if coin:
                transfer["asset_account"]["coin_id"] = coin.id

        transaction = schemas.AddTransaction(
            **add_transaction_data,
            network_id=network.id,
            wallet_id=wallet.id,
        )
        try:
            transaction = self.add_manually(transaction, commit_and_sync=False)
            transaction.internal_id = internal_transaction.internal_id
            self.db.commit()
            self.db.refresh(transaction)
            return transaction
        except HTTP400Exception as error:
            raise HTTP400Exception(
                f"Error saving {internal_transaction.internal_id} - {internal_transaction.tx_hash}: {error.detail}"
            )

    def add_manually(
        self,
        add_schema: schemas.AddTransaction,
        *,
        client_transaction_id: int | None = None,
        commit_and_sync=True,
    ) -> Transaction:
        account_manager = AccountManager(self.db)
        network_manager = NetworkManager(self.db)
        wallet_manager = WalletManager(self.db)

        network = network_manager.get_by_id(add_schema.network_id)
        wallet = wallet_manager.get_by_id(add_schema.wallet_id)
        wallet_account = account_manager.get_or_create(
            wallet.address, network, commit=commit_and_sync
        )

        if not add_schema.transfers:
            raise HTTP400Exception("Transaction has no transfers")
        if self.tx_hash_exists(network.id, add_schema.tx_hash):
            raise HTTP400Exception(
                f"A transaction with that Tx Hash on {network.name} already exists"
            )

        # Transaction should not get saved till `add_with_transfers` is called below
        transaction = Transaction(
            comment=add_schema.comment,
            confirmed_at=add_schema.confirmed_at,
            fees_paid=add_schema.fees_paid,
            is_deposit=add_schema.is_deposit,
            network_id=add_schema.network_id,
            signer_account_id=wallet_account.id,
            tx_hash=add_schema.tx_hash,
            wallet_id=wallet.id,
        )

        error = None
        transfers_data: list[dict] = []
        wallet_in_transfers = False

        # Validate transfers data
        for transfer_schema in add_schema.transfers:
            if not transfer_schema.amount:
                error = "Transfer amount cannot be 0"
                break
            transfer_data = {"amount": transfer_schema.amount, "transaction": transaction}
            asset_coin_id = transfer_schema.asset_account.coin_id or (
                transfer_schema.asset_account.coin.id
                if transfer_schema.asset_account.coin
                else None
            )

            if not transfer_schema.asset_account.is_pool_token and not asset_coin_id:
                error = "Asset account has no Coin"
                break
            if transfer_schema.asset_account.is_wallet:
                error = "Asset account cannot be a Wallet"
                break

            # Get or create Accounts
            for account_name, account_schema in (
                ("asset_account", transfer_schema.asset_account),
                ("from_account", transfer_schema.from_account),
                ("to_account", transfer_schema.to_account),
            ):
                if not account_schema.address and not account_schema.id:
                    error = f"{account_name} has no address or ID"
                    break
                account = (
                    account_manager.get_by_id(account_schema.id)
                    if account_schema.id
                    else account_manager.get_or_create_with_network_id(
                        account_schema.address,
                        add_schema.network_id,
                        coin_id=asset_coin_id if account_name == "asset_account" else None,
                        is_pool_token=account_schema.is_pool_token,
                    )
                )
                wallet_in_transfers = wallet_in_transfers or account.is_wallet
                transfer_data[account_name] = account
            if error:
                break

            asset_account = transfer_data["asset_account"]
            if not asset_account.coin_id:
                asset_account.coin_id = asset_coin_id
            elif asset_coin_id != asset_account.coin.id:
                error = f"Asset address {asset_account.address} is already configured for {asset_account.coin.uid}"
                break

            transfers_data.append(transfer_data)

        transfers = list(Transfer(**transfer_data) for transfer_data in transfers_data)
        if not error and not wallet_in_transfers:
            error = "Transaction does not involve any saved Wallet"

        # Validate that the client loan doesn't involve a lending protocol account
        if not error and add_schema.user_id and add_schema.action == TransactionAction.loan:
            accounts = (acc for t in transfers for acc in (t.from_account, t.to_account))
            if any(account.lending_protocol_id is not None for account in accounts):
                error = (
                    "Cannot add a client loan transaction involving a lending protocol account"
                )

        if error:
            if commit_and_sync:
                self.db.rollback()
                self.db.expunge_all()
            raise HTTP400Exception(error)

        client_transaction = None
        if client_transaction_id:
            client_transaction = (
                self.db.query(ClientTransaction)
                .filter(ClientTransaction.id == client_transaction_id)
                .one_or_none()
            )
            if not client_transaction:
                raise HTTP400Exception("Client Transaction not found")

        try:
            self.add_with_transfers(
                wallet,
                {transaction: transfers},
                commit=commit_and_sync,
            )
            if not commit_and_sync:
                self.db.flush()
            self.db.refresh(transaction)
            transaction = self.classify(
                schemas.TransactionClassify(**add_schema.model_dump(), id=transaction.id),
                client_transaction=client_transaction,
                transaction=transaction,
                commit_and_sync=commit_and_sync,
            )
        except Exception as error:
            for transfer in transaction.transfers:
                if transfer.id:
                    self.db.delete(transfer)
            if transaction.id:
                ClientTransactionManager(self.db).delete_by_transaction_id(
                    transaction.id, commit=False
                )
                self.db.delete(transaction)
            if commit_and_sync:
                self.db.commit()
            raise error

        if client_transaction:
            client_transaction.reviewed_at = datetime.now()
            client_transaction.transaction_id = transaction.id
            if commit_and_sync:
                self.db.commit()
        return transaction

    def add_with_transfers(
        self,
        wallet: Wallet | None,
        transactions_to_transfers: dict[Transaction, list[Transfer]],
        *,
        skip_existence_check=False,
        commit=True,
    ):
        """
        Save a non-persisted Transaction with its Transfers.
        Simplifies the Transfers by combining those with the same asset and set of accounts.
        """
        transfers_to_add: list[Transfer] = []
        transactions_to_add: list[Transaction] = []
        for transaction, transfers in transactions_to_transfers.items():
            network = transaction.network
            # Non-persisted Transaction may only set either `network_id` or `network`
            network_id = network.id if network else transaction.network_id
            if not skip_existence_check:
                if not wallet:
                    raise ValueError("wallet is required when skip_existence_check is false")
                if not transfers and (
                    transaction.signer_account.address.casefold() != wallet.address.casefold()
                ):
                    logger.debug(
                        "Skipped transaction because it does not involve a saved wallet: network_id=%d, tx_hash=%s",
                        network_id,
                        transaction.tx_hash,
                    )
                    continue
                if self.tx_hash_exists(network_id, transaction.tx_hash):
                    logger.debug(
                        "Skipped transaction because already exists: network_id=%d, tx_hash=%s",
                        network_id,
                        transaction.tx_hash,
                    )
                    continue

            transaction_transfers: list[Transfer] = []
            for transfer in transfers:
                # Combine transfer amounts if they have the same asset and set of accounts
                existing_transfers = filter(
                    lambda x: (
                        x.asset_account.id == transfer.asset_account.id
                        and {x.from_account.id, x.to_account.id}
                        == {transfer.from_account.id, transfer.to_account.id}
                    ),
                    transaction_transfers,
                )
                existing_transfer = next(existing_transfers, None)
                if existing_transfer:
                    assert len(list(existing_transfers)) == 0
                    existing_transfer.amount += transfer.amount * (
                        # Flip sign if the new transfer is in the opposite direction
                        1
                        if existing_transfer.from_account == transfer.from_account
                        else -1
                    )
                else:
                    # Transfer with this asset Account does not exist yet
                    transaction_transfers.append(transfer)

            transaction_transfers = list(
                transfer
                for transfer in transaction_transfers
                if Decimal(transfer.amount) != Decimal(0)
            )
            transfers_to_add.extend(transaction_transfers)
            transaction.transfers = transaction_transfers
            transactions_to_add.append(transaction)

        for transfer in transfers_to_add:
            # Swap from and to Accounts for negative summed amounts
            if transfer.amount < 0:
                assert transfer.from_account.is_wallet or transfer.to_account.is_wallet
                from_account = transfer.from_account
                transfer.from_account = transfer.to_account
                transfer.to_account = from_account
                transfer.amount = abs(transfer.amount)

        self.db.add_all(transactions_to_add)  # transfers are within their transactions
        if commit:
            self.db.commit()

    @staticmethod
    def confirmed_at_subquery(
        alias: type[Transaction], id_param: BindParameter[int]
    ) -> ScalarSelect[datetime]:
        return select(alias.confirmed_at).where(alias.id == id_param).scalar_subquery()

    def delete(self, transaction_id: int, *, commit=True):
        # First get the AccountedTransaction IDs that will be deleted
        transfer_subq = (
            select(Transfer.id)
            .where(Transfer.transaction_id == transaction_id)
            .scalar_subquery()
        )
        accounted_txn_ids = self.db.scalars(
            select(AccountedTransaction.id).where(
                AccountedTransaction.transfer_id.in_(transfer_subq)
            )
        ).all()

        if accounted_txn_ids:
            # Delete any CapitalPositions that reference these AccountedTransactions
            self.db.execute(
                delete(CapitalPosition).where(
                    or_(
                        CapitalPosition.accounted_buy_transaction_id.in_(accounted_txn_ids),
                        CapitalPosition.accounted_sell_transaction_id.in_(accounted_txn_ids),
                    )
                )
            )
            # Then delete the AccountedTransactions
            self.db.execute(
                delete(AccountedTransaction).where(
                    AccountedTransaction.id.in_(accounted_txn_ids)
                )
            )

        # Finally delete the transfers and transaction
        self.db.execute(delete(Transfer).where(Transfer.transaction_id == transaction_id))
        self.db.execute(delete(Transaction).where(Transaction.id == transaction_id))
        if commit:
            self.db.commit()

    def get_latest(self, network: Network, wallet: Wallet) -> Transaction | None:
        return self.db.scalars(
            select(Transaction)
            .filter_by(network=network, wallet=wallet)
            .order_by(Transaction.confirmed_at.desc())
            .limit(1)
        ).one_or_none()

    def get_earliest_classified_for_account(
        self, network, account_id: int, action: TransactionAction
    ) -> Transaction | None:
        return (
            self.db.query(Transaction)
            .join(Transfer)
            .filter(
                Transaction.action == action.value,
                Transaction.network == network,
                or_(
                    Transfer.from_account_id == account_id, Transfer.to_account_id == account_id
                ),
            )
            .order_by(Transaction.confirmed_at.asc())
            .first()
        )

    def get_paginated_transactions(
        self,
        classified=True,
        limit: int = 0,
        search_query: str = "",
        time_query: str = "",
        from_id: int = 0,
        desc: bool = True,
    ) -> ListResult[Transaction]:
        """Return transactions and whether more exist beyond the limit.

        Transactions are ordered by confirmation time and ID. The default ordering
        is descending (most recent first). Use `desc=False` to reverse.

        The result has the next and prev booleans that flag whether there are more
        transactions next to the last transaction and previous to the first
        transaction, respectively. Then, from_id can be used to get the next or
        previous transactions. The transaction specified on from_id is not
        included.
        """
        search_result = aliased(
            Transaction,
            alias=self._search(classified, search_query, time_query).cte("search_result"),
        )
        stmt = select(search_result)

        if from_id:
            stmt = stmt.where(self.pagination_condition(search_result, from_id, desc))

        stmt = (
            stmt.order_by(search_result.confirmed_at.desc(), search_result.id.desc())
            if desc
            else stmt.order_by(search_result.confirmed_at, search_result.id)
        )

        if limit:
            stmt = stmt.limit(limit)

        transactions = list(self.db.scalars(stmt))

        if (from_id or limit) and transactions:
            prev, next_ = self.paginated_has_more(search_result, desc, transactions)
        else:
            prev, next_ = False, False

        return ListResult(transactions, next=next_, prev=prev)

    @staticmethod
    def pagination_condition(
        alias: type[Transaction], id_value: int, desc: bool
    ) -> ColumnElement[bool]:
        id_param = bindparam("tx_id", id_value, unique=True, type_=Integer)
        row_values = tuple_(alias.confirmed_at, alias.id)
        ref_values = tuple_(TransactionManager.confirmed_at_subquery(alias, id_param), id_param)
        if desc:
            return row_values < ref_values
        return row_values > ref_values

    def paginated_has_more(
        self,
        alias: type[Transaction],
        desc: bool,
        transactions: list[Transaction],
    ) -> tuple[bool, bool]:
        """Return whether there are more rows on both direction (prev, next)."""
        first_transaction_id = transactions[0].id
        last_transaction_id = transactions[-1].id
        prev_stmt = (
            select(1)
            .where(self.pagination_condition(alias, first_transaction_id, not desc))
            .exists()
        )
        next_stmt = (
            select(1)
            .where(self.pagination_condition(alias, last_transaction_id, desc))
            .exists()
        )
        stmt = select(prev_stmt, next_stmt)
        prev, next_ = self.db.execute(stmt).one()._tuple()
        return prev, next_

    def reset(self, transaction_id: int):
        # Remove associated ClientTransaction and reset Transaction
        transaction = self.get_by_id(transaction_id)
        ClientTransactionManager(self.db).delete_by_transaction_id(transaction_id)
        transaction.action = None
        self.db.commit()

    def simplify_swap_if_same_assets(self, transaction: Transaction) -> None:
        """
        If all assets being swapped are the same, then simplify the transaction into a
        spend or income.

        Provided transaction must be a swap. This will modify the transaction in place.

        Attention: this can delete transfers!
        """
        unique_coins = set(
            transfer.asset_account.alias_coin for transfer in transaction.transfers
        )
        if len(unique_coins) > 1:
            return

        coin = unique_coins.pop()
        if coin is None:
            logger.error("Got None when simplifying swap transaction %d", transaction.id)
            return

        total_income = Decimal(0)
        total_spend = Decimal(0)
        for transfer in transaction.transfers:
            if transfer.is_deposit:
                total_income += transfer.amount
            elif transfer.is_withdrawal:
                total_spend += transfer.amount
            else:
                logger.error(
                    "Transfer in swap transaction is neither deposit nor withdrawal",
                    extra={"transaction_id": transaction.id, "transfer_id": transfer.id},
                )
                return

        balance = total_income - total_spend
        if balance != 0:
            for transfer in transaction.transfers:
                self.db.delete(transfer)

        if balance > 0:
            transaction.action = TransactionAction.income.value
            transaction.is_deposit = True
            transaction.is_collateral = False

            ref_transfer = next(
                transfer for transfer in transaction.transfers if transfer.is_deposit
            )

            transaction.transfers = [
                Transfer(
                    amount=balance,
                    asset_account=ref_transfer.asset_account,
                    from_account=ref_transfer.from_account,
                    to_account=ref_transfer.to_account,
                    transaction=transaction,
                )
            ]
        elif balance < 0:
            transaction.action = TransactionAction.spend.value
            transaction.is_deposit = False
            transaction.is_collateral = False

            ref_transfer = next(
                transfer for transfer in transaction.transfers if transfer.is_withdrawal
            )

            transaction.transfers = [
                Transfer(
                    amount=balance.copy_negate(),
                    asset_account=ref_transfer.asset_account,
                    from_account=ref_transfer.from_account,
                    to_account=ref_transfer.to_account,
                    transaction=transaction,
                )
            ]

    def _search(
        self,
        classified: bool,
        search_query: str,
        time_query: str,
    ) -> Select[tuple[Transaction]]:
        search_query = search_query.strip().lower()
        query = (
            select(Transaction)
            .distinct(Transaction.id)
            .where(Transaction.action != None if classified else Transaction.action == None)
            .options(*eager_load.TRANSACTION)
        )
        if time_query:
            datetime_filter = get_datetime_filter(time_query)
            if datetime_filter:
                return query.where(*datetime_filter)

        if not search_query:
            return query

        if is_number(search_query):
            search_decimal = bindparam("search_decimal", Decimal(search_query.replace(",", "")))
            search_window = bindparam("search_window", 1 / Decimal(100))
            return query.join(Transaction.transfers).where(
                search_decimal - search_window < Transfer.amount,
                Transfer.amount < search_decimal + search_window,
            )

        search_param = bindparam("search_query", f"%{search_query}%")

        search_conditions = (
            Transaction.action.ilike(search_param),
            Transaction.internal_id.ilike(search_param),
            Transaction.tx_hash.ilike(search_param),
            Transaction.comment.ilike(search_param),
            Transaction.network.has(Network.name.ilike(search_param)),
            Transaction.wallet.has(Wallet.address.ilike(search_param)),
            Transfer.asset_account.has(Account.coin.has(Coin.name.ilike(search_param))),
            Transfer.asset_account.has(Account.coin.has(Coin.ticker.ilike(search_param))),
            Transfer.from_account.has(Account.address.ilike(search_param)),
            Transfer.from_account.has(Account.bridge.has(Bridge.name.ilike(search_param))),
            Transfer.from_account.has(Account.exchange.has(Exchange.name.ilike(search_param))),
            Transfer.from_account.has(Account.farm.has(Farm.name.ilike(search_param))),
            Transfer.from_account.has(
                Account.lending_protocol.has(LendingProtocol.name.ilike(search_param))
            ),
            Transfer.from_account.has(Account.wallet.has(Wallet.name.ilike(search_param))),
            Transfer.to_account.has(Account.address.ilike(search_param)),
            Transfer.to_account.has(Account.bridge.has(Bridge.name.ilike(search_param))),
            Transfer.to_account.has(Account.exchange.has(Exchange.name.ilike(search_param))),
            Transfer.to_account.has(Account.farm.has(Farm.name.ilike(search_param))),
            Transfer.to_account.has(
                Account.lending_protocol.has(LendingProtocol.name.ilike(search_param))
            ),
            Transfer.to_account.has(Account.wallet.has(Wallet.name.ilike(search_param))),
        )

        return query.outerjoin(Transaction.transfers).where(or_(*search_conditions))

    def tx_hash_exists(self, network_id, tx_hash) -> bool:
        return self.db.query(
            exists().where(Transaction.network_id == network_id, Transaction.tx_hash == tx_hash)
        ).scalar()

    def filter_existing_tx_hashes(self, network_id: int, tx_hashes: list[str]) -> Sequence[str]:
        """Return only the tx hashes that already exist."""
        return self.db.scalars(
            select(Transaction.tx_hash).where(
                Transaction.network_id == network_id, Transaction.tx_hash.in_(tx_hashes)
            )
        ).all()

    def filter_non_existing_tx_hashes(
        self, network_id: int, tx_hashes: list[str]
    ) -> Sequence[str]:
        """Return only the tx hashes that do not exist."""
        tx_hash_values = values(column("tx_hash", String), name="tx_hashes").data(
            [(tx_hash,) for tx_hash in tx_hashes]
        )
        stmt: Select[tuple[str]] = (
            select(tx_hash_values.c.tx_hash)
            .outerjoin(
                Transaction,
                (Transaction.network_id == network_id)
                & (Transaction.tx_hash == tx_hash_values.c.tx_hash),
            )
            .where(Transaction.tx_hash == None)
        )
        return self.db.scalars(stmt).all()

    def get_expenses(self) -> schemas.TransactionsOverview:
        """Return all spend transactions along with coin totals."""
        transactions: list[Transaction] = (
            self.db.query(Transaction)
            .where(Transaction.action == TransactionAction.spend.value)
            .all()
        )
        return transactions_overview(transactions, amount_inverted=True)

    def get_income(self) -> schemas.TransactionsOverview:
        """Return all income transactions along with coin totals."""
        transactions: list[Transaction] = (
            self.db.query(Transaction)
            .where(Transaction.action == TransactionAction.income.value)
            .all()
        )
        return transactions_overview(transactions)

    def get_possible_actions(self, transaction_id: int):
        return (
            self.db.scalars(select(Transaction).where(Transaction.id == transaction_id))
            .one()
            .possible_actions
        )

    def get_stakes(self) -> list[schemas.Stake]:
        """Return asset totals for stake transactions."""
        transactions: list[Transaction] = (
            self.db.query(Transaction)
            .where(Transaction.action == TransactionAction.stake.value)
            .all()
        )

        return get_stakes_with_total_amounts(transactions)

    def get_coin_stake(
        self, farm_id: int, coin_id: int, network_id: int
    ) -> schemas.StakeDetails:
        transactions: list[Transaction] = self.db.scalars(
            select(Transaction)
            .join(Transfer, Transfer.transaction_id == Transaction.id)
            .where(
                Transaction.action == TransactionAction.stake.value,
                Transaction.network_id == network_id,
                Transfer.asset_account.has(Account.coin_id == coin_id),
                or_(
                    Transfer.to_account.has(Account.farm_id == farm_id),
                    Transfer.from_account.has(Account.farm_id == farm_id),
                ),
            )
            .distinct()
        ).all()

        if not transactions:
            raise HTTPNotFoundException("No stake transactions found for this Coin")

        stakes = get_stakes_with_total_amounts(transactions, coin_id=coin_id)
        assert len(stakes) == 1
        stake = stakes[0]

        return schemas.StakeDetails(
            farm=stake.farm,
            asset=stake.asset,
            amount=stake.amount,
            network=stake.network,
            transactions=transactions,
        )

    def get_asset_holdings(self) -> schemas.AssetHoldingsResponse:
        """Get all asset holdings with their current values and 24h changes."""
        # Get all classified transactions (action is not None)
        transactions = self.get_asset_transactions()

        # Initialize holdings dictionary to track coin amounts
        holdings: dict[int, Decimal] = {}  # coin_id -> amount

        for transaction in transactions:
            # Process transfers based on transaction action
            for transfer in transaction.transfers:
                if not transfer.asset_account.alias_coin:
                    continue

                coin_id = transfer.asset_account.alias_coin.id
                amount = Decimal(transfer.amount)

                if transaction.action == "income":
                    # Income increases holdings
                    holdings[coin_id] = holdings.get(coin_id, Decimal(0)) + amount
                elif transaction.action == "spend":
                    # Spend decreases holdings
                    holdings[coin_id] = holdings.get(coin_id, Decimal(0)) - amount
                elif transaction.action == "swap":
                    # For swaps, decrease if withdrawal, increase if deposit
                    if transfer.is_withdrawal:
                        holdings[coin_id] = holdings.get(coin_id, Decimal(0)) - amount
                    else:
                        holdings[coin_id] = holdings.get(coin_id, Decimal(0)) + amount

        # Get coin details for all coins in holdings
        coin_ids = list(holdings.keys())
        coins = self.db.query(Coin).filter(Coin.id.in_(coin_ids)).all()

        # Calculate total value and prepare response
        total_value = Decimal(0)
        total_weighted_change = Decimal(0)
        assets: list[schemas.AssetHolding] = []

        for coin in coins:
            amount = holdings[coin.id]
            if amount == 0:
                continue

            value = amount * Decimal(str(coin.latest_usd_price))
            total_value += value

            if coin.usd_24h_change:
                total_weighted_change += Decimal(str(coin.usd_24h_change)) * (
                    value / total_value
                )

            assets.append(
                schemas.AssetHolding(
                    coin_id=coin.id,
                    coin_name=coin.name,
                    coin_ticker=coin.ticker,
                    amount=str(amount),
                    value_usd=str(value),
                    price_usd=str(coin.latest_usd_price),
                    change_24h=coin.usd_24h_change,
                )
            )

        return schemas.AssetHoldingsResponse(
            total_value_usd=str(total_value),
            total_24h_change=float(total_weighted_change) if total_weighted_change else None,
            assets=sorted(assets, key=lambda x: float(x.value_usd), reverse=True),
        )

    def get_asset_transactions(self, coin_id: int | None = None) -> list[Transaction]:
        """Get all income/spend/swap transactions involving a specific coin that:
        1. Aren't linked to ClientTransactions
        """
        query = (
            self.db.query(Transaction)
            .join(Transfer)
            .outerjoin(ClientTransaction)
            .filter(
                Transaction.action.in_(
                    [
                        TransactionAction.income.value,
                        TransactionAction.spend.value,
                        TransactionAction.swap.value,
                    ]
                ),
                ClientTransaction.transaction_id == None,
            )
        )

        if coin_id:
            query = query.join(Account, Transfer.asset_account_id == Account.id).filter(
                or_(
                    Account.coin_id == coin_id,
                    Account.coin.has(Coin.alias_coin.has(AliasCoin.coin_id == coin_id)),
                )
            )

        return query.distinct().all()

    def get_in_range(self, start_date: datetime, end_date: datetime) -> list[Transaction]:
        return self.db.scalars(
            select(Transaction)
            .where(Transaction.confirmed_at >= start_date, Transaction.confirmed_at <= end_date)
            .outerjoin(ClientTransaction)
            .where(ClientTransaction.transaction_id == None)
        ).all()

    def get_internal_sync_transaction_ids(self) -> list[str]:
        """Get all transaction IDs with sync actions, excluding ClientTransactions."""
        stmt = (
            select(Transaction.id)
            .outerjoin(ClientTransaction)
            .where(
                Transaction.action.in_(INTERNAL_SYNC_ACTIONS),
                ClientTransaction.transaction_id
                == None,  # Exclude transactions with client transactions
            )
        )
        result = self.db.execute(stmt)
        return [str(row[0]) for row in result]

    def get_internal_sync_transactions(
        self, transaction_ids: list[int]
    ) -> Sequence[Transaction]:
        """Get full transaction data for the specified IDs, excluding ClientTransactions."""
        stmt = (
            select(Transaction)
            .outerjoin(ClientTransaction)
            .where(
                Transaction.id.in_(transaction_ids),
                ClientTransaction.transaction_id
                == None,  # Exclude transactions with client transactions
            )
        )
        return self.db.scalars(stmt).all()


def get_min_coin_count(action: TransactionAction) -> int:
    match action:
        case TransactionAction.bridge:
            return 0
        case TransactionAction.income:
            return 1
        case TransactionAction.loan:
            return 1
        case TransactionAction.pool:
            return 2
        case TransactionAction.spend:
            return 1
        case TransactionAction.stake:
            return 1
        case TransactionAction.swap:
            return 2
        case _:
            raise ValueError(f"Invalid action: {action}")


def get_stakes_with_total_amounts(
    transactions: list[Transaction],
    coin_id: int | None = None,
) -> list[schemas.Stake]:
    # In case the same transactions has transfers for multiple assets like with Kamino Finance
    condition = (
        is_stake_asset_transfer
        if coin_id is None
        else lambda transfer: transfer.asset_account.coin_id == coin_id
        and is_stake_asset_transfer(transfer)
    )

    asset_amounts_dict: dict[int, dict[str, dict]] = {}
    for tx in transactions:
        network_id = tx.network_id
        if asset_amounts_dict.get(network_id, None) is None:
            asset_amounts_dict[network_id] = {"network": tx.network, "farms": {}}
        for transfer in tx.transfers:
            if condition(transfer):
                farm = transfer.to_account.farm or transfer.from_account.farm
                if asset_amounts_dict[network_id]["farms"].get(farm.name, None) is None:
                    asset_amounts_dict[network_id]["farms"][farm.name] = {
                        "farm": farm,
                        "asset_account": transfer.asset_account,
                        "amount": Decimal("0.0"),
                    }
                asset_amounts_dict[network_id]["farms"][farm.name][
                    "amount"
                ] += transfer.signed_amount(inverted=True)

    stakes: list[schemas.Stake] = []

    for network_id in asset_amounts_dict.keys():
        for farm_name in asset_amounts_dict[network_id]["farms"].keys():
            stakes.append(
                schemas.Stake(
                    farm=asset_amounts_dict[network_id]["farms"][farm_name]["farm"],
                    asset=asset_amounts_dict[network_id]["farms"][farm_name]["asset_account"],
                    amount=asset_amounts_dict[network_id]["farms"][farm_name]["amount"],
                    network=asset_amounts_dict[network_id]["network"],
                )
            )

    return stakes


def transactions_overview(
    transactions: list[Transaction], *, amount_inverted=False
) -> schemas.TransactionsOverview:
    """Return a TransactionsOverview from the transactions."""
    coin_amount_pairs = (
        (transfer.asset_account.coin, transfer.signed_amount(inverted=amount_inverted))
        for tx in transactions
        for transfer in tx.transfers
        if transfer.asset_account.coin
    )
    coin_amount_pairs = sorted(coin_amount_pairs, key=coin_amount_pair_key)

    coin_totals: list[schemas.CoinTotal] = []
    for _, pairs in itertools.groupby(coin_amount_pairs, coin_amount_pair_key):
        pairs = list(pairs)
        coin, _ = pairs[0]
        total_amount = sum(amount for _, amount in pairs)
        coin_totals.append(schemas.CoinTotal(coin=coin, amount=total_amount))

    return schemas.TransactionsOverview(coin_totals=coin_totals, transactions=transactions)


def try_parse_datetime(fmt: str, value: str) -> datetime | None:
    try:
        return datetime.strptime(value, fmt)
    except ValueError:
        logger.info(f"Failed to parse datetime {value} with format {fmt}")
        return None


def get_datetime_filter(time_query: str):
    # Datetime
    timestamp = try_parse_datetime("%Y-%m-%dT%H:%M:%S%z", time_query)
    if timestamp:
        return (Transaction.confirmed_at == timestamp,)

    # Datetime 2
    timestamp = try_parse_datetime("%m-%d-%Y %H:%M:%S", time_query)
    if timestamp:
        return (Transaction.confirmed_at == timestamp,)

    # Datetime 3
    timestamp = try_parse_datetime("%m-%d-%Y %H:%M", time_query)
    if timestamp:
        logger.info(f"datetime {timestamp}")
        start_time = timestamp.replace(second=0)
        end_time = timestamp + timedelta(minutes=1)
        return (Transaction.confirmed_at >= start_time, Transaction.confirmed_at <= end_time)

    # Date
    timestamp = try_parse_datetime("%m-%Y", time_query)
    if timestamp:
        start_date = timestamp.date()
        end_date = start_date + relativedelta(months=1)
        sql_date = cast(Transaction.confirmed_at, Date)
        return (sql_date >= start_date, sql_date < end_date)

    # Date 2
    timestamp = try_parse_datetime("%m-%d-%Y", time_query)
    if timestamp:
        date = timestamp.date()
        sql_date = cast(Transaction.confirmed_at, Date)
        return (sql_date == date,)

    # Time
    timestamp = try_parse_datetime("%H:%M:%S", time_query)
    if timestamp:
        start_time = timestamp.time().replace(second=0)
        end_time = (timestamp + timedelta(minutes=1)).time()
        sql_time = cast(Transaction.confirmed_at, Time)
        return (sql_time >= start_time, sql_time <= end_time)

    # Time 2
    timestamp = try_parse_datetime("%H:%M", time_query)
    if timestamp:
        start_time = timestamp.time().replace(second=0)
        end_time = (timestamp + timedelta(minutes=1)).time()
        sql_time = cast(Transaction.confirmed_at, Time)
        return (sql_time >= start_time, sql_time <= end_time)

    return None


def is_number(value):
    # Allows to verify for 0.002 float values in the search string
    # and 10,000.02 as well.
    try:
        float(value.replace(",", ""))
    except ValueError:
        return False
    return True


def coin_amount_pair_key(pair: tuple[Coin, Decimal]) -> str:
    return pair[0].uid


def account_amount_pair_key(pair: tuple[Account, Decimal]):
    return pair[0].id


def is_stake_asset_transfer(transfer: Transfer):
    """Return whether the transfer is a stake asset transfer.

    This doesn't check whether the transaction is a stake transaction.
    This only checks the transaction and transfer directions.
    """
    if transfer.transaction.is_deposit:
        # if going from a wallet to the contract
        return transfer.is_withdrawal
    # if going from the contract to a wallet
    return transfer.is_deposit


class WalletManager(BaseManager[Wallet]):
    model = Wallet
    order_by = "Wallet.name"
    schema_add = schemas.AddWallet

    def add_wallet(
        self, schema: schemas.AddWallet, user_id: int | None, *, commit: bool = True
    ):
        networks = NetworkManager(self.db).get_by_ids(schema.network_ids)
        try:
            return self.add_model(
                Wallet(
                    **schema.model_dump(exclude={"network_ids"}),
                    networks=networks,
                    user_id=user_id,
                ),
                commit=commit,
            )
        except IntegrityError as error:
            error_str = str(error.orig)
            field = None
            if "address" in error_str:
                field = "address"
            if "name" in error_str:
                field = "name"
            raise HTTP400Exception(f"A Wallet with that {field} already exists")

    def address_exists(self, address: str) -> bool:
        return self.db.query(exists().where(self.model.address == address)).scalar()

    def edit(self, edit_schema: schemas.EditWallet, user_id: int | None):
        networks = NetworkManager(self.db).get_by_ids(edit_schema.network_ids)
        is_usable_by_clients = edit_schema.is_usable_by_clients
        wallet = self.get_by_id(edit_schema.id)
        if wallet.user_id != user_id:
            raise HTTPUnauthorizedException
        if is_usable_by_clients is not None:
            if is_usable_by_clients is True:
                if self.db.query(
                    exists().where(
                        Wallet.id != wallet.id,
                        Wallet.networks.any(Network.id.in_(edit_schema.network_ids)),
                        Wallet.is_usable_by_clients == True,
                    )
                ).scalar():
                    raise HTTP400Exception(
                        "A Client facing Wallet already exists for one of these Networks"
                    )
            wallet.is_usable_by_clients = edit_schema.is_usable_by_clients
        wallet.name = edit_schema.name
        wallet.networks = networks
        self.db.commit()
        self.db.refresh(wallet)
        return wallet

    def exists_for_user_on_network(self, user_id: int, network_id: int) -> bool:
        return self.db.query(
            exists().where(
                Wallet.user_id == user_id, Wallet.networks.any(Network.id == network_id)
            )
        ).scalar()

    def get_all_active(
        self,
        network: Network | None,
        user_id: int | None = None,
        is_archived: bool | None = None,
    ) -> list[Wallet]:
        query = self.db.query(Wallet).filter(
            Wallet.is_active == True, Wallet.user_id == user_id
        )
        if network:
            query = query.filter(Wallet.networks.contains(network))
        if is_archived is not None:
            query = query.filter(Wallet.is_archived == is_archived)
        return query.all()

    def get_all_active_from_network_names(
        self, network_names: Sequence[str]
    ) -> Sequence[Wallet]:
        return self.db.scalars(
            select(Wallet)
            .distinct(Wallet.id)
            .join(Wallet.networks)
            .where(Wallet.is_active == True, Network.name.in_(network_names))
        ).all()

    def get_active_by_network_address(self, network: Network, address: str) -> Wallet | None:
        return (
            self.db.query(Wallet)
            .filter(Wallet.address.ilike(address), Wallet.networks.contains(network))
            .first()
        )

    def get_by_network_id_and_address(self, network_id: int, address: str) -> Wallet | None:
        return self.db.scalars(
            select(Wallet)
            .join(Wallet.networks)
            .where(Wallet.address == address, Network.id == network_id)
        ).one_or_none()

    def get_deposit_wallet_for_network(self, network_id: int) -> Wallet | None:
        return (
            self.db.query(Wallet)
            .filter(
                Wallet.is_active == True,
                Wallet.is_usable_by_clients == True,
                Wallet.networks.any(Network.id == network_id),
            )
            .one_or_none()
        )

    def archive_wallet(self, wallet_id: int, user_id: int | None) -> Wallet:
        """
        Archive a given user wallet by setting is_archived to True.

        Args:
            wallet_id: The ID of the wallet to archive
            user_id: The ID of the user who owns the wallet

        Returns:
            The updated wallet

        Raises:
            HTTPNotFoundException: If the wallet can't be found
        """
        with self.db.begin_nested():
            wallet = self.db.scalar(
                select(Wallet)
                .where(
                    Wallet.id == wallet_id,
                    Wallet.user_id == user_id,
                )
                .with_for_update(key_share=True)
            )
            if not wallet:
                raise HTTPNotFoundException("Wallet not found")
            wallet.is_archived = True
        return wallet

    def get_or_create_by_address(
        self,
        address: str,
        user_id: int | None = None,
        commit: bool = True,
        ilike: bool = False,
        **kwargs,
    ) -> Wallet:
        stmt = select(Wallet).where(Wallet.user_id == user_id)
        if ilike:
            stmt = stmt.where(Wallet.address.ilike(address))
        else:
            stmt = stmt.where(Wallet.address == address)
        wallet = self.db.scalars(stmt).one_or_none()
        if not wallet:
            # Extract networks from kwargs to handle them after wallet creation
            networks = kwargs.pop("networks", [])
            # Create and add wallet to session first
            wallet = Wallet(address=address, **kwargs)
            self.db.add(wallet)
            self.db.flush()  # Ensure wallet is in session with an ID
            # Now set up networks relationship
            if networks:
                wallet.networks = networks
            if commit:
                self.db.commit()
        return wallet


class CovalentSyncManager(BaseManager[CovalentSync]):
    model = CovalentSync

    def get_or_create(self, chain_name: str, address: str, *, retry=True) -> CovalentSync:
        covalent_sync = self.db.scalar(
            select(CovalentSync).where(
                CovalentSync.chain_name == chain_name, CovalentSync.address == address
            )
        )

        if not covalent_sync:
            try:
                covalent_sync = self.db.scalars(
                    insert(CovalentSync).returning(CovalentSync),
                    {"chain_name": chain_name, "address": address, "latest_page": 0},
                ).one()
            except IntegrityError:
                if retry:
                    return self.get_or_create(chain_name, address, retry=False)
                raise

        return covalent_sync
