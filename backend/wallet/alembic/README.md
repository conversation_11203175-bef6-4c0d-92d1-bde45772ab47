# [Alembic Migrations](https://alembic.sqlalchemy.org/en/latest/index.html)

Alembic migrations are run from the `init` container every time it is run using the
`init.pre_start` script.

Alembic commands need to be run from the `alembic` directory directly or from the `backend`
directory using

```
alembic -c alembic/alembic.ini
```

## [Autogenerate](https://alembic.sqlalchemy.org/en/latest/autogenerate.html)

Models' Base metadata is loaded into alembic using `target_metadata` in the `alembic.env`
script.

New migrations can be autogenerated from model changes using

```
alembic revision --autogenerate --rev-id 0001 -m "Migration Name"
```

**Note**: `--rev-id` argument should be a four digit ascending number based on the previous
migration.

## (Un)Apply migrations

This applies any un-applied migrations

```
alembic upgrade head
```

This un-applies the latest migration

```
alembic downgrade -1
```

## Squash Migrations

https://notes.alexkehayias.com/squash-migrations-using-alembic/
