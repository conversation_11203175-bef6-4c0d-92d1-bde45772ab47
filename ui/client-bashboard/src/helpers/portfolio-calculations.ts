import { isSahaba } from "@/context/env";
import { getCoinDisplayName } from "@/helpers/coin";
import { Dashboard, TransactionForClient } from "@/types/schemas";
import { useMemo } from "react";

/**
 * Calculate appreciation amount based on interest rate
 */
function getAppreciationAmount(
  amount: number,
  startedAt: number,
  isWithdrawal: boolean,
  interestRate: number,
): number {
  const now = Date.now();
  const appreciation =
    amount * ((now - startedAt) / 1000) * (interestRate / 365 / 24 / 60 / 60);
  return isWithdrawal ? appreciation * -1 : appreciation;
}

/**
 * Calculate profit and loss ratio
 */
function getPnlRatio(value: number, cost: number): number | null {
  return value > 0 && cost !== 0 ? (value - cost) / cost : null;
}

export interface DashboardRow {
  readonly id: number;
  readonly amount: number;
  readonly displayName: string;
  readonly name: string;
  readonly ticker: string;
  readonly dollarValue: number;
  readonly price: number;
  readonly pnlRatio: number | null;
  readonly interestAmount: number;
  readonly interestEarnedDollars: number;
  readonly usd24hChange: number | null;
}

/**
 * Process dashboard data to calculate portfolio values
 */
export function processDashboard(dashboard: Dashboard, interestRate: number) {
  const txsByCoinMap = splitTransactionsByCoin(dashboard.transactions);

  const rows: DashboardRow[] = [];
  let totalCost = 0,
    totalInvestment = 0,
    totalInterestEarnedValue = 0,
    totalInitialDeposit = 0,
    lifetimeInterestEarned = 0;
  for (const [coinId, transactions] of txsByCoinMap) {
    const coin = dashboard.coins.find((coin) => coin.id === coinId);
    if (coin == null) continue;

    const priceUsd = isSahaba
      ? 1 // For Sahaba we'll assume that all coins are stablecoins perfectly pegged to 1 USD
      : Number.parseFloat(coin.latestUsdPrice); // Otherwise, use the latest USD price

    let coinAmount = 0,
      coinCost = 0,
      coinInterestEarnedAmount = 0,
      coinLifetimeInterestEarned = 0;
    for (const {
      clientAction,
      confirmedAt,
      costBasisUsd,
      isInterestPayment,
      transfers,
    } of transactions) {
      let amount = 0;
      for (const transfer of transfers) {
        if (transfer.assetAccount.coin == null) continue;
        if (transfer.assetAccount.coin.id !== coinId) continue;

        const transferAmount = Number.parseFloat(transfer.amount);

        switch (clientAction) {
          case "Deposit":
            amount += transferAmount;
            break;
          case "Withdrawal":
            amount -= transferAmount;
            break;
          case "Swap":
            if (transfer.isWithdrawal) {
              if (transfer.assetPriceUsd == null) {
                console.log("assetPriceUsd is null, transfer id:", transfer.id);
                continue;
              }
              amount -= transferAmount;
              coinCost -= transferAmount * Number.parseFloat(transfer.assetPriceUsd);
            } else {
              if (costBasisUsd == null) {
                console.log("costBasisUsd is null, transfer id:", transfer.id);
                continue;
              }
              amount += transferAmount;
              coinCost += transferAmount * Number.parseFloat(costBasisUsd);
            }
            break;
        }
        if (interestRate !== 0) {
          if (isInterestPayment) coinInterestEarnedAmount -= transferAmount;
          else {
            const earnedAmount = getAppreciationAmount(
              transferAmount,
              Date.parse(confirmedAt),
              transfer.isWithdrawal,
              interestRate,
            );
            coinInterestEarnedAmount += earnedAmount;
            coinLifetimeInterestEarned += earnedAmount;
          }
        }
      }
      coinAmount += amount;
    }
    coinCost = Math.max(coinCost, 0);

    const currentValue = coinAmount * priceUsd;
    const coinInterestEarnedValue = coinInterestEarnedAmount * priceUsd;
    const coinLifetimeInterestEarnedValue = coinLifetimeInterestEarned * priceUsd;
    const pnlRatio = getPnlRatio(currentValue, coinCost);

    rows.push({
      id: coinId,
      amount: coinAmount,
      displayName: getCoinDisplayName(coin),
      name: coin.name,
      ticker: coin.ticker.toUpperCase(),
      dollarValue: currentValue,
      price: priceUsd,
      pnlRatio: pnlRatio,
      interestAmount: coinInterestEarnedAmount,
      interestEarnedDollars: coinInterestEarnedValue,
      usd24hChange: coin.usd24hChange,
    });
    totalCost += coinCost;
    totalInvestment += currentValue;
    totalInterestEarnedValue += coinInterestEarnedValue;
    lifetimeInterestEarned += coinLifetimeInterestEarnedValue;
    // Leftover of initial deposit
    if (pnlRatio === null) totalInitialDeposit += currentValue;
  }

  const portfolioDollars = isSahaba
    ? // For Sahaba we use a revenue share model, so no interest here
      totalInvestment
    : // Otherwise, it is the sum of the investment and the interest earned
      totalInvestment + totalInterestEarnedValue;
  const totalUnrealizedGainsPercent =
    getPnlRatio(totalInvestment - totalInitialDeposit, totalCost) ?? 0;

  return {
    interestEarnedDollars: totalInterestEarnedValue,
    portfolioDollars,
    rows,
    totalUnrealizedGainsPercent,
    lifetimeInterestEarned,
  };
}

/**
 * Hook to calculate average cost for a selected coin
 */
export function useAverageCost(
  transactions: readonly TransactionForClient[],
  coinId: number | null,
) {
  const averageCost = useMemo(() => {
    if (!coinId) return null;
    return calculateAverageCost(coinId, transactions);
  }, [transactions, coinId]);

  return averageCost;
}

/**
 * Calculate amount and cost for a coin from transactions
 */
function calculateAmountAndCost(
  coinId: number,
  transactions: readonly TransactionForClient[],
): [number, number] | null {
  let totalAmount = 0;
  let totalCost = 0;
  for (const { costBasisUsd, isInterestPayment, transfers } of transactions) {
    if (costBasisUsd == null || isInterestPayment) continue;
    let amount = 0;
    for (const transfer of transfers) {
      if (transfer.assetAccount.coin == null) continue;
      if (transfer.assetAccount.coin.id !== coinId) continue;

      const transferAmount = Number.parseFloat(transfer.amount);
      if (transfer.isWithdrawal) amount -= transferAmount;
      else amount += transferAmount;
    }
    totalAmount += amount;
    totalCost += amount * Number.parseFloat(costBasisUsd);
  }
  if (totalAmount === 0) return null;
  return [totalAmount, totalCost];
}

/**
 * Calculate average cost for a coin
 */
function calculateAverageCost(
  coinId: number,
  transactions: readonly TransactionForClient[],
): number | null {
  const amountAndCost = calculateAmountAndCost(coinId, transactions);
  if (amountAndCost == null) return null;
  const [totalAmount, totalCost] = amountAndCost;
  if (totalAmount === 0) return null;
  return totalCost / totalAmount;
}

/**
 * Split transactions by coin
 */
function splitTransactionsByCoin(transactions: readonly TransactionForClient[]) {
  const txsByCoinMap = new Map<number, TransactionForClient[]>();

  for (const transaction of transactions) {
    for (const transfer of transaction.transfers) {
      if (transfer.assetAccount.coin == null) continue;

      const coinId = transfer.assetAccount.coin.id;
      if (!transaction.transfers.some((transfer) => transfer.assetAccount.coin?.id === coinId))
        continue;

      let transactions = txsByCoinMap.get(coinId);
      if (transactions == null) {
        transactions = [];
        txsByCoinMap.set(coinId, transactions);
      }

      if (!transactions.some((tx) => tx.id === transaction.id)) transactions.push(transaction);
    }
  }
  return txsByCoinMap;
}
