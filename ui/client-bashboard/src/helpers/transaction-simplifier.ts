import { Coin, Transaction } from "@/types/schemas";

/**
 * Interface for simplified transfers where amounts of the same coin are combined
 */
export interface SimpleTransfer {
  id: number;
  amount: number;
  coin: Coin;
  isWithdrawal: boolean;
}

export type SimpleTransaction<T extends Transaction = Transaction> = Omit<T, "transfers"> & {
  transfers: SimpleTransfer[];
  costBasisUsd?: string | undefined | null;
};

/**
 * Simplify and combine Transfers with the same Coin
 *
 * This function aggregates multiple transfers of the same coin within a transaction
 * into a single transfer with the combined amount.
 *
 * @param transaction The transaction with potentially multiple transfers of the same coin
 * @returns A new transaction object with simplified transfers
 */
export function simplifyTransactions<T extends Transaction>(
  transaction: T,
): SimpleTransaction<T> {
  const coinTransfers: Record<number, SimpleTransfer> = {};

  for (const transfer of transaction.transfers) {
    if (transfer.assetAccount.coin) {
      const amount = transfer.amount;
      const coinId = transfer.assetAccount.coin.id;

      if (coinTransfers[coinId]) {
        // Add to existing transfer of the same coin
        coinTransfers[coinId].amount += Number(amount);
      } else {
        // Create a new entry for this coin
        coinTransfers[coinId] = {
          id: transfer.id,
          amount: Number(amount),
          coin: transfer.assetAccount.coin,
          isWithdrawal: transfer.isWithdrawal,
        };
      }
    }
  }

  // Sort so withdrawals appear first
  return {
    ...transaction,
    transfers: Object.values(coinTransfers).sort((a) => (a.isWithdrawal ? -1 : 1)),
  };
}
