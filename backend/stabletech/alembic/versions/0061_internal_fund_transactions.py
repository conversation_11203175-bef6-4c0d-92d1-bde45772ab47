"""Internal Fund Transactions

Revision ID: 0061
Revises: 0060
Create Date: 2025-01-11 01:00:29.556638

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy import text

from common.conf import settings

# revision identifiers, used by Alembic.
revision = "0061"
down_revision = "0060"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Set has_prices to false for any coins where it's currently null
    op.execute("UPDATE coin SET has_prices = false WHERE has_prices IS NULL")

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "coin",
        sa.Column(
            "market_cap_usd",
            sa.Numeric(precision=32, scale=18),
            server_default="0",
            nullable=False,
        ),
    )
    op.alter_column("coin", "has_prices", existing_type=sa.BOOLEAN(), nullable=False)
    op.create_index(op.f("ix_coin_has_prices"), "coin", ["has_prices"], unique=False)
    op.alter_column("clienttransaction", "user_id", existing_type=sa.INTEGER(), nullable=True)
    # ### end Alembic commands ###

    if settings.enable_pooled_fund:
        # Create ClientTransactions for all classified transactions
        conn = op.get_bind()

        # Get all classified transactions without client transactions
        transactions = conn.execute(
            text(
                """
            WITH first_transfer AS (
                SELECT DISTINCT ON (tr.transaction_id)
                    t.id, t.action, t.confirmed_at, tr.amount, tr.asset_account_id
                FROM transaction t
                JOIN transfer tr ON tr.transaction_id = t.id
                WHERE t.action IS NOT NULL
                ORDER BY tr.transaction_id, tr.id
            )
            SELECT ft.*
            FROM first_transfer ft
            LEFT JOIN clienttransaction ct ON ct.transaction_id = ft.id
            WHERE ct.id IS NULL
        """
            )
        ).fetchall()

        # Create client transactions in bulk
        if transactions:
            # Get coin_ids for all asset accounts at once
            asset_account_ids = [tx.asset_account_id for tx in transactions]
            coin_ids = dict(
                conn.execute(
                    text(
                        """
                        SELECT id, coin_id FROM account WHERE id = ANY(:account_ids)
                        """
                    ),
                    {"account_ids": asset_account_ids},
                ).fetchall()
            )

            # Build values for bulk insert
            values = []
            for tx in transactions:
                coin_id = coin_ids.get(tx.asset_account_id)
                if coin_id:
                    values.append(
                        {
                            "amount": tx.amount,
                            "coin_id": coin_id,
                            "created_at": tx.confirmed_at,
                            "tx_id": tx.id,
                        }
                    )

            if values:
                # Bulk insert all client transactions
                conn.execute(
                    text(
                        """
                    INSERT INTO clienttransaction (
                        amount, coin_id, created_at, network_id, reviewed_at,
                        transaction_id, user_id
                    )
                    SELECT
                        v.amount, v.coin_id, v.created_at, t.network_id, NOW(), t.id, NULL
                    FROM (
                        SELECT UNNEST(:amounts) as amount,
                               UNNEST(:coin_ids) as coin_id,
                               UNNEST(:created_ats) as created_at,
                               UNNEST(:tx_ids) as tx_id
                    ) v
                    JOIN transaction t ON t.id = v.tx_id
                """
                    ),
                    {
                        "amounts": [v["amount"] for v in values],
                        "coin_ids": [v["coin_id"] for v in values],
                        "created_ats": [v["created_at"] for v in values],
                        "tx_ids": [v["tx_id"] for v in values],
                    },
                )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_coin_has_prices"), table_name="coin")
    op.alter_column("coin", "has_prices", existing_type=sa.BOOLEAN(), nullable=True)
    op.drop_column("coin", "market_cap_usd")
    op.alter_column("clienttransaction", "user_id", existing_type=sa.INTEGER(), nullable=False)
    # ### end Alembic commands ###
