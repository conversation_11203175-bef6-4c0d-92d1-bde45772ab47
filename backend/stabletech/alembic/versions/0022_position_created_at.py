"""Position created_at

Revision ID: 0022
Revises: 0021
Create Date: 2023-04-28 06:38:25.551236

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0022"
down_revision = "0021"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "position",
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
    )
    op.create_index(op.f("ix_position_created_at"), "position", ["created_at"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_position_created_at"), table_name="position")
    op.drop_column("position", "created_at")
    # ### end Alembic commands ###
