"""Init

Revision ID: 0001
Revises:
Create Date: 2022-10-29 03:17:29.451677

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0001"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("email", sa.String(), nullable=True),
        sa.Column("is_active", sa.<PERSON>(), nullable=True),
        sa.Column("first_name", sa.String(), nullable=True),
        sa.Column("last_name", sa.String(), nullable=True),
        sa.Column("google_id", sa.String(), nullable=True),
        sa.Column("picture_url", sa.String(), nullable=True),
        sa.Column("role", sa.String(), server_default="client", nullable=False),
        sa.<PERSON>ey<PERSON>onstraint("id"),
    )
    op.create_index(op.f("ix_user_email"), "user", ["email"], unique=True)
    op.create_index(op.f("ix_user_id"), "user", ["id"], unique=False)
    op.create_table(
        "coin",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("has_prices", sa.Boolean(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("ticker", sa.String(), nullable=False),
        sa.Column("uid", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_coin_id"), "coin", ["id"], unique=False)
    op.create_index(op.f("ix_coin_uid"), "coin", ["uid"], unique=True)
    op.create_table(
        "price",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("at", sa.DateTime(), nullable=False),
        sa.Column("coin_id", sa.Integer(), nullable=False),
        sa.Column("dollar_price", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("last_updated_at", sa.DateTime(), nullable=False),
        sa.Column("market_cap", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("price_change_last_day", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("volume_last_day", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_price_at"), "price", ["at"], unique=False)
    op.create_index(op.f("ix_price_coin_id"), "price", ["coin_id"], unique=False)
    op.create_index(op.f("ix_price_id"), "price", ["id"], unique=False)
    op.create_table(
        "position",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("amount", sa.Numeric(precision=32, scale=18), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("started_at", sa.Date(), nullable=False),
        sa.Column("coin_id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["coin_id"], ["coin.id"]),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_position_id"), "position", ["id"], unique=False)
    op.create_index(op.f("ix_position_user_id"), "position", ["user_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_position_user_id"), table_name="position")
    op.drop_index(op.f("ix_position_id"), table_name="position")
    op.drop_table("position")
    op.drop_index(op.f("ix_price_id"), table_name="price")
    op.drop_index(op.f("ix_price_coin_id"), table_name="price")
    op.drop_index(op.f("ix_price_at"), table_name="price")
    op.drop_table("price")
    op.drop_index(op.f("ix_coin_uid"), table_name="coin")
    op.drop_index(op.f("ix_coin_id"), table_name="coin")
    op.drop_table("coin")
    op.drop_index(op.f("ix_user_id"), table_name="user")
    op.drop_index(op.f("ix_user_email"), table_name="user")
    op.drop_table("user")
    # ### end Alembic commands ###
