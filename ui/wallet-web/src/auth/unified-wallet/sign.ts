import { CubeSignerAccount, UnifiedAccount } from "@/auth/accounts";
import { KeyDecoder, signEthereum<PERSON>ith<PERSON>ey, signSolana<PERSON><PERSON><PERSON><PERSON> } from "@/lib/secret-store";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { Hex, type TransactionSerializable } from "viem";

export async function unifiedSign(
  account: UnifiedAccount,
  message: Uint8Array | TransactionSerializable,
  decoder?: KeyDecoder,
): Promise<Uint8Array | Hex> {
  switch (account.provider) {
    case "local":
      if (decoder == null) throw new Error("Unlocking the wallet is required for local keys");
      switch (account.network) {
        case "solana":
          if (!(message instanceof Uint8Array)) {
            throw new TypeError("Solana message must be Uint8Array");
          }
          return await signSolanaWithKey({
            keyId: account.key.id,
            message,
            decoder,
          });
        case "ethereum":
          // For Ethereum, we need to handle TransactionSerializable objects
          if (message instanceof Uint8Array) {
            throw new TypeError("Ethereum message must be TransactionSerializable obj");
          }
          return await signEthereum<PERSON><PERSON><PERSON>ey({
            keyId: account.key.id,
            message,
            decoder,
          });
        default:
          throw new Error(`Unsupported network: ${account.network}`);
      }
    case "cubesigner":
      if (!(message instanceof Uint8Array)) {
        throw new TypeError("Invalid message type for CubeSigner");
      }
      return await signWithCubeSignerAccount(account, message);
    default:
      throw new Error(`Unsupported provider: ${(account as UnifiedAccount).provider}`);
  }
}

async function signWithCubeSignerAccount(
  account: CubeSignerAccount,
  message: Uint8Array,
): Promise<Uint8Array> {
  const signingResp = await account.key.signSolana({
    message_base64: Buffer.from(message).toString("base64"),
  });
  return new Uint8Array(Buffer.from(signingResp.data().signature.slice(2), "hex"));
}
