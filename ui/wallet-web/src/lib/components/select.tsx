"use client";

import * as RadixSelect from "@radix-ui/react-select";
import { cx } from "class-variance-authority";
import { Check, ChevronDown, ChevronUp } from "lucide-react";
import type { UseFormRegisterReturn } from "react-hook-form";

export const Select = RadixSelect.Root;

export function SelectTrigger({
  placeholder,
  className,
  children,
  ...props
}: React.ComponentProps<typeof RadixSelect.Trigger> & { placeholder?: React.ReactNode }) {
  return (
    <RadixSelect.Trigger
      {...props}
      className={cx(
        "inline-flex h-9 w-full items-center justify-between overflow-hidden rounded-md border px-3 text-left text-sm shadow-xs focus:ring-1 focus:ring-ring/60 focus:outline-hidden disabled:cursor-not-allowed disabled:bg-muted disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive data-placeholder:text-muted-foreground dark:focus:ring-ring/80 [&>span]:overflow-hidden [&>span]:text-nowrap [&>span]:text-ellipsis",
        className,
      )}
    >
      <RadixSelect.Value placeholder={placeholder}>{children}</RadixSelect.Value>
      <RadixSelect.Icon className="-mr-1.5 flex-none text-foreground opacity-60">
        <ChevronDown strokeWidth={1} />
      </RadixSelect.Icon>
    </RadixSelect.Trigger>
  );
}

export function SelectScrollUpButton({
  className,
  ...props
}: React.ComponentProps<typeof RadixSelect.ScrollUpButton>) {
  return (
    <RadixSelect.ScrollUpButton
      className={cx("flex cursor-default items-center justify-center py-1", className)}
      {...props}
    >
      <ChevronUp className="h-4 w-4" />
    </RadixSelect.ScrollUpButton>
  );
}

export function SelectScrollDownButton({
  className,
  ...props
}: React.ComponentProps<typeof RadixSelect.ScrollDownButton>) {
  return (
    <RadixSelect.ScrollDownButton
      className={cx("flex cursor-default items-center justify-center py-1", className)}
      {...props}
    >
      <ChevronDown className="h-4 w-4" />
    </RadixSelect.ScrollDownButton>
  );
}

function SelectContent({
  className,
  children,
  position = "item-aligned",
  ...props
}: React.ComponentProps<typeof RadixSelect.Content>) {
  return (
    <RadixSelect.Portal>
      <RadixSelect.Content
        className={cx(
          "relative max-w-[99dvw] min-w-32 overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
          position === "popper" &&
            "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
          className,
        )}
        position={position}
        {...props}
      >
        <SelectScrollUpButton />
        <RadixSelect.Viewport
          className={cx(
            "p-1",
            position === "popper" &&
              "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]",
          )}
        >
          {children}
        </RadixSelect.Viewport>
        <SelectScrollDownButton />
      </RadixSelect.Content>
    </RadixSelect.Portal>
  );
}

export function SelectLabel({
  className,
  ...props
}: React.ComponentProps<typeof RadixSelect.Label>) {
  return (
    <RadixSelect.Label
      className={cx("py-1.5 pr-2 pl-8 text-sm font-semibold", className)}
      {...props}
    />
  );
}

function SelectItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof RadixSelect.Item>) {
  return (
    <RadixSelect.Item
      className={cx(
        "relative flex w-full cursor-default items-center rounded-xs py-1.5 pr-2 pl-7 text-sm outline-hidden select-none focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        <RadixSelect.ItemIndicator>
          <Check className="h-4 w-4" />
        </RadixSelect.ItemIndicator>
      </span>
      <span className="w-full overflow-hidden text-ellipsis">
        <RadixSelect.ItemText>{children}</RadixSelect.ItemText>
      </span>
    </RadixSelect.Item>
  );
}

interface FormSelectProps extends UseFormRegisterReturn {
  id?: string;
  className?: string;
  placeholder?: string;
  invalid?: boolean;
  defaultValue?: string;
  children?: React.ReactNode;
}

/**
 * A select component that works with react-hook-form.
 */
export function FormSelect(props: FormSelectProps) {
  const {
    name,
    onBlur,
    onChange,
    children,
    id,
    className,
    placeholder,
    invalid,
    ref,
    ...rest
  } = props;
  return (
    <Select
      name={name}
      onValueChange={(value) => {
        void onChange({ target: { name, value } });
      }}
      {...rest}
    >
      <SelectTrigger
        id={id}
        ref={ref}
        placeholder={placeholder}
        className={className}
        onBlur={onBlur} // eslint-disable-line @typescript-eslint/no-misused-promises
        aria-invalid={invalid}
      />
      <SelectContent>{children}</SelectContent>
    </Select>
  );
}

export {
  SelectContent as Content,
  SelectItem as Item,
  SelectLabel as Label,
  Select as Root,
  SelectScrollDownButton as ScrollDownButton,
  SelectScrollUpButton as ScrollUpButton,
  SelectTrigger as Trigger,
};
