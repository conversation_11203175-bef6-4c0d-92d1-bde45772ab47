import random
import urllib.parse
from datetime import datetime
from decimal import Decimal
from enum import IntEnum
from inspect import getmembers, ismethod
from typing import Annotated

import httpx
from pydantic import BaseModel, Field, RootModel
from solders.keypair import Keypair
from tenacity import Retrying

from lootloot.heist.bots.schemas import TradeSession, TradeStatus

BASE_URL = "https://api.theheist.game/"


class District(BaseModel):
    """From `GET /location` response (`[].district`).

    Not exhaustive! Check raw response for more fields.
    """

    id: int
    name: str


class Location(BaseModel):
    """From `GET /location` response (array item).

    Not exhaustive! Check raw response for more fields.
    """

    location_type: Annotated[str, Field(alias="type")]
    id: int
    name: str
    risk: str
    isActive: bool
    totalBaselineTokensToEmit: int
    minHeistDurationInSec: int
    lootTripDurationInSec: int
    gorillaTargetCount: int
    districtId: int | None
    district: District | None
    activeChimpCount: int
    activeOrangutanCount: int
    activeGorillaCount: int
    isBusy: bool


LocationsList = RootModel[list[Location]]
"""`GET /location` response."""


class HeistNFT(BaseModel):
    """Represents an NFT in `GET /heist/claim-feed` response.

    For example, present on response's paths:
       - `[].heist.arrestingOfficer`
       - `[].heist.opposingFactionNft`

    Not exhaustive! Check raw response for more fields.
    """

    walletId: str


class HeistClaim(BaseModel):
    """From `GET /heist/claim-feed` response (`[].heist`).

    Not exhaustive! Check raw response for more fields.
    """

    id: int
    startedAt: datetime
    endedAt: datetime
    totalAmountEmitted: Decimal
    totalAmountClaimed: Decimal
    nftId: str
    walletId: str
    locationId: int
    eventId: int
    arrestingOfficerId: str | None
    arrestingOfficer: HeistNFT | None
    isBonusRoll: bool
    secondaryEvent: str | None
    opposingFactionNftId: str | None
    opposingFactionNft: HeistNFT | None


class DeedWallet(BaseModel):
    """From `GET /farming/deed-history` response (`[].wallet`).

    Not exhaustive! Check raw response for more fields.
    """

    id: str


class Deed(BaseModel):
    """From `GET /farming/deed-history` response (`[].deed`).

    Not exhaustive! Check raw response for more fields.
    """

    id: str
    name: str
    isFrozen: bool
    plotId: str


class DeedClaim(BaseModel):
    """From `GET /farming/deed-history` response (array item).

    Not exhaustive! Check raw response for more fields.
    """

    claim_type: Annotated[str, Field(alias="type")]
    totalAmount: Decimal
    createdAt: datetime
    wallet: DeedWallet
    deed: Deed


DeedHistoryList = RootModel[list[list[DeedClaim]]]
"""`GET /farming/deed-history` response."""


class Event(BaseModel):
    """From `GET /location/events` response (array item).

    Not exhaustive! Check raw response for more fields.
    """

    id: int
    name: str
    type: str
    is_bad: bool


# Sourced from https://api.theheist.game/location/events (requires auth)
# on 2024-03-08
HEIST_EVENTS = {
    event.id: event
    for event in (
        Event(id=1, name="10X", type="X10Loot", is_bad=False),
        Event(id=2, name="5X", type="X5Loot", is_bad=False),
        Event(id=3, name="Doubled", type="X2Loot", is_bad=False),
        Event(id=4, name="Expected", type="ExpectedLoot", is_bad=False),
        Event(id=5, name="Fumbled", type="Bribe100", is_bad=True),
        Event(id=6, name="Confiscation", type="LoseItems", is_bad=True),
        Event(id=7, name="Arrested", type="Arrest", is_bad=True),
        Event(id=8, name="Rekt", type="Death", is_bad=True),
        Event(id=9, name="20X", type="X20Loot", is_bad=False),
        Event(id=10, name="3X", type="X3Loot", is_bad=False),
        Event(id=11, name="4X", type="X4Loot", is_bad=False),
        Event(id=12, name="8X", type="X8Loot", is_bad=False),
        Event(id=13, name="16X", type="X16Loot", is_bad=False),
        Event(id=14, name="50X", type="X50Loot", is_bad=False),
        Event(id=-1, name="Captured", type="Capture", is_bad=False),
        Event(id=-2, name="Ambushed", type="Ambush", is_bad=False),
    )
}


class HeistEvent(IntEnum):
    """Heist event's IDs."""

    x10 = 1
    x5 = 2
    x2 = 3
    expected = 4
    fumbled = 5
    confiscation = 6
    arrested = 7
    rekt = 8
    x20 = 9
    x3 = 10
    x4 = 11
    x8 = 12
    x16 = 13
    x50 = 14
    captured = -1
    ambushed = -2


class HeistClient:
    def __init__(self, *, timeout=30, retry: Retrying | None = None) -> None:
        self._client = httpx.Client(base_url=BASE_URL, timeout=timeout)

        if retry:
            # wrap all public methods with retry
            for name, method in getmembers(self, ismethod):
                if name.startswith("_"):
                    continue
                setattr(self, name, retry.wraps(method))

    def __enter__(self) -> "HeistClient":
        self._client.__enter__()
        return self

    def __exit__(self, *args) -> None:
        self._client.__exit__(*args)

    @property
    def cookies(self):
        return self._client.cookies

    def authenticate(self, keypair: Keypair) -> None:
        address = str(keypair.pubkey())

        resp = self._client.get(f"auth/request-message/{address}")
        resp.raise_for_status()
        login_message: str = resp.json()["message"]

        signature = keypair.sign_message(bytes(login_message, "utf-8"))

        resp = self._client.get(f"auth/message-login/{address}/{str(signature)}")
        resp.raise_for_status()

        self._client.cookies.extract_cookies(resp)

    def authenticate_with_keypairs_file(
        self, keypairs_path="lootloot/external/burn_wallets.txt"
    ) -> str:
        """Returns the key's public address."""
        with open(keypairs_path) as f:
            keys_list = f.readlines()
            if not keys_list:
                raise ValueError("No keys found in the file")
            chosen_key = random.choice(keys_list)
            keypair = Keypair.from_base58_string(chosen_key.strip())
        self.authenticate(keypair)
        return str(keypair.pubkey())

    def authenticate_with_private_key(self, private_key: str) -> None:
        keypair = Keypair.from_base58_string(private_key)
        self.authenticate(keypair)

    def accept_friend_request(self, wallet_address: str):
        self._client.put("social/me/friends/accept", json={"walletId": wallet_address})

    def get_claim_feed_raw(self, *, limit: int, offset: int) -> httpx.Response:
        resp = self._client.get("heist/claim-feed", params={"limit": limit, "offset": offset})
        resp.raise_for_status()
        return resp

    def get_deed_history_raw(self, nft_address: str) -> httpx.Response:
        nft_address_escaped = urllib.parse.quote(nft_address, safe="")
        resp = self._client.get(f"farming/deed-history/{nft_address_escaped}")
        resp.raise_for_status()
        return resp

    def get_locations(self) -> list[Location]:
        resp = self._client.get("location")
        resp.raise_for_status()
        return LocationsList.model_validate_json(resp.content).root

    def get_pending_friend_request_addresses(self) -> list[str]:
        resp = self._client.get("social/me/friends/received?searchTerm=&limit=100&offset=0")
        resp.raise_for_status()
        return [friend["id"] for friend in resp.json()]

    def trade_set_fungible(
        self,
        asset_name: str,
        amount: int,
        trade_session_id: int,
    ) -> httpx.Response:
        resp = self._client.put(
            "trading-session/fungible-asset-amount",
            json={
                "tradingSessionId": trade_session_id,
                "tradingSessionFungibleEntry": {
                    "fungibleAssetType": asset_name,
                    "amount": amount,
                    "participant": "Recipient",
                },
            },
        )
        resp.raise_for_status()
        return resp

    def trade_set_non_fungible(
        self,
        asset_type: str,
        asset_id: str,
        trade_session_id: int,
    ) -> httpx.Response:
        resp = self._client.post(
            "trading-session/trading-session-entry",
            json={"tradingSessionId": trade_session_id, f"{asset_type}Id": asset_id},
        )
        resp.raise_for_status()
        return resp

    def trade_update_status(
        self, trade_session: TradeSession, status: TradeStatus
    ) -> httpx.Response:
        trade_session_data = trade_session.model_dump(mode="json")

        json_data = {
            "tradingSessionId": trade_session_data["id"],
            "tradingSessionFungibleEntries": trade_session_data[
                "tradingSessionFungibleEntries"
            ],
            "tradingSessionNonFungibleEntries": [
                {
                    "id": entry.id,
                    "consumableId": entry.consumableId,
                    "cosmeticId": entry.cosmeticId,
                    "itemId": entry.itemId,
                }
                for entry in trade_session.tradingSessionNonFungibleEntries
            ],
            "tradingSessionParticipantStatus": status.value,
        }
        resp = self._client.put(
            "trading-session/trading-session-status",
            json=json_data,
        )
        resp.raise_for_status()
        return resp
