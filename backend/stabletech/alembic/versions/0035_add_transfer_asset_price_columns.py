"""Add transfer asset price columns

Revision ID: 0035
Revises: 0034
Create Date: 2023-09-13 15:43:06.263674

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0035"
down_revision = "0034"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "transfer",
        sa.Column("asset_price_usd", sa.Numeric(precision=32, scale=18), nullable=True),
    )
    op.add_column(
        "transfer",
        sa.Column("asset_prices_json", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("transfer", "asset_prices_json")
    op.drop_column("transfer", "asset_price_usd")
    # ### end Alembic commands ###
