from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Named<PERSON>uple, Sequence

from sqlalchemy import delete, func, insert, literal, select
from sqlalchemy.dialects.postgresql import JSONPATH
from sqlalchemy.dialects.postgresql import insert as postgresql_insert

from common.conf import logger
from common.helius.models import NFT as HeliusNFT
from lootloot import schemas
from lootloot.base import BaseManager
from lootloot.models import NFT, NFTCollection, NFTListing, UserWallet


class OwnedNFTCollection(NamedTuple):
    collection: NFTCollection
    nft_count: int
    image_url: str


class NFTCollectionManager(BaseManager):
    def get_all(self) -> Sequence[NFTCollection]:
        return self.db.scalars(select(NFTCollection)).all()

    def get_all_public(self) -> list[schemas.PublicNFTCollectionListItem]:
        result = self.db.execute(
            select(
                NFTCollection.uid,
                NFTCollection.name,
                select(func.count(NFT.id))
                .where(NFT.nft_collection_uid == NFTCollection.uid, ~NFT.is_rekt_sql())
                .scalar_subquery()
                .label("nft_count"),
            )
        )
        return [
            schemas.PublicNFTCollectionListItem(uid=uid, name=name, nft_count=nft_count)
            for uid, name, nft_count in result
        ]

    def list_owned(self, address_uris: list[str]) -> list[OwnedNFTCollection]:
        """Get NFT collections that have at least one NFT owned by the given address_uris.

        It also returns the number of NFTs owned by collection. And the image_url of the
        first NFT owned in each collection.
        """
        # Aggregate the number of NFTs owned by collection.
        stmt = (
            select(
                NFTCollection.uid.label("collection_uid"), func.count(NFT.id).label("nft_count")
            )
            .join(NFT, NFT.nft_collection_uid == NFTCollection.uid)
            .where(
                NFT.owner_address_uri.in_(address_uris),
                NFT.nft_collection_uid == NFTCollection.uid,
            )
            .group_by(NFTCollection.id)
            .cte("collection_count")
        )
        # Get the first owned NFT image_url by collection.
        stmt = (
            select(stmt.c.collection_uid, stmt.c.nft_count, NFT.image_url)
            .distinct(stmt.c.collection_uid)
            .join_from(NFT, stmt, NFT.nft_collection_uid == stmt.c.collection_uid)
            .where(NFT.owner_address_uri.in_(address_uris))
            .order_by(stmt.c.collection_uid, NFT.id)
            .cte("collection_count_and_image_url")
        )
        # Get the NFTCollections data ordered by name.
        stmt = (
            select(NFTCollection, stmt.c.nft_count, stmt.c.image_url)
            .join_from(NFTCollection, stmt, NFTCollection.uid == stmt.c.collection_uid)
            .order_by(NFTCollection.name)
        )

        result = self.db.execute(stmt)
        return [OwnedNFTCollection(*row) for row in result]

    def exists(self, nft_collection_uid: str) -> bool:
        result = self.db.scalar(
            select(NFTCollection.id).where(NFTCollection.uid == nft_collection_uid)
        )
        if result:
            return True
        return False


class NFTManager(BaseManager):
    def get(self, address_uri: str) -> NFT | None:
        return self.db.scalar(select(NFT).where(NFT.address_uri == address_uri))

    def get_count_for_user(self, user_id: int) -> int:
        return self.db.scalars(
            select(func.count(NFT.id))
            .where(UserWallet.user_id == user_id)
            .join(UserWallet, NFT.owner_address_uri == UserWallet.address_uri_sql())
            .group_by(UserWallet.user_id)
        ).one()

    def get_from_collection_and_owner(
        self, *, collection_uid: str, owner_address_uris: list[str]
    ) -> Sequence[NFT]:
        return self.db.scalars(
            select(NFT)
            .where(
                NFT.nft_collection_uid == collection_uid,
                NFT.owner_address_uri.in_(owner_address_uris),
            )
            .order_by(NFT.id)
        ).all()

    def save_nfts_from_helius(
        self, nft_collection: NFTCollection, helius_nfts: list[HeliusNFT]
    ):
        if not helius_nfts:
            return

        stmt = postgresql_insert(NFT)
        stmt = stmt.on_conflict_do_update(
            index_elements=(NFT.address_uri,),
            set_={
                "attributes_data": stmt.excluded.attributes_data,
                "image_url": stmt.excluded.image_url,
                "is_burnt": stmt.excluded.is_burnt,
                "is_frozen": stmt.excluded.is_frozen,
                "uid": stmt.excluded.uid,
                "owner_address_uri": stmt.excluded.owner_address_uri,
            },
        )
        self.db.execute(
            stmt,
            [
                {
                    "attributes_data": [
                        attribute.model_dump(mode="json")
                        for attribute in helius_nft.content.metadata.attributes
                    ],
                    "image_url": helius_nft.content.links.image,
                    "uid": helius_nft.content.metadata.name,
                    "is_burnt": helius_nft.burnt,
                    "is_frozen": helius_nft.ownership.frozen,
                    "nft_collection_uid": nft_collection.uid,
                    "address_uri": f"solana:{helius_nft.id}",
                    "owner_address_uri": f"solana:{helius_nft.ownership.owner}",
                }
                for helius_nft in helius_nfts
            ],
        )
        self.db.commit()
        logger.info(f"Saving NFT collections from Helius collection={nft_collection.uid}")

    def get_nft_collection_count(self, nft_collection_uid: str) -> int:
        return self.db.scalars(
            select(func.count(NFT.id)).where(NFT.nft_collection_uid == nft_collection_uid)
        ).one()

    def delete_missing_nfts(self, address_uris: list[str]):
        """Deletes NFTs that are not in the given address_uris."""
        nfts_to_delete = self.db.scalars(
            select(NFT.address_uri).where(NFT.address_uri.not_in(address_uris))
        ).all()
        if not nfts_to_delete:
            return
        self.db.execute(
            delete(NFTListing).where(NFTListing.nft_address_uri.in_(nfts_to_delete))
        )
        self.db.execute(delete(NFT).where(NFT.address_uri.in_(nfts_to_delete)))
        self.db.commit()
        logger.info(f"Deleted NFTs by address_uris={address_uris}")

    def get_address_uris_from_collection(self, nft_collection_uid: str):
        return self.db.scalars(
            select(NFT.address_uri).where(NFT.nft_collection_uid == nft_collection_uid)
        )

    def get_heist_wardrobes(self) -> list[schemas.HeistWardrobe]:
        ZERO = literal(0, literal_execute=True)
        attributes_columns = [
            func.jsonb_path_query(
                NFT.attributes_data,
                literal(f'$[*] ? (@.traitType == "{trait}").value', JSONPATH),
            )
            .op("->>")(ZERO)
            .label(trait.lower())
            for trait in ("Species", "Background", "Mouth", "Fur", "Clothing", "Head")
        ]

        nft_attrs = (
            select(*attributes_columns)
            .where(NFT.nft_collection_uid.in_(("theheist", "the_heist_generations")))
            .cte("nft_attrs")
        )

        stmt = (
            select(
                nft_attrs.c.species,
                func.array_agg(nft_attrs.c.background.distinct()).label("background"),
                func.array_agg(nft_attrs.c.mouth.distinct()).label("mouth"),
                func.array_agg(nft_attrs.c.fur.distinct()).label("fur"),
                func.array_agg(nft_attrs.c.clothing.distinct()).label("clothing"),
                func.array_agg(nft_attrs.c.head.distinct()).label("head"),
            )
            .group_by(nft_attrs.c.species)
            .order_by(nft_attrs.c.species)
        )

        result = self.db.execute(stmt)
        return [
            schemas.HeistWardrobe(
                species=row.species,
                background=row.background,
                mouth=row.mouth,
                fur=row.fur,
                clothing=row.clothing,
                head=row.head,
            )
            for row in result
        ]


class NFTListingManager(BaseManager):
    def create(self, add_nft_listing: schemas.AddNFTListing) -> NFTListing:
        return (
            self.db.scalars(
                insert(NFTListing).returning(NFTListing),
                add_nft_listing.model_dump(),
            )
            .unique()
            .one()
        )

    def get_nft_collections_sol_prices(self, uids: list[str] = None) -> dict[str, Decimal]:
        """Get the floor SOL prices for NFT collections.

        In case `uids` are provided, it will only return the prices for the
        given `uids`. Otherwise, it will return the prices for all collections.
        In both cases it will only consider the listings from the last 24 hours.
        """
        if not uids:
            uids = []
        stmt = (
            select(NFTListing.nft_collection_uid, func.min(NFTListing.sol_price))
            .where(NFTListing.at > (datetime.now(timezone.utc) - timedelta(days=1)))
            .group_by(NFTListing.nft_collection_uid)
        )

        if uids:
            stmt = stmt.where(NFTListing.nft_collection_uid.in_(uids))

        rows = self.db.execute(stmt)
        return {row[0]: row[1] for row in rows.t}
