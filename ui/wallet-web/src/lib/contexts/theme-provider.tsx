import { createContext, use, useEffect, useState } from "react";

type Theme = "dark" | "light" | "system";

interface ThemeProviderProps {
  readonly children: React.ReactNode;
  readonly defaultTheme?: Theme;
  readonly storageKey?: string;
}

type ThemeProviderValue = readonly [current: Theme, set: (theme: Theme) => void];

const ThemeProviderContext = createContext<ThemeProviderValue | null>(null);
ThemeProviderContext.displayName = "ThemeProvider";

export function useTheme() {
  const context = use(ThemeProviderContext);
  if (context == null) throw new Error("useTheme must be used within a ThemeProvider");
  return context;
}

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "ui-theme",
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) ?? defaultTheme) as Theme,
  );

  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove("light", "dark");

    let className: "dark" | "light";
    if (theme === "system") {
      className = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    } else {
      className = theme;
    }

    root.classList.add(className);
  }, [theme]);

  const value: ThemeProviderValue = [
    theme,
    (newTheme: Theme) => {
      localStorage.setItem(storageKey, newTheme);
      setTheme(newTheme);
    },
  ];

  return <ThemeProviderContext value={value}>{children}</ThemeProviderContext>;
}
