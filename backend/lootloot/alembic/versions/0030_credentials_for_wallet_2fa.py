"""Credentials for wallet 2fa

Revision ID: 0030
Revises: 0029
Create Date: 2024-11-12 16:29:40.720007

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0030"
down_revision = "0029"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "walletuser",
        sa.Column(
            "credentials_id",
            sa.Uuid(),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
        ),
    )
    op.create_index(
        op.f("ix_walletuser_credentials_id"), "walletuser", ["credentials_id"], unique=True
    )
    op.create_table(
        "credential",
        sa.Column("id", sa.LargeBinary(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("public_key", sa.LargeBinary(), nullable=False),
        sa.Column("backed_up", sa.<PERSON>(), nullable=False),
        sa.Column("device_type", sa.String(), nullable=False),
        sa.Column("transports", postgresql.ARRAY(sa.String()), nullable=False),
        sa.Column("sign_count", sa.BigInteger(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "latest_use_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("user_credentials_id", sa.Uuid(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_credentials_id"],
            ["walletuser.credentials_id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_credential_user_credentials_id"),
        "credential",
        ["user_credentials_id"],
        unique=False,
    )
    op.create_table(
        "ongoingcredentialauthentication",
        sa.Column("challenge", sa.LargeBinary(), nullable=False),
        sa.Column("require_user_verification", sa.Boolean(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["walletuser.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_ongoingcredentialauthentication_user_id"),
        "ongoingcredentialauthentication",
        ["user_id"],
        unique=True,
    )
    op.create_table(
        "ongoingcredentialregistration",
        sa.Column("challenge", sa.LargeBinary(), nullable=False),
        sa.Column("require_user_verification", sa.Boolean(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["walletuser.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_ongoingcredentialregistration_user_id"),
        "ongoingcredentialregistration",
        ["user_id"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_ongoingcredentialregistration_user_id"),
        table_name="ongoingcredentialregistration",
    )
    op.drop_table("ongoingcredentialregistration")
    op.drop_index(
        op.f("ix_ongoingcredentialauthentication_user_id"),
        table_name="ongoingcredentialauthentication",
    )
    op.drop_table("ongoingcredentialauthentication")
    op.drop_index(op.f("ix_credential_user_credentials_id"), table_name="credential")
    op.drop_table("credential")
    op.drop_index(op.f("ix_walletuser_credentials_id"), table_name="walletuser")
    op.drop_column("walletuser", "credentials_id")
    # ### end Alembic commands ###
