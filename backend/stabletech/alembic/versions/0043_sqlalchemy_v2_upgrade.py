"""sqlalchemy v2 upgrade

Revision ID: 0043
Revises: 0042
Create Date: 2023-12-13 09:32:37.621807

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0043"
down_revision = "0042"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("coin", "uid", existing_type=sa.VARCHAR(), nullable=False)
    op.alter_column(
        "transaction",
        "comment",
        existing_type=sa.VARCHAR(),
        nullable=False,
        existing_server_default=sa.text("''::character varying"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "transaction",
        "comment",
        existing_type=sa.VARCHAR(),
        nullable=True,
        existing_server_default=sa.text("''::character varying"),
    )
    op.alter_column("coin", "uid", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###
