from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail
from sentry_sdk import capture_exception, new_scope

from common.conf import logger, settings


class StubClient:
    def send(self, message: Mail) -> None:
        logger.info("Skipped email send: %s", message.get())


_client: SendGridAPIClient | StubClient | None = None


def _create_client() -> SendGridAPIClient | StubClient:
    if settings.is_local and not settings.sendgrid_api_key:
        logger.info("Sendgrid is not configured in local environment, using stub client")
        return StubClient()
    assert settings.sendgrid_api_key, "Sendgrid API key is not configured"
    return SendGridAPIClient(settings.sendgrid_api_key)


def _send(message: Mail) -> None:
    global _client
    if _client is None:
        _client = _create_client()
    _client.send(message)


def send_email(message: Mail, *, raise_on_except=False) -> None:
    """Send an email message.

    On non-local environments, exceptions are caught and logged, but not raised by
    default. This is to prevent errors in email sending from affecting the rest of
    the application.
    """
    with new_scope() as scope:
        scope.set_context("email", message.get())
        try:
            _send(message)
        except Exception as e:
            capture_exception(e)
            if raise_on_except or settings.is_local:
                raise
