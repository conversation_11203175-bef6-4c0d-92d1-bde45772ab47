import Spinner from "@/@core/components/spinner";
import { DataGridTable } from "@/components/DataGridTable";
import DepositWithdrawDialog from "@/components/DepositWithdrawDialog";
import { SnackSuccess } from "@/components/SnackSuccess";
import { formatDate, formatDecimal } from "@/helpers/formatters";
import { useClientTransactionsPendingQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import {
  Alert,
  Button,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Stack,
  Typography,
} from "@mui/material";
import { useState } from "react";

const DepositWithdrawOld = () => {
  const { data: pendingClientTransactions, error } = useClientTransactionsPendingQuery();

  const [addPane, setAddPane] = useState<"Deposit" | "Withdrawal" | null>(null);
  const [showSuccessSnack, setShowSuccessSnack] = useState(false);

  if (error != null) {
    return (
      <Alert severity="error">
        {getErrorDetail(error, { fallback: "Failed loading your pending transactions" })}
      </Alert>
    );
  }
  if (!pendingClientTransactions) return <Spinner />;

  const pendingTransactionsHeader = (
    <CardHeader
      title="Pending Transactions"
      action={
        <Stack spacing={2} direction="row">
          <Button
            color="success"
            onClick={() => setAddPane(addPane === "Deposit" ? null : "Deposit")}
            variant="outlined"
          >
            Deposit
          </Button>
          <Button
            color="error"
            onClick={() => setAddPane(addPane === "Withdrawal" ? null : "Withdrawal")}
            variant="outlined"
          >
            Withdraw
          </Button>
        </Stack>
      }
    />
  );

  const table = (
    <DataGridTable
      rows={pendingClientTransactions}
      tableColumns={[
        {
          field: "isDeposit",
          header: "Action",
          renderCell: (params) =>
            params.value ? (
              <Typography color={"success.dark"}>Deposit</Typography>
            ) : (
              <Typography color={"error.dark"}>Withdraw</Typography>
            ),
        },
        {
          field: "amount",
          header: "Amount",
          renderCell: ({ row }) => formatDecimal(Number.parseFloat(row.amount)),
        },
        { field: "coin", header: "Coin", renderCell: ({ row }) => row.coin.name },
        {
          field: "createdAt",
          header: "Submitted On",
          renderCell: ({ row }) => formatDate(row.createdAt),
        },
      ]}
    />
  );

  return (
    <Grid container spacing={4}>
      <SnackSuccess
        showSuccessSnack={showSuccessSnack}
        setShowSuccessSnack={setShowSuccessSnack}
      />

      <Grid item xs={12}>
        <Card>
          {pendingTransactionsHeader}
          <CardContent>{table}</CardContent>
        </Card>
      </Grid>

      {addPane && (
        <DepositWithdrawDialog
          isDeposit={addPane == "Deposit"}
          handleClose={() => setAddPane(null)}
        />
      )}
    </Grid>
  );
};

DepositWithdrawOld.acl = {
  action: "read",
  subject: "Position",
};

export { DepositWithdrawOld };
