import Spinner from "@/@core/components/spinner";
import FilteredByCoinDisplay from "@/components/admin/FilteredByCoinDisplay";
import TransactionsTable, {
  transfersMapperCombined,
} from "@/components/admin/TransactionsTable";
import { currencyFormatter, formatPercentage } from "@/helpers/formatters";
import { useAssetHoldingsQuery, useAssetTransactionsQuery } from "@/store/services/api";
import { AssetHolding } from "@/types/schemas";
import { Card, CardContent, CardHeader, Grid, Stack, Typography } from "@mui/material";
import { DataGridPro, GridColDef, GridInitialState } from "@mui/x-data-grid-pro";
import { useCallback, useRef, useState } from "react";

const INITIAL_STATE: GridInitialState = {
  sorting: { sortModel: [{ field: "valueUsd", sort: "desc" }] },
};

const AssetHoldingsPage = () => {
  const [selectedCoinId, setSelectedCoinId] = useState<number | null>(null);
  const { data: assetHoldings, isLoading } = useAssetHoldingsQuery();
  const { data: transactions } = useAssetTransactionsQuery(selectedCoinId || 0, {
    skip: !selectedCoinId,
  });

  const transactionsRef = useRef<HTMLDivElement>(null);

  const handleRowClick = useCallback(
    ({ row }: { row: AssetHolding }) => {
      const newSelectedId = row.coinId === selectedCoinId ? null : row.coinId;
      setSelectedCoinId(newSelectedId);

      if (newSelectedId && transactionsRef.current) {
        transactionsRef.current.scrollIntoView({ behavior: "smooth" });
      }
    },
    [selectedCoinId],
  );

  const selectedCoin = assetHoldings?.assets.find((asset) => asset.coinId === selectedCoinId);

  const columns: GridColDef<AssetHolding>[] = [
    {
      field: "coinName",
      headerName: "Asset",
      flex: 1,
      renderCell: (params) => (
        <span>
          {params.row.coinName}{" "}
          <Typography component="span" color="text.secondary">
            ({params.row.coinTicker.toUpperCase()})
          </Typography>
        </span>
      ),
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params) => currencyFormatter.format(Number(params.value)),
      sortComparator: (v1, v2) => Number(v1) - Number(v2),
    },
    {
      field: "priceUsd",
      headerName: "Price",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params) => currencyFormatter.format(Number(params.value)),
      sortComparator: (v1, v2) => Number(v1) - Number(v2),
    },
    {
      field: "valueUsd",
      headerName: "$ Value",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params) => currencyFormatter.format(Number(params.value)),
      sortComparator: (v1, v2) => Number(v1) - Number(v2),
    },
    {
      field: "change24h",
      headerName: "24h Change",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params) => {
        const change = params.value as number | null;
        return (
          <Typography
            sx={{
              color:
                change === null ? "text.primary" : change >= 0 ? "success.main" : "error.main",
            }}
          >
            {change === null ? "-" : formatPercentage(change / 100)}
          </Typography>
        );
      },
    },
  ];

  if (isLoading || !assetHoldings) {
    return (
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Spinner />
        </Grid>
      </Grid>
    );
  }

  return (
    <Grid container spacing={6}>
      {/* Total Value Card */}
      <Grid item md={6} xs={12}>
        <Card sx={{ textAlign: "center" }}>
          <CardHeader title="Σ Assets Value" />
          <CardContent>
            <Typography variant="h4">
              $ {currencyFormatter.format(Number(assetHoldings.totalValueUsd))}
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* 24h Change Card */}
      {assetHoldings.total24hChange !== null && (
        <Grid item md={6} xs={12}>
          <Card sx={{ textAlign: "center" }}>
            <CardHeader title="Average 24H Change" />
            <CardContent>
              <Typography
                variant="h4"
                sx={{
                  color: assetHoldings.total24hChange >= 0 ? "success.main" : "error.main",
                }}
              >
                {formatPercentage(assetHoldings.total24hChange / 100)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      )}

      {/* Assets Table */}
      <Grid item xs={12}>
        <Card>
          <CardHeader title="Assets" />
          <CardContent>
            <DataGridPro
              autoHeight
              columns={columns}
              rows={assetHoldings.assets}
              getRowId={(row) => row.coinId}
              initialState={INITIAL_STATE}
              selectionModel={selectedCoinId ? [selectedCoinId] : []}
              onRowClick={handleRowClick}
            />
          </CardContent>
        </Card>
      </Grid>

      {/* Transactions Table */}
      <Grid item xs={12} ref={transactionsRef}>
        <Card>
          <CardHeader
            title={
              <Stack direction="row" gap={4} alignItems="flex-end">
                <span>Transactions</span>
                {selectedCoin && (
                  <FilteredByCoinDisplay
                    selectedCoin={{
                      id: selectedCoin.coinId,
                      name: selectedCoin.coinName,
                      ticker: selectedCoin.coinTicker,
                      uid: selectedCoin.coinTicker.toLowerCase(),
                      isUsableByClients: true,
                      latestUsdPrice: selectedCoin.priceUsd,
                      latestUsdPriceUpdatedAt: null,
                      usd24hChange: selectedCoin.change24h,
                    }}
                    averageCost={null}
                    onClearSelectedCoin={() => setSelectedCoinId(null)}
                  />
                )}
              </Stack>
            }
          />
          <CardContent>
            {transactions && (
              <TransactionsTable
                rows={transactions}
                transfersMapper={transfersMapperCombined}
              />
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

AssetHoldingsPage.acl = {
  action: "read",
  subject: "admin-asset-holdings",
};

export default AssetHoldingsPage;
