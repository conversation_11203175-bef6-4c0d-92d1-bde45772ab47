from datetime import datetime, timezone

from sqlalchemy.orm import Session

from stabletech.indexer.crud import PoolPriceAlertManager
from stabletech.utils import slack


def trigger_price_alerts(db: Session):
    for price_alert in PoolPriceAlertManager(db).get_all_in_alert():
        price_alert.last_triggered_at = datetime.now(timezone.utc)
        slack.post_message(
            slack.stable_tech_channel(),
            (
                f"Price alert for {price_alert.coin.name} ({price_alert.coin.ticker}) — "
                f"current price $ {price_alert.coin.latest_usd_price} — "
                f"range ({price_alert.min_price_usd}, {price_alert.max_price_usd})"
            ),
        )
    db.commit()
