import { CloseOutlined, Delete } from "@mui/icons-material";
import {
  <PERSON>,
  But<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  FormGroup,
  Grid,
  IconButton,
  Slide,
  Stack,
  Switch,
  TextField,
  Typography,
} from "@mui/material";
import { GridCellParams } from "@mui/x-data-grid-pro";

import Spinner from "@/@core/components/spinner";
import { ConfirmationDialog, useConfirmationDialog } from "@/components/ConfirmationDialog";
import { useState } from "react";

import { DataGridTable, TableColumns } from "@/components/DataGridTable";
import { NetworksSelect } from "@/components/NetworksSelect";
import { Toast, useToast } from "@/components/Toast";
import { CoinSearch } from "@/components/autocomplete/CoinSearch";
import { CancelButton } from "@/components/button/CancelButton";
import { useModal } from "@/hooks/modal";
import {
  useIndexerNetworksQuery,
  useMarketCoinAddMutation,
  useMarketCoinDeleteMutation,
  useMarketCoinUpdateHasPricesMutation,
  useMarketCoinsWithPricesQuery,
} from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Coin, CoinWithNetworks, Network } from "@/types/schemas";

interface SubmitAddCoin {
  name: string;
  ticker: string;
  uid: string;
}

interface EditState {
  open: boolean;
  coin: CoinWithNetworks | null;
  update: Partial<CoinWithNetworks>;
}

const INITIAL_EDIT_STATE: EditState = {
  open: false,
  coin: null,
  update: {},
};

const Coins = () => {
  const tableColumns: TableColumns[] = [
    { field: "id", header: "ID" },
    { field: "name", header: "Coin" },
    {
      field: "ticker",
      header: "Ticker",
      renderCell: (params: GridCellParams) => params.value.toUpperCase(),
    },
    {
      field: "uid",
      header: "UID",
    },
    {
      field: "isUsableByClients",
      header: "Usable By Clients",
      type: "boolean",
    },
    {
      field: "networks",
      header: "Networks",
      sortable: false,
      renderCell: (params: GridCellParams) =>
        params.value.map((network: Network) => (
          <Box key={network.name} pl={2}>
            <Chip label={network.name} />
          </Box>
        )),
    },
  ];

  const { data: coinsData } = useMarketCoinsWithPricesQuery({});
  const [addCoin] = useMarketCoinAddMutation();
  const [deleteCoin] = useMarketCoinDeleteMutation();
  const [updateCoin] = useMarketCoinUpdateHasPricesMutation();

  const [submitData, setSubmitData] = useState<SubmitAddCoin>({
    name: "",
    ticker: "",
    uid: "",
  });
  const [openCoinAdd, setOpenCoinAdd] = useState(false);
  const [openCoinDelete, setOpenCoinDelete] = useState(false);
  const [openEnableCoin, setOpenEnableCoin] = useState(false);
  const [coinSelected, setCoinSelected] = useState<Coin | null>(null);

  const editModal = useModal(INITIAL_EDIT_STATE);
  const coinToEdit = editModal.state.coin;
  const coinUpdate = editModal.state.update;
  const setCoinUpdate = <K extends keyof CoinWithNetworks>(
    key: K,
    value: CoinWithNetworks[K],
  ) => {
    editModal.setState((state) => ({
      ...state,
      update: { ...state.update, [key]: value },
    }));
  };

  const [confirmProps, confirm] = useConfirmationDialog();

  const [toastProps, toast] = useToast();

  const clearData = () => {
    setCoinSelected(null);
    setOpenEnableCoin(false);
    setSubmitData({ name: "", ticker: "", uid: "" });
    setOpenCoinAdd(false);
    setOpenCoinDelete(false);
  };

  const cancelButton = <CancelButton onClick={clearData} />;

  if (!coinsData) {
    return <Spinner />;
  }

  return (
    <Grid container spacing={6}>
      <Grid item xs>
        <Card>
          <CardContent>
            <Grid container spacing={6}>
              <Grid item xs={4}>
                <Typography variant={"h4"}> Supported Coins </Typography>
              </Grid>
              <Grid item xs={8} display={"Grid"} justifyContent={"end"}>
                <Stack direction={"row"} justifyContent={"end"} spacing={4}>
                  <Button
                    onClick={() => {
                      clearData();
                      setOpenCoinAdd(!openCoinAdd);
                    }}
                  >
                    {openCoinAdd ? "-" : "+"} Add Coin
                  </Button>
                  <Button
                    onClick={() => {
                      clearData();
                      setOpenCoinDelete(!openCoinDelete);
                    }}
                  >
                    {openCoinDelete ? "-" : "+"} Delete Coin
                  </Button>
                  <Button
                    onClick={() => {
                      clearData();
                      setOpenEnableCoin(!openEnableCoin);
                    }}
                  >
                    {openEnableCoin ? "-" : "+"} Enable Coin
                  </Button>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <DataGridTable
                  hideIdColumn
                  onRowClick={(params: GridCellParams<unknown, CoinWithNetworks>) => {
                    editModal.setState({ open: true, coin: params.row, update: {} });
                  }}
                  rows={coinsData}
                  sortByField={"name"}
                  tableColumns={tableColumns}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs="auto">
        <Slide direction={"left"} in={openEnableCoin} mountOnEnter unmountOnExit>
          <Card sx={{ width: 400 }}>
            <CardHeader
              title={"Enable a Coin's Prices"}
              action={
                <Button color={"error"}>
                  <CloseOutlined onClick={clearData} />
                </Button>
              }
            />

            <CardContent>
              <Stack spacing={6}>
                <CoinSearch selectedCoin={coinSelected} setSelectedCoin={setCoinSelected} />
                <Stack direction={"row"} spacing={4}>
                  <Button
                    disabled={!coinSelected}
                    color={"success"}
                    fullWidth={true}
                    variant={"outlined"}
                    onClick={() => {
                      if (coinSelected) {
                        updateCoin({
                          id: coinSelected.id,
                          hasPrices: true,
                        })
                          .unwrap()
                          .then(() => {
                            clearData();
                            toast({
                              message: "Coin prices enabled successfully",
                              severity: "success",
                              timeout: 3000,
                            });
                          })
                          .catch((error: unknown) => {
                            toast({
                              message: `Failed to enable coin prices: ${getErrorDetail(error)}`,
                              severity: "error",
                            });
                          });
                      }
                    }}
                  >
                    Update
                  </Button>
                  {cancelButton}
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Slide>

        <Slide direction="left" in={openCoinAdd} mountOnEnter unmountOnExit>
          <Card sx={{ width: 400 }}>
            <CardHeader
              title={"Add a Coin not on Coingecko"}
              action={
                <Button color={"error"}>
                  <CloseOutlined onClick={clearData} />
                </Button>
              }
            />

            <CardContent>
              <Stack spacing={6}>
                <TextField
                  label={"Name"}
                  onChange={(event) => {
                    setSubmitData({ ...submitData, name: event.target.value });
                  }}
                  value={submitData.name}
                />
                <TextField
                  label={"Ticker"}
                  onChange={(event) => {
                    setSubmitData({ ...submitData, ticker: event.target.value });
                  }}
                  value={submitData.ticker}
                />
                <TextField
                  label={"UID"}
                  onChange={(event) => {
                    setSubmitData({ ...submitData, uid: event.target.value });
                  }}
                  value={submitData.uid}
                />
                <Stack direction={"row"} spacing={4}>
                  <Button
                    disabled={!submitData.name || !submitData.ticker || !submitData.uid}
                    color={"success"}
                    fullWidth={true}
                    variant={"outlined"}
                    onClick={() => {
                      addCoin(submitData)
                        .unwrap()
                        .then(() => {
                          clearData();
                          toast({
                            message: "Coin added successfully",
                            severity: "success",
                            timeout: 3000,
                          });
                        })
                        .catch(() => {
                          toast({
                            message: "Coin with provided UID already exists",
                            severity: "error",
                          });
                        });
                    }}
                  >
                    Add
                  </Button>
                  {cancelButton}
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Slide>

        <Slide direction="left" in={openCoinDelete} mountOnEnter unmountOnExit>
          <Card sx={{ width: 400 }}>
            <CardHeader
              title="Delete Coin"
              action={
                <Button color={"error"}>
                  <CloseOutlined onClick={clearData} />
                </Button>
              }
            />

            <CardContent>
              <Stack spacing={6}>
                <CoinSearch selectedCoin={coinSelected} setSelectedCoin={setCoinSelected} />
                <Stack direction={"row"} spacing={4}>
                  <Button
                    disabled={!coinSelected}
                    color="error"
                    fullWidth
                    variant="outlined"
                    onClick={() => {
                      if (!coinSelected) return;
                      deleteCoin(coinSelected.id)
                        .unwrap()
                        .then(() => {
                          clearData();
                          toast({
                            message: "Coin deleted",
                            severity: "info",
                            timeout: 3000,
                          });
                        })
                        .catch((error: unknown) => {
                          toast({
                            message: `Failed to delete coin: ${getErrorDetail(error)}`,
                            severity: "error",
                          });
                        });
                    }}
                  >
                    Delete
                  </Button>
                  <Button color="info" variant="outlined" onClick={clearData}>
                    Cancel
                  </Button>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Slide>
      </Grid>

      <Dialog
        open={editModal.state.open}
        onClose={(_, reason) => {
          if (reason === "backdropClick") return; // ignore backdrop click
          editModal.close();
        }}
      >
        <DialogTitle>
          {coinToEdit?.name} ({coinToEdit?.ticker.toUpperCase()})
        </DialogTitle>
        <DialogContent>
          <Stack gap={4}>
            <FormGroup>
              <FormControlLabel
                control={<Switch />}
                label="Usable By Clients"
                checked={coinUpdate.isUsableByClients ?? coinToEdit?.isUsableByClients ?? false}
                onChange={(_, checked) => {
                  setCoinUpdate("isUsableByClients", checked);
                }}
              />
              <FormControlLabel
                control={<Switch />}
                label="Has Prices"
                checked={coinUpdate.hasPrices ?? coinToEdit?.hasPrices ?? false}
                onChange={(_, checked) => {
                  setCoinUpdate("hasPrices", checked);
                }}
              />
            </FormGroup>
            <TextField
              label="Name"
              value={coinUpdate.name ?? coinToEdit?.name}
              sx={{ mt: 1 }}
              onChange={(event) => {
                setCoinUpdate("name", event.target.value);
              }}
            />
            <TextField
              label="Ticker"
              value={coinUpdate.ticker ?? coinToEdit?.ticker.toUpperCase()}
              onChange={(event) => {
                setCoinUpdate("ticker", event.target.value);
              }}
            />
            <TextField
              label="UID"
              value={coinUpdate.uid ?? coinToEdit?.uid}
              onChange={(event) => {
                setCoinUpdate("uid", event.target.value);
              }}
            />
            <NetworksSelect
              getNetworksMutation={useIndexerNetworksQuery}
              selectedNetworks={coinUpdate.networks ?? coinToEdit?.networks ?? []}
              setSelectedNetworks={(networks) => setCoinUpdate("networks", networks)}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <IconButton
            aria-label="Delete coin"
            title="Delete coin"
            color="error"
            onClick={() => {
              if (!coinToEdit) return;
              confirm({
                title: "Delete Coin",
                message: `Are you sure you want to delete ${coinToEdit.name} (${coinToEdit.ticker.toUpperCase()})?`,
                action: () =>
                  deleteCoin(coinToEdit.id)
                    .unwrap()
                    .then(() => {
                      editModal.close();
                      toast({ message: "Coin deleted", severity: "info", timeout: 3000 });
                    })
                    .catch((error: unknown) => {
                      toast({
                        message: `Unable to delete the coin: ${getErrorDetail(error)}`,
                        severity: "error",
                      });
                    }),
              });
            }}
            sx={{ marginRight: "auto" }}
          >
            <Delete />
          </IconButton>
          <Button variant="contained" color="secondary" onClick={editModal.close}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (!coinToEdit) return;
              updateCoin({
                id: coinToEdit.id,
                hasPrices: coinUpdate.hasPrices,
                isUsableByClients: coinUpdate.isUsableByClients,
                name: coinUpdate.name,
                networkIds: coinUpdate.networks?.map((network) => network.id),
                ticker: coinUpdate.ticker,
                uid: coinUpdate.uid,
              })
                .unwrap()
                .then(() => {
                  editModal.close();
                  toast({
                    message: "Coin updated successfully",
                    severity: "success",
                    timeout: 3000,
                  });
                })
                .catch((error: unknown) => {
                  toast({
                    message: `Failed to update coin: ${getErrorDetail(error)}`,
                    severity: "error",
                  });
                });
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <Toast {...toastProps} />

      <ConfirmationDialog {...confirmProps} />
    </Grid>
  );
};

Coins.acl = {
  action: "manage",
  subject: "Coin",
};

export default Coins;
