from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from common.conf import settings
from common.psql import DatabaseSession
from stabletech.auth.crud import AuthTokenManager
from stabletech.auth.models import User


def authenticate(
    db: DatabaseSession,
    credential: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
) -> User:
    auth_token = AuthTokenManager(db).get_by_token(credential.credentials)

    if not auth_token:
        if settings.is_local:
            # Allow using any value for the token when running locally for debug purposes
            # Authenticates using the configured ADMIN_EMAIL
            user = db.query(User).filter_by(email=settings.admin_email).first()
            if user:
                return user

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": 'Bearer realm="auth_required"'},
            detail="Invalid Token",
        )

    return auth_token.user


depends_authentication = Depends(authenticate)

CurrentUser = Annotated[User, depends_authentication]


def admin_role(user: CurrentUser):
    if not user or not user.is_admin:
        raise HTTPException(status_code=403, detail="Permission Denied")


depends_admin_role = Depends(admin_role)
