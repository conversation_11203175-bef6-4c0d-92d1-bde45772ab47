"use client";

import { AddWalletButton } from "@/app/wallets/add-wallet";
import { TransfersList } from "@/app/wallets/transfers-list";
import { useWalletSearch } from "@/app/wallets/wallet-search";
import { AlertError } from "@/lib/components/alert";
import { Button, button } from "@/lib/components/button";
import { ConfirmationButton } from "@/lib/components/confirmation-dialog";
import { IndexTransfersButton } from "@/lib/components/index-transfers-button";
import { InputClearable } from "@/lib/components/input";
import { Spinner } from "@/lib/components/spinner";
import { isProduction } from "@/lib/environment";
import { getErrorMessage } from "@/lib/error";
import { cyrb53a } from "@/lib/hashing";
import { useDeleteWallet, useWalletsQuery } from "@/lib/wallets";
import { cx } from "class-variance-authority";
import { ArrowLeft, CircleX } from "lucide-react";
import { useState } from "react";

const EMPTY_WALLETS = [] as const;

export default function Wallets() {
  const walletsQuery = useWalletsQuery();
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const allWallets = walletsQuery.data?.items ?? EMPTY_WALLETS;

  const { search, setSearch, wallets } = useWalletSearch(allWallets);

  const deleteWallet = useDeleteWallet();

  const selectedWalletData = selectedWallet
    ? walletsQuery.data?.items.find((w) => w.address === selectedWallet)
    : null;

  if (selectedWallet) {
    if (walletsQuery.isLoading) {
      return <Spinner className="mt-12 flex justify-center" size={50} priority />;
    }

    if (walletsQuery.isError) {
      return <AlertError>{getErrorMessage(walletsQuery.error)}</AlertError>;
    }

    if (!selectedWalletData) {
      return (
        <div>
          <Button
            variant="ghost"
            onClick={() => {
              setSelectedWallet(null);
            }}
            className="mb-4 px-2!"
            aria-label="Go back"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="p-4">Wallet not found</div>
        </div>
      );
    }

    return (
      <main className="px-3 py-6">
        <div className="mb-6">
          <div className="mb-4 flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => {
                setSelectedWallet(null);
              }}
              className="px-2!"
              aria-label="Go back"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="mb-1 text-2xl">{selectedWalletData.name}</h1>
              <p className="font-mono text-sm text-gray-500">{selectedWalletData.address}</p>
            </div>
          </div>
          {!isProduction() && (
            <div className="max-w-lg">
              <IndexTransfersButton />
            </div>
          )}
        </div>

        {!isProduction() && <TransfersList address={selectedWallet} />}
      </main>
    );
  }

  const walletsContent = walletsQuery.isLoading ? (
    <Spinner className="mt-12 flex justify-center" size={50} priority />
  ) : walletsQuery.isError ? (
    <AlertError>{getErrorMessage(walletsQuery.error)}</AlertError>
  ) : wallets.length === 0 ? (
    <>
      <p className="mt-12 text-center">No wallets found.</p>
      {search && (
        <p className="text-center">
          <Button
            variant="link"
            onClick={() => {
              setSearch("");
            }}
          >
            Clear search
          </Button>
        </p>
      )}
    </>
  ) : (
    <ul className="flex max-w-lg flex-col gap-4">
      {wallets.map((wallet) => (
        <li key={wallet.address}>
          <button
            area-label="Select wallet"
            title="Select wallet"
            type="button"
            className="group relative flex w-full cursor-pointer items-center gap-3 rounded-lg p-2 text-left"
            onClick={() => {
              if (!isProduction()) setSelectedWallet(wallet.address);
            }}
          >
            <div
              aria-hidden
              className={cx(
                "grid h-10 w-10 shrink-0 place-items-center rounded-full leading-none font-medium",
                colors[cyrb53a(wallet.address) % colors.length],
              )}
            >
              {getInitials(wallet.name)}
            </div>
            <dl className="flex-1 overflow-x-hidden">
              <dt className="sr-only">Name</dt>
              <dd className="overflow-hidden text-sm font-medium text-ellipsis">
                {wallet.name}
              </dd>
              <dt className="sr-only">Address</dt>
              <dd className="overflow-hidden text-xs font-light text-ellipsis">
                {wallet.address}
              </dd>
            </dl>

            <ConfirmationButton
              asChild
              action={async () =>
                deleteWallet.mutateAsync({
                  networkGroup: wallet.network_group,
                  address: wallet.address,
                })
              }
              message={
                <span>
                  Delete <em>{wallet.name}</em> with address{" "}
                  <code className="break-words">{wallet.address}</code> ?
                </span>
              }
              title="Confirm wallet deletion"
            >
              <Button variant="ghost" size="icon">
                <CircleX strokeWidth={1} />
              </Button>
            </ConfirmationButton>
          </button>
        </li>
      ))}
    </ul>
  );

  return (
    <main className="px-3 py-6">
      <h1 className="mb-4 text-2xl">Wallets</h1>
      <InputClearable
        className="mb-4 max-w-lg"
        placeholder="Search wallet's address or name"
        value={search}
        onChange={(event) => {
          setSearch(event.target.value);
        }}
        onClear={() => {
          setSearch("");
        }}
        clearHidden={!search}
      />
      <AddWalletButton
        className={button({ variant: "ghost", className: "mb-4 px-2.5! font-normal!" })}
      />
      {walletsContent}
    </main>
  );
}

function getInitials(name: string): string {
  const segments = name.split(/[^0-9A-Za-z]+/u);
  const first = segments[0];
  const last = segments.length > 1 ? segments.at(-1) : undefined;
  if (!first) return "–";
  if (!last) {
    const [char0, char1] = first;
    if (!char1) return `${char0}`.toUpperCase();
    return `${char0}${char1}`.toUpperCase();
  }
  return `${first[0]}${last[0]}`.toUpperCase();
}

const colors = [
  "text-white bg-slate-500",
  "text-white bg-zinc-500",
  "text-white bg-stone-600",
  "text-white bg-red-500",
  "text-white bg-orange-500",
  "text-white bg-amber-500",
  "text-white bg-yellow-500",
  "text-white bg-lime-500",
  "text-white bg-green-500",
  "text-white bg-emerald-500",
  "text-white bg-teal-500",
  "text-white bg-cyan-500",
  "text-white bg-sky-500",
  "text-white bg-blue-500",
  "text-white bg-indigo-500",
  "text-white bg-violet-500",
  "text-white bg-purple-500",
  "text-white bg-fuchsia-500",
  "text-white bg-pink-500",
  "text-white bg-rose-500",
  "text-black bg-slate-300",
  "text-black bg-zinc-200",
  "text-black bg-stone-300",
  "text-black bg-red-300",
  "text-black bg-orange-300",
  "text-black bg-amber-200",
  "text-black bg-yellow-300",
  "text-black bg-lime-200",
  "text-black bg-green-200",
  "text-black bg-emerald-200",
  "text-black bg-teal-200",
  "text-black bg-cyan-200",
  "text-black bg-sky-200",
  "text-black bg-indigo-200",
  "text-black bg-violet-300",
  "text-black bg-purple-200",
  "text-black bg-fuchsia-300",
  "text-black bg-pink-200",
  "text-black bg-rose-200",
];
