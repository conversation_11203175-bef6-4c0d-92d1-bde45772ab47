import { useAddTransactionMutation, useDepositAddressQuery } from "@/api/wallets";
import { Alert } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import * as Dialog from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { SelectCombobox } from "@/components/ui/select-combobox";
import { Spinner } from "@/components/ui/spinner";
import { getErrorDetail } from "@/store/services/helpers";
import { Coin, Network } from "@/types/schemas";
import { valibotResolver } from "@hookform/resolvers/valibot";
import { UseQueryResult } from "@tanstack/react-query";
import { useId, useState } from "react";
import { Control, Controller, useForm, useFormState, useWatch } from "react-hook-form";
import * as v from "valibot";

const formSchema = v.object({
  amount: v.pipe(
    v.string(),
    v.minLength(1, "Amount is required"),
    v.decimal("Amount must be a number"),
    v.check((value) => Number.parseFloat(value) > 0, "Amount must be a positive number"),
  ),
  coinId: v.pipe(v.number(), v.integer(), v.gtValue(0, "Coin is required")),
  networkId: v.pipe(v.number(), v.integer(), v.gtValue(0, "Network is required")),
});

type TransactionFormData = v.InferOutput<typeof formSchema>;

export interface DepositWithdrawDialogProps {
  isDeposit: boolean;
  children: React.ReactNode;
  coins: Coin[];
  networks: Network[];
}

export function DepositWithdrawDialog(props: DepositWithdrawDialogProps) {
  const id = useId();
  const [isOpen, setIsOpen] = useState(false);

  const {
    handleSubmit,
    formState: { errors },
    watch,
    reset,
    control,
  } = useForm({
    resolver: valibotResolver(formSchema),
    defaultValues: {
      amount: "",
      coinId: 0,
      networkId: 0,
    },
  });

  // Get the networkId for the deposit address query
  const networkId = watch("networkId");

  // Fetch deposit address
  const depositAddressQuery = useDepositAddressQuery(networkId);

  const addTransactionMutation = useAddTransactionMutation();

  const handleOpenChange = (open: boolean) => {
    if (open) {
      reset();
      addTransactionMutation.reset();
      setIsOpen(true);
    } else if (!addTransactionMutation.isPending) {
      setIsOpen(false);
    }
  };

  const onSubmit = (data: TransactionFormData) => {
    addTransactionMutation.mutate({
      amount: data.amount,
      coinId: data.coinId,
      isDeposit: props.isDeposit,
      networkId: data.networkId,
    });
  };

  // Convert coins and networks to options for SelectCombobox
  const coinOptions = props.coins.map((coin) => ({
    label: `${coin.name} (${coin.ticker.toUpperCase()})`,
    value: coin.id.toString(),
  }));

  const networkOptions = props.networks.map((network) => ({
    label: network.name,
    value: network.id.toString(),
  }));

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.Trigger asChild>{props.children}</Dialog.Trigger>
      <Dialog.Content
        title={props.isDeposit ? "Deposit" : "Withdraw"}
        aria-describedby=""
        width="lg"
      >
        {addTransactionMutation.isSuccess ? (
          <div className="py-4">
            <Alert variant="success">Your request has been submitted successfully.</Alert>
            <div className="mt-4 flex justify-end">
              <Dialog.Close asChild>
                <Button>Close</Button>
              </Dialog.Close>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
            {addTransactionMutation.error && (
              <Alert variant="error" onClear={() => addTransactionMutation.reset()}>
                {getErrorDetail(addTransactionMutation.error, {
                  fallback: `Failed to ${props.isDeposit ? "deposit" : "withdraw"}`,
                })}
              </Alert>
            )}
            {props.isDeposit && depositAddressQuery.error && (
              <Alert variant="error">
                {getErrorDetail(depositAddressQuery.error, {
                  fallback: "Couldn't get deposit address",
                })}
              </Alert>
            )}

            <div className="space-y-4">
              <div>
                <label htmlFor={`amount-${id}`} className="mb-2 block text-sm font-medium">
                  Amount
                </label>
                <Controller
                  name="amount"
                  control={control}
                  disabled={addTransactionMutation.isPending}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id={`amount-${id}`}
                      placeholder="Enter amount"
                      onChange={(e) => {
                        // Remove all commas from input
                        let raw = e.target.value.replaceAll(",", "");
                        // Remove left zeros
                        raw = raw.replace(/^0+(?=\d)/u, "");
                        // Only allow valid number input (allow decimals)
                        if (/^\d*\.?\d*$/.test(raw)) {
                          field.onChange(raw);
                        }
                      }}
                      value={formatAmount(field.value)}
                    />
                  )}
                />
                {errors.amount && (
                  <p className="mt-1 text-xs text-destructive">{errors.amount.message}</p>
                )}
              </div>

              <div>
                <label htmlFor={`coin-${id}`} className="mb-2 block text-sm font-medium">
                  Coin
                </label>
                <Controller
                  name="coinId"
                  control={control}
                  disabled={addTransactionMutation.isPending}
                  render={({ field: { ref, value, disabled, onChange, onBlur } }) => (
                    <SelectCombobox
                      ref={ref}
                      id={`coin-${id}`}
                      options={coinOptions}
                      placeholder="Select coin"
                      value={value.toString()}
                      disabled={disabled}
                      onValueChange={(newValue) => {
                        onChange(Number.parseInt(newValue));
                      }}
                      onBlur={onBlur}
                      emptyText="No coins found"
                      searchText="Search coins..."
                    />
                  )}
                />
                {errors.coinId && (
                  <p className="mt-1 text-xs text-destructive">{errors.coinId.message}</p>
                )}
              </div>

              <div>
                <label htmlFor={`network-${id}`} className="mb-2 block text-sm font-medium">
                  Network
                </label>
                <Controller
                  name="networkId"
                  control={control}
                  disabled={addTransactionMutation.isPending}
                  render={({ field: { ref, value, disabled, onChange, onBlur } }) => (
                    <SelectCombobox
                      ref={ref}
                      id={`network-${id}`}
                      options={networkOptions}
                      placeholder="Select network"
                      value={value.toString()}
                      disabled={disabled}
                      onValueChange={(newValue) => {
                        onChange(Number.parseInt(newValue));
                      }}
                      onBlur={onBlur}
                      emptyText="No networks found"
                      searchText="Search networks..."
                    />
                  )}
                />
                {errors.networkId && (
                  <p className="mt-1 text-xs text-destructive">{errors.networkId.message}</p>
                )}
              </div>
            </div>

            <TransactionSummary
              isDeposit={props.isDeposit}
              depositAddressQuery={depositAddressQuery}
              control={control}
              coins={props.coins}
              networks={props.networks}
            />

            <div className="mt-4 flex justify-end gap-2">
              <Dialog.Close asChild>
                <Button variant="outline" disabled={addTransactionMutation.isPending}>
                  Cancel
                </Button>
              </Dialog.Close>
              <Button
                type="submit"
                disabled={
                  (props.isDeposit &&
                    (depositAddressQuery.isFetching || depositAddressQuery.isError)) ||
                  addTransactionMutation.isPending
                }
              >
                {addTransactionMutation.isPending ? (
                  <>
                    <Spinner className="mr-2 size-4" />
                    {props.isDeposit ? "Processing..." : "Processing..."}
                  </>
                ) : props.isDeposit ? (
                  "Confirm Deposit Sent"
                ) : (
                  "Confirm Withdrawal"
                )}
              </Button>
            </div>
          </form>
        )}
      </Dialog.Content>
    </Dialog.Root>
  );
}

/**
 * Formats an amount input with comma separators for thousands
 *
 * @param value - The raw amount value to format
 * @returns The formatted amount with commas as thousand separators
 */
function formatAmount(value: string) {
  if (!value) return "";
  // Split integer and decimal parts
  const [int, dec = ""] = value.split(".");

  // Format integer part with commas - use regex for comma positioning
  // We need to use replace with regex for thousands separators positioning
  const intFormatted = int.replaceAll(/\B(?=(\d{3})+(?!\d))/gu, ",");

  // Ensure a trailing dot is preserved (e.g., "123.") or include the decimal part if present
  return value.endsWith(".") || dec ? `${intFormatted}.${dec}` : intFormatted;
}

interface TransactionSummaryProps {
  isDeposit: boolean;
  depositAddressQuery: UseQueryResult<string | null>;
  control: Control<TransactionFormData>; // Properly typed control
  coins: Coin[];
  networks: Network[];
}

/**
 * Display transaction summary for deposit or withdrawal
 */
function TransactionSummary(props: TransactionSummaryProps) {
  const { isDeposit, depositAddressQuery, control, coins, networks } = props;

  const { isValid } = useFormState({ control });

  const [amount, coinId, networkId] = useWatch({
    control,
    name: ["amount", "coinId", "networkId"],
  });

  // Format amount with commas
  const amountFormatted = formatAmount(amount);

  // Get coin and network objects based on their IDs
  const selectedCoin = coins.find((coin) => coin.id === coinId);
  const selectedNetwork = networks.find((network) => network.id === networkId);

  // Return nothing if form is invalid or coin/network not defined
  if (!isValid || !selectedCoin || !selectedNetwork) {
    return null;
  }

  return (
    <div className="mt-2 rounded-md bg-muted/50 p-4">
      {isDeposit ? (
        depositAddressQuery.isFetching ? (
          <p className="text-center">Loading deposit address...</p>
        ) : depositAddressQuery.data ? (
          <div className="space-y-3">
            <p className="text-center">
              Please send{" "}
              <span className="text-warning-foreground font-medium">
                {amountFormatted} {selectedCoin.ticker.toUpperCase()}
              </span>{" "}
              to the following{" "}
              <span className="text-warning-foreground font-medium">
                {selectedNetwork.name}
              </span>{" "}
              address:
            </p>
            <p className="rounded bg-background p-2 text-center font-mono text-sm break-all">
              {depositAddressQuery.data}
            </p>
          </div>
        ) : (
          <p className="text-center">Unable to load deposit address</p>
        )
      ) : (
        <div className="space-y-2 text-center">
          <p>Begin processing a withdrawal on {selectedNetwork.name} for</p>
          <p className="font-medium text-destructive">
            {amountFormatted} {selectedCoin.ticker.toUpperCase()}
          </p>
          <p className="text-sm text-muted-foreground">
            Please note it might take up to 10 business days to process.
          </p>
        </div>
      )}
    </div>
  );
}
