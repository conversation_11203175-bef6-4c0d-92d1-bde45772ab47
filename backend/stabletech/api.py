import json
import logging

from fastapi import FastAP<PERSON>, Response
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError, ResponseValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from common import conf
from stabletech.accounting.api import router as accounting_router
from stabletech.auth.api import router as auth_router
from stabletech.client.api import router as client_router
from stabletech.indexer.api_admin import router as indexer_router
from stabletech.indexer.api_public import router as indexer_public_router
from stabletech.indexer.internal_sync.api import router as internal_sync_router
from stabletech.indexer.internal_sync.api_admin import router as internal_sync_admin_router
from stabletech.market.api import router as market_router
from stabletech.public.api_public import router as public_router
from stabletech.utils.fastapi import depends_admin_role, depends_authentication

app = FastAPI(
    docs_url=(
        "/docs" if conf.settings.is_local else None
    )  # Disable docs site when not running locally
)
app.include_router(auth_router, prefix="/auth")
app.include_router(client_router, prefix="/client", dependencies=[depends_authentication])
app.include_router(indexer_router, prefix="/indexer/admin", dependencies=[depends_admin_role])

if conf.settings.is_stabletech or conf.settings.is_local:
    app.include_router(
        internal_sync_admin_router,
        prefix="/indexer/admin/internal-sync",
        dependencies=[depends_admin_role],
    )
app.include_router(internal_sync_router, prefix="/indexer/internal-sync")
app.include_router(indexer_public_router, prefix="/indexer-public")
app.include_router(public_router, prefix="/public")
app.include_router(market_router, prefix="/market", dependencies=[depends_authentication])
app.include_router(accounting_router, prefix="/accounting", dependencies=[depends_admin_role])
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_headers=["*"],
    allow_methods=["*"],
    allow_origins=["*"],
)


def log_pydantic_error(exception, logger, detail):
    """Log Pydantic validation errors internally and return a standard response error."""
    logger(
        f"API error: {json.dumps(jsonable_encoder(exception.__dict__['_errors']), indent=2)}"
    )
    return JSONResponse(content={"detail": detail}, status_code=500)


@app.exception_handler(RequestValidationError)
async def request_validation_exception_handler(request, exception):
    """Log as info as the first error is exposed directly to the User for now."""
    return log_pydantic_error(
        exception,
        conf.logger.info,
        f"{exception.errors()[0]['msg']}: {exception.errors()[0]['loc'][-1]}",
    )


@app.exception_handler(ResponseValidationError)
async def response_validation_exception_handler(request, exception):
    """Log as error since these should not occur expectedly."""
    return log_pydantic_error(exception, conf.logger.error, "Internal validation error")


@app.router.get("/")
def health_check():
    return Response("", status_code=200)


class AccessLoggerFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        method = record.args[1]  # type: ignore
        route = record.args[2]  # type: ignore
        # filter-out health checks
        return not (method == "GET" and route == "/")


logging.getLogger("uvicorn.access").addFilter(AccessLoggerFilter())


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("stabletech.api:app", host="localhost", port=8080, reload=True)
