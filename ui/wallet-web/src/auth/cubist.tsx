import * as cs from "@cubist-labs/cubesigner-sdk";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useCubeSignerAuth, useCubeSignerClient } from "@/auth/cubesigner-auth-store";
import { CUBESIGNER_KEYS_QUERY_KEY } from "@/auth/cubesigner-keys";
import { CubistTOTP } from "@/auth/cubist-totp";
import { apiMutation } from "@/lib/api";
import { AlertError } from "@/lib/components/alert";
import { Button } from "@/lib/components/button";
import { Spinner } from "@/lib/components/spinner";
import { getErrorMessage } from "@/lib/error";

export function CubistInfo() {
  const cubistAuth = useCubeSignerAuth();
  const cubeSignerClient = useCubeSignerClient();

  const getCubistUser = useQuery({
    queryKey: ["cubist", "user", cubeSignerClient],
    queryFn: async () => {
      if (!cubeSignerClient) return null;
      return await cubeSignerClient.user();
    },
  });

  const cubistUser: cs.UserInfo | undefined | null = getCubistUser.data;

  const queryClient = useQueryClient();

  const cubistCreateKey = useMutation({
    mutationFn: async (keyType: cs.Ed25519 | cs.Secp256k1) => {
      return await apiMutation<cs.CreateKeyImportKeyResponse>({
        method: "POST",
        resource: "cubist/create/key",
        body: {
          key_type: keyType as string,
        },
      });
    },
    onSettled: () => {
      void queryClient.invalidateQueries({ queryKey: CUBESIGNER_KEYS_QUERY_KEY });
    },
  });

  return (
    <div>
      <h3 className="mt-4 mb-1 text-center text-lg">Cubist Info</h3>
      {cubistAuth.status === "new" ? (
        <div className="flex justify-center py-4">
          <Spinner size={32} />
        </div>
      ) : cubistAuth.status === "mfa-required" ? (
        <CubistTOTP />
      ) : cubistAuth.status === "signed-out" ? (
        <div className="text-center">
          <p>You&apos;re signed out of CubeSigner</p>
        </div>
      ) : null}
      {cubistUser && (
        <div className="mx-auto max-w-sm overflow-hidden rounded-lg">
          <div className="py-2 text-center">
            <h3 className="mb-1 text-2xl font-bold">{cubistUser.name}</h3>
            <div className="inline-flex items-center text-sm">
              <p className="text-sm">{cubistUser.email}</p>
            </div>
            <div className="inline-flex items-center text-sm">
              <p className="text-sm">{cubistUser.user_id}</p>
            </div>
            {cubistUser.orgs.map((org) => (
              <div className="inline-flex items-center text-sm" key={org.org_id}>
                <p className="text-sm">{org.org_id}</p>
                {org.status === "enabled" ? (
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">
                    <span className="me-1 h-2 w-2 rounded-full bg-green-500"></span>
                    Enabled
                  </span>
                ) : (
                  <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-300">
                    <span className="me-1 h-2 w-2 rounded-full bg-red-500"></span>
                    Disabled
                  </span>
                )}
              </div>
            ))}
          </div>
          <div className="py-2 text-center">
            <Button
              onClick={() => {
                cubistCreateKey.mutate(cs.Ed25519.Solana);
              }}
            >
              Create Key
            </Button>
          </div>
        </div>
      )}
      <div className="grid justify-center gap-2">
        {cubistCreateKey.error && (
          <AlertError>Error creating key: {getErrorMessage(cubistCreateKey.error)}</AlertError>
        )}
      </div>
    </div>
  );
}
