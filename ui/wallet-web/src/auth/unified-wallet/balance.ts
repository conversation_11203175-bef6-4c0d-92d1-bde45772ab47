import { UnifiedAccount } from "@/auth/accounts";
import { EthereumRPC } from "@/lib/networks/ethereum-rpc";
import { SolanaRPC } from "@/lib/networks/solana-rpc";

export async function getBalance(account: UnifiedAccount) {
  let rpc: SolanaRPC | EthereumRPC;
  switch (account.network) {
    case "solana":
      rpc = SolanaRPC.create("devnet");
      break;
    case "ethereum":
      rpc = EthereumRPC.create("sepolia");
      break;
    default:
      throw new Error("Unknown network");
  }
  return await rpc.getBalance(account.address);
}
