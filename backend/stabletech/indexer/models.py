from datetime import datetime, timezone
from decimal import Decimal
from typing import Any

from sqlalchemy import Column, DateTime, ForeignKey, Numeric, Table
from sqlalchemy.dialects.postgresql import JSON, JSONB
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy.schema import Index, UniqueConstraint
from sqlalchemy.sql.functions import coalesce

from common.models import BaseModel
from stabletech.auth.models import User
from stabletech.indexer.enums import JobKind, JobStatus, TransactionAction


class Base(DeclarativeBase, BaseModel):
    pass


coin_network_association_table = Table(
    "coin_network_association",
    Base.metadata,
    Column("coin_id", ForeignKey("coin.id"), primary_key=True),
    Column("network_id", ForeignKey("network.id"), primary_key=True),
)


class AliasCoin(Base):
    coin_id: Mapped[int] = mapped_column(ForeignKey("coin.id", use_alter=True), index=True)
    coin: Mapped["Coin"] = relationship(foreign_keys=[coin_id], lazy="selectin")
    aliased_coins: Mapped[list["Coin"]] = relationship(
        back_populates="alias_coin", foreign_keys=lambda: Coin.alias_coin_id
    )

    def __repr__(self) -> str:
        return f"AliasCoin(coin={self.coin!r}, aliased_coins={self.aliased_coins!r})"


class Coin(Base):
    alias_coin_id: Mapped[int | None] = mapped_column(ForeignKey(AliasCoin.id))
    alias_coin: Mapped[AliasCoin | None] = relationship(
        back_populates="aliased_coins", foreign_keys=alias_coin_id
    )
    # Enables fetching Prices from CoinGecko
    has_prices: Mapped[bool] = mapped_column(default=False, server_default="false", index=True)
    # Allows a Coin to be deposited by Clients
    is_usable_by_clients: Mapped[bool | None] = mapped_column(
        server_default="false", index=True
    )
    market_cap_usd: Mapped[Decimal] = mapped_column(
        Numeric(precision=32, scale=18), server_default="0"
    )
    name: Mapped[str]
    networks: Mapped[list["Network"]] = relationship(
        secondary=coin_network_association_table, lazy="selectin"
    )
    ticker: Mapped[str]
    uid: Mapped[str] = mapped_column(index=True, unique=True)

    latest_usd_price: Mapped[Decimal] = mapped_column(
        Numeric(precision=32, scale=18), server_default="0"
    )
    """Initialized to 0 to avoid nulls, check its "updated at" timestamp if required."""

    latest_usd_price_updated_at: Mapped[datetime | None]
    """None if the price has never been populated."""

    usd_24h_change: Mapped[float | None] = mapped_column(default=None)
    """24-hour price change percentage."""

    DOLLAR_UID = "usd-dollar"

    def __repr__(self) -> str:
        return f"Coin(name={self.name!r}, ticker={self.ticker!r}, uid={self.uid!r})"

    @property
    def is_dollar(self) -> bool:
        return self.uid.lower() == self.DOLLAR_UID

    def enable_prices(self, commit=True):
        if self.has_prices is False:
            self.has_prices = True
            if commit and self.db:
                self.db.commit()


network_wallet_association_table = Table(
    "network_wallet_association",
    Base.metadata,
    Column("network_id", ForeignKey("network.id", ondelete="CASCADE"), primary_key=True),
    Column("wallet_id", ForeignKey("wallet.id", ondelete="CASCADE"), primary_key=True),
    UniqueConstraint("network_id", "wallet_id"),
)


class Network(Base):
    covalent_chain_id: Mapped[int | None]
    explorer_url: Mapped[str | None]
    is_usable_by_clients: Mapped[bool | None] = mapped_column(
        server_default="false", index=True
    )
    name: Mapped[str] = mapped_column(unique=True, index=True)
    native_coin_id: Mapped[int | None] = mapped_column(ForeignKey(Coin.id), index=True)
    native_coin: Mapped[Coin | None] = relationship(foreign_keys=[native_coin_id])
    wallets: Mapped[list["Wallet"]] = relationship(
        back_populates="networks",
        cascade="all,delete",
        secondary=network_wallet_association_table,
    )

    def __repr__(self) -> str:
        return f"Network(name={self.name!r})"

    @property
    def is_matic(self):
        return self.name.lower() == "matic"

    @property
    def is_moonbeam(self):
        return self.name.lower() == "moonbeam"

    @property
    def is_solana(self):
        return self.name.lower() == "solana"


class Wallet(Base):
    address: Mapped[str] = mapped_column(index=True)
    is_active: Mapped[bool | None] = mapped_column(default=True, index=True)
    is_archived: Mapped[bool] = mapped_column(default=False, index=True, server_default="false")
    is_usable_by_clients: Mapped[bool | None] = mapped_column(default=False, index=True)
    name: Mapped[str] = mapped_column(index=True)
    networks: Mapped[list[Network]] = relationship(
        back_populates="wallets",
        secondary=network_wallet_association_table,
        cascade="all,delete",
        lazy="selectin",
    )
    # When `user_id` is null, this is the fund's wallet
    user_id: Mapped[int | None] = mapped_column(ForeignKey(User.id), index=True)
    user: Mapped[User | None] = relationship(foreign_keys=[user_id])

    __table_args__ = (
        Index("ix_wallet_user_id_address", coalesce(user_id, 0), address, unique=True),
        Index("ix_wallet_user_id_name", coalesce(user_id, 0), name, unique=True),
    )

    def __repr__(self) -> str:
        return f"Wallet(name={self.name!r}, address={self.address!r}, user={self.user!r})"


class Bridge(Base):
    name: Mapped[str] = mapped_column(unique=True, index=True)
    url: Mapped[str]

    def __repr__(self) -> str:
        return f"Bridge(name={self.name!r})"


class Exchange(Base):
    name: Mapped[str] = mapped_column(unique=True, index=True)
    url: Mapped[str]

    def __repr__(self) -> str:
        return f"Exchange(name={self.name!r})"


class Farm(Base):
    name: Mapped[str] = mapped_column(unique=True, index=True)
    url: Mapped[str]

    def __repr__(self) -> str:
        return f"Farm(name={self.name!r})"


class LendingProtocol(Base):
    name: Mapped[str] = mapped_column(unique=True, index=True)
    url: Mapped[str]
    liquidation_thresholds: Mapped[dict[str, str] | None] = mapped_column(
        MutableDict.as_mutable(JSONB())
    )

    def __repr__(self) -> str:
        return f"LendingProtocol(name={self.name!r})"


class NFT(Base):
    attributes: Mapped[list] = mapped_column(JSON)
    image_url: Mapped[str | None]
    uid: Mapped[int | None]


class Account(Base):
    """
    A Blockchain Account with an address that receives/sends funds.
    Could be a wallet, Coin, or NFT.
    """

    EVM_BURN_ADDRESS = "******************************************"
    NATIVE_TOKEN_ADDRESS = "-1"

    address: Mapped[str] = mapped_column(index=True)
    network_id: Mapped[int] = mapped_column(ForeignKey(Network.id), index=True)
    network: Mapped[Network] = relationship(foreign_keys=[network_id])
    # Assumes it's a fund Wallet for now and not a client's
    wallet_id: Mapped[int | None] = mapped_column(ForeignKey(Wallet.id), index=True)
    wallet: Mapped[Wallet | None] = relationship(foreign_keys=[wallet_id])

    api_name: Mapped[str | None] = mapped_column(index=True)
    ticker_symbol: Mapped[str | None] = mapped_column(index=True)

    bridge_id: Mapped[int | None] = mapped_column(ForeignKey(Bridge.id))
    bridge: Mapped[Bridge | None] = relationship(foreign_keys=[bridge_id])
    coin_id: Mapped[int | None] = mapped_column(ForeignKey(Coin.id), index=True)
    coin: Mapped[Coin | None] = relationship(foreign_keys=[coin_id])
    exchange_id: Mapped[int | None] = mapped_column(ForeignKey(Exchange.id))
    exchange: Mapped[Exchange | None] = relationship(foreign_keys=[exchange_id])
    farm_id: Mapped[int | None] = mapped_column(ForeignKey(Farm.id))
    farm: Mapped[Farm | None] = relationship(foreign_keys=[farm_id])
    lending_protocol_id: Mapped[int | None] = mapped_column(ForeignKey(LendingProtocol.id))
    lending_protocol: Mapped[LendingProtocol | None] = relationship(
        foreign_keys=[lending_protocol_id]
    )
    nft_id: Mapped[int | None] = mapped_column(ForeignKey(NFT.id), index=True)
    nft: Mapped[NFT | None] = relationship(foreign_keys=[nft_id])
    # Account represents a liquidity pool token with or without a Coin set
    is_pool_token: Mapped[bool] = mapped_column(server_default="false")

    __table_args__ = (UniqueConstraint("address", "network_id"),)

    def __repr__(self) -> str:
        network_name = self.network and self.network.name
        return (
            f"Account(name={self.name!r}, network={network_name!r}, address={self.address!r})"
        )

    @classmethod
    def address_is_burn(cls, address):
        return address == "" or address == cls.EVM_BURN_ADDRESS

    @property
    def alias_coin(self):
        if self.coin:
            return self.coin.alias_coin.coin if self.coin.alias_coin else self.coin
        return None

    @property
    def is_burn(self):
        return self.address_is_burn(self.address)

    @property
    def is_coin(self):
        return self.coin_id is not None

    @property
    def is_native_token(self):
        return self.address == self.NATIVE_TOKEN_ADDRESS

    @property
    def is_wallet(self):
        # Only considers Fund Wallets as everything is tracked from that perspective
        return self.wallet_id is not None and self.wallet and self.wallet.user_id is None

    @property
    def name(self) -> str:
        return (
            self.is_burn
            and "Null Address"
            or self.bridge
            and self.bridge.name
            or self.exchange
            and self.exchange.name
            or self.farm
            and self.farm.name
            or self.lending_protocol
            and self.lending_protocol.name
            or self.api_name
            and self.api_name
            or self.wallet
            and self.wallet.name
            or ""
        )


class Transaction(Base):
    internal_id: Mapped[str | None] = mapped_column(
        default=None, index=True, unique=True
    )  # Format: "{company_name}:{transaction_id}"
    action: Mapped[str | None] = mapped_column(index=True)  # TransactionAction
    api_type: Mapped[str | None]
    confirmed_at: Mapped[datetime]
    json_data: Mapped[str | None]
    fees_paid: Mapped[Decimal | None] = mapped_column(Numeric(precision=32, scale=18))
    """
    Fees paid for transaction, if fees_asset_account is None the fees were paid in the
    network's native coin, otherwise in the coin represented by the asset account.
    """
    fees_asset_account_id: Mapped[int | None] = mapped_column(ForeignKey(Account.id))
    fees_asset_account: Mapped[Account | None] = relationship(
        foreign_keys=[fees_asset_account_id]
    )
    """
    Fees paid in the coin represented by this account. If `None`, fees were paid in the
    network's native coin.
    """
    # Transaction spent gas on an Approval
    has_approval: Mapped[bool] = mapped_column(server_default="false")
    is_collateral: Mapped[bool] = mapped_column(server_default="false")
    is_deposit: Mapped[bool | None]
    native_coin_dollar_price: Mapped[Decimal | None] = mapped_column(
        Numeric(precision=32, scale=18)
    )
    network_id: Mapped[int] = mapped_column(ForeignKey(Network.id), index=True)
    network: Mapped[Network] = relationship(foreign_keys=[network_id], lazy="selectin")
    signer_account_id: Mapped[int] = mapped_column(ForeignKey(Account.id), index=True)
    signer_account: Mapped[Account] = relationship(foreign_keys=[signer_account_id])
    transfers: Mapped[list["Transfer"]] = relationship(
        back_populates="transaction", lazy="selectin"
    )
    tx_hash: Mapped[str]
    wallet_id: Mapped[int] = mapped_column(ForeignKey(Wallet.id), index=True)
    wallet: Mapped[Wallet] = relationship(foreign_keys=[wallet_id], lazy="selectin")
    comment: Mapped[str] = mapped_column(server_default="")

    __table_args__ = (UniqueConstraint("network_id", "tx_hash"),)

    def __repr__(self) -> str:
        network_name = self.network and self.network.name
        return (
            f"Transaction(id={self.id!r}, network={network_name!r}, tx_hash={self.tx_hash!r})"
        )

    @property
    def added_manually(self):
        return not self.json_data

    @property
    def could_be_bridge(self):
        return not self.is_approval and (
            self.could_be_income or self.could_be_spend or self.could_be_swap
        )

    @property
    def could_be_farm(self):
        return self.could_be_bridge

    @property
    def could_be_income(self):
        # Assets moving to a Wallet
        return (
            not self.is_approval
            and len(self.transfers)
            and all(transfer.to_account.is_wallet for transfer in self.transfers)
        )

    @property
    def could_be_loan(self):
        # Single asset going from or to a Wallet
        return not self.is_approval and (
            self.could_be_income or self.could_be_spend or self.could_be_swap
        )

    @property
    def could_be_pool(self):
        return (
            not self.is_approval
            and len(self.transfers)
            and (
                self.could_be_income
                or self.could_be_spend
                or self.could_be_stake
                or self.could_be_swap
            )
        )

    @property
    def could_be_spend(self):
        # Assets moving out of a Wallet or no transfers
        return all(transfer.from_account.wallet for transfer in self.transfers)

    @property
    def could_be_stake(self):
        return not self.is_approval and (self.could_be_loan or self.could_be_swap)

    @property
    def could_be_swap(self):
        # Assets moving both from and to a Wallet
        return (
            not self.is_approval
            and len(self.transfers) > 1
            and (
                any(transfer.from_account.is_wallet for transfer in self.transfers)
                and any(transfer.to_account.is_wallet for transfer in self.transfers)
            )
        )

    def fees_paid_dollars(self):
        if self.fees_paid and self.native_coin_dollar_price:
            return self.fees_paid * self.native_coin_dollar_price
        return 0

    def get_action_enum(self) -> TransactionAction | None:
        if self.action:
            return TransactionAction[self.action]
        return None

    @property
    def has_exchange_account(self):
        return any(
            any((transfer.from_account.exchange_id, transfer.to_account.exchange_id))
            for transfer in self.transfers
        )

    @property
    def has_farm_account(self):
        return any(
            any((transfer.from_account.farm_id, transfer.to_account.farm_id))
            for transfer in self.transfers
        )

    @property
    def is_approval(self):
        return self.has_approval and len(self.transfers) == 0

    @property
    def possible_actions(self) -> list[TransactionAction]:
        return [
            action for action in TransactionAction if getattr(self, f"could_be_{action.value}")
        ]


class Transfer(Base):
    """
    Movement of an amount of an Asset between two other Accounts
    """

    asset_account_id: Mapped[int] = mapped_column(ForeignKey(Account.id), index=True)
    asset_account: Mapped[Account] = relationship(
        foreign_keys=[asset_account_id], lazy="joined"
    )
    asset_price_usd: Mapped[Decimal | None] = mapped_column(Numeric(precision=32, scale=18))
    asset_prices_json: Mapped[dict[str, float] | None] = mapped_column(JSONB)
    amount: Mapped[Decimal] = mapped_column(Numeric(precision=48, scale=18))
    nft_id: Mapped[int | None] = mapped_column(Numeric(precision=32, scale=0))

    from_account_id: Mapped[int] = mapped_column(ForeignKey(Account.id), index=True)
    from_account: Mapped[Account] = relationship(foreign_keys=[from_account_id], lazy="joined")
    to_account_id: Mapped[int] = mapped_column(ForeignKey(Account.id), index=True)
    to_account: Mapped[Account] = relationship(foreign_keys=[to_account_id], lazy="joined")
    transaction: Mapped[Transaction] = relationship(back_populates="transfers")
    transaction_id: Mapped[int] = mapped_column(ForeignKey(Transaction.id), index=True)

    def __repr__(self) -> str:
        coin_uid = None
        if self.asset_account and self.asset_account.alias_coin:
            coin_uid = self.asset_account.alias_coin.uid
        return (
            f"Transfer(id={self.id!r}, amount={self.amount!r}, coin={coin_uid!r}, "
            f"from_account={self.from_account!r}, to_account={self.to_account!r})"
        )

    @property
    def is_deposit(self):
        if self.transaction.action == TransactionAction.income.value:
            return True
        elif self.transaction.action == TransactionAction.spend.value:
            return False
        return self.to_account.is_wallet

    @property
    def is_withdrawal(self):
        return not self.is_deposit or self.from_account.is_wallet

    def signed_amount(self, *, inverted=False) -> Decimal:
        if self.transaction.action == TransactionAction.stake.value:
            negate = self.is_deposit if inverted else self.is_withdrawal
            return self.amount.copy_negate() if negate else self.amount

        # TODO: Define for which Transaction actions exactly we can use this method.
        if inverted:
            return self.amount.copy_negate()
        return self.amount


class CovalentSync(Base):
    chain_name: Mapped[str] = mapped_column()
    address: Mapped[str] = mapped_column()
    latest_page: Mapped[int]

    __table_args__ = (
        Index("ix_covalent_sync_chain_name_address", chain_name, address, unique=True),
    )


class Job(Base):
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    kind: Mapped[JobKind]
    status: Mapped[JobStatus] = mapped_column()
    created_at: Mapped[datetime]
    completed_at: Mapped[datetime | None]
    data: Mapped[dict[str, Any]] = mapped_column(JSONB)
    data_text: Mapped[str | None] = mapped_column(deferred=True)
    error: Mapped[str | None]
    result: Mapped[dict[str, Any] | None] = mapped_column(JSONB)

    __table_args__ = (Index("ix_job_status_id", status, id, unique=True),)

    @classmethod
    def create(cls, **kwargs):
        return cls(**kwargs, status="pending", created_at=datetime.now(timezone.utc))


class PoolPriceAlert(Base):
    account_id: Mapped[int] = mapped_column(ForeignKey(Account.id), index=True)
    account: Mapped[Account] = relationship(foreign_keys=[account_id])
    coin_id: Mapped[int] = mapped_column(ForeignKey(Coin.id), index=True)
    coin: Mapped[Coin] = relationship(foreign_keys=[coin_id])
    last_triggered_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))

    min_price_usd: Mapped[Decimal]
    """Alert will trigger if price is equal or below this value"""

    max_price_usd: Mapped[Decimal]
    """Alert will trigger if price is equal or above this value"""

    __table_args__ = (
        Index("ix_pool_price_alert_unique_key", account_id, coin_id, unique=True),
    )
