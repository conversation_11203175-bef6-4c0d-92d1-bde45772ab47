# Environments

## admin

Contains Terraform configurations for administrative resources that should be created and
managed by admin users only. These resources typically require elevated permissions and are
critical for the infrastructure's security and management. The admin environment contains
resources that are relevant and shared across multiple environments.

## Gotchas

### Creation of bucket with a domain name

When creating a bucket, if the bucket name is a domain name, the user creating it must be an
owner of the domain. Otherwise, the bucket creation will fail with an error like:

```
Error: googleapi: Error 403: Another user owns the domain temet.tech or a parent domain.
You can either verify domain ownership at https://search.google.com/search-console/welcome?new_domain_name=temet.tech
or find the current owner and ask that person to create the bucket for you, forbidden
```

To allow terraform to create this bucket, first add your account as the owner of the domain,
then add the service account (the one used by terraform) as an owner as well:

1. [Domain ownership verification](https://cloud.google.com/storage/docs/domain-name-verification#verification)
2. [Delegate ownership to service account](https://cloud.google.com/storage/docs/domain-name-verification#additional_verified_owners)

> Tip: the service account email can be found in the `key.json` file.

### New managed zone for a domain

When creating a new managed zone for a domain, we need to manually add the NS records to the
domain on the parent domain managed zone (which should be not managed by terraform, see below).

For example, if you've applied changes and terraform created a managed zone for
`stage.temet.tech`:

1. The NS records for the new managed zone can be found on the
   [Cloud DNS page](https://console.cloud.google.com/net-services/dns/zones)
2. Add the NS record on the parent managed zone (e.g. `temet.tech`), with the same name servers
   of the NS record on the newly created managed zone.

### Not all managed zones are managed by terraform

Some managed zones are not managed by terraform, so beware it might be required to do manual
changes on these zones:

- temet-tech (`temet.tech`)
- stabletech-capital `stabletech.capital`
