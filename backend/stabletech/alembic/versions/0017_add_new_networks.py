"""Add New Networks

Revision ID: 0017
Revises: 0016
Create Date: 2023-02-17 22:41:30.466369

"""

from alembic import op
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = "0017"
down_revision = "0016"
branch_labels = None
depends_on = None


class Base:
    __allow_unmapped__ = True


Base = declarative_base(cls=Base)


class Network(Base):
    id = Column(Integer, primary_key=True)
    covalent_chain_id: int = Column(Integer)
    explorer_url: str = Column(String)
    name: str = Column(String, nullable=False, index=True, unique=True)
    __tablename__ = "network"


def upgrade() -> None:
    session = Session(bind=op.get_bind())

    for name in ("Canto", "Kujira", "Secret", "Stride"):
        if not session.query(Network).filter_by(name=name).first():
            session.add(Network(name=name))
    session.commit()


def downgrade() -> None:
    pass
    # ### end Alembic commands ###
