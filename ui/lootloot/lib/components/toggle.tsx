"use client";

import * as RadixToggle from "@radix-ui/react-toggle";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

export const toggle = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:ring-1 focus-visible:ring-ring/60 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground dark:focus-visible:ring-ring/80",
  {
    variants: {
      variant: {
        default: "bg-transparent",
        outline:
          "border-input border bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground",
      },
      size: {
        default: "h-9 px-3",
        sm: "h-8 px-2",
        lg: "h-10 px-3",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export const Toggle = React.forwardRef<
  React.ElementRef<typeof RadixToggle.Root>,
  RadixToggle.ToggleProps & VariantProps<typeof toggle>
>(function Toggle({ className, variant, size, ...props }, ref) {
  return (
    <RadixToggle.Root ref={ref} className={toggle({ variant, size, className })} {...props} />
  );
});
