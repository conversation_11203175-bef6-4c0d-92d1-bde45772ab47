"""aliascoin

Revision ID: 0050
Revises: 0049
Create Date: 2024-05-24 20:13:08.300406

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0050"
down_revision = "0049"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "aliascoin",
        sa.<PERSON>n("coin_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.ForeignKeyConstraint(
            ["coin_id"],
            ["coin.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_aliascoin_coin_id"), "aliascoin", ["coin_id"], unique=False)
    op.create_index(op.f("ix_aliascoin_id"), "aliascoin", ["id"], unique=False)
    op.add_column("coin", sa.Column("alias_coin_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "coin", "aliascoin", ["alias_coin_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "coin", type_="foreignkey")
    op.drop_column("coin", "alias_coin_id")
    op.drop_index(op.f("ix_aliascoin_id"), table_name="aliascoin")
    op.drop_index(op.f("ix_aliascoin_coin_id"), table_name="aliascoin")
    op.drop_table("aliascoin")
    # ### end Alembic commands ###
