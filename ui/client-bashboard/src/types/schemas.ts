/**
 * This file was automatically generated by running common/pydantic2ts.py.
 * Do not modify it by hand - just update the pydantic models and then re-run the script
 */

export type TransactionAction =
  | "bridge"
  | "income"
  | "loan"
  | "pool"
  | "spend"
  | "stake"
  | "swap";

export interface Account {
  id: number;
  address: string;
  bridge?: Bridge | null;
  exchange?: Exchange | null;
  farm?: Farm | null;
  coin: Coin | null;
  isPoolToken: boolean;
  isWallet: boolean;
  lendingProtocol?: LendingProtocol | null;
  name?: string | null;
  tickerSymbol?: string | null;
}
export interface Bridge {
  id?: number | null;
  name: string;
  url: string;
}
export interface Exchange {
  name: string;
  url: string;
  id: number;
}
export interface Farm {
  name: string;
  url: string;
  id: number;
}
export interface Coin {
  name: string;
  ticker: string;
  uid: string;
  id: number;
  isUsableByClients: boolean;
  latestUsdPrice: string;
  latestUsdPriceUpdatedAt: string | null;
  usd24hChange: number | null;
}
export interface LendingProtocol {
  name: string;
  url: string;
  id: number;
}
export interface AccountLoan {
  id: number;
  name: string;
  network: Network;
  totalCollateral: string;
  totalBorrowed: string;
  totalLiquidationThreshold: string;
}
export interface Network {
  id: number;
  explorerUrl?: string | null;
  name: string;
  isUsableByClients: boolean;
}
export interface AccountLoanFull {
  id: number;
  name: string;
  network: Network;
  totalCollateral: string;
  totalBorrowed: string;
  totalLiquidationThreshold: string;
  coinTotals: LoanCoinTotal[];
  transactions: LoanTransaction[];
}
export interface LoanCoinTotal {
  coin: Coin;
  borrowAmount: string;
  collateralAmount: string;
  lossAmount: string;
  profitAmount: string;
  liquidationThreshold: string;
}
export interface LoanTransaction {
  id: number;
  confirmedAt: string;
  hasApproval: boolean;
  network: Network;
  signerAccount: Account;
  transfers: Transfer[];
  txHash: string;
  wallet: Wallet;
  action: TransactionAction;
  clientTransactionId?: number | null;
  comment: string;
  feesPaid: string | null;
  internalId?: string | null;
  isCollateral: boolean;
  isDeposit: boolean | null;
}
export interface Transfer {
  id: number;
  amount: string;
  isWithdrawal: boolean;
  isDeposit: boolean;
  assetAccount: Account;
  fromAccount: Account;
  toAccount: Account;
  assetPriceUsd: string | null;
}
export interface Wallet {
  address: string;
  isUsableByClients?: boolean | null;
  name: string;
  id: number;
  networks: Network[];
}
export interface AccountPool {
  id: number;
  name: string;
  exchange?: Exchange | null;
  impermanentLossPercent?: string | null;
  network: Network;
  state: "has-funds" | "no-balance" | "missing-transactions";
  tvl: string;
}
export interface AccountPoolFull {
  id: number;
  name: string;
  exchange?: Exchange | null;
  impermanentLossPercent?: string | null;
  network: Network;
  state: "has-funds" | "no-balance" | "missing-transactions";
  tvl: string;
  coinTotals: PoolCoinTotal[];
  kRatio: string;
  tickers: string[];
  transactions: Transaction[];
}
export interface PoolCoinTotal {
  coin: Coin;
  dollarPrice: string;
  amountAdded: string;
  amountCurrent: string;
  amountTotal: string;
}
export interface Transaction {
  id: number;
  confirmedAt: string;
  hasApproval: boolean;
  network: Network;
  signerAccount: Account;
  transfers: Transfer[];
  txHash: string;
  wallet: Wallet;
  action: TransactionAction;
  clientTransactionId?: number | null;
  comment: string;
  feesPaid: string | null;
  internalId?: string | null;
  isCollateral: boolean;
  isDeposit: boolean | null;
}
export interface AccountedTransaction {
  id: number;
  txHash: string;
  transferId: number;
  action: string;
  confirmedAt: string;
  transactionAction: string;
  amount: string;
  remainingAmount: string;
  coin: Coin | null;
  assetPriceUsd: string | null;
  tickerSymbol: string;
  matchedCapitalPositionsCount?: number | null;
  flattenedTransactionsCount?: number | null;
  loanWithdrawalsConsumedCount?: number | null;
}
export interface AddAccount {
  id?: number | null;
  address: string;
  coin?: Coin | null;
  coinId?: number | null;
  isPoolToken?: boolean;
  isWallet?: boolean | null;
}
export interface AddAliasCoin {
  coinId: number;
}
export interface AddClientTransaction {
  amount: string;
  coinId: number;
  isDeposit: boolean;
  networkId: number;
}
export interface AddCoin {
  name: string;
  ticker: string;
  uid: string;
}
export interface AddExchange {
  name: string;
  url: string;
}
export interface AddFarm {
  name: string;
  url: string;
}
export interface AddLendingProtocol {
  name: string;
  url: string;
}
export interface AddNetwork {
  explorerUrl?: string | null;
  name: string;
  nativeCoinId: number;
}
export interface AddTransaction {
  action: TransactionAction;
  bridgeId?: number | null;
  exchangeId?: number | null;
  farmId?: number | null;
  isCollateral: boolean;
  isForPooledFund?: boolean;
  isDeposit?: boolean | null;
  lendingProtocolId?: number | null;
  userId?: number | null;
  comment?: string | null;
  confirmedAt: string;
  feesPaid: string;
  networkId: number;
  transfers: AddTransfer[];
  txHash: string;
  walletId: number;
}
export interface AddTransfer {
  amount: string;
  assetAccount: AddAccount;
  fromAccount: AddAccount;
  toAccount: AddAccount;
}
export interface AddWallet {
  address: string;
  isUsableByClients?: boolean | null;
  name: string;
  networkIds: number[];
}
export interface AdminClientTransaction {
  id: number;
  amount: string | null;
  coin: Coin;
  coinId: number;
  createdAt: string;
  isDeposit: boolean | null;
  network: Network | null;
  reviewedAt?: string | null;
  transaction?: TransactionForClient | null;
  userId: number;
}
export interface TransactionForClient {
  id: number;
  confirmedAt: string;
  hasApproval: boolean;
  network: Network;
  signerAccount: Account;
  transfers: Transfer[];
  txHash: string;
  wallet: Wallet;
  action: TransactionAction;
  clientTransactionId?: number | null;
  comment: string;
  feesPaid: string | null;
  internalId?: string | null;
  isCollateral: boolean;
  isDeposit: boolean | null;
  costBasisUsd?: string | null;
  isInterestPayment?: boolean;
  clientAction: "Deposit" | "Interest Payment" | "Swap" | "Withdrawal";
}
export interface AliasCoin {
  id: number;
  coin: Coin;
  aliasedCoins: Coin[];
}
export interface AssetHolding {
  coinId: number;
  coinName: string;
  coinTicker: string;
  amount: string;
  valueUsd: string;
  priceUsd: string;
  change24h: number | null;
}
export interface AssetHoldingsResponse {
  totalValueUsd: string;
  total24hChange: number | null;
  assets: AssetHolding[];
}
export interface BaseAddTransaction {
  action: TransactionAction;
  bridgeId?: number | null;
  exchangeId?: number | null;
  farmId?: number | null;
  isCollateral: boolean;
  isForPooledFund?: boolean;
  isDeposit?: boolean | null;
  lendingProtocolId?: number | null;
  userId?: number | null;
}
export interface BasePool {
  name: string;
  url: string;
}
export interface BaseTransaction {
  id: number;
  confirmedAt: string;
  hasApproval: boolean;
  network: Network;
  signerAccount: Account;
  transfers: Transfer[];
  txHash: string;
  wallet: Wallet;
}
export interface BaseWallet {
  address: string;
  isUsableByClients?: boolean | null;
  name: string;
}
export interface CapitalPosition {
  amount: string;
  boughtAt: string;
  boughtUsdAmount: string;
  coin: Coin;
  coinId: number;
  id: number;
  soldAt: string;
  soldUsdAmount: string;
  pnl: string;
}
export interface CapitalPositionSummary {
  coin: Coin;
  cost: string;
  latestCapitalPositions?: CapitalPosition[] | null;
  pnl: string;
  proceeds: string;
  volume: string;
}
export interface CapitalPositionSummaryList {
  latestCapitalPositions: CapitalPosition[];
  summary: CapitalPositionSummary[];
  totalCost: string;
  totalPnl: string;
  totalProceeds: string;
  totalVolume: string;
}
export interface ClientTransaction {
  amount: string;
  coin: Coin;
  createdAt: string;
  id: number;
  isDeposit: boolean | null;
  reviewedAt?: string | null;
}
export interface ClientTransactionWithUser {
  amount: string;
  coin: Coin;
  createdAt: string;
  id: number;
  isDeposit: boolean | null;
  reviewedAt?: string | null;
  user: User | null;
}
export interface User {
  id: number;
  agreementAcceptedAt: string | null;
  authToken?: string | null;
  earnsInterest: boolean;
  email: string;
  firstName: string | null;
  interestRate: string;
  lastName: string | null;
  pictureUrl: string | null;
  role: string;
  usdInvested: string;
}
export interface CoinTotal {
  coin: Coin;
  amount: string;
}
export interface CoinWithAssetAddress {
  name: string;
  ticker: string;
  uid: string;
  id: number;
  isUsableByClients: boolean;
  latestUsdPrice: string;
  latestUsdPriceUpdatedAt: string | null;
  usd24hChange: number | null;
  assetAddress: string;
  networkId: number;
}
export interface CoinWithNetworks {
  name: string;
  ticker: string;
  uid: string;
  id: number;
  isUsableByClients: boolean;
  latestUsdPrice: string;
  latestUsdPriceUpdatedAt: string | null;
  usd24hChange: number | null;
  networks: Network[];
  hasPrices: boolean;
}
export interface Dashboard {
  coins: Coin[];
  transactions: TransactionForClient[];
}
export interface EditWallet {
  id: number;
  isUsableByClients?: boolean | null;
  name: string;
  networkIds: number[];
}
export interface ExchangeWithAccountAddress {
  name: string;
  url: string;
  id: number;
  accountAddress: string | null;
  network: Network | null;
}
export interface FarmWithAccountAddress {
  name: string;
  url: string;
  id: number;
  accountAddress: string | null;
  network: Network | null;
}
export interface ImportFailedRow {
  error: "already-in-db" | "in-file-duplicate" | "unmatched-buy" | "unmatched-sell";
  index: number;
  row: string[];
}
export interface ImportJobParams {
  mergeInFileDuplicates: boolean;
  skipAlreadyInDb: boolean;
  userId: number;
}
export interface ImportResult {
  ok: boolean;
  importedRowsCount: number;
  header: string[];
  failedRows: ImportFailedRow[];
}
export interface JobResponse {
  id: number;
  kind: "import_cointracking_csv" | "import_bitcointax_csv" | "sync_wallet";
  status: "pending" | "failed" | "completed";
  createdAt: string;
  completedAt: string | null;
  data: ImportJobParams | WalletSyncJobParams;
  error: string | null;
  result: ImportResult | WalletSyncJobResult | null;
}
export interface WalletSyncJobParams {
  walletId: number;
  initial?: boolean;
}
export interface WalletSyncJobResult {
  results: [unknown, unknown][];
}
export interface LendingProtocolLiquidationThreshold {
  coinUid: string;
  liquidationThreshold: string;
}
export interface LendingProtocolWithAccountAddress {
  name: string;
  url: string;
  id: number;
  accountAddress: string | null;
  network: Network | null;
}
export interface Liability {
  coinLatestPriceUsd: string;
  coinName: string;
  currentBalance: string;
  totalDebt: string;
}
export interface LoginPassword {
  email: string;
  password: string;
}
export interface NetworkWithNativeCoin {
  id: number;
  explorerUrl?: string | null;
  name: string;
  isUsableByClients: boolean;
  nativeCoin: Coin | null;
}
export interface OpenPositionSummary {
  coin: Coin;
  openConfirmedAt: string;
  amount: string;
  avgCost: string;
  latestAccountedTransactions?: AccountedTransaction[] | null;
  unrealizedPnl: string;
  cost: string;
  value: string;
}
export interface OpenPositionSummaryList {
  summaries: OpenPositionSummary[];
  latestAccountedTransactions: AccountedTransaction[];
  totalUnrealizedPnl: string;
  totalCost: string;
  totalValue: string;
}
export interface PaginatedResultTransactionUnclassified {
  next: boolean;
  prev: boolean;
  items: TransactionUnclassified[];
}
export interface TransactionUnclassified {
  id: number;
  confirmedAt: string;
  hasApproval: boolean;
  network: Network;
  signerAccount: Account;
  transfers: Transfer[];
  txHash: string;
  wallet: Wallet;
  comment: string;
  feesPaid: string | null;
  possibleActions: TransactionAction[];
}
export interface PaginatedResultTransaction {
  next: boolean;
  prev: boolean;
  items: Transaction[];
}
export interface Pool {
  name: string;
  url: string;
  id: number;
  exchange?: Exchange | null;
  farm: Farm;
  isClosed: boolean;
  network: Network;
  tvl?: string | null;
}
export interface PoolPriceAlert {
  minPriceUsd: string;
  maxPriceUsd: string;
  accountId: number;
  coinId: number;
}
export interface PoolPriceAlertRange {
  minPriceUsd: string;
  maxPriceUsd: string;
}
export interface ReplaceTransaction {
  addTransaction: AddTransaction;
  clientTransactionId: number | null;
  transactionId: number;
}
export interface SahabaDashboard {
  coins: Coin[];
  clientTransactions: ClientTransaction[];
}
export interface Stake {
  asset: Account;
  amount: string;
  farm: Farm;
  network: Network;
}
export interface StakeDetails {
  asset: Account;
  amount: string;
  farm: Farm;
  network: Network;
  transactions: Transaction[];
}
export interface TransactionClassify {
  action: TransactionAction;
  bridgeId?: number | null;
  exchangeId?: number | null;
  farmId?: number | null;
  isCollateral: boolean;
  isForPooledFund?: boolean;
  isDeposit?: boolean | null;
  lendingProtocolId?: number | null;
  userId?: number | null;
  id: number;
  isRevenueShare?: boolean;
}
export interface TransactionsOverview {
  coinTotals: CoinTotal[];
  transactions: Transaction[];
}
export interface UpdateAliasCoin {
  aliasedCoinsIds: number[];
}
export interface UpdateCoin {
  id: number;
  hasPrices?: boolean | null;
  isUsableByClients?: boolean | null;
  name?: string | null;
  networkIds?: number[] | null;
  ticker?: string | null;
  uid?: string | null;
}
export interface UserLiability {
  dueAt: string;
  liabilities: Liability[];
  totalUsdDebt: string;
  user: User;
}
/**
 * Result of a wallet sync.
 */
export interface WalletSyncResult {
  networkName: string;
  provider: "covalent" | "mintscan" | "helius";
  providerNetwork: string;
  processedTransactions: number;
  errors: string[];
}
