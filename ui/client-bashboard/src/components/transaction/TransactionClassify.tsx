import {
  Classification,
  State as ClassificationState,
} from "@/components/transaction/Classification";
import { updateStateField } from "@/helpers/state";
import { useIndexerTransactionClassifyMutation } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import {
  Bridge,
  ExchangeWithAccountAddress,
  FarmWithAccountAddress,
  LendingProtocolWithAccountAddress,
  Transaction,
  TransactionUnclassified,
} from "@/types/schemas";
import Alert from "@mui/material/Alert";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import LinearProgress from "@mui/material/LinearProgress";
import { useState } from "react";

interface Props {
  transaction: TransactionUnclassified;
}

export function TransactionClassify(props: Props) {
  const { transaction } = props;
  const { possibleActions } = transaction;

  const { bridge, exchange, farm, lendingProtocol } = getRelevantEntities(transaction);

  const defaults: ClassificationState = {
    action: null,
    bridge: bridge,
    exchange: exchange,
    farm: farm,
    isClientLoan: false,
    isCollateral: null,
    isDeposit: null,
    isForPooledFund: false,
    isRevenueShare: false,
    lendingProtocol: lendingProtocol,
    user: null,

    clientTransactionId: null,
  };
  const [state, setState] = useState(defaults);

  const [classifyTransaction, classifyTransactionResult] =
    useIndexerTransactionClassifyMutation();

  return (
    <Classification
      possibleActions={possibleActions}
      state={state}
      onChange={(field, value) => {
        updateStateField(setState, field, value);
      }}
    >
      {state.action && (
        <>
          <Box sx={{ width: "100%" }}>
            {classifyTransactionResult.isLoading && <LinearProgress color="inherit" />}
          </Box>
          <Button
            fullWidth
            disabled={
              !(
                (state.action === "bridge" &&
                  state.bridge !== null &&
                  state.isDeposit !== null) ||
                (state.action === "pool" &&
                  state.exchange !== null &&
                  state.isDeposit !== null) ||
                (state.action === "stake" && state.farm !== null && state.isDeposit !== null) ||
                (state.action === "swap" && state.exchange !== null) ||
                ["income", "loan", "spend"].includes(state.action)
              ) || classifyTransactionResult.isLoading
            }
            variant={"outlined"}
            onClick={() => {
              if (transaction && state.action) {
                classifyTransaction([
                  {
                    id: transaction.id,
                    action: state.action,
                    bridgeId: state.bridge?.id || null,
                    exchangeId: state.exchange?.id || null,
                    farmId: state.farm?.id || null,
                    isCollateral: state.isCollateral ?? false,
                    isDeposit: state.isDeposit,
                    isForPooledFund: state.isForPooledFund,
                    isRevenueShare: state.isRevenueShare,
                    lendingProtocolId: state.lendingProtocol?.id || null,
                    userId: state.user?.id || null,
                  },
                  state.clientTransactionId,
                ]);
              }
            }}
          >
            Save
          </Button>
        </>
      )}

      {classifyTransactionResult.error && (
        <Alert severity="error">{getErrorDetail(classifyTransactionResult.error)}</Alert>
      )}
    </Classification>
  );
}

export function getRelevantEntities(transaction: Transaction | TransactionUnclassified) {
  let bridge: Bridge | null = null;
  let exchange: ExchangeWithAccountAddress | null = null;
  let farm: FarmWithAccountAddress | null = null;
  let lendingProtocol: LendingProtocolWithAccountAddress | null = null;

  const accounts = transaction.transfers.flatMap((transfer) => [
    transfer.fromAccount,
    transfer.toAccount,
  ]);

  for (const account of accounts) {
    if (bridge == null && account.bridge != null) {
      bridge = account.bridge;
    }
    if (exchange == null && account.exchange != null) {
      exchange = {
        ...account.exchange,
        accountAddress: account.address,
        network: transaction.network,
      };
    }
    if (farm == null && account.farm != null) {
      farm = {
        ...account.farm,
        accountAddress: account.address,
        network: transaction.network,
      };
    }
    if (lendingProtocol == null && account.lendingProtocol != null) {
      lendingProtocol = {
        ...account.lendingProtocol,
        accountAddress: account.address,
        network: transaction.network,
      };
    }
  }

  return { bridge, exchange, farm, lendingProtocol };
}
