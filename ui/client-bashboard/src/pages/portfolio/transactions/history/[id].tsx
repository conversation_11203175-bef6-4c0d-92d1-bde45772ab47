import Spinner from "@/@core/components/spinner";
import { TransactionCardHistory } from "@/components/TransactionCard";
import { usePathId } from "@/helpers/routing";
import { useIndexerTransactionQuery } from "@/store/services/api";
import { getErrorDetail } from "@/store/services/helpers";
import { Alert } from "@mui/material";
import { skipToken } from "@reduxjs/toolkit/query";

export default function TransactionDetailPage() {
  const id = usePathId();
  const {
    data: transaction,
    isFetching,
    isLoading,
    error,
  } = useIndexerTransactionQuery(id ?? skipToken);

  if (error != null || id == null) {
    return (
      <Alert severity="error">
        Error: {getErrorDetail(error, { fallback: "Failed to load transaction" })}
      </Alert>
    );
  }

  if (transaction == null || isLoading) {
    return <Spinner />;
  }

  return <TransactionCardHistory isLoading={isFetching} transaction={transaction} />;
}

TransactionDetailPage.acl = {
  action: "manage",
  subject: "Transaction",
};
