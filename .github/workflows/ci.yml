name: CI
on:
  push:
    branches: [main, stage]
  pull_request:
permissions:
  contents: read
  pull-requests: read
jobs:
  # Job to check code style.
  # Currently it takes less than 1 minute to run. So splitting it into multiple
  # jobs would be overkill.
  style:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        id: checkout
      - uses: pnpm/action-setup@v4
        id: pnpm-setup
        if: ${{ !cancelled() && steps.checkout.outcome == 'success' }}
        with:
          version: 10
          run_install: |
            args: [--frozen-lockfile, --ignore-scripts, --prefer-offline]
      - name: prettier
        if: ${{ !cancelled() && steps.pnpm-setup.outcome == 'success' }}
        run: ./node_modules/.bin/prettier --check .
      - uses: actions/setup-python@v5
        id: python-setup
        if: ${{ !cancelled() && steps.checkout.outcome == 'success' }}
        with:
          python-version: "3.12"
      - name: isort
        if: ${{ !cancelled() && steps.python-setup.outcome == 'success' }}
        uses: isort/isort-action@v1
        with:
          isort-version: "6.0.1"
      - name: black
        if: ${{ !cancelled() && steps.python-setup.outcome == 'success' }}
        run: |
          pip install -q black~=25.1
          black --check --diff --color .
      - uses: hashicorp/setup-terraform@v3
        id: setup-terraform
        if: ${{ !cancelled() && steps.checkout.outcome == 'success' }}
        with:
          terraform_version: "~1.11.4"
      - name: terraform-fmt
        if: ${{ !cancelled() && steps.setup-terraform.outcome == 'success' }}
        run: terraform fmt -diff -check -recursive
      - name: ruff
        uses: astral-sh/ruff-action@v3
        if: ${{ !cancelled() && steps.checkout.outcome == 'success' }}
        with:
          version: "~0.11.9"
