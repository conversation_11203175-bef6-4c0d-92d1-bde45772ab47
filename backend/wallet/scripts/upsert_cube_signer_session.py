import argparse
import asyncio

from sqlalchemy.dialects.postgresql import insert

from common.psql import SessionLocal
from wallet.cubist.session_manager import SessionData, _init_secret_box
from wallet.models import CubeSignerSession

parser = argparse.ArgumentParser(description="Upsert Cube Signer Session")
parser.add_argument("session_name", type=str, help="Name of the session")
parser.add_argument("file_path", type=str, help="Path to the JSON session file")

args = parser.parse_args()
session_name = args.session_name
file_path = args.file_path


async def main():
    with open(file_path, "r") as file:
        session_data_obj = SessionData.model_validate_json(file.read())
    secret_box = await _init_secret_box()
    session_data = secret_box.encrypt(
        session_data_obj.model_dump_json(round_trip=True).encode("utf-8")
    )
    session_data_obj = None

    with SessionLocal.begin() as db:
        stmt = insert(CubeSignerSession)
        stmt = stmt.on_conflict_do_update(
            index_elements=[CubeSignerSession.session_name],
            set_={CubeSignerSession.data: stmt.excluded.data},
        )
        db.execute(
            stmt,
            {
                "session_name": session_name,
                "data": session_data,
            },
        )


asyncio.run(main())
