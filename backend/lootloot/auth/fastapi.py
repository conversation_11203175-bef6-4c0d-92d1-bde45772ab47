from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from common.psql import DatabaseSession
from lootloot.auth.crud import AuthTokenManager
from lootloot.auth.models import AuthToken, User

security = HTTPBearer()

Credentials = Annotated[HTTPAuthorizationCredentials, Depends(security)]


def authentication(credentials: Credentials, db: DatabaseSession) -> AuthToken:
    auth_token = AuthTokenManager(db).get_by_token(credentials.credentials)
    if not auth_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"},
            detail="Invalid Token",
        )
    return auth_token


depends_authentication = Depends(authentication)

Authentication = Annotated[AuthToken, depends_authentication]


def get_current_user(auth_token: Authentication) -> User:
    return auth_token.user


depends_current_user = Depends(get_current_user)

CurrentUser = Annotated[User, depends_current_user]


def get_current_admin(user: CurrentUser) -> User:
    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    return user


depends_current_admin = Depends(get_current_admin)

CurrentAdmin = Annotated[User, depends_current_admin]
