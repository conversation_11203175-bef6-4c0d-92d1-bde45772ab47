import logging
import signal
import time
from datetime import datetime, timedelta, timezone

from pydantic import BaseModel
from sentry_sdk import capture_exception, isolation_scope
from sqlalchemy import delete
from sqlalchemy.orm import Session

from common.psql import SessionLocal
from stabletech.accounting.bitcointax.import_csv import (
    process_import_job as bitcoin_tax_process_import_job,
)
from stabletech.indexer import cointracking, jobs
from stabletech.indexer.models import Job

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProcessState:
    terminated = False

    def __init__(self) -> None:
        signal.signal(signal.SIGTERM, self.handle_sigterm)

    def handle_sigterm(self, *args):
        logger.info("Received SIGTERM")
        self.terminated = True


state = ProcessState()


def process_job(db: Session, job: Job) -> str | BaseModel:
    """Process a job, return an error string or a BaseModel if succeeded."""
    match job.kind:
        case "import_cointracking_csv":
            return cointracking.process_import_job(db, job)
        case "import_bitcointax_csv":
            return bitcoin_tax_process_import_job(db, job)
        case "sync_wallet":
            return jobs.sync_wallet(db, job)
        case _:
            return "Unknown job kind"


def process_jobs_batch(db: Session) -> bool:
    """Go through a batch of jobs processing each. Return whether there were any jobs."""
    jobs: list[Job] = (
        db.query(Job).where(Job.status == "pending").order_by(Job.id).limit(10).all()
    )
    if not jobs:
        return False
    for job in jobs:
        if state.terminated:
            break
        with isolation_scope() as scope:
            job_ctx = {"id": job.id, "kind": job.kind, "created_at": job.created_at}
            scope.set_context("job", job_ctx)

            try:
                result_or_error = process_job(db, job)
            except Exception as e:
                capture_exception(e)
                logger.exception(e)
                db.rollback()
                result_or_error = "Unexpected error"

            if isinstance(result_or_error, BaseModel):
                job.completed_at = datetime.now(timezone.utc)
                job.status = "completed"
                job.result = result_or_error.model_dump(mode="json", round_trip=True)
            else:
                job.status = "failed"
                job.error = result_or_error

            db.commit()
    return True


RETENTION_PERIOD = timedelta(days=7)


def cleanup_old_jobs(db: Session):
    """Delete old jobs."""
    oldest_allowed = datetime.now(timezone.utc) - RETENTION_PERIOD
    db.execute(delete(Job).where(Job.status != "pending", Job.created_at < oldest_allowed))
    db.commit()


CLEANUP_INTERVAL = 60 * 60 * 24  # 1 day
MAX_CONTINUOUS_PROCESSING_TIME = 60 * 60 * 2  # 2 hours

last_cleanup = float(0)

while not state.terminated:
    db_session = SessionLocal()
    try:
        start_processing = time.time()
        while (
            not state.terminated
            and (time.time() - start_processing) < MAX_CONTINUOUS_PROCESSING_TIME
        ):
            had_jobs = process_jobs_batch(db_session)
            if not had_jobs:
                break

        if state.terminated:
            break
        now = time.time()
        if (now - last_cleanup) > CLEANUP_INTERVAL:
            logger.info("Cleaning up old jobs")
            cleanup_old_jobs(db_session)
            last_cleanup = now
    finally:
        db_session.close()
    if not state.terminated:
        time.sleep(1)
