"use client";

import * as RadixSeparator from "@radix-ui/react-separator";

import { cva, cx } from "class-variance-authority";

const separator = cva("shrink-0 bg-border", {
  variants: {
    orientation: {
      horizontal: "h-[1px] w-full",
      vertical: "h-full w-[1px]",
    },
  },
});

export function Separator({
  className,
  orientation = "horizontal",
  ...props
}: RadixSeparator.SeparatorProps) {
  return (
    <RadixSeparator.Root
      orientation={orientation}
      className={cx(separator({ orientation }), className)}
      {...props}
    />
  );
}
