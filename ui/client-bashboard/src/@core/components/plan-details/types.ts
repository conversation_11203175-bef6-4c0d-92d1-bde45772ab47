export interface PricingPlanType {
  title: string;
  imgSrc: string;
  imgWidth: number;
  subtitle: string;
  imgHeight: number;
  currentPlan: boolean;
  popularPlan: boolean;
  monthlyPrice: number;
  planBenefits: string[];
  yearlyPlan: {
    perMonth: number;
    totalAnnual: number;
  };
}

export interface PricingPlanProps {
  plan: string;
  data?: PricingPlanType;
}

export interface PricingFaqType {
  id: string;
  answer: string;
  question: string;
}

export interface PricingDataType {
  faq: PricingFaqType[];
  pricingPlans: PricingPlanType[];
}
