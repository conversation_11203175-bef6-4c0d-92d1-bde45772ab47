# syntax=docker/dockerfile:1
FROM python:3.12-slim AS python-runner

WORKDIR /usr/src/app/backend

# Disable pip warning about running as root, not relevant in a container
ENV PIP_ROOT_USER_ACTION=ignore

# Install dependencies
COPY backend/common/requirements.txt ./common/requirements.txt
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    --mount=type=cache,target=/root/.cache/pip,sharing=locked \
    bash -euxo pipefail <<EOF
# Remove apt docker-clean configuration to enable caching
rm -rf /etc/apt/apt.conf.d/docker-clean

# Install debian packages
apt-get update
apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    libpq5 \
    vim

# Upgrade pip and install requirements
pip install --upgrade pip
pip install --no-compile -r common/requirements.txt

# Remove build dependencies
apt-get purge -y --auto-remove \
    build-essential \
    libpq-dev
EOF

ENV PYTHONPATH=/usr/src/app/backend
CMD ["bash", "-c", "echo 'No command specified' && exit 1"]

# Target for development, i.e. to be used on develper's local machine
FROM python-runner AS dev

# Install development dependencies
COPY backend/common/dev-requirements.txt ./common/dev-requirements.txt
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    --mount=type=cache,target=/root/.cache/pip,sharing=locked \
    bash -euxo pipefail <<EOF
apt-get update
apt-get install -y --no-install-recommends ca-certificates curl gnupg lsb-release

# Add Node.js apt repository (https://github.com/nodesource/distributions)
mkdir -p /etc/apt/keyrings
curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg
NODE_MAJOR=22
echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_\$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list

# Add PostgreSQL apt repository
curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg
echo "deb https://apt.postgresql.org/pub/repos/apt bookworm-pgdg main" > /etc/apt/sources.list.d/pgdg.list

apt-get update
apt-get install -y --no-install-recommends nodejs postgresql-client-17

pip install ipython
pip install --no-compile -r common/dev-requirements.txt
EOF

# Use entrypoint to run commands before CMD
COPY infra/docker/python-runner-dev.entrypoint.sh /usr/src/
RUN chmod +x /usr/src/python-runner-dev.entrypoint.sh
ENTRYPOINT ["/usr/src/python-runner-dev.entrypoint.sh"]

CMD ["bash", "-c", "echo 'No command specified' && exit 1"]

# Last target is the default target, to be used for non-dev builds
FROM python-runner AS prod

COPY backend/ ./

ARG SENTRY_RELEASE
ENV SENTRY_RELEASE=${SENTRY_RELEASE}
